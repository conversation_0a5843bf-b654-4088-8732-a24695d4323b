{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../handlersRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 57, "index": 57}}], "key": "Q8MtNj8/mrt1iN8Kay94o881ERE=", "exportNames": ["*"]}}, {"name": "../../../RNGestureHandlerModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 58}, "end": {"line": 2, "column": 69, "index": 127}}], "key": "2BYIjnTRSFId8SRJ7sJFxLD1BD4=", "exportNames": ["*"]}}, {"name": "../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 128}, "end": {"line": 3, "column": 68, "index": 196}}], "key": "ByXat9lt9duIJLDmSeH0V+tRq1s=", "exportNames": ["*"]}}, {"name": "../../../ghQueueMicrotask", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 197}, "end": {"line": 4, "column": 61, "index": 258}}], "key": "6QYiO8x9sAoDBctSRJ19A8sqUNk=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 259}, "end": {"line": 5, "column": 99, "index": 358}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.updateHandlers = updateHandlers;\n  var _handlersRegistry = require(_dependencyMap[1], \"../../handlersRegistry\");\n  var _RNGestureHandlerModule = _interopRequireDefault(require(_dependencyMap[2], \"../../../RNGestureHandlerModule\"));\n  var _utils = require(_dependencyMap[3], \"../../utils\");\n  var _ghQueueMicrotask = require(_dependencyMap[4], \"../../../ghQueueMicrotask\");\n  var _utils2 = require(_dependencyMap[5], \"./utils\");\n  function updateHandlers(preparedGesture, gestureConfig, newGestures) {\n    gestureConfig.prepare();\n    for (let i = 0; i < newGestures.length; i++) {\n      const handler = preparedGesture.attachedGestures[i];\n      (0, _utils2.checkGestureCallbacksForWorklets)(handler); // Only update handlerTag when it's actually different, it may be the same\n      // if gesture config object is wrapped with useMemo\n\n      if (newGestures[i].handlerTag !== handler.handlerTag) {\n        newGestures[i].handlerTag = handler.handlerTag;\n        newGestures[i].handlers.handlerTag = handler.handlerTag;\n      }\n    } // Use queueMicrotask to extract handlerTags, because when it's ran, all refs should be updated\n    // and handlerTags in BaseGesture references should be updated in the loop above (we need to wait\n    // in case of external relations)\n\n    (0, _ghQueueMicrotask.ghQueueMicrotask)(() => {\n      if (!preparedGesture.isMounted) {\n        return;\n      } // If amount of gesture configs changes, we need to update the callbacks in shared value\n\n      let shouldUpdateSharedValueIfUsed = preparedGesture.attachedGestures.length !== newGestures.length;\n      for (let i = 0; i < newGestures.length; i++) {\n        const handler = preparedGesture.attachedGestures[i]; // If the gestureId is different (gesture isn't wrapped with useMemo or its dependencies changed),\n        // we need to update the shared value, assuming the gesture runs on UI thread or the thread changed\n\n        if (handler.handlers.gestureId !== newGestures[i].handlers.gestureId && (newGestures[i].shouldUseReanimated || handler.shouldUseReanimated)) {\n          shouldUpdateSharedValueIfUsed = true;\n        }\n        handler.config = newGestures[i].config;\n        handler.handlers = newGestures[i].handlers;\n        _RNGestureHandlerModule.default.updateGestureHandler(handler.handlerTag, (0, _utils.filterConfig)(handler.config, _utils2.ALLOWED_PROPS, (0, _utils2.extractGestureRelations)(handler)));\n        (0, _handlersRegistry.registerHandler)(handler.handlerTag, handler, handler.config.testId);\n      }\n      if (preparedGesture.animatedHandlers && shouldUpdateSharedValueIfUsed) {\n        const newHandlersValue = preparedGesture.attachedGestures.filter(g => g.shouldUseReanimated) // Ignore gestures that shouldn't run on UI\n        .map(g => g.handlers);\n        preparedGesture.animatedHandlers.value = newHandlersValue;\n      }\n      (0, _utils.scheduleFlushOperations)();\n    });\n  }\n});", "lineCount": 53, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_handlersRegistry"], [7, 23, 1, 0], [7, 26, 1, 0, "require"], [7, 33, 1, 0], [7, 34, 1, 0, "_dependencyMap"], [7, 48, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_RNGestureHandlerModule"], [8, 29, 2, 0], [8, 32, 2, 0, "_interopRequireDefault"], [8, 54, 2, 0], [8, 55, 2, 0, "require"], [8, 62, 2, 0], [8, 63, 2, 0, "_dependencyMap"], [8, 77, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_utils"], [9, 12, 3, 0], [9, 15, 3, 0, "require"], [9, 22, 3, 0], [9, 23, 3, 0, "_dependencyMap"], [9, 37, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_ghQueueMicrotask"], [10, 23, 4, 0], [10, 26, 4, 0, "require"], [10, 33, 4, 0], [10, 34, 4, 0, "_dependencyMap"], [10, 48, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_utils2"], [11, 13, 5, 0], [11, 16, 5, 0, "require"], [11, 23, 5, 0], [11, 24, 5, 0, "_dependencyMap"], [11, 38, 5, 0], [12, 2, 6, 7], [12, 11, 6, 16, "updateHandlers"], [12, 25, 6, 30, "updateHandlers"], [12, 26, 6, 31, "preparedGesture"], [12, 41, 6, 46], [12, 43, 6, 48, "gestureConfig"], [12, 56, 6, 61], [12, 58, 6, 63, "newGestures"], [12, 69, 6, 74], [12, 71, 6, 76], [13, 4, 7, 2, "gestureConfig"], [13, 17, 7, 15], [13, 18, 7, 16, "prepare"], [13, 25, 7, 23], [13, 26, 7, 24], [13, 27, 7, 25], [14, 4, 9, 2], [14, 9, 9, 7], [14, 13, 9, 11, "i"], [14, 14, 9, 12], [14, 17, 9, 15], [14, 18, 9, 16], [14, 20, 9, 18, "i"], [14, 21, 9, 19], [14, 24, 9, 22, "newGestures"], [14, 35, 9, 33], [14, 36, 9, 34, "length"], [14, 42, 9, 40], [14, 44, 9, 42, "i"], [14, 45, 9, 43], [14, 47, 9, 45], [14, 49, 9, 47], [15, 6, 10, 4], [15, 12, 10, 10, "handler"], [15, 19, 10, 17], [15, 22, 10, 20, "preparedGesture"], [15, 37, 10, 35], [15, 38, 10, 36, "attachedGestures"], [15, 54, 10, 52], [15, 55, 10, 53, "i"], [15, 56, 10, 54], [15, 57, 10, 55], [16, 6, 11, 4], [16, 10, 11, 4, "checkGestureCallbacksForWorklets"], [16, 50, 11, 36], [16, 52, 11, 37, "handler"], [16, 59, 11, 44], [16, 60, 11, 45], [16, 61, 11, 46], [16, 62, 11, 47], [17, 6, 12, 4], [19, 6, 14, 4], [19, 10, 14, 8, "newGestures"], [19, 21, 14, 19], [19, 22, 14, 20, "i"], [19, 23, 14, 21], [19, 24, 14, 22], [19, 25, 14, 23, "handlerTag"], [19, 35, 14, 33], [19, 40, 14, 38, "handler"], [19, 47, 14, 45], [19, 48, 14, 46, "handlerTag"], [19, 58, 14, 56], [19, 60, 14, 58], [20, 8, 15, 6, "newGestures"], [20, 19, 15, 17], [20, 20, 15, 18, "i"], [20, 21, 15, 19], [20, 22, 15, 20], [20, 23, 15, 21, "handlerTag"], [20, 33, 15, 31], [20, 36, 15, 34, "handler"], [20, 43, 15, 41], [20, 44, 15, 42, "handlerTag"], [20, 54, 15, 52], [21, 8, 16, 6, "newGestures"], [21, 19, 16, 17], [21, 20, 16, 18, "i"], [21, 21, 16, 19], [21, 22, 16, 20], [21, 23, 16, 21, "handlers"], [21, 31, 16, 29], [21, 32, 16, 30, "handlerTag"], [21, 42, 16, 40], [21, 45, 16, 43, "handler"], [21, 52, 16, 50], [21, 53, 16, 51, "handlerTag"], [21, 63, 16, 61], [22, 6, 17, 4], [23, 4, 18, 2], [23, 5, 18, 3], [23, 6, 18, 4], [24, 4, 19, 2], [25, 4, 20, 2], [27, 4, 23, 2], [27, 8, 23, 2, "ghQueueMicrotask"], [27, 42, 23, 18], [27, 44, 23, 19], [27, 50, 23, 25], [28, 6, 24, 4], [28, 10, 24, 8], [28, 11, 24, 9, "preparedGesture"], [28, 26, 24, 24], [28, 27, 24, 25, "isMounted"], [28, 36, 24, 34], [28, 38, 24, 36], [29, 8, 25, 6], [30, 6, 26, 4], [30, 7, 26, 5], [30, 8, 26, 6], [32, 6, 29, 4], [32, 10, 29, 8, "shouldUpdateSharedValueIfUsed"], [32, 39, 29, 37], [32, 42, 29, 40, "preparedGesture"], [32, 57, 29, 55], [32, 58, 29, 56, "attachedGestures"], [32, 74, 29, 72], [32, 75, 29, 73, "length"], [32, 81, 29, 79], [32, 86, 29, 84, "newGestures"], [32, 97, 29, 95], [32, 98, 29, 96, "length"], [32, 104, 29, 102], [33, 6, 31, 4], [33, 11, 31, 9], [33, 15, 31, 13, "i"], [33, 16, 31, 14], [33, 19, 31, 17], [33, 20, 31, 18], [33, 22, 31, 20, "i"], [33, 23, 31, 21], [33, 26, 31, 24, "newGestures"], [33, 37, 31, 35], [33, 38, 31, 36, "length"], [33, 44, 31, 42], [33, 46, 31, 44, "i"], [33, 47, 31, 45], [33, 49, 31, 47], [33, 51, 31, 49], [34, 8, 32, 6], [34, 14, 32, 12, "handler"], [34, 21, 32, 19], [34, 24, 32, 22, "preparedGesture"], [34, 39, 32, 37], [34, 40, 32, 38, "attachedGestures"], [34, 56, 32, 54], [34, 57, 32, 55, "i"], [34, 58, 32, 56], [34, 59, 32, 57], [34, 60, 32, 58], [34, 61, 32, 59], [35, 8, 33, 6], [37, 8, 35, 6], [37, 12, 35, 10, "handler"], [37, 19, 35, 17], [37, 20, 35, 18, "handlers"], [37, 28, 35, 26], [37, 29, 35, 27, "gestureId"], [37, 38, 35, 36], [37, 43, 35, 41, "newGestures"], [37, 54, 35, 52], [37, 55, 35, 53, "i"], [37, 56, 35, 54], [37, 57, 35, 55], [37, 58, 35, 56, "handlers"], [37, 66, 35, 64], [37, 67, 35, 65, "gestureId"], [37, 76, 35, 74], [37, 81, 35, 79, "newGestures"], [37, 92, 35, 90], [37, 93, 35, 91, "i"], [37, 94, 35, 92], [37, 95, 35, 93], [37, 96, 35, 94, "shouldUseReanimated"], [37, 115, 35, 113], [37, 119, 35, 117, "handler"], [37, 126, 35, 124], [37, 127, 35, 125, "shouldUseReanimated"], [37, 146, 35, 144], [37, 147, 35, 145], [37, 149, 35, 147], [38, 10, 36, 8, "shouldUpdateSharedValueIfUsed"], [38, 39, 36, 37], [38, 42, 36, 40], [38, 46, 36, 44], [39, 8, 37, 6], [40, 8, 39, 6, "handler"], [40, 15, 39, 13], [40, 16, 39, 14, "config"], [40, 22, 39, 20], [40, 25, 39, 23, "newGestures"], [40, 36, 39, 34], [40, 37, 39, 35, "i"], [40, 38, 39, 36], [40, 39, 39, 37], [40, 40, 39, 38, "config"], [40, 46, 39, 44], [41, 8, 40, 6, "handler"], [41, 15, 40, 13], [41, 16, 40, 14, "handlers"], [41, 24, 40, 22], [41, 27, 40, 25, "newGestures"], [41, 38, 40, 36], [41, 39, 40, 37, "i"], [41, 40, 40, 38], [41, 41, 40, 39], [41, 42, 40, 40, "handlers"], [41, 50, 40, 48], [42, 8, 41, 6, "RNGestureHandlerModule"], [42, 39, 41, 28], [42, 40, 41, 29, "updateGestureHandler"], [42, 60, 41, 49], [42, 61, 41, 50, "handler"], [42, 68, 41, 57], [42, 69, 41, 58, "handlerTag"], [42, 79, 41, 68], [42, 81, 41, 70], [42, 85, 41, 70, "filterConfig"], [42, 104, 41, 82], [42, 106, 41, 83, "handler"], [42, 113, 41, 90], [42, 114, 41, 91, "config"], [42, 120, 41, 97], [42, 122, 41, 99, "ALLOWED_PROPS"], [42, 143, 41, 112], [42, 145, 41, 114], [42, 149, 41, 114, "extractGestureRelations"], [42, 180, 41, 137], [42, 182, 41, 138, "handler"], [42, 189, 41, 145], [42, 190, 41, 146], [42, 191, 41, 147], [42, 192, 41, 148], [43, 8, 42, 6], [43, 12, 42, 6, "registerHandler"], [43, 45, 42, 21], [43, 47, 42, 22, "handler"], [43, 54, 42, 29], [43, 55, 42, 30, "handlerTag"], [43, 65, 42, 40], [43, 67, 42, 42, "handler"], [43, 74, 42, 49], [43, 76, 42, 51, "handler"], [43, 83, 42, 58], [43, 84, 42, 59, "config"], [43, 90, 42, 65], [43, 91, 42, 66, "testId"], [43, 97, 42, 72], [43, 98, 42, 73], [44, 6, 43, 4], [45, 6, 45, 4], [45, 10, 45, 8, "preparedGesture"], [45, 25, 45, 23], [45, 26, 45, 24, "animatedHandlers"], [45, 42, 45, 40], [45, 46, 45, 44, "shouldUpdateSharedValueIfUsed"], [45, 75, 45, 73], [45, 77, 45, 75], [46, 8, 46, 6], [46, 14, 46, 12, "newHandlersValue"], [46, 30, 46, 28], [46, 33, 46, 31, "preparedGesture"], [46, 48, 46, 46], [46, 49, 46, 47, "attachedGestures"], [46, 65, 46, 63], [46, 66, 46, 64, "filter"], [46, 72, 46, 70], [46, 73, 46, 71, "g"], [46, 74, 46, 72], [46, 78, 46, 76, "g"], [46, 79, 46, 77], [46, 80, 46, 78, "shouldUseReanimated"], [46, 99, 46, 97], [46, 100, 46, 98], [46, 101, 46, 99], [47, 8, 46, 99], [47, 9, 47, 7, "map"], [47, 12, 47, 10], [47, 13, 47, 11, "g"], [47, 14, 47, 12], [47, 18, 47, 16, "g"], [47, 19, 47, 17], [47, 20, 47, 18, "handlers"], [47, 28, 47, 26], [47, 29, 47, 27], [48, 8, 48, 6, "preparedGesture"], [48, 23, 48, 21], [48, 24, 48, 22, "animatedHandlers"], [48, 40, 48, 38], [48, 41, 48, 39, "value"], [48, 46, 48, 44], [48, 49, 48, 47, "newHandlersValue"], [48, 65, 48, 63], [49, 6, 49, 4], [50, 6, 51, 4], [50, 10, 51, 4, "scheduleFlushOperations"], [50, 40, 51, 27], [50, 42, 51, 28], [50, 43, 51, 29], [51, 4, 52, 2], [51, 5, 52, 3], [51, 6, 52, 4], [52, 2, 53, 0], [53, 0, 53, 1], [53, 3]], "functionMap": {"names": ["<global>", "updateHandlers", "ghQueueMicrotask$argument_0", "preparedGesture.attachedGestures.filter$argument_0", "preparedGesture.attachedGestures.filter.map$argument_0"], "mappings": "AAA;OCK;mBCiB;uECuB,0BD;WEC,eF;GDK;CDC"}}, "type": "js/module"}]}