{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 89}}], "key": "TuB5rvhhYFP7S1O2+poQUZyTlqI=", "exportNames": ["*"]}}, {"name": "../../Utilities/codegenNativeCommands", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 74}}], "key": "4G4LeVO/m4MSH9iel0oMRTsRk+g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.Commands = void 0;\n  var NativeComponentRegistry = _interopRequireWildcard(require(_dependencyMap[1], \"../../NativeComponent/NativeComponentRegistry\"));\n  var _codegenNativeCommands = _interopRequireDefault(require(_dependencyMap[2], \"../../Utilities/codegenNativeCommands\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var ViewNativeComponent = NativeComponentRegistry.get('RCTView', () => ({\n    uiViewClassName: 'RCTView'\n  }));\n  var Commands = exports.Commands = (0, _codegenNativeCommands.default)({\n    supportedCommands: ['hotspotUpdate', 'setPressed']\n  });\n  var _default = exports.default = ViewNativeComponent;\n});", "lineCount": 17, "map": [[7, 2, 14, 0], [7, 6, 14, 0, "NativeComponentRegistry"], [7, 29, 14, 0], [7, 32, 14, 0, "_interopRequireWildcard"], [7, 55, 14, 0], [7, 56, 14, 0, "require"], [7, 63, 14, 0], [7, 64, 14, 0, "_dependencyMap"], [7, 78, 14, 0], [8, 2, 15, 0], [8, 6, 15, 0, "_codegenNativeCommands"], [8, 28, 15, 0], [8, 31, 15, 0, "_interopRequireDefault"], [8, 53, 15, 0], [8, 54, 15, 0, "require"], [8, 61, 15, 0], [8, 62, 15, 0, "_dependencyMap"], [8, 76, 15, 0], [9, 2, 15, 74], [9, 11, 15, 74, "_interopRequireWildcard"], [9, 35, 15, 74, "e"], [9, 36, 15, 74], [9, 38, 15, 74, "t"], [9, 39, 15, 74], [9, 68, 15, 74, "WeakMap"], [9, 75, 15, 74], [9, 81, 15, 74, "r"], [9, 82, 15, 74], [9, 89, 15, 74, "WeakMap"], [9, 96, 15, 74], [9, 100, 15, 74, "n"], [9, 101, 15, 74], [9, 108, 15, 74, "WeakMap"], [9, 115, 15, 74], [9, 127, 15, 74, "_interopRequireWildcard"], [9, 150, 15, 74], [9, 162, 15, 74, "_interopRequireWildcard"], [9, 163, 15, 74, "e"], [9, 164, 15, 74], [9, 166, 15, 74, "t"], [9, 167, 15, 74], [9, 176, 15, 74, "t"], [9, 177, 15, 74], [9, 181, 15, 74, "e"], [9, 182, 15, 74], [9, 186, 15, 74, "e"], [9, 187, 15, 74], [9, 188, 15, 74, "__esModule"], [9, 198, 15, 74], [9, 207, 15, 74, "e"], [9, 208, 15, 74], [9, 214, 15, 74, "o"], [9, 215, 15, 74], [9, 217, 15, 74, "i"], [9, 218, 15, 74], [9, 220, 15, 74, "f"], [9, 221, 15, 74], [9, 226, 15, 74, "__proto__"], [9, 235, 15, 74], [9, 243, 15, 74, "default"], [9, 250, 15, 74], [9, 252, 15, 74, "e"], [9, 253, 15, 74], [9, 270, 15, 74, "e"], [9, 271, 15, 74], [9, 294, 15, 74, "e"], [9, 295, 15, 74], [9, 320, 15, 74, "e"], [9, 321, 15, 74], [9, 330, 15, 74, "f"], [9, 331, 15, 74], [9, 337, 15, 74, "o"], [9, 338, 15, 74], [9, 341, 15, 74, "t"], [9, 342, 15, 74], [9, 345, 15, 74, "n"], [9, 346, 15, 74], [9, 349, 15, 74, "r"], [9, 350, 15, 74], [9, 358, 15, 74, "o"], [9, 359, 15, 74], [9, 360, 15, 74, "has"], [9, 363, 15, 74], [9, 364, 15, 74, "e"], [9, 365, 15, 74], [9, 375, 15, 74, "o"], [9, 376, 15, 74], [9, 377, 15, 74, "get"], [9, 380, 15, 74], [9, 381, 15, 74, "e"], [9, 382, 15, 74], [9, 385, 15, 74, "o"], [9, 386, 15, 74], [9, 387, 15, 74, "set"], [9, 390, 15, 74], [9, 391, 15, 74, "e"], [9, 392, 15, 74], [9, 394, 15, 74, "f"], [9, 395, 15, 74], [9, 409, 15, 74, "_t"], [9, 411, 15, 74], [9, 415, 15, 74, "e"], [9, 416, 15, 74], [9, 432, 15, 74, "_t"], [9, 434, 15, 74], [9, 441, 15, 74, "hasOwnProperty"], [9, 455, 15, 74], [9, 456, 15, 74, "call"], [9, 460, 15, 74], [9, 461, 15, 74, "e"], [9, 462, 15, 74], [9, 464, 15, 74, "_t"], [9, 466, 15, 74], [9, 473, 15, 74, "i"], [9, 474, 15, 74], [9, 478, 15, 74, "o"], [9, 479, 15, 74], [9, 482, 15, 74, "Object"], [9, 488, 15, 74], [9, 489, 15, 74, "defineProperty"], [9, 503, 15, 74], [9, 508, 15, 74, "Object"], [9, 514, 15, 74], [9, 515, 15, 74, "getOwnPropertyDescriptor"], [9, 539, 15, 74], [9, 540, 15, 74, "e"], [9, 541, 15, 74], [9, 543, 15, 74, "_t"], [9, 545, 15, 74], [9, 552, 15, 74, "i"], [9, 553, 15, 74], [9, 554, 15, 74, "get"], [9, 557, 15, 74], [9, 561, 15, 74, "i"], [9, 562, 15, 74], [9, 563, 15, 74, "set"], [9, 566, 15, 74], [9, 570, 15, 74, "o"], [9, 571, 15, 74], [9, 572, 15, 74, "f"], [9, 573, 15, 74], [9, 575, 15, 74, "_t"], [9, 577, 15, 74], [9, 579, 15, 74, "i"], [9, 580, 15, 74], [9, 584, 15, 74, "f"], [9, 585, 15, 74], [9, 586, 15, 74, "_t"], [9, 588, 15, 74], [9, 592, 15, 74, "e"], [9, 593, 15, 74], [9, 594, 15, 74, "_t"], [9, 596, 15, 74], [9, 607, 15, 74, "f"], [9, 608, 15, 74], [9, 613, 15, 74, "e"], [9, 614, 15, 74], [9, 616, 15, 74, "t"], [9, 617, 15, 74], [10, 2, 18, 0], [10, 6, 18, 6, "ViewNativeComponent"], [10, 25, 18, 47], [10, 28, 19, 2, "NativeComponentRegistry"], [10, 51, 19, 25], [10, 52, 19, 26, "get"], [10, 55, 19, 29], [10, 56, 19, 37], [10, 65, 19, 46], [10, 67, 19, 48], [10, 74, 19, 55], [11, 4, 20, 4, "uiViewClassName"], [11, 19, 20, 19], [11, 21, 20, 21], [12, 2, 21, 2], [12, 3, 21, 3], [12, 4, 21, 4], [12, 5, 21, 5], [13, 2, 28, 7], [13, 6, 28, 13, "Commands"], [13, 14, 28, 37], [13, 17, 28, 37, "exports"], [13, 24, 28, 37], [13, 25, 28, 37, "Commands"], [13, 33, 28, 37], [13, 36, 28, 40], [13, 40, 28, 40, "codegenNativeCommands"], [13, 70, 28, 61], [13, 72, 28, 78], [14, 4, 29, 2, "supportedCommands"], [14, 21, 29, 19], [14, 23, 29, 21], [14, 24, 29, 22], [14, 39, 29, 37], [14, 41, 29, 39], [14, 53, 29, 51], [15, 2, 30, 0], [15, 3, 30, 1], [15, 4, 30, 2], [16, 2, 30, 3], [16, 6, 30, 3, "_default"], [16, 14, 30, 3], [16, 17, 30, 3, "exports"], [16, 24, 30, 3], [16, 25, 30, 3, "default"], [16, 32, 30, 3], [16, 35, 32, 15, "ViewNativeComponent"], [16, 54, 32, 34], [17, 0, 32, 34], [17, 3]], "functionMap": {"names": ["<global>", "NativeComponentRegistry.get$argument_1"], "mappings": "AAA;gDCkB;IDE"}}, "type": "js/module"}]}