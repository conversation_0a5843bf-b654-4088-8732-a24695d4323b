import { useColorScheme } from 'react-native';

export const useTheme = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const colors = {
    // Base surfaces
    background: isDark ? '#121212' : '#FFFFFF',
    surface: isDark ? '#1E1E1E' : '#F5F5F7',
    elevated: isDark ? '#262626' : '#FFFFFF',
    
    // Text colors
    text: isDark ? 'rgba(255, 255, 255, 0.87)' : '#000000',
    textSecondary: isDark ? 'rgba(255, 255, 255, 0.60)' : '#6B7280',
    textTertiary: isDark ? 'rgba(255, 255, 255, 0.45)' : '#9CA3AF',
    
    // Brand colors
    primary: isDark ? '#8B5CF6' : '#7C3AED',
    accent: isDark ? '#A78BFA' : '#8B5CF6',
    
    // Semantic colors
    error: isDark ? '#FF6B6B' : '#FF4444',
    success: isDark ? '#51CF66' : '#4CAF50',
    warning: isDark ? '#FFD43B' : '#FF9800',
    
    // UI elements
    border: isDark ? '#333333' : '#E5E7EB',
    headerBorder: isDark ? '#333333' : '#E5E7EB',
    cardBackground: isDark ? '#1E1E1E' : '#F9FAFB',
    buttonPrimary: isDark ? '#FFFFFF' : '#000000',
    buttonPrimaryText: isDark ? '#000000' : '#FFFFFF',
    
    // Special cases
    headerBackground: isDark ? 'rgba(18, 18, 18, 0.95)' : 'rgba(255, 255, 255, 0.95)',
    shadow: isDark ? 'rgba(0, 0, 0, 0.6)' : 'rgba(0, 0, 0, 0.1)',
    
    // Empty state
    emptyStateBackground: isDark ? '#262626' : '#F3F4F6',
  };

  return {
    colors,
    isDark,
  };
};