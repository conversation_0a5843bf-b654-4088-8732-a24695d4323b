{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../modules/getBoundingClientRect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 180}, "end": {"line": 10, "column": 72, "index": 252}}], "key": "i4wgS+O9VZXcSNXv3Sd7drhcmAo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = createResponderEvent;\n  var _getBoundingClientRect = _interopRequireDefault(require(_dependencyMap[1], \"../../modules/getBoundingClientRect\"));\n  /**\n   * Copyright (c) Nicolas Gallagher\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var emptyFunction = () => {};\n  var emptyObject = {};\n  var emptyArray = [];\n\n  /**\n   * <PERSON><PERSON> produces very large identifiers that would cause the `touchBank` array\n   * length to be so large as to crash the browser, if not normalized like this.\n   * In the future the `touchBank` should use an object/map instead.\n   */\n  function normalizeIdentifier(identifier) {\n    return identifier > 20 ? identifier % 20 : identifier;\n  }\n\n  /**\n   * Converts a native DOM event to a ResponderEvent.\n   * Mouse events are transformed into fake touch events.\n   */\n  function createResponderEvent(domEvent, responderTouchHistoryStore) {\n    var rect;\n    var propagationWasStopped = false;\n    var changedTouches;\n    var touches;\n    var domEventChangedTouches = domEvent.changedTouches;\n    var domEventType = domEvent.type;\n    var metaKey = domEvent.metaKey === true;\n    var shiftKey = domEvent.shiftKey === true;\n    var force = domEventChangedTouches && domEventChangedTouches[0].force || 0;\n    var identifier = normalizeIdentifier(domEventChangedTouches && domEventChangedTouches[0].identifier || 0);\n    var clientX = domEventChangedTouches && domEventChangedTouches[0].clientX || domEvent.clientX;\n    var clientY = domEventChangedTouches && domEventChangedTouches[0].clientY || domEvent.clientY;\n    var pageX = domEventChangedTouches && domEventChangedTouches[0].pageX || domEvent.pageX;\n    var pageY = domEventChangedTouches && domEventChangedTouches[0].pageY || domEvent.pageY;\n    var preventDefault = typeof domEvent.preventDefault === 'function' ? domEvent.preventDefault.bind(domEvent) : emptyFunction;\n    var timestamp = domEvent.timeStamp;\n    function normalizeTouches(touches) {\n      return Array.prototype.slice.call(touches).map(touch => {\n        return {\n          force: touch.force,\n          identifier: normalizeIdentifier(touch.identifier),\n          get locationX() {\n            return locationX(touch.clientX);\n          },\n          get locationY() {\n            return locationY(touch.clientY);\n          },\n          pageX: touch.pageX,\n          pageY: touch.pageY,\n          target: touch.target,\n          timestamp\n        };\n      });\n    }\n    if (domEventChangedTouches != null) {\n      changedTouches = normalizeTouches(domEventChangedTouches);\n      touches = normalizeTouches(domEvent.touches);\n    } else {\n      var emulatedTouches = [{\n        force,\n        identifier,\n        get locationX() {\n          return locationX(clientX);\n        },\n        get locationY() {\n          return locationY(clientY);\n        },\n        pageX,\n        pageY,\n        target: domEvent.target,\n        timestamp\n      }];\n      changedTouches = emulatedTouches;\n      touches = domEventType === 'mouseup' || domEventType === 'dragstart' ? emptyArray : emulatedTouches;\n    }\n    var responderEvent = {\n      bubbles: true,\n      cancelable: true,\n      // `currentTarget` is set before dispatch\n      currentTarget: null,\n      defaultPrevented: domEvent.defaultPrevented,\n      dispatchConfig: emptyObject,\n      eventPhase: domEvent.eventPhase,\n      isDefaultPrevented() {\n        return domEvent.defaultPrevented;\n      },\n      isPropagationStopped() {\n        return propagationWasStopped;\n      },\n      isTrusted: domEvent.isTrusted,\n      nativeEvent: {\n        altKey: false,\n        ctrlKey: false,\n        metaKey,\n        shiftKey,\n        changedTouches,\n        force,\n        identifier,\n        get locationX() {\n          return locationX(clientX);\n        },\n        get locationY() {\n          return locationY(clientY);\n        },\n        pageX,\n        pageY,\n        target: domEvent.target,\n        timestamp,\n        touches,\n        type: domEventType\n      },\n      persist: emptyFunction,\n      preventDefault,\n      stopPropagation() {\n        propagationWasStopped = true;\n      },\n      target: domEvent.target,\n      timeStamp: timestamp,\n      touchHistory: responderTouchHistoryStore.touchHistory\n    };\n\n    // Using getters and functions serves two purposes:\n    // 1) The value of `currentTarget` is not initially available.\n    // 2) Measuring the clientRect may cause layout jank and should only be done on-demand.\n    function locationX(x) {\n      rect = rect || (0, _getBoundingClientRect.default)(responderEvent.currentTarget);\n      if (rect) {\n        return x - rect.left;\n      }\n    }\n    function locationY(y) {\n      rect = rect || (0, _getBoundingClientRect.default)(responderEvent.currentTarget);\n      if (rect) {\n        return y - rect.top;\n      }\n    }\n    return responderEvent;\n  }\n});", "lineCount": 153, "map": [[7, 2, 10, 0], [7, 6, 10, 0, "_getBoundingClientRect"], [7, 28, 10, 0], [7, 31, 10, 0, "_interopRequireDefault"], [7, 53, 10, 0], [7, 54, 10, 0, "require"], [7, 61, 10, 0], [7, 62, 10, 0, "_dependencyMap"], [7, 76, 10, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [14, 0, 7, 0], [15, 0, 8, 0], [17, 2, 11, 0], [17, 6, 11, 4, "emptyFunction"], [17, 19, 11, 17], [17, 22, 11, 20, "emptyFunction"], [17, 23, 11, 20], [17, 28, 11, 26], [17, 29, 11, 27], [17, 30, 11, 28], [18, 2, 12, 0], [18, 6, 12, 4, "emptyObject"], [18, 17, 12, 15], [18, 20, 12, 18], [18, 21, 12, 19], [18, 22, 12, 20], [19, 2, 13, 0], [19, 6, 13, 4, "emptyArray"], [19, 16, 13, 14], [19, 19, 13, 17], [19, 21, 13, 19], [21, 2, 15, 0], [22, 0, 16, 0], [23, 0, 17, 0], [24, 0, 18, 0], [25, 0, 19, 0], [26, 2, 20, 0], [26, 11, 20, 9, "normalizeIdentifier"], [26, 30, 20, 28, "normalizeIdentifier"], [26, 31, 20, 29, "identifier"], [26, 41, 20, 39], [26, 43, 20, 41], [27, 4, 21, 2], [27, 11, 21, 9, "identifier"], [27, 21, 21, 19], [27, 24, 21, 22], [27, 26, 21, 24], [27, 29, 21, 27, "identifier"], [27, 39, 21, 37], [27, 42, 21, 40], [27, 44, 21, 42], [27, 47, 21, 45, "identifier"], [27, 57, 21, 55], [28, 2, 22, 0], [30, 2, 24, 0], [31, 0, 25, 0], [32, 0, 26, 0], [33, 0, 27, 0], [34, 2, 28, 15], [34, 11, 28, 24, "createResponderEvent"], [34, 31, 28, 44, "createResponderEvent"], [34, 32, 28, 45, "domEvent"], [34, 40, 28, 53], [34, 42, 28, 55, "responderTouchHistoryStore"], [34, 68, 28, 81], [34, 70, 28, 83], [35, 4, 29, 2], [35, 8, 29, 6, "rect"], [35, 12, 29, 10], [36, 4, 30, 2], [36, 8, 30, 6, "propagationWasStopped"], [36, 29, 30, 27], [36, 32, 30, 30], [36, 37, 30, 35], [37, 4, 31, 2], [37, 8, 31, 6, "changedTouches"], [37, 22, 31, 20], [38, 4, 32, 2], [38, 8, 32, 6, "touches"], [38, 15, 32, 13], [39, 4, 33, 2], [39, 8, 33, 6, "domEventChangedTouches"], [39, 30, 33, 28], [39, 33, 33, 31, "domEvent"], [39, 41, 33, 39], [39, 42, 33, 40, "changedTouches"], [39, 56, 33, 54], [40, 4, 34, 2], [40, 8, 34, 6, "domEventType"], [40, 20, 34, 18], [40, 23, 34, 21, "domEvent"], [40, 31, 34, 29], [40, 32, 34, 30, "type"], [40, 36, 34, 34], [41, 4, 35, 2], [41, 8, 35, 6, "metaKey"], [41, 15, 35, 13], [41, 18, 35, 16, "domEvent"], [41, 26, 35, 24], [41, 27, 35, 25, "metaKey"], [41, 34, 35, 32], [41, 39, 35, 37], [41, 43, 35, 41], [42, 4, 36, 2], [42, 8, 36, 6, "shift<PERSON>ey"], [42, 16, 36, 14], [42, 19, 36, 17, "domEvent"], [42, 27, 36, 25], [42, 28, 36, 26, "shift<PERSON>ey"], [42, 36, 36, 34], [42, 41, 36, 39], [42, 45, 36, 43], [43, 4, 37, 2], [43, 8, 37, 6, "force"], [43, 13, 37, 11], [43, 16, 37, 14, "domEventChangedTouches"], [43, 38, 37, 36], [43, 42, 37, 40, "domEventChangedTouches"], [43, 64, 37, 62], [43, 65, 37, 63], [43, 66, 37, 64], [43, 67, 37, 65], [43, 68, 37, 66, "force"], [43, 73, 37, 71], [43, 77, 37, 75], [43, 78, 37, 76], [44, 4, 38, 2], [44, 8, 38, 6, "identifier"], [44, 18, 38, 16], [44, 21, 38, 19, "normalizeIdentifier"], [44, 40, 38, 38], [44, 41, 38, 39, "domEventChangedTouches"], [44, 63, 38, 61], [44, 67, 38, 65, "domEventChangedTouches"], [44, 89, 38, 87], [44, 90, 38, 88], [44, 91, 38, 89], [44, 92, 38, 90], [44, 93, 38, 91, "identifier"], [44, 103, 38, 101], [44, 107, 38, 105], [44, 108, 38, 106], [44, 109, 38, 107], [45, 4, 39, 2], [45, 8, 39, 6, "clientX"], [45, 15, 39, 13], [45, 18, 39, 16, "domEventChangedTouches"], [45, 40, 39, 38], [45, 44, 39, 42, "domEventChangedTouches"], [45, 66, 39, 64], [45, 67, 39, 65], [45, 68, 39, 66], [45, 69, 39, 67], [45, 70, 39, 68, "clientX"], [45, 77, 39, 75], [45, 81, 39, 79, "domEvent"], [45, 89, 39, 87], [45, 90, 39, 88, "clientX"], [45, 97, 39, 95], [46, 4, 40, 2], [46, 8, 40, 6, "clientY"], [46, 15, 40, 13], [46, 18, 40, 16, "domEventChangedTouches"], [46, 40, 40, 38], [46, 44, 40, 42, "domEventChangedTouches"], [46, 66, 40, 64], [46, 67, 40, 65], [46, 68, 40, 66], [46, 69, 40, 67], [46, 70, 40, 68, "clientY"], [46, 77, 40, 75], [46, 81, 40, 79, "domEvent"], [46, 89, 40, 87], [46, 90, 40, 88, "clientY"], [46, 97, 40, 95], [47, 4, 41, 2], [47, 8, 41, 6, "pageX"], [47, 13, 41, 11], [47, 16, 41, 14, "domEventChangedTouches"], [47, 38, 41, 36], [47, 42, 41, 40, "domEventChangedTouches"], [47, 64, 41, 62], [47, 65, 41, 63], [47, 66, 41, 64], [47, 67, 41, 65], [47, 68, 41, 66, "pageX"], [47, 73, 41, 71], [47, 77, 41, 75, "domEvent"], [47, 85, 41, 83], [47, 86, 41, 84, "pageX"], [47, 91, 41, 89], [48, 4, 42, 2], [48, 8, 42, 6, "pageY"], [48, 13, 42, 11], [48, 16, 42, 14, "domEventChangedTouches"], [48, 38, 42, 36], [48, 42, 42, 40, "domEventChangedTouches"], [48, 64, 42, 62], [48, 65, 42, 63], [48, 66, 42, 64], [48, 67, 42, 65], [48, 68, 42, 66, "pageY"], [48, 73, 42, 71], [48, 77, 42, 75, "domEvent"], [48, 85, 42, 83], [48, 86, 42, 84, "pageY"], [48, 91, 42, 89], [49, 4, 43, 2], [49, 8, 43, 6, "preventDefault"], [49, 22, 43, 20], [49, 25, 43, 23], [49, 32, 43, 30, "domEvent"], [49, 40, 43, 38], [49, 41, 43, 39, "preventDefault"], [49, 55, 43, 53], [49, 60, 43, 58], [49, 70, 43, 68], [49, 73, 43, 71, "domEvent"], [49, 81, 43, 79], [49, 82, 43, 80, "preventDefault"], [49, 96, 43, 94], [49, 97, 43, 95, "bind"], [49, 101, 43, 99], [49, 102, 43, 100, "domEvent"], [49, 110, 43, 108], [49, 111, 43, 109], [49, 114, 43, 112, "emptyFunction"], [49, 127, 43, 125], [50, 4, 44, 2], [50, 8, 44, 6, "timestamp"], [50, 17, 44, 15], [50, 20, 44, 18, "domEvent"], [50, 28, 44, 26], [50, 29, 44, 27, "timeStamp"], [50, 38, 44, 36], [51, 4, 45, 2], [51, 13, 45, 11, "normalizeTouches"], [51, 29, 45, 27, "normalizeTouches"], [51, 30, 45, 28, "touches"], [51, 37, 45, 35], [51, 39, 45, 37], [52, 6, 46, 4], [52, 13, 46, 11, "Array"], [52, 18, 46, 16], [52, 19, 46, 17, "prototype"], [52, 28, 46, 26], [52, 29, 46, 27, "slice"], [52, 34, 46, 32], [52, 35, 46, 33, "call"], [52, 39, 46, 37], [52, 40, 46, 38, "touches"], [52, 47, 46, 45], [52, 48, 46, 46], [52, 49, 46, 47, "map"], [52, 52, 46, 50], [52, 53, 46, 51, "touch"], [52, 58, 46, 56], [52, 62, 46, 60], [53, 8, 47, 6], [53, 15, 47, 13], [54, 10, 48, 8, "force"], [54, 15, 48, 13], [54, 17, 48, 15, "touch"], [54, 22, 48, 20], [54, 23, 48, 21, "force"], [54, 28, 48, 26], [55, 10, 49, 8, "identifier"], [55, 20, 49, 18], [55, 22, 49, 20, "normalizeIdentifier"], [55, 41, 49, 39], [55, 42, 49, 40, "touch"], [55, 47, 49, 45], [55, 48, 49, 46, "identifier"], [55, 58, 49, 56], [55, 59, 49, 57], [56, 10, 50, 8], [56, 14, 50, 12, "locationX"], [56, 23, 50, 21, "locationX"], [56, 24, 50, 21], [56, 26, 50, 24], [57, 12, 51, 10], [57, 19, 51, 17, "locationX"], [57, 28, 51, 26], [57, 29, 51, 27, "touch"], [57, 34, 51, 32], [57, 35, 51, 33, "clientX"], [57, 42, 51, 40], [57, 43, 51, 41], [58, 10, 52, 8], [58, 11, 52, 9], [59, 10, 53, 8], [59, 14, 53, 12, "locationY"], [59, 23, 53, 21, "locationY"], [59, 24, 53, 21], [59, 26, 53, 24], [60, 12, 54, 10], [60, 19, 54, 17, "locationY"], [60, 28, 54, 26], [60, 29, 54, 27, "touch"], [60, 34, 54, 32], [60, 35, 54, 33, "clientY"], [60, 42, 54, 40], [60, 43, 54, 41], [61, 10, 55, 8], [61, 11, 55, 9], [62, 10, 56, 8, "pageX"], [62, 15, 56, 13], [62, 17, 56, 15, "touch"], [62, 22, 56, 20], [62, 23, 56, 21, "pageX"], [62, 28, 56, 26], [63, 10, 57, 8, "pageY"], [63, 15, 57, 13], [63, 17, 57, 15, "touch"], [63, 22, 57, 20], [63, 23, 57, 21, "pageY"], [63, 28, 57, 26], [64, 10, 58, 8, "target"], [64, 16, 58, 14], [64, 18, 58, 16, "touch"], [64, 23, 58, 21], [64, 24, 58, 22, "target"], [64, 30, 58, 28], [65, 10, 59, 8, "timestamp"], [66, 8, 60, 6], [66, 9, 60, 7], [67, 6, 61, 4], [67, 7, 61, 5], [67, 8, 61, 6], [68, 4, 62, 2], [69, 4, 63, 2], [69, 8, 63, 6, "domEventChangedTouches"], [69, 30, 63, 28], [69, 34, 63, 32], [69, 38, 63, 36], [69, 40, 63, 38], [70, 6, 64, 4, "changedTouches"], [70, 20, 64, 18], [70, 23, 64, 21, "normalizeTouches"], [70, 39, 64, 37], [70, 40, 64, 38, "domEventChangedTouches"], [70, 62, 64, 60], [70, 63, 64, 61], [71, 6, 65, 4, "touches"], [71, 13, 65, 11], [71, 16, 65, 14, "normalizeTouches"], [71, 32, 65, 30], [71, 33, 65, 31, "domEvent"], [71, 41, 65, 39], [71, 42, 65, 40, "touches"], [71, 49, 65, 47], [71, 50, 65, 48], [72, 4, 66, 2], [72, 5, 66, 3], [72, 11, 66, 9], [73, 6, 67, 4], [73, 10, 67, 8, "emulatedTouches"], [73, 25, 67, 23], [73, 28, 67, 26], [73, 29, 67, 27], [74, 8, 68, 6, "force"], [74, 13, 68, 11], [75, 8, 69, 6, "identifier"], [75, 18, 69, 16], [76, 8, 70, 6], [76, 12, 70, 10, "locationX"], [76, 21, 70, 19, "locationX"], [76, 22, 70, 19], [76, 24, 70, 22], [77, 10, 71, 8], [77, 17, 71, 15, "locationX"], [77, 26, 71, 24], [77, 27, 71, 25, "clientX"], [77, 34, 71, 32], [77, 35, 71, 33], [78, 8, 72, 6], [78, 9, 72, 7], [79, 8, 73, 6], [79, 12, 73, 10, "locationY"], [79, 21, 73, 19, "locationY"], [79, 22, 73, 19], [79, 24, 73, 22], [80, 10, 74, 8], [80, 17, 74, 15, "locationY"], [80, 26, 74, 24], [80, 27, 74, 25, "clientY"], [80, 34, 74, 32], [80, 35, 74, 33], [81, 8, 75, 6], [81, 9, 75, 7], [82, 8, 76, 6, "pageX"], [82, 13, 76, 11], [83, 8, 77, 6, "pageY"], [83, 13, 77, 11], [84, 8, 78, 6, "target"], [84, 14, 78, 12], [84, 16, 78, 14, "domEvent"], [84, 24, 78, 22], [84, 25, 78, 23, "target"], [84, 31, 78, 29], [85, 8, 79, 6, "timestamp"], [86, 6, 80, 4], [86, 7, 80, 5], [86, 8, 80, 6], [87, 6, 81, 4, "changedTouches"], [87, 20, 81, 18], [87, 23, 81, 21, "emulatedTouches"], [87, 38, 81, 36], [88, 6, 82, 4, "touches"], [88, 13, 82, 11], [88, 16, 82, 14, "domEventType"], [88, 28, 82, 26], [88, 33, 82, 31], [88, 42, 82, 40], [88, 46, 82, 44, "domEventType"], [88, 58, 82, 56], [88, 63, 82, 61], [88, 74, 82, 72], [88, 77, 82, 75, "emptyArray"], [88, 87, 82, 85], [88, 90, 82, 88, "emulatedTouches"], [88, 105, 82, 103], [89, 4, 83, 2], [90, 4, 84, 2], [90, 8, 84, 6, "responderEvent"], [90, 22, 84, 20], [90, 25, 84, 23], [91, 6, 85, 4, "bubbles"], [91, 13, 85, 11], [91, 15, 85, 13], [91, 19, 85, 17], [92, 6, 86, 4, "cancelable"], [92, 16, 86, 14], [92, 18, 86, 16], [92, 22, 86, 20], [93, 6, 87, 4], [94, 6, 88, 4, "currentTarget"], [94, 19, 88, 17], [94, 21, 88, 19], [94, 25, 88, 23], [95, 6, 89, 4, "defaultPrevented"], [95, 22, 89, 20], [95, 24, 89, 22, "domEvent"], [95, 32, 89, 30], [95, 33, 89, 31, "defaultPrevented"], [95, 49, 89, 47], [96, 6, 90, 4, "dispatchConfig"], [96, 20, 90, 18], [96, 22, 90, 20, "emptyObject"], [96, 33, 90, 31], [97, 6, 91, 4, "eventPhase"], [97, 16, 91, 14], [97, 18, 91, 16, "domEvent"], [97, 26, 91, 24], [97, 27, 91, 25, "eventPhase"], [97, 37, 91, 35], [98, 6, 92, 4, "isDefaultPrevented"], [98, 24, 92, 22, "isDefaultPrevented"], [98, 25, 92, 22], [98, 27, 92, 25], [99, 8, 93, 6], [99, 15, 93, 13, "domEvent"], [99, 23, 93, 21], [99, 24, 93, 22, "defaultPrevented"], [99, 40, 93, 38], [100, 6, 94, 4], [100, 7, 94, 5], [101, 6, 95, 4, "isPropagationStopped"], [101, 26, 95, 24, "isPropagationStopped"], [101, 27, 95, 24], [101, 29, 95, 27], [102, 8, 96, 6], [102, 15, 96, 13, "propagationWasStopped"], [102, 36, 96, 34], [103, 6, 97, 4], [103, 7, 97, 5], [104, 6, 98, 4, "isTrusted"], [104, 15, 98, 13], [104, 17, 98, 15, "domEvent"], [104, 25, 98, 23], [104, 26, 98, 24, "isTrusted"], [104, 35, 98, 33], [105, 6, 99, 4, "nativeEvent"], [105, 17, 99, 15], [105, 19, 99, 17], [106, 8, 100, 6, "altKey"], [106, 14, 100, 12], [106, 16, 100, 14], [106, 21, 100, 19], [107, 8, 101, 6, "ctrl<PERSON>ey"], [107, 15, 101, 13], [107, 17, 101, 15], [107, 22, 101, 20], [108, 8, 102, 6, "metaKey"], [108, 15, 102, 13], [109, 8, 103, 6, "shift<PERSON>ey"], [109, 16, 103, 14], [110, 8, 104, 6, "changedTouches"], [110, 22, 104, 20], [111, 8, 105, 6, "force"], [111, 13, 105, 11], [112, 8, 106, 6, "identifier"], [112, 18, 106, 16], [113, 8, 107, 6], [113, 12, 107, 10, "locationX"], [113, 21, 107, 19, "locationX"], [113, 22, 107, 19], [113, 24, 107, 22], [114, 10, 108, 8], [114, 17, 108, 15, "locationX"], [114, 26, 108, 24], [114, 27, 108, 25, "clientX"], [114, 34, 108, 32], [114, 35, 108, 33], [115, 8, 109, 6], [115, 9, 109, 7], [116, 8, 110, 6], [116, 12, 110, 10, "locationY"], [116, 21, 110, 19, "locationY"], [116, 22, 110, 19], [116, 24, 110, 22], [117, 10, 111, 8], [117, 17, 111, 15, "locationY"], [117, 26, 111, 24], [117, 27, 111, 25, "clientY"], [117, 34, 111, 32], [117, 35, 111, 33], [118, 8, 112, 6], [118, 9, 112, 7], [119, 8, 113, 6, "pageX"], [119, 13, 113, 11], [120, 8, 114, 6, "pageY"], [120, 13, 114, 11], [121, 8, 115, 6, "target"], [121, 14, 115, 12], [121, 16, 115, 14, "domEvent"], [121, 24, 115, 22], [121, 25, 115, 23, "target"], [121, 31, 115, 29], [122, 8, 116, 6, "timestamp"], [122, 17, 116, 15], [123, 8, 117, 6, "touches"], [123, 15, 117, 13], [124, 8, 118, 6, "type"], [124, 12, 118, 10], [124, 14, 118, 12, "domEventType"], [125, 6, 119, 4], [125, 7, 119, 5], [126, 6, 120, 4, "persist"], [126, 13, 120, 11], [126, 15, 120, 13, "emptyFunction"], [126, 28, 120, 26], [127, 6, 121, 4, "preventDefault"], [127, 20, 121, 18], [128, 6, 122, 4, "stopPropagation"], [128, 21, 122, 19, "stopPropagation"], [128, 22, 122, 19], [128, 24, 122, 22], [129, 8, 123, 6, "propagationWasStopped"], [129, 29, 123, 27], [129, 32, 123, 30], [129, 36, 123, 34], [130, 6, 124, 4], [130, 7, 124, 5], [131, 6, 125, 4, "target"], [131, 12, 125, 10], [131, 14, 125, 12, "domEvent"], [131, 22, 125, 20], [131, 23, 125, 21, "target"], [131, 29, 125, 27], [132, 6, 126, 4, "timeStamp"], [132, 15, 126, 13], [132, 17, 126, 15, "timestamp"], [132, 26, 126, 24], [133, 6, 127, 4, "touchHistory"], [133, 18, 127, 16], [133, 20, 127, 18, "responderTouchHistoryStore"], [133, 46, 127, 44], [133, 47, 127, 45, "touchHistory"], [134, 4, 128, 2], [134, 5, 128, 3], [136, 4, 130, 2], [137, 4, 131, 2], [138, 4, 132, 2], [139, 4, 133, 2], [139, 13, 133, 11, "locationX"], [139, 22, 133, 20, "locationX"], [139, 23, 133, 21, "x"], [139, 24, 133, 22], [139, 26, 133, 24], [140, 6, 134, 4, "rect"], [140, 10, 134, 8], [140, 13, 134, 11, "rect"], [140, 17, 134, 15], [140, 21, 134, 19], [140, 25, 134, 19, "getBoundingClientRect"], [140, 55, 134, 40], [140, 57, 134, 41, "responderEvent"], [140, 71, 134, 55], [140, 72, 134, 56, "currentTarget"], [140, 85, 134, 69], [140, 86, 134, 70], [141, 6, 135, 4], [141, 10, 135, 8, "rect"], [141, 14, 135, 12], [141, 16, 135, 14], [142, 8, 136, 6], [142, 15, 136, 13, "x"], [142, 16, 136, 14], [142, 19, 136, 17, "rect"], [142, 23, 136, 21], [142, 24, 136, 22, "left"], [142, 28, 136, 26], [143, 6, 137, 4], [144, 4, 138, 2], [145, 4, 139, 2], [145, 13, 139, 11, "locationY"], [145, 22, 139, 20, "locationY"], [145, 23, 139, 21, "y"], [145, 24, 139, 22], [145, 26, 139, 24], [146, 6, 140, 4, "rect"], [146, 10, 140, 8], [146, 13, 140, 11, "rect"], [146, 17, 140, 15], [146, 21, 140, 19], [146, 25, 140, 19, "getBoundingClientRect"], [146, 55, 140, 40], [146, 57, 140, 41, "responderEvent"], [146, 71, 140, 55], [146, 72, 140, 56, "currentTarget"], [146, 85, 140, 69], [146, 86, 140, 70], [147, 6, 141, 4], [147, 10, 141, 8, "rect"], [147, 14, 141, 12], [147, 16, 141, 14], [148, 8, 142, 6], [148, 15, 142, 13, "y"], [148, 16, 142, 14], [148, 19, 142, 17, "rect"], [148, 23, 142, 21], [148, 24, 142, 22, "top"], [148, 27, 142, 25], [149, 6, 143, 4], [150, 4, 144, 2], [151, 4, 145, 2], [151, 11, 145, 9, "responderEvent"], [151, 25, 145, 23], [152, 2, 146, 0], [153, 0, 146, 1], [153, 3]], "functionMap": {"names": ["<global>", "emptyFunction", "normalizeIdentifier", "createResponderEvent", "normalizeTouches", "Array.prototype.slice.call.map$argument_0", "get__locationX", "get__locationY", "responderEvent.isDefaultPrevented", "responderEvent.isPropagationStopped", "responderEvent.nativeEvent.get__locationX", "responderEvent.nativeEvent.get__locationY", "responderEvent.stopPropagation", "locationX", "locationY"], "mappings": "AAA;oBCU,QD;AES;CFE;eGM;ECiB;mDCC;QCI;SDE;QEC;SFE;KDM;GDC;MGQ;OHE;MIC;OJE;IKiB;KLE;IMC;KNE;MOU;OPE;MQC;ORE;ISU;KTE;EUS;GVK;EWC;GXK"}}, "type": "js/module"}]}