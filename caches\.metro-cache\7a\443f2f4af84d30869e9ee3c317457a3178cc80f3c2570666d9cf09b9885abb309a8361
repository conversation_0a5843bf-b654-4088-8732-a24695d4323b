{"dependencies": [{"name": "react-native-css-interop", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "GojJv+4gs9E3DINH6CdbjjB/YmY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 42, "index": 42}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 43}, "end": {"line": 2, "column": 44, "index": 87}}], "key": "4wo4OYT4MSo2InL8kiWmZxvepwE=", "exportNames": ["*"]}}, {"name": "../../../web/utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 88}, "end": {"line": 3, "column": 49, "index": 137}}], "key": "UXQPLeApUxcnnwEW1z1pKojSfqo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Wrap = exports.AnimatedWrap = void 0;\n  var _ReactNativeCSSInterop = _interopRequireWildcard(require(_dependencyMap[0], \"react-native-css-interop\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _utils = require(_dependencyMap[2], \"../../../utils\");\n  var _utils2 = require(_dependencyMap[3], \"../../../web/utils\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const Wrap = exports.Wrap = /*#__PURE__*/(0, _react.forwardRef)(({\n    children\n  }, ref) => {\n    try {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const child = _react.default.Children.only(children);\n      if ((0, _utils2.isRNSVGNode)(child)) {\n        const clone = /*#__PURE__*/_react.default.cloneElement(child, {\n          ref\n        },\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        child.props.children);\n        return clone;\n      }\n      return /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(\"div\", {\n        ref: ref,\n        style: {\n          display: 'contents'\n        }\n      }, child);\n    } catch (e) {\n      throw new Error((0, _utils.tagMessage)(`GestureDetector got more than one view as a child. If you want the gesture to work on multiple views, wrap them with a common parent and attach the gesture to that view.`));\n    }\n  }); // On web we never take a path with Reanimated,\n  // therefore we can simply export Wrap\n\n  const AnimatedWrap = exports.AnimatedWrap = Wrap;\n});", "lineCount": 38, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_utils"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_utils2"], [9, 13, 3, 0], [9, 16, 3, 0, "require"], [9, 23, 3, 0], [9, 24, 3, 0, "_dependencyMap"], [9, 38, 3, 0], [10, 2, 3, 49], [10, 11, 3, 49, "_interopRequireWildcard"], [10, 35, 3, 49, "e"], [10, 36, 3, 49], [10, 38, 3, 49, "t"], [10, 39, 3, 49], [10, 68, 3, 49, "WeakMap"], [10, 75, 3, 49], [10, 81, 3, 49, "r"], [10, 82, 3, 49], [10, 89, 3, 49, "WeakMap"], [10, 96, 3, 49], [10, 100, 3, 49, "n"], [10, 101, 3, 49], [10, 108, 3, 49, "WeakMap"], [10, 115, 3, 49], [10, 127, 3, 49, "_interopRequireWildcard"], [10, 150, 3, 49], [10, 162, 3, 49, "_interopRequireWildcard"], [10, 163, 3, 49, "e"], [10, 164, 3, 49], [10, 166, 3, 49, "t"], [10, 167, 3, 49], [10, 176, 3, 49, "t"], [10, 177, 3, 49], [10, 181, 3, 49, "e"], [10, 182, 3, 49], [10, 186, 3, 49, "e"], [10, 187, 3, 49], [10, 188, 3, 49, "__esModule"], [10, 198, 3, 49], [10, 207, 3, 49, "e"], [10, 208, 3, 49], [10, 214, 3, 49, "o"], [10, 215, 3, 49], [10, 217, 3, 49, "i"], [10, 218, 3, 49], [10, 220, 3, 49, "f"], [10, 221, 3, 49], [10, 226, 3, 49, "__proto__"], [10, 235, 3, 49], [10, 243, 3, 49, "default"], [10, 250, 3, 49], [10, 252, 3, 49, "e"], [10, 253, 3, 49], [10, 270, 3, 49, "e"], [10, 271, 3, 49], [10, 294, 3, 49, "e"], [10, 295, 3, 49], [10, 320, 3, 49, "e"], [10, 321, 3, 49], [10, 330, 3, 49, "f"], [10, 331, 3, 49], [10, 337, 3, 49, "o"], [10, 338, 3, 49], [10, 341, 3, 49, "t"], [10, 342, 3, 49], [10, 345, 3, 49, "n"], [10, 346, 3, 49], [10, 349, 3, 49, "r"], [10, 350, 3, 49], [10, 358, 3, 49, "o"], [10, 359, 3, 49], [10, 360, 3, 49, "has"], [10, 363, 3, 49], [10, 364, 3, 49, "e"], [10, 365, 3, 49], [10, 375, 3, 49, "o"], [10, 376, 3, 49], [10, 377, 3, 49, "get"], [10, 380, 3, 49], [10, 381, 3, 49, "e"], [10, 382, 3, 49], [10, 385, 3, 49, "o"], [10, 386, 3, 49], [10, 387, 3, 49, "set"], [10, 390, 3, 49], [10, 391, 3, 49, "e"], [10, 392, 3, 49], [10, 394, 3, 49, "f"], [10, 395, 3, 49], [10, 411, 3, 49, "t"], [10, 412, 3, 49], [10, 416, 3, 49, "e"], [10, 417, 3, 49], [10, 433, 3, 49, "t"], [10, 434, 3, 49], [10, 441, 3, 49, "hasOwnProperty"], [10, 455, 3, 49], [10, 456, 3, 49, "call"], [10, 460, 3, 49], [10, 461, 3, 49, "e"], [10, 462, 3, 49], [10, 464, 3, 49, "t"], [10, 465, 3, 49], [10, 472, 3, 49, "i"], [10, 473, 3, 49], [10, 477, 3, 49, "o"], [10, 478, 3, 49], [10, 481, 3, 49, "Object"], [10, 487, 3, 49], [10, 488, 3, 49, "defineProperty"], [10, 502, 3, 49], [10, 507, 3, 49, "Object"], [10, 513, 3, 49], [10, 514, 3, 49, "getOwnPropertyDescriptor"], [10, 538, 3, 49], [10, 539, 3, 49, "e"], [10, 540, 3, 49], [10, 542, 3, 49, "t"], [10, 543, 3, 49], [10, 550, 3, 49, "i"], [10, 551, 3, 49], [10, 552, 3, 49, "get"], [10, 555, 3, 49], [10, 559, 3, 49, "i"], [10, 560, 3, 49], [10, 561, 3, 49, "set"], [10, 564, 3, 49], [10, 568, 3, 49, "o"], [10, 569, 3, 49], [10, 570, 3, 49, "f"], [10, 571, 3, 49], [10, 573, 3, 49, "t"], [10, 574, 3, 49], [10, 576, 3, 49, "i"], [10, 577, 3, 49], [10, 581, 3, 49, "f"], [10, 582, 3, 49], [10, 583, 3, 49, "t"], [10, 584, 3, 49], [10, 588, 3, 49, "e"], [10, 589, 3, 49], [10, 590, 3, 49, "t"], [10, 591, 3, 49], [10, 602, 3, 49, "f"], [10, 603, 3, 49], [10, 608, 3, 49, "e"], [10, 609, 3, 49], [10, 611, 3, 49, "t"], [10, 612, 3, 49], [11, 2, 4, 7], [11, 8, 4, 13, "Wrap"], [11, 12, 4, 17], [11, 15, 4, 17, "exports"], [11, 22, 4, 17], [11, 23, 4, 17, "Wrap"], [11, 27, 4, 17], [11, 30, 4, 20], [11, 43, 4, 33], [11, 47, 4, 33, "forwardRef"], [11, 64, 4, 43], [11, 66, 4, 44], [11, 67, 4, 45], [12, 4, 5, 2, "children"], [13, 2, 6, 0], [13, 3, 6, 1], [13, 5, 6, 3, "ref"], [13, 8, 6, 6], [13, 13, 6, 11], [14, 4, 7, 2], [14, 8, 7, 6], [15, 6, 8, 4], [16, 6, 9, 4], [16, 12, 9, 10, "child"], [16, 17, 9, 15], [16, 20, 9, 18, "React"], [16, 34, 9, 23], [16, 35, 9, 24, "Children"], [16, 43, 9, 32], [16, 44, 9, 33, "only"], [16, 48, 9, 37], [16, 49, 9, 38, "children"], [16, 57, 9, 46], [16, 58, 9, 47], [17, 6, 11, 4], [17, 10, 11, 8], [17, 14, 11, 8, "isRNSVGNode"], [17, 33, 11, 19], [17, 35, 11, 20, "child"], [17, 40, 11, 25], [17, 41, 11, 26], [17, 43, 11, 28], [18, 8, 12, 6], [18, 14, 12, 12, "clone"], [18, 19, 12, 17], [18, 22, 12, 20], [18, 35, 12, 33, "React"], [18, 49, 12, 38], [18, 50, 12, 39, "cloneElement"], [18, 62, 12, 51], [18, 63, 12, 52, "child"], [18, 68, 12, 57], [18, 70, 12, 59], [19, 10, 13, 8, "ref"], [20, 8, 14, 6], [20, 9, 14, 7], [21, 8, 14, 9], [22, 8, 15, 6, "child"], [22, 13, 15, 11], [22, 14, 15, 12, "props"], [22, 19, 15, 17], [22, 20, 15, 18, "children"], [22, 28, 15, 26], [22, 29, 15, 27], [23, 8, 16, 6], [23, 15, 16, 13, "clone"], [23, 20, 16, 18], [24, 6, 17, 4], [25, 6, 19, 4], [25, 13, 19, 11], [25, 26, 19, 24, "_ReactNativeCSSInterop"], [25, 48, 19, 24], [25, 49, 19, 24, "createInteropElement"], [25, 69, 19, 24], [25, 70, 19, 44], [25, 75, 19, 49], [25, 77, 19, 51], [26, 8, 20, 6, "ref"], [26, 11, 20, 9], [26, 13, 20, 11, "ref"], [26, 16, 20, 14], [27, 8, 21, 6, "style"], [27, 13, 21, 11], [27, 15, 21, 13], [28, 10, 22, 8, "display"], [28, 17, 22, 15], [28, 19, 22, 17], [29, 8, 23, 6], [30, 6, 24, 4], [30, 7, 24, 5], [30, 9, 24, 7, "child"], [30, 14, 24, 12], [30, 15, 24, 13], [31, 4, 25, 2], [31, 5, 25, 3], [31, 6, 25, 4], [31, 13, 25, 11, "e"], [31, 14, 25, 12], [31, 16, 25, 14], [32, 6, 26, 4], [32, 12, 26, 10], [32, 16, 26, 14, "Error"], [32, 21, 26, 19], [32, 22, 26, 20], [32, 26, 26, 20, "tagMessage"], [32, 43, 26, 30], [32, 45, 26, 31], [32, 216, 26, 202], [32, 217, 26, 203], [32, 218, 26, 204], [33, 4, 27, 2], [34, 2, 28, 0], [34, 3, 28, 1], [34, 4, 28, 2], [34, 5, 28, 3], [34, 6, 28, 4], [35, 2, 29, 0], [37, 2, 31, 7], [37, 8, 31, 13, "AnimatedWrap"], [37, 20, 31, 25], [37, 23, 31, 25, "exports"], [37, 30, 31, 25], [37, 31, 31, 25, "AnimatedWrap"], [37, 43, 31, 25], [37, 46, 31, 28, "Wrap"], [37, 50, 31, 32], [38, 0, 31, 33], [38, 3]], "functionMap": {"names": ["<global>", "forwardRef$argument_0"], "mappings": "AAA;4CCG;CDwB"}}, "type": "js/module"}]}