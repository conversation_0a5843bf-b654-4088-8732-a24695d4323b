import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  Pressable,
  StyleSheet,
  Alert,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Image } from 'expo-image';
import { 
  Settings, 
  Heart, 
  Bookmark,
  Share,
  Camera,
  Edit3,
  Grid3X3,
  List,
  Users,
  Crown,
  Sparkles
} from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../../utils/theme';
import { useFonts, Inter_400Regular, Inter_500Medium, Inter_600SemiBold } from '@expo-google-fonts/inter';

// Sample user data
const userData = {
  name: '<PERSON>',
  username: '@sarahstyle',
  avatar: 'https://images.unsplash.com/photo-1494790108755-2616b72bb37e?w=200',
  bio: 'Fashion enthusiast • AI stylist • Creating looks that inspire ✨',
  followers: 1234,
  following: 567,
  outfits: 89,
  isPremium: true,
};

// Sample saved outfits
const savedOutfits = [
  {
    id: '1',
    name: 'Summer Casual',
    image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',
    likes: 23,
    createdAt: '2 days ago',
  },
  {
    id: '2',
    name: 'Business Meeting',
    image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',
    likes: 45,
    createdAt: '1 week ago',
  },
  {
    id: '3',
    name: 'Weekend Vibes',
    image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=400',
    likes: 67,
    createdAt: '2 weeks ago',
  },
  {
    id: '4',
    name: 'Date Night',
    image: 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5e?w=400',
    likes: 89,
    createdAt: '3 weeks ago',
  },
];

const tabs = [
  { id: 'outfits', name: 'Outfits', icon: Grid3X3 },
  { id: 'saved', name: 'Saved', icon: Bookmark },
  { id: 'liked', name: 'Liked', icon: Heart },
];

export default function ProfileScreen() {
  const insets = useSafeAreaInsets();
  const { colors, isDark } = useTheme();
  const [selectedTab, setSelectedTab] = useState('outfits');
  const [viewMode, setViewMode] = useState('grid');

  const [fontsLoaded] = useFonts({
    Inter_400Regular,
    Inter_500Medium,
    Inter_600SemiBold,
  });

  if (!fontsLoaded) {
    return null;
  }

  const handleEditProfile = () => {
    Alert.alert('Edit Profile', 'Profile editing functionality coming soon!');
  };

  const handleShare = () => {
    Alert.alert('Share Profile', 'Profile sharing functionality coming soon!');
  };

  const renderStatItem = (label, value) => (
    <View style={styles.statItem}>
      <Text style={[styles.statValue, { color: colors.text }]}>
        {value}
      </Text>
      <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
        {label}
      </Text>
    </View>
  );

  const renderTabButton = (tab) => {
    const isSelected = selectedTab === tab.id;
    const IconComponent = tab.icon;
    
    return (
      <Pressable
        key={tab.id}
        style={[
          styles.tabButton,
          isSelected && { borderBottomColor: colors.primary, borderBottomWidth: 2 }
        ]}
        onPress={() => setSelectedTab(tab.id)}
      >
        <IconComponent 
          size={20} 
          color={isSelected ? colors.primary : colors.textSecondary} 
        />
        <Text style={[
          styles.tabText,
          { color: colors.textSecondary },
          isSelected && { color: colors.primary }
        ]}>
          {tab.name}
        </Text>
      </Pressable>
    );
  };

  const renderOutfitCard = (outfit) => (
    <Pressable
      key={outfit.id}
      style={[
        styles.outfitCard,
        { 
          backgroundColor: colors.cardBackground,
          width: viewMode === 'grid' ? '48%' : '100%'
        }
      ]}
    >
      <Image
        source={{ uri: outfit.image }}
        style={[
          styles.outfitCardImage,
          viewMode === 'list' && styles.listImage
        ]}
        contentFit="cover"
        transition={200}
      />
      <View style={[
        styles.outfitCardInfo,
        viewMode === 'list' && styles.listInfo
      ]}>
        <Text 
          style={[styles.outfitName, { color: colors.text }]}
          numberOfLines={1}
        >
          {outfit.name}
        </Text>
        <View style={styles.outfitMeta}>
          <View style={styles.metaItem}>
            <Heart size={12} color={colors.textTertiary} />
            <Text style={[styles.metaText, { color: colors.textTertiary }]}>
              {outfit.likes}
            </Text>
          </View>
          <Text style={[styles.createdAt, { color: colors.textTertiary }]}>
            {outfit.createdAt}
          </Text>
        </View>
      </View>
    </Pressable>
  );

  const renderEmptyTab = (message) => (
    <View style={styles.emptyTab}>
      <View style={[styles.emptyIconContainer, { backgroundColor: colors.emptyStateBackground }]}>
        <Sparkles size={48} color={colors.textTertiary} strokeWidth={1} />
      </View>
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        Nothing here yet
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        {message}
      </Text>
    </View>
  );

  const renderTabContent = () => {
    switch (selectedTab) {
      case 'outfits':
        return (
          <View style={[
            styles.outfitsGrid,
            viewMode === 'list' && styles.outfitsList
          ]}>
            {savedOutfits.map(renderOutfitCard)}
          </View>
        );
      case 'saved':
        return renderEmptyTab('Save outfits you love to see them here');
      case 'liked':
        return renderEmptyTab('Outfits you like will appear here');
      default:
        return null;
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      
      {/* Header */}
      <View style={[
        styles.header,
        { 
          paddingTop: insets.top + 16,
          backgroundColor: colors.background,
        }
      ]}>
        <View style={styles.headerActions}>
          <Text style={[styles.username, { color: colors.text }]}>
            {userData.username}
          </Text>
          <Pressable
            style={[styles.headerButton, { backgroundColor: colors.surface }]}
            onPress={handleShare}
          >
            <Share size={20} color={colors.text} />
          </Pressable>
          <Pressable
            style={[styles.headerButton, { backgroundColor: colors.surface }]}
          >
            <Settings size={20} color={colors.text} />
          </Pressable>
        </View>
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={[styles.contentContainer, { paddingBottom: insets.bottom + 20 }]}
        showsVerticalScrollIndicator={false}
      >
        {/* Profile Info */}
        <View style={styles.profileSection}>
          <View style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              <Image
                source={{ uri: userData.avatar }}
                style={styles.avatar}
                contentFit="cover"
                transition={200}
              />
              <Pressable style={[styles.cameraButton, { backgroundColor: colors.primary }]}>
                <Camera size={16} color={colors.buttonPrimaryText} />
              </Pressable>
            </View>
            
            <View style={styles.profileInfo}>
              <View style={styles.nameContainer}>
                <Text style={[styles.name, { color: colors.text }]}>
                  {userData.name}
                </Text>
                {userData.isPremium && (
                  <Crown size={16} color="#FFD700" fill="#FFD700" />
                )}
              </View>
              <Text style={[styles.bio, { color: colors.textSecondary }]}>
                {userData.bio}
              </Text>
            </View>
          </View>

          {/* Stats */}
          <View style={styles.statsContainer}>
            {renderStatItem('Outfits', userData.outfits)}
            {renderStatItem('Followers', userData.followers)}
            {renderStatItem('Following', userData.following)}
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <Pressable
              style={[styles.editButton, { backgroundColor: colors.primary }]}
              onPress={handleEditProfile}
            >
              <Edit3 size={16} color={colors.buttonPrimaryText} />
              <Text style={[styles.editButtonText, { color: colors.buttonPrimaryText }]}>
                Edit Profile
              </Text>
            </Pressable>
            
            <Pressable
              style={[styles.shareButton, { borderColor: colors.border, backgroundColor: colors.surface }]}
              onPress={handleShare}
            >
              <Share size={16} color={colors.text} />
              <Text style={[styles.shareButtonText, { color: colors.text }]}>
                Share
              </Text>
            </Pressable>
          </View>
        </View>

        {/* Tabs */}
        <View style={[styles.tabsContainer, { borderBottomColor: colors.border }]}>
          <View style={styles.tabsWrapper}>
            {tabs.map(renderTabButton)}
          </View>
          
          <Pressable
            style={[styles.viewModeButton, { backgroundColor: colors.surface }]}
            onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
          >
            {viewMode === 'grid' ? 
              <List size={16} color={colors.text} /> : 
              <Grid3X3 size={16} color={colors.text} />
            }
          </Pressable>
        </View>

        {/* Tab Content */}
        <View style={styles.tabContent}>
          {renderTabContent()}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  headerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  username: {
    fontSize: 20,
    fontFamily: 'Inter_600SemiBold',
    flex: 1,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  profileSection: {
    marginBottom: 32,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileInfo: {
    flex: 1,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  name: {
    fontSize: 24,
    fontFamily: 'Inter_600SemiBold',
  },
  bio: {
    fontSize: 14,
    fontFamily: 'Inter_400Regular',
    lineHeight: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
    paddingVertical: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Inter_600SemiBold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter_500Medium',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  editButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 6,
  },
  editButtonText: {
    fontSize: 14,
    fontFamily: 'Inter_500Medium',
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 6,
  },
  shareButtonText: {
    fontSize: 14,
    fontFamily: 'Inter_500Medium',
  },
  tabsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    marginBottom: 24,
  },
  tabsWrapper: {
    flexDirection: 'row',
    flex: 1,
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 16,
    gap: 6,
  },
  tabText: {
    fontSize: 14,
    fontFamily: 'Inter_500Medium',
  },
  viewModeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabContent: {
    flex: 1,
  },
  outfitsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  outfitsList: {
    flexDirection: 'column',
    gap: 16,
  },
  outfitCard: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  outfitCardImage: {
    width: '100%',
    height: 160,
  },
  listImage: {
    width: 80,
    height: 80,
  },
  outfitCardInfo: {
    padding: 12,
  },
  listInfo: {
    flex: 1,
    padding: 0,
    paddingLeft: 12,
    justifyContent: 'center',
  },
  outfitName: {
    fontSize: 14,
    fontFamily: 'Inter_500Medium',
    marginBottom: 4,
  },
  outfitMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    fontFamily: 'Inter_400Regular',
  },
  createdAt: {
    fontSize: 11,
    fontFamily: 'Inter_400Regular',
  },
  emptyTab: {
    alignItems: 'center',
    paddingVertical: 80,
    paddingHorizontal: 40,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontFamily: 'Inter_600SemiBold',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    fontFamily: 'Inter_400Regular',
    textAlign: 'center',
    lineHeight: 20,
  },
});