{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  /**\n   * Parse a path into an array of parts with information about each segment.\n   */\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getPatternParts = getPatternParts;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  function getPatternParts(path) {\n    var parts = [];\n    var current = {\n      segment: ''\n    };\n    var isRegex = false;\n    var isParam = false;\n    var regexInnerParens = 0;\n\n    // One extra iteration to add the last character\n    for (var i = 0; i <= path.length; i++) {\n      var char = path[i];\n      if (char != null) {\n        current.segment += char;\n      }\n      if (char === ':') {\n        // The segment must start with a colon if it's a param\n        if (current.segment === ':') {\n          isParam = true;\n        } else if (!isRegex) {\n          throw new Error(`Encountered ':' in the middle of a segment in path: ${path}`);\n        }\n      } else if (char === '(') {\n        if (isParam) {\n          if (isRegex) {\n            // The '(' is part of the regex if we're already inside one\n            regexInnerParens++;\n          } else {\n            isRegex = true;\n          }\n        } else {\n          throw new Error(`Encountered '(' without preceding ':' in path: ${path}`);\n        }\n      } else if (char === ')') {\n        if (isParam && isRegex) {\n          if (regexInnerParens) {\n            // The ')' is part of the regex if we're already inside one\n            regexInnerParens--;\n            current.regex += char;\n          } else {\n            isRegex = false;\n            isParam = false;\n          }\n        } else {\n          throw new Error(`Encountered ')' without preceding '(' in path: ${path}`);\n        }\n      } else if (char === '?') {\n        if (current.param) {\n          isParam = false;\n          current.optional = true;\n        } else {\n          throw new Error(`Encountered '?' without preceding ':' in path: ${path}`);\n        }\n      } else if (char == null || char === '/' && !isRegex) {\n        isParam = false;\n\n        // Remove trailing slash from segment\n        current.segment = current.segment.replace(/\\/$/, '');\n        if (current.segment === '') {\n          continue;\n        }\n        if (current.param) {\n          current.param = current.param.replace(/^:/, '');\n        }\n        if (current.regex) {\n          current.regex = current.regex.replace(/^\\(/, '').replace(/\\)$/, '');\n        }\n        parts.push(current);\n        if (char == null) {\n          break;\n        }\n        current = {\n          segment: ''\n        };\n      }\n      if (isRegex) {\n        current.regex = current.regex || '';\n        current.regex += char;\n      }\n      if (isParam && !isRegex) {\n        current.param = current.param || '';\n        current.param += char;\n      }\n    }\n    if (isRegex) {\n      throw new Error(`Could not find closing ')' in path: ${path}`);\n    }\n    var params = parts.map(part => part.param).filter(Boolean);\n    for (var _ref of params.entries()) {\n      var _ref2 = (0, _slicedToArray2.default)(_ref, 2);\n      var index = _ref2[0];\n      var param = _ref2[1];\n      if (params.indexOf(param) !== index) {\n        throw new Error(`Duplicate param name '${param}' found in path: ${path}`);\n      }\n    }\n    return parts;\n  }\n});", "lineCount": 111, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 2, 3, 0], [7, 6, 3, 0, "_interopRequireDefault"], [7, 28, 3, 0], [7, 31, 3, 0, "require"], [7, 38, 3, 0], [7, 39, 3, 0, "_dependencyMap"], [7, 53, 3, 0], [8, 2, 3, 0, "Object"], [8, 8, 3, 0], [8, 9, 3, 0, "defineProperty"], [8, 23, 3, 0], [8, 24, 3, 0, "exports"], [8, 31, 3, 0], [9, 4, 3, 0, "value"], [9, 9, 3, 0], [10, 2, 3, 0], [11, 2, 3, 0, "exports"], [11, 9, 3, 0], [11, 10, 3, 0, "getPatternParts"], [11, 25, 3, 0], [11, 28, 3, 0, "getPatternParts"], [11, 43, 3, 0], [12, 2, 3, 0], [12, 6, 3, 0, "_slicedToArray2"], [12, 21, 3, 0], [12, 24, 3, 0, "_interopRequireDefault"], [12, 46, 3, 0], [12, 47, 3, 0, "require"], [12, 54, 3, 0], [12, 55, 3, 0, "_dependencyMap"], [12, 69, 3, 0], [13, 2, 6, 7], [13, 11, 6, 16, "getPatternParts"], [13, 26, 6, 31, "getPatternParts"], [13, 27, 6, 32, "path"], [13, 31, 6, 36], [13, 33, 6, 38], [14, 4, 7, 2], [14, 8, 7, 8, "parts"], [14, 13, 7, 13], [14, 16, 7, 16], [14, 18, 7, 18], [15, 4, 8, 2], [15, 8, 8, 6, "current"], [15, 15, 8, 13], [15, 18, 8, 16], [16, 6, 9, 4, "segment"], [16, 13, 9, 11], [16, 15, 9, 13], [17, 4, 10, 2], [17, 5, 10, 3], [18, 4, 11, 2], [18, 8, 11, 6, "isRegex"], [18, 15, 11, 13], [18, 18, 11, 16], [18, 23, 11, 21], [19, 4, 12, 2], [19, 8, 12, 6, "isParam"], [19, 15, 12, 13], [19, 18, 12, 16], [19, 23, 12, 21], [20, 4, 13, 2], [20, 8, 13, 6, "regexInnerParens"], [20, 24, 13, 22], [20, 27, 13, 25], [20, 28, 13, 26], [22, 4, 15, 2], [23, 4, 16, 2], [23, 9, 16, 7], [23, 13, 16, 11, "i"], [23, 14, 16, 12], [23, 17, 16, 15], [23, 18, 16, 16], [23, 20, 16, 18, "i"], [23, 21, 16, 19], [23, 25, 16, 23, "path"], [23, 29, 16, 27], [23, 30, 16, 28, "length"], [23, 36, 16, 34], [23, 38, 16, 36, "i"], [23, 39, 16, 37], [23, 41, 16, 39], [23, 43, 16, 41], [24, 6, 17, 4], [24, 10, 17, 10, "char"], [24, 14, 17, 14], [24, 17, 17, 17, "path"], [24, 21, 17, 21], [24, 22, 17, 22, "i"], [24, 23, 17, 23], [24, 24, 17, 24], [25, 6, 18, 4], [25, 10, 18, 8, "char"], [25, 14, 18, 12], [25, 18, 18, 16], [25, 22, 18, 20], [25, 24, 18, 22], [26, 8, 19, 6, "current"], [26, 15, 19, 13], [26, 16, 19, 14, "segment"], [26, 23, 19, 21], [26, 27, 19, 25, "char"], [26, 31, 19, 29], [27, 6, 20, 4], [28, 6, 21, 4], [28, 10, 21, 8, "char"], [28, 14, 21, 12], [28, 19, 21, 17], [28, 22, 21, 20], [28, 24, 21, 22], [29, 8, 22, 6], [30, 8, 23, 6], [30, 12, 23, 10, "current"], [30, 19, 23, 17], [30, 20, 23, 18, "segment"], [30, 27, 23, 25], [30, 32, 23, 30], [30, 35, 23, 33], [30, 37, 23, 35], [31, 10, 24, 8, "isParam"], [31, 17, 24, 15], [31, 20, 24, 18], [31, 24, 24, 22], [32, 8, 25, 6], [32, 9, 25, 7], [32, 15, 25, 13], [32, 19, 25, 17], [32, 20, 25, 18, "isRegex"], [32, 27, 25, 25], [32, 29, 25, 27], [33, 10, 26, 8], [33, 16, 26, 14], [33, 20, 26, 18, "Error"], [33, 25, 26, 23], [33, 26, 26, 24], [33, 81, 26, 79, "path"], [33, 85, 26, 83], [33, 87, 26, 85], [33, 88, 26, 86], [34, 8, 27, 6], [35, 6, 28, 4], [35, 7, 28, 5], [35, 13, 28, 11], [35, 17, 28, 15, "char"], [35, 21, 28, 19], [35, 26, 28, 24], [35, 29, 28, 27], [35, 31, 28, 29], [36, 8, 29, 6], [36, 12, 29, 10, "isParam"], [36, 19, 29, 17], [36, 21, 29, 19], [37, 10, 30, 8], [37, 14, 30, 12, "isRegex"], [37, 21, 30, 19], [37, 23, 30, 21], [38, 12, 31, 10], [39, 12, 32, 10, "regexInnerParens"], [39, 28, 32, 26], [39, 30, 32, 28], [40, 10, 33, 8], [40, 11, 33, 9], [40, 17, 33, 15], [41, 12, 34, 10, "isRegex"], [41, 19, 34, 17], [41, 22, 34, 20], [41, 26, 34, 24], [42, 10, 35, 8], [43, 8, 36, 6], [43, 9, 36, 7], [43, 15, 36, 13], [44, 10, 37, 8], [44, 16, 37, 14], [44, 20, 37, 18, "Error"], [44, 25, 37, 23], [44, 26, 37, 24], [44, 76, 37, 74, "path"], [44, 80, 37, 78], [44, 82, 37, 80], [44, 83, 37, 81], [45, 8, 38, 6], [46, 6, 39, 4], [46, 7, 39, 5], [46, 13, 39, 11], [46, 17, 39, 15, "char"], [46, 21, 39, 19], [46, 26, 39, 24], [46, 29, 39, 27], [46, 31, 39, 29], [47, 8, 40, 6], [47, 12, 40, 10, "isParam"], [47, 19, 40, 17], [47, 23, 40, 21, "isRegex"], [47, 30, 40, 28], [47, 32, 40, 30], [48, 10, 41, 8], [48, 14, 41, 12, "regexInnerParens"], [48, 30, 41, 28], [48, 32, 41, 30], [49, 12, 42, 10], [50, 12, 43, 10, "regexInnerParens"], [50, 28, 43, 26], [50, 30, 43, 28], [51, 12, 44, 10, "current"], [51, 19, 44, 17], [51, 20, 44, 18, "regex"], [51, 25, 44, 23], [51, 29, 44, 27, "char"], [51, 33, 44, 31], [52, 10, 45, 8], [52, 11, 45, 9], [52, 17, 45, 15], [53, 12, 46, 10, "isRegex"], [53, 19, 46, 17], [53, 22, 46, 20], [53, 27, 46, 25], [54, 12, 47, 10, "isParam"], [54, 19, 47, 17], [54, 22, 47, 20], [54, 27, 47, 25], [55, 10, 48, 8], [56, 8, 49, 6], [56, 9, 49, 7], [56, 15, 49, 13], [57, 10, 50, 8], [57, 16, 50, 14], [57, 20, 50, 18, "Error"], [57, 25, 50, 23], [57, 26, 50, 24], [57, 76, 50, 74, "path"], [57, 80, 50, 78], [57, 82, 50, 80], [57, 83, 50, 81], [58, 8, 51, 6], [59, 6, 52, 4], [59, 7, 52, 5], [59, 13, 52, 11], [59, 17, 52, 15, "char"], [59, 21, 52, 19], [59, 26, 52, 24], [59, 29, 52, 27], [59, 31, 52, 29], [60, 8, 53, 6], [60, 12, 53, 10, "current"], [60, 19, 53, 17], [60, 20, 53, 18, "param"], [60, 25, 53, 23], [60, 27, 53, 25], [61, 10, 54, 8, "isParam"], [61, 17, 54, 15], [61, 20, 54, 18], [61, 25, 54, 23], [62, 10, 55, 8, "current"], [62, 17, 55, 15], [62, 18, 55, 16, "optional"], [62, 26, 55, 24], [62, 29, 55, 27], [62, 33, 55, 31], [63, 8, 56, 6], [63, 9, 56, 7], [63, 15, 56, 13], [64, 10, 57, 8], [64, 16, 57, 14], [64, 20, 57, 18, "Error"], [64, 25, 57, 23], [64, 26, 57, 24], [64, 76, 57, 74, "path"], [64, 80, 57, 78], [64, 82, 57, 80], [64, 83, 57, 81], [65, 8, 58, 6], [66, 6, 59, 4], [66, 7, 59, 5], [66, 13, 59, 11], [66, 17, 59, 15, "char"], [66, 21, 59, 19], [66, 25, 59, 23], [66, 29, 59, 27], [66, 33, 59, 31, "char"], [66, 37, 59, 35], [66, 42, 59, 40], [66, 45, 59, 43], [66, 49, 59, 47], [66, 50, 59, 48, "isRegex"], [66, 57, 59, 55], [66, 59, 59, 57], [67, 8, 60, 6, "isParam"], [67, 15, 60, 13], [67, 18, 60, 16], [67, 23, 60, 21], [69, 8, 62, 6], [70, 8, 63, 6, "current"], [70, 15, 63, 13], [70, 16, 63, 14, "segment"], [70, 23, 63, 21], [70, 26, 63, 24, "current"], [70, 33, 63, 31], [70, 34, 63, 32, "segment"], [70, 41, 63, 39], [70, 42, 63, 40, "replace"], [70, 49, 63, 47], [70, 50, 63, 48], [70, 55, 63, 53], [70, 57, 63, 55], [70, 59, 63, 57], [70, 60, 63, 58], [71, 8, 64, 6], [71, 12, 64, 10, "current"], [71, 19, 64, 17], [71, 20, 64, 18, "segment"], [71, 27, 64, 25], [71, 32, 64, 30], [71, 34, 64, 32], [71, 36, 64, 34], [72, 10, 65, 8], [73, 8, 66, 6], [74, 8, 67, 6], [74, 12, 67, 10, "current"], [74, 19, 67, 17], [74, 20, 67, 18, "param"], [74, 25, 67, 23], [74, 27, 67, 25], [75, 10, 68, 8, "current"], [75, 17, 68, 15], [75, 18, 68, 16, "param"], [75, 23, 68, 21], [75, 26, 68, 24, "current"], [75, 33, 68, 31], [75, 34, 68, 32, "param"], [75, 39, 68, 37], [75, 40, 68, 38, "replace"], [75, 47, 68, 45], [75, 48, 68, 46], [75, 52, 68, 50], [75, 54, 68, 52], [75, 56, 68, 54], [75, 57, 68, 55], [76, 8, 69, 6], [77, 8, 70, 6], [77, 12, 70, 10, "current"], [77, 19, 70, 17], [77, 20, 70, 18, "regex"], [77, 25, 70, 23], [77, 27, 70, 25], [78, 10, 71, 8, "current"], [78, 17, 71, 15], [78, 18, 71, 16, "regex"], [78, 23, 71, 21], [78, 26, 71, 24, "current"], [78, 33, 71, 31], [78, 34, 71, 32, "regex"], [78, 39, 71, 37], [78, 40, 71, 38, "replace"], [78, 47, 71, 45], [78, 48, 71, 46], [78, 53, 71, 51], [78, 55, 71, 53], [78, 57, 71, 55], [78, 58, 71, 56], [78, 59, 71, 57, "replace"], [78, 66, 71, 64], [78, 67, 71, 65], [78, 72, 71, 70], [78, 74, 71, 72], [78, 76, 71, 74], [78, 77, 71, 75], [79, 8, 72, 6], [80, 8, 73, 6, "parts"], [80, 13, 73, 11], [80, 14, 73, 12, "push"], [80, 18, 73, 16], [80, 19, 73, 17, "current"], [80, 26, 73, 24], [80, 27, 73, 25], [81, 8, 74, 6], [81, 12, 74, 10, "char"], [81, 16, 74, 14], [81, 20, 74, 18], [81, 24, 74, 22], [81, 26, 74, 24], [82, 10, 75, 8], [83, 8, 76, 6], [84, 8, 77, 6, "current"], [84, 15, 77, 13], [84, 18, 77, 16], [85, 10, 78, 8, "segment"], [85, 17, 78, 15], [85, 19, 78, 17], [86, 8, 79, 6], [86, 9, 79, 7], [87, 6, 80, 4], [88, 6, 81, 4], [88, 10, 81, 8, "isRegex"], [88, 17, 81, 15], [88, 19, 81, 17], [89, 8, 82, 6, "current"], [89, 15, 82, 13], [89, 16, 82, 14, "regex"], [89, 21, 82, 19], [89, 24, 82, 22, "current"], [89, 31, 82, 29], [89, 32, 82, 30, "regex"], [89, 37, 82, 35], [89, 41, 82, 39], [89, 43, 82, 41], [90, 8, 83, 6, "current"], [90, 15, 83, 13], [90, 16, 83, 14, "regex"], [90, 21, 83, 19], [90, 25, 83, 23, "char"], [90, 29, 83, 27], [91, 6, 84, 4], [92, 6, 85, 4], [92, 10, 85, 8, "isParam"], [92, 17, 85, 15], [92, 21, 85, 19], [92, 22, 85, 20, "isRegex"], [92, 29, 85, 27], [92, 31, 85, 29], [93, 8, 86, 6, "current"], [93, 15, 86, 13], [93, 16, 86, 14, "param"], [93, 21, 86, 19], [93, 24, 86, 22, "current"], [93, 31, 86, 29], [93, 32, 86, 30, "param"], [93, 37, 86, 35], [93, 41, 86, 39], [93, 43, 86, 41], [94, 8, 87, 6, "current"], [94, 15, 87, 13], [94, 16, 87, 14, "param"], [94, 21, 87, 19], [94, 25, 87, 23, "char"], [94, 29, 87, 27], [95, 6, 88, 4], [96, 4, 89, 2], [97, 4, 90, 2], [97, 8, 90, 6, "isRegex"], [97, 15, 90, 13], [97, 17, 90, 15], [98, 6, 91, 4], [98, 12, 91, 10], [98, 16, 91, 14, "Error"], [98, 21, 91, 19], [98, 22, 91, 20], [98, 61, 91, 59, "path"], [98, 65, 91, 63], [98, 67, 91, 65], [98, 68, 91, 66], [99, 4, 92, 2], [100, 4, 93, 2], [100, 8, 93, 8, "params"], [100, 14, 93, 14], [100, 17, 93, 17, "parts"], [100, 22, 93, 22], [100, 23, 93, 23, "map"], [100, 26, 93, 26], [100, 27, 93, 27, "part"], [100, 31, 93, 31], [100, 35, 93, 35, "part"], [100, 39, 93, 39], [100, 40, 93, 40, "param"], [100, 45, 93, 45], [100, 46, 93, 46], [100, 47, 93, 47, "filter"], [100, 53, 93, 53], [100, 54, 93, 54, "Boolean"], [100, 61, 93, 61], [100, 62, 93, 62], [101, 4, 94, 2], [101, 13, 94, 2, "_ref"], [101, 17, 94, 2], [101, 21, 94, 31, "params"], [101, 27, 94, 37], [101, 28, 94, 38, "entries"], [101, 35, 94, 45], [101, 36, 94, 46], [101, 37, 94, 47], [101, 39, 94, 49], [102, 6, 94, 49], [102, 10, 94, 49, "_ref2"], [102, 15, 94, 49], [102, 22, 94, 49, "_slicedToArray2"], [102, 37, 94, 49], [102, 38, 94, 49, "default"], [102, 45, 94, 49], [102, 47, 94, 49, "_ref"], [102, 51, 94, 49], [103, 6, 94, 49], [103, 10, 94, 14, "index"], [103, 15, 94, 19], [103, 18, 94, 19, "_ref2"], [103, 23, 94, 19], [104, 6, 94, 19], [104, 10, 94, 21, "param"], [104, 15, 94, 26], [104, 18, 94, 26, "_ref2"], [104, 23, 94, 26], [105, 6, 95, 4], [105, 10, 95, 8, "params"], [105, 16, 95, 14], [105, 17, 95, 15, "indexOf"], [105, 24, 95, 22], [105, 25, 95, 23, "param"], [105, 30, 95, 28], [105, 31, 95, 29], [105, 36, 95, 34, "index"], [105, 41, 95, 39], [105, 43, 95, 41], [106, 8, 96, 6], [106, 14, 96, 12], [106, 18, 96, 16, "Error"], [106, 23, 96, 21], [106, 24, 96, 22], [106, 49, 96, 47, "param"], [106, 54, 96, 52], [106, 74, 96, 72, "path"], [106, 78, 96, 76], [106, 80, 96, 78], [106, 81, 96, 79], [107, 6, 97, 4], [108, 4, 98, 2], [109, 4, 99, 2], [109, 11, 99, 9, "parts"], [109, 16, 99, 14], [110, 2, 100, 0], [111, 0, 100, 1], [111, 3]], "functionMap": {"names": ["<global>", "getPatternParts", "parts.map$argument_0"], "mappings": "AAA;OCK;2BCuF,kBD;CDO"}}, "type": "js/module"}]}