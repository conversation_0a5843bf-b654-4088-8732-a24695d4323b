{"dependencies": [{"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 40, "column": 17, "index": 1637}, "end": {"line": 40, "column": 52, "index": 1672}}], "key": "uE+cRVNnMKkS9OYKR5fpRqPul5s=", "exportNames": ["*"]}}, {"name": "escape-string-regexp", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 41, "column": 47, "index": 1721}, "end": {"line": 41, "column": 78, "index": 1752}}], "key": "14QBo/6I2jfYu7Ct29OWBvp5ENM=", "exportNames": ["*"]}}, {"name": "./findFocusedRoute", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 42, "column": 27, "index": 1782}, "end": {"line": 42, "column": 56, "index": 1811}}], "key": "JPe8xQMI59bnv97OB8syZ4AgEvA=", "exportNames": ["*"]}}, {"name": "./getStateFromPath-forks", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 43, "column": 26, "index": 1839}, "end": {"line": 43, "column": 61, "index": 1874}}], "key": "R0kZl7h5DI+/CZCR2+pJK8wn5iY=", "exportNames": ["*"]}}, {"name": "../constants", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 44, "column": 20, "index": 1897}, "end": {"line": 44, "column": 43, "index": 1920}}], "key": "zmjjtqoQxi2W71eIMIIaEi1mOpU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var __createBinding = this && this.__createBinding || (Object.create ? function (o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = {\n        enumerable: true,\n        get: function () {\n          return m[k];\n        }\n      };\n    }\n    Object.defineProperty(o, k2, desc);\n  } : function (o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n  });\n  var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function (o, v) {\n    Object.defineProperty(o, \"default\", {\n      enumerable: true,\n      value: v\n    });\n  } : function (o, v) {\n    o[\"default\"] = v;\n  });\n  var __importStar = this && this.__importStar || function () {\n    var ownKeys = function (o) {\n      ownKeys = Object.getOwnPropertyNames || function (o) {\n        var ar = [];\n        for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n        return ar;\n      };\n      return ownKeys(o);\n    };\n    return function (mod) {\n      if (mod && mod.__esModule) return mod;\n      var result = {};\n      if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n      __setModuleDefault(result, mod);\n      return result;\n    };\n  }();\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getStateFromPath = getStateFromPath;\n  const native_1 = require(_dependencyMap[0], \"@react-navigation/native\");\n  const escape_string_regexp_1 = __importDefault(require(_dependencyMap[1], \"escape-string-regexp\"));\n  const findFocusedRoute_1 = require(_dependencyMap[2], \"./findFocusedRoute\");\n  const expo = __importStar(require(_dependencyMap[3], \"./getStateFromPath-forks\"));\n  const constants_1 = require(_dependencyMap[4], \"../constants\");\n  /**\n   * Utility to parse a path string to initial state object accepted by the container.\n   * This is useful for deep linking when we need to handle the incoming URL.\n   *\n   * @example\n   * ```js\n   * getStateFromPath(\n   *   '/chat/jane/42',\n   *   {\n   *     screens: {\n   *       Chat: {\n   *         path: 'chat/:author/:id',\n   *         parse: { id: Number }\n   *       }\n   *     }\n   *   }\n   * )\n   * ```\n   * @param path Path string to parse and convert, e.g. /foo/bar?count=42.\n   * @param options Extra options to fine-tune how to parse the path.\n   */\n  function getStateFromPath(path, options,\n  // START FORK\n  segments = []\n  // END FORK\n  ) {\n    const {\n      initialRoutes,\n      configs,\n      configWithRegexes\n    } = getConfigResources(options,\n    // START FORK\n    segments\n    // END FORK\n    );\n    const screens = options?.screens;\n    // START FORK\n    const expoPath = expo.getUrlWithReactNavigationConcessions(path);\n    // END FORK\n    // START FORK\n    let remaining = expo.cleanPath(expoPath.nonstandardPathname);\n    // let remaining = path\n    //   .replace(/\\/+/g, '/') // Replace multiple slash (//) with single ones\n    //   .replace(/^\\//, '') // Remove extra leading slash\n    //   .replace(/\\?.*$/, ''); // Remove query params which we will handle later\n    // // Make sure there is a trailing slash\n    // remaining = remaining.endsWith('/') ? remaining : `${remaining}/`;\n    // END FORK\n    const prefix = options?.path?.replace(/^\\//, ''); // Remove extra leading slash\n    if (prefix) {\n      // Make sure there is a trailing slash\n      const normalizedPrefix = prefix.endsWith('/') ? prefix : `${prefix}/`;\n      // If the path doesn't start with the prefix, it's not a match\n      if (!remaining.startsWith(normalizedPrefix)) {\n        return undefined;\n      }\n      // Remove the prefix from the path\n      remaining = remaining.replace(normalizedPrefix, '');\n    }\n    if (screens === undefined) {\n      // When no config is specified, use the path segments as route names\n      const routes = remaining.split('/').filter(Boolean).map(segment => {\n        const name = decodeURIComponent(segment);\n        return {\n          name\n        };\n      });\n      if (routes.length) {\n        return createNestedStateObject(expoPath, routes, initialRoutes, [], expoPath.url.hash);\n      }\n      return undefined;\n    }\n    if (remaining === '/') {\n      // We need to add special handling of empty path so navigation to empty path also works\n      // When handling empty path, we should only look at the root level config\n      // START FORK\n      const match = expo.matchForEmptyPath(configWithRegexes);\n      // const match = configs.find(\n      //   (config) =>\n      //     config.path === '' &&\n      //     config.routeNames.every(\n      //       // Make sure that none of the parent configs have a non-empty path defined\n      //       (name) => !configs.find((c) => c.screen === name)?.path\n      //     )\n      // );\n      // END FORK\n      if (match) {\n        return createNestedStateObject(expoPath, match.routeNames.map(name => ({\n          name\n        })), initialRoutes, configs, expoPath.url.hash);\n      }\n      return undefined;\n    }\n    let result;\n    let current;\n    // We match the whole path against the regex instead of segments\n    // This makes sure matches such as wildcard will catch any unmatched routes, even if nested\n    const {\n      routes,\n      remainingPath\n    } = matchAgainstConfigs(remaining, configWithRegexes);\n    if (routes !== undefined) {\n      // This will always be empty if full path matched\n      current = createNestedStateObject(expoPath, routes, initialRoutes, configs, expoPath.url.hash);\n      remaining = remainingPath;\n      result = current;\n    }\n    if (current == null || result == null) {\n      return undefined;\n    }\n    return result;\n  }\n  /**\n   * Reference to the last used config resources. This is used to avoid recomputing the config resources when the options are the same.\n   */\n  let cachedConfigResources = [undefined, prepareConfigResources()];\n  function getConfigResources(options,\n  // START FORK\n  previousSegments\n  // END FORK\n  ) {\n    // START FORK - We need to disable this caching as our configs can change based upon the current state\n    // if (cachedConfigResources[0] !== options) {\n    cachedConfigResources = [options, prepareConfigResources(options, previousSegments)];\n    // }\n    // END FORK FORK\n    return cachedConfigResources[1];\n  }\n  function prepareConfigResources(options, previousSegments) {\n    if (options) {\n      (0, native_1.validatePathConfig)(options);\n    }\n    const initialRoutes = getInitialRoutes(options);\n    const configs = getNormalizedConfigs(initialRoutes, options?.screens, previousSegments);\n    checkForDuplicatedConfigs(configs);\n    const configWithRegexes = getConfigsWithRegexes(configs);\n    return {\n      initialRoutes,\n      configs,\n      configWithRegexes\n    };\n  }\n  function getInitialRoutes(options) {\n    const initialRoutes = [];\n    if (options?.initialRouteName) {\n      initialRoutes.push({\n        initialRouteName: options.initialRouteName,\n        parentScreens: []\n      });\n    }\n    return initialRoutes;\n  }\n  function getNormalizedConfigs(initialRoutes, screens = {},\n  // START FORK\n  previousSegments\n  // END FORK\n  ) {\n    // Create a normalized configs array which will be easier to use\n    return [].concat(...Object.keys(screens).map(key => createNormalizedConfigs(key, screens, [], initialRoutes, []))).map(expo.appendIsInitial(initialRoutes)).sort(expo.getRouteConfigSorter(previousSegments));\n    // .sort((a, b) => {\n    //   // Sort config so that:\n    //   // - the most exhaustive ones are always at the beginning\n    //   // - patterns with wildcard are always at the end\n    //   // If 2 patterns are same, move the one with less route names up\n    //   // This is an error state, so it's only useful for consistent error messages\n    //   if (a.pattern === b.pattern) {\n    //     return b.routeNames.join('>').localeCompare(a.routeNames.join('>'));\n    //   }\n    //   // If one of the patterns starts with the other, it's more exhaustive\n    //   // So move it up\n    //   if (a.pattern.startsWith(b.pattern)) {\n    //     return -1;\n    //   }\n    //   if (b.pattern.startsWith(a.pattern)) {\n    //     return 1;\n    //   }\n    //   const aParts = a.pattern.split('/');\n    //   const bParts = b.pattern.split('/');\n    //   for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {\n    //     // if b is longer, b get higher priority\n    //     if (aParts[i] == null) {\n    //       return 1;\n    //     }\n    //     // if a is longer, a get higher priority\n    //     if (bParts[i] == null) {\n    //       return -1;\n    //     }\n    //     const aWildCard = aParts[i] === '*' || aParts[i].startsWith(':');\n    //     const bWildCard = bParts[i] === '*' || bParts[i].startsWith(':');\n    //     // if both are wildcard we compare next component\n    //     if (aWildCard && bWildCard) {\n    //       continue;\n    //     }\n    //     // if only a is wild card, b get higher priority\n    //     if (aWildCard) {\n    //       return 1;\n    //     }\n    //     // if only b is wild card, a get higher priority\n    //     if (bWildCard) {\n    //       return -1;\n    //     }\n    //   }\n    //   return bParts.length - aParts.length;\n    // });\n  }\n  function checkForDuplicatedConfigs(configs) {\n    // Check for duplicate patterns in the config\n    configs.reduce((acc, config) => {\n      if (acc[config.pattern]) {\n        const a = acc[config.pattern].routeNames;\n        const b = config.routeNames;\n        // It's not a problem if the path string omitted from a inner most screen\n        // For example, it's ok if a path resolves to `A > B > C` or `A > B`\n        const intersects = a.length > b.length ? b.every((it, i) => a[i] === it) : a.every((it, i) => b[i] === it);\n        if (!intersects) {\n          throw new Error(`Found conflicting screens with the same pattern. The pattern '${config.pattern}' resolves to both '${a.join(' > ')}' and '${b.join(' > ')}'. Patterns must be unique and cannot resolve to more than one screen.`);\n        }\n      }\n      return Object.assign(acc, {\n        [config.pattern]: config\n      });\n    }, {});\n  }\n  function getConfigsWithRegexes(configs) {\n    return configs.map(c => ({\n      ...c,\n      // Add `$` to the regex to make sure it matches till end of the path and not just beginning\n      // START FORK\n      // regex: c.regex ? new RegExp(c.regex.source + '$') : undefined,\n      regex: expo.configRegExp(c)\n      // END FORK\n    }));\n  }\n  const joinPaths = (...paths) => [].concat(...paths.map(p => p.split('/'))).filter(Boolean).join('/');\n  const matchAgainstConfigs = (remaining, configs) => {\n    let routes;\n    let remainingPath = remaining;\n    // START FORK\n    const allParams = Object.create(null);\n    // END FORK\n    // Go through all configs, and see if the next path segment matches our regex\n    for (const config of configs) {\n      if (!config.regex) {\n        continue;\n      }\n      const match = remainingPath.match(config.regex);\n      // If our regex matches, we need to extract params from the path\n      if (match) {\n        const matchResult = config.pattern?.split('/').reduce((acc, p, index) => {\n          if (!expo.isDynamicPart(p)) {\n            return acc;\n          }\n          acc.pos += 1;\n          // START FORK\n          const decodedParamSegment = expo.safelyDecodeURIComponent(\n          // const decodedParamSegment = decodeURIComponent(\n          // The param segments appear every second item starting from 2 in the regex match result\n          match[(acc.pos + 1) * 2]\n          // Remove trailing slash\n          .replace(/\\/$/, ''));\n          // END FORK\n          Object.assign(acc.matchedParams, {\n            [p]: Object.assign(acc.matchedParams[p] || {}, {\n              [index]: decodedParamSegment\n            })\n          });\n          return acc;\n        }, {\n          pos: -1,\n          matchedParams: {}\n        });\n        const matchedParams = matchResult.matchedParams || {};\n        routes = config.routeNames.map(name => {\n          const routeConfig = configs.find(c => {\n            // Check matching name AND pattern in case same screen is used at different levels in config\n            return c.screen === name && config.pattern.startsWith(c.pattern);\n          });\n          // Normalize pattern to remove any leading, trailing slashes, duplicate slashes etc.\n          const normalizedPath = routeConfig?.path.split('/').filter(Boolean).join('/');\n          // Get the number of segments in the initial pattern\n          const numInitialSegments = routeConfig?.pattern\n          // Extract the prefix from the pattern by removing the ending path pattern (e.g pattern=`a/b/c/d` and normalizedPath=`c/d` becomes `a/b`)\n          .replace(new RegExp(`${(0, escape_string_regexp_1.default)(normalizedPath)}$`), '')?.split('/').length;\n          const params = normalizedPath?.split('/').reduce((acc, p, index) => {\n            if (!expo.isDynamicPart(p)) {\n              return acc;\n            }\n            // Get the real index of the path parameter in the matched path\n            // by offsetting by the number of segments in the initial pattern\n            const offset = numInitialSegments ? numInitialSegments - 1 : 0;\n            // START FORK\n            // const value = matchedParams[p]?.[index + offset];\n            const value = expo.getParamValue(p, matchedParams[p]?.[index + offset]);\n            // END FORK\n            if (value) {\n              // START FORK\n              // const key = p.replace(/^:/, '').replace(/\\?$/, '');\n              const key = expo.replacePart(p);\n              // END FORK\n              acc[key] = routeConfig?.parse?.[key] ? routeConfig.parse[key](value) : value;\n            }\n            return acc;\n          }, {});\n          if (params && Object.keys(params).length) {\n            Object.assign(allParams, params);\n            return {\n              name,\n              params\n            };\n          }\n          return {\n            name\n          };\n        });\n        remainingPath = remainingPath.replace(match[1], '');\n        break;\n      }\n    }\n    // START FORK\n    expo.populateParams(routes, allParams);\n    // END FORK\n    return {\n      routes,\n      remainingPath\n    };\n  };\n  const createNormalizedConfigs = (screen, routeConfig, routeNames = [], initials, parentScreens, parentPattern) => {\n    const configs = [];\n    routeNames.push(screen);\n    parentScreens.push(screen);\n    // @ts-expect-error: TODO(@kitten): This is entirely untyped. The index access just flags this, but we're not typing the config properly here\n    const config = routeConfig[screen];\n    if (typeof config === 'string') {\n      // If a string is specified as the value of the key(e.g. Foo: '/path'), use it as the pattern\n      const pattern = parentPattern ? joinPaths(parentPattern, config) : config;\n      configs.push(createConfigItem(screen, routeNames, pattern, config));\n    } else if (typeof config === 'object') {\n      let pattern;\n      // if an object is specified as the value (e.g. Foo: { ... }),\n      // it can have `path` property and\n      // it could have `screens` prop which has nested configs\n      if (typeof config.path === 'string') {\n        if (config.exact && config.path === undefined) {\n          throw new Error(\"A 'path' needs to be specified when specifying 'exact: true'. If you don't want this screen in the URL, specify it as empty string, e.g. `path: ''`.\");\n        }\n        pattern = config.exact !== true ? joinPaths(parentPattern || '', config.path || '') : config.path || '';\n        if (screen !== constants_1.INTERNAL_SLOT_NAME) {\n          configs.push(createConfigItem(screen, routeNames, pattern, config.path, config.parse, config));\n        }\n      }\n      if (config.screens) {\n        // property `initialRouteName` without `screens` has no purpose\n        if (config.initialRouteName) {\n          initials.push({\n            initialRouteName: config.initialRouteName,\n            parentScreens\n          });\n        }\n        Object.keys(config.screens).forEach(nestedConfig => {\n          const result = createNormalizedConfigs(nestedConfig, config.screens, routeNames, initials, [...parentScreens], pattern ?? parentPattern);\n          configs.push(...result);\n        });\n      }\n    }\n    routeNames.pop();\n    return configs;\n  };\n  const createConfigItem = (screen, routeNames, pattern, path, parse = undefined, config = {}) => {\n    // Normalize pattern to remove any leading, trailing slashes, duplicate slashes etc.\n    pattern = pattern.split('/').filter(Boolean).join('/');\n    // START FORK\n    const regex = pattern ? expo.routePatternToRegex(pattern) : undefined;\n    // const regex = pattern\n    //   ? new RegExp(\n    //       `^(${pattern\n    //         .split('/')\n    //         .map((it) => {\n    //           if (it.startsWith(':')) {\n    //             return `(([^/]+\\\\/)${it.endsWith('?') ? '?' : ''})`;\n    //           }\n    //           return `${it === '*' ? '.*' : escape(it)}\\\\/`;\n    //         })\n    //         .join('')})`\n    //     )\n    //   : undefined;\n    // END FORK\n    return {\n      screen,\n      regex,\n      pattern,\n      path,\n      // The routeNames array is mutated, so copy it to keep the current state\n      routeNames: [...routeNames],\n      parse,\n      // START FORK\n      ...expo.createConfig(screen, pattern, routeNames, config)\n      // END FORK\n    };\n  };\n  const findParseConfigForRoute = (routeName, flatConfig) => {\n    for (const config of flatConfig) {\n      if (routeName === config.routeNames[config.routeNames.length - 1]) {\n        return config.parse;\n      }\n    }\n    return undefined;\n  };\n  // Try to find an initial route connected with the one passed\n  const findInitialRoute = (routeName, parentScreens, initialRoutes) => {\n    for (const config of initialRoutes) {\n      if (parentScreens.length === config.parentScreens.length) {\n        let sameParents = true;\n        for (let i = 0; i < parentScreens.length; i++) {\n          if (parentScreens[i].localeCompare(config.parentScreens[i]) !== 0) {\n            sameParents = false;\n            break;\n          }\n        }\n        if (sameParents) {\n          return routeName !== config.initialRouteName ? config.initialRouteName : undefined;\n        }\n      }\n    }\n    return undefined;\n  };\n  // returns state object with values depending on whether\n  // it is the end of state and if there is initialRoute for this level\n  const createStateObject = (initialRoute, route, isEmpty) => {\n    if (isEmpty) {\n      if (initialRoute) {\n        return {\n          index: 1,\n          routes: [{\n            name: initialRoute,\n            params: route.params\n          }, route]\n        };\n      } else {\n        return {\n          routes: [route]\n        };\n      }\n    } else {\n      if (initialRoute) {\n        return {\n          index: 1,\n          routes: [{\n            name: initialRoute,\n            params: route.params\n          }, {\n            ...route,\n            state: {\n              routes: []\n            }\n          }]\n        };\n      } else {\n        return {\n          routes: [{\n            ...route,\n            state: {\n              routes: []\n            }\n          }]\n        };\n      }\n    }\n  };\n  const createNestedStateObject = ({\n    path,\n    ...expoURL\n  }, routes, initialRoutes, flatConfig, hash) => {\n    let route = routes.shift();\n    const parentScreens = [];\n    let initialRoute = findInitialRoute(route.name, parentScreens, initialRoutes);\n    parentScreens.push(route.name);\n    const state = createStateObject(initialRoute, route, routes.length === 0);\n    if (routes.length > 0) {\n      let nestedState = state;\n      while (route = routes.shift()) {\n        initialRoute = findInitialRoute(route.name, parentScreens, initialRoutes);\n        const nestedStateIndex = nestedState.index || nestedState.routes.length - 1;\n        nestedState.routes[nestedStateIndex].state = createStateObject(initialRoute, route, routes.length === 0);\n        if (routes.length > 0) {\n          nestedState = nestedState.routes[nestedStateIndex].state;\n        }\n        parentScreens.push(route.name);\n      }\n    }\n    route = (0, findFocusedRoute_1.findFocusedRoute)(state);\n    // START FORK\n    route.path = expoURL.pathWithoutGroups;\n    // route.path = path;\n    // END FORK\n    // START FORK\n    // const params = parseQueryParams(\n    const params = expo.parseQueryParams(path, route, flatConfig ? findParseConfigForRoute(route.name, flatConfig) : undefined, hash);\n    // END FORK\n    // START FORK\n    // expo.handleUrlParams(route, params, hash);\n    if (params) {\n      route.params = {\n        ...route.params,\n        ...params\n      };\n    }\n    // END FORK\n    return state;\n  };\n  // START FORK\n  // const parseQueryParams = (path: string, parseConfig?: Record<string, (value: string) => any>) => {\n  //   const query = path.split('?')[1];\n  //   const params = queryString.parse(query);\n  //   if (parseConfig) {\n  //     Object.keys(params).forEach((name) => {\n  //       if (Object.hasOwnProperty.call(parseConfig, name) && typeof params[name] === 'string') {\n  //         params[name] = parseConfig[name](params[name] as string);\n  //       }\n  //     });\n  //   }\n  //   return Object.keys(params).length ? params : undefined;\n  // };\n  // END FORK\n});", "lineCount": 582, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [4, 6, 2, 4, "__createBinding"], [4, 21, 2, 19], [4, 24, 2, 23], [4, 28, 2, 27], [4, 32, 2, 31], [4, 36, 2, 35], [4, 37, 2, 36, "__createBinding"], [4, 52, 2, 51], [4, 57, 2, 57, "Object"], [4, 63, 2, 63], [4, 64, 2, 64, "create"], [4, 70, 2, 70], [4, 73, 2, 74], [4, 83, 2, 83, "o"], [4, 84, 2, 84], [4, 86, 2, 86, "m"], [4, 87, 2, 87], [4, 89, 2, 89, "k"], [4, 90, 2, 90], [4, 92, 2, 92, "k2"], [4, 94, 2, 94], [4, 96, 2, 96], [5, 4, 3, 4], [5, 8, 3, 8, "k2"], [5, 10, 3, 10], [5, 15, 3, 15, "undefined"], [5, 24, 3, 24], [5, 26, 3, 26, "k2"], [5, 28, 3, 28], [5, 31, 3, 31, "k"], [5, 32, 3, 32], [6, 4, 4, 4], [6, 8, 4, 8, "desc"], [6, 12, 4, 12], [6, 15, 4, 15, "Object"], [6, 21, 4, 21], [6, 22, 4, 22, "getOwnPropertyDescriptor"], [6, 46, 4, 46], [6, 47, 4, 47, "m"], [6, 48, 4, 48], [6, 50, 4, 50, "k"], [6, 51, 4, 51], [6, 52, 4, 52], [7, 4, 5, 4], [7, 8, 5, 8], [7, 9, 5, 9, "desc"], [7, 13, 5, 13], [7, 18, 5, 18], [7, 23, 5, 23], [7, 27, 5, 27, "desc"], [7, 31, 5, 31], [7, 34, 5, 34], [7, 35, 5, 35, "m"], [7, 36, 5, 36], [7, 37, 5, 37, "__esModule"], [7, 47, 5, 47], [7, 50, 5, 50, "desc"], [7, 54, 5, 54], [7, 55, 5, 55, "writable"], [7, 63, 5, 63], [7, 67, 5, 67, "desc"], [7, 71, 5, 71], [7, 72, 5, 72, "configurable"], [7, 84, 5, 84], [7, 85, 5, 85], [7, 87, 5, 87], [8, 6, 6, 6, "desc"], [8, 10, 6, 10], [8, 13, 6, 13], [9, 8, 6, 15, "enumerable"], [9, 18, 6, 25], [9, 20, 6, 27], [9, 24, 6, 31], [10, 8, 6, 33, "get"], [10, 11, 6, 36], [10, 13, 6, 38], [10, 22, 6, 38, "get"], [10, 23, 6, 38], [10, 25, 6, 49], [11, 10, 6, 51], [11, 17, 6, 58, "m"], [11, 18, 6, 59], [11, 19, 6, 60, "k"], [11, 20, 6, 61], [11, 21, 6, 62], [12, 8, 6, 64], [13, 6, 6, 66], [13, 7, 6, 67], [14, 4, 7, 4], [15, 4, 8, 4, "Object"], [15, 10, 8, 10], [15, 11, 8, 11, "defineProperty"], [15, 25, 8, 25], [15, 26, 8, 26, "o"], [15, 27, 8, 27], [15, 29, 8, 29, "k2"], [15, 31, 8, 31], [15, 33, 8, 33, "desc"], [15, 37, 8, 37], [15, 38, 8, 38], [16, 2, 9, 0], [16, 3, 9, 1], [16, 6, 9, 6], [16, 16, 9, 15, "o"], [16, 17, 9, 16], [16, 19, 9, 18, "m"], [16, 20, 9, 19], [16, 22, 9, 21, "k"], [16, 23, 9, 22], [16, 25, 9, 24, "k2"], [16, 27, 9, 26], [16, 29, 9, 28], [17, 4, 10, 4], [17, 8, 10, 8, "k2"], [17, 10, 10, 10], [17, 15, 10, 15, "undefined"], [17, 24, 10, 24], [17, 26, 10, 26, "k2"], [17, 28, 10, 28], [17, 31, 10, 31, "k"], [17, 32, 10, 32], [18, 4, 11, 4, "o"], [18, 5, 11, 5], [18, 6, 11, 6, "k2"], [18, 8, 11, 8], [18, 9, 11, 9], [18, 12, 11, 12, "m"], [18, 13, 11, 13], [18, 14, 11, 14, "k"], [18, 15, 11, 15], [18, 16, 11, 16], [19, 2, 12, 0], [19, 3, 12, 2], [19, 4, 12, 3], [20, 2, 13, 0], [20, 6, 13, 4, "__setModuleDefault"], [20, 24, 13, 22], [20, 27, 13, 26], [20, 31, 13, 30], [20, 35, 13, 34], [20, 39, 13, 38], [20, 40, 13, 39, "__setModuleDefault"], [20, 58, 13, 57], [20, 63, 13, 63, "Object"], [20, 69, 13, 69], [20, 70, 13, 70, "create"], [20, 76, 13, 76], [20, 79, 13, 80], [20, 89, 13, 89, "o"], [20, 90, 13, 90], [20, 92, 13, 92, "v"], [20, 93, 13, 93], [20, 95, 13, 95], [21, 4, 14, 4, "Object"], [21, 10, 14, 10], [21, 11, 14, 11, "defineProperty"], [21, 25, 14, 25], [21, 26, 14, 26, "o"], [21, 27, 14, 27], [21, 29, 14, 29], [21, 38, 14, 38], [21, 40, 14, 40], [22, 6, 14, 42, "enumerable"], [22, 16, 14, 52], [22, 18, 14, 54], [22, 22, 14, 58], [23, 6, 14, 60, "value"], [23, 11, 14, 65], [23, 13, 14, 67, "v"], [24, 4, 14, 69], [24, 5, 14, 70], [24, 6, 14, 71], [25, 2, 15, 0], [25, 3, 15, 1], [25, 6, 15, 5], [25, 16, 15, 14, "o"], [25, 17, 15, 15], [25, 19, 15, 17, "v"], [25, 20, 15, 18], [25, 22, 15, 20], [26, 4, 16, 4, "o"], [26, 5, 16, 5], [26, 6, 16, 6], [26, 15, 16, 15], [26, 16, 16, 16], [26, 19, 16, 19, "v"], [26, 20, 16, 20], [27, 2, 17, 0], [27, 3, 17, 1], [27, 4, 17, 2], [28, 2, 18, 0], [28, 6, 18, 4, "__importStar"], [28, 18, 18, 16], [28, 21, 18, 20], [28, 25, 18, 24], [28, 29, 18, 28], [28, 33, 18, 32], [28, 34, 18, 33, "__importStar"], [28, 46, 18, 45], [28, 50, 18, 51], [28, 62, 18, 63], [29, 4, 19, 4], [29, 8, 19, 8, "ownKeys"], [29, 15, 19, 15], [29, 18, 19, 18], [29, 27, 19, 18, "ownKeys"], [29, 28, 19, 27, "o"], [29, 29, 19, 28], [29, 31, 19, 30], [30, 6, 20, 8, "ownKeys"], [30, 13, 20, 15], [30, 16, 20, 18, "Object"], [30, 22, 20, 24], [30, 23, 20, 25, "getOwnPropertyNames"], [30, 42, 20, 44], [30, 46, 20, 48], [30, 56, 20, 58, "o"], [30, 57, 20, 59], [30, 59, 20, 61], [31, 8, 21, 12], [31, 12, 21, 16, "ar"], [31, 14, 21, 18], [31, 17, 21, 21], [31, 19, 21, 23], [32, 8, 22, 12], [32, 13, 22, 17], [32, 17, 22, 21, "k"], [32, 18, 22, 22], [32, 22, 22, 26, "o"], [32, 23, 22, 27], [32, 25, 22, 29], [32, 29, 22, 33, "Object"], [32, 35, 22, 39], [32, 36, 22, 40, "prototype"], [32, 45, 22, 49], [32, 46, 22, 50, "hasOwnProperty"], [32, 60, 22, 64], [32, 61, 22, 65, "call"], [32, 65, 22, 69], [32, 66, 22, 70, "o"], [32, 67, 22, 71], [32, 69, 22, 73, "k"], [32, 70, 22, 74], [32, 71, 22, 75], [32, 73, 22, 77, "ar"], [32, 75, 22, 79], [32, 76, 22, 80, "ar"], [32, 78, 22, 82], [32, 79, 22, 83, "length"], [32, 85, 22, 89], [32, 86, 22, 90], [32, 89, 22, 93, "k"], [32, 90, 22, 94], [33, 8, 23, 12], [33, 15, 23, 19, "ar"], [33, 17, 23, 21], [34, 6, 24, 8], [34, 7, 24, 9], [35, 6, 25, 8], [35, 13, 25, 15, "ownKeys"], [35, 20, 25, 22], [35, 21, 25, 23, "o"], [35, 22, 25, 24], [35, 23, 25, 25], [36, 4, 26, 4], [36, 5, 26, 5], [37, 4, 27, 4], [37, 11, 27, 11], [37, 21, 27, 21, "mod"], [37, 24, 27, 24], [37, 26, 27, 26], [38, 6, 28, 8], [38, 10, 28, 12, "mod"], [38, 13, 28, 15], [38, 17, 28, 19, "mod"], [38, 20, 28, 22], [38, 21, 28, 23, "__esModule"], [38, 31, 28, 33], [38, 33, 28, 35], [38, 40, 28, 42, "mod"], [38, 43, 28, 45], [39, 6, 29, 8], [39, 10, 29, 12, "result"], [39, 16, 29, 18], [39, 19, 29, 21], [39, 20, 29, 22], [39, 21, 29, 23], [40, 6, 30, 8], [40, 10, 30, 12, "mod"], [40, 13, 30, 15], [40, 17, 30, 19], [40, 21, 30, 23], [40, 23, 30, 25], [40, 28, 30, 30], [40, 32, 30, 34, "k"], [40, 33, 30, 35], [40, 36, 30, 38, "ownKeys"], [40, 43, 30, 45], [40, 44, 30, 46, "mod"], [40, 47, 30, 49], [40, 48, 30, 50], [40, 50, 30, 52, "i"], [40, 51, 30, 53], [40, 54, 30, 56], [40, 55, 30, 57], [40, 57, 30, 59, "i"], [40, 58, 30, 60], [40, 61, 30, 63, "k"], [40, 62, 30, 64], [40, 63, 30, 65, "length"], [40, 69, 30, 71], [40, 71, 30, 73, "i"], [40, 72, 30, 74], [40, 74, 30, 76], [40, 76, 30, 78], [40, 80, 30, 82, "k"], [40, 81, 30, 83], [40, 82, 30, 84, "i"], [40, 83, 30, 85], [40, 84, 30, 86], [40, 89, 30, 91], [40, 98, 30, 100], [40, 100, 30, 102, "__createBinding"], [40, 115, 30, 117], [40, 116, 30, 118, "result"], [40, 122, 30, 124], [40, 124, 30, 126, "mod"], [40, 127, 30, 129], [40, 129, 30, 131, "k"], [40, 130, 30, 132], [40, 131, 30, 133, "i"], [40, 132, 30, 134], [40, 133, 30, 135], [40, 134, 30, 136], [41, 6, 31, 8, "__setModuleDefault"], [41, 24, 31, 26], [41, 25, 31, 27, "result"], [41, 31, 31, 33], [41, 33, 31, 35, "mod"], [41, 36, 31, 38], [41, 37, 31, 39], [42, 6, 32, 8], [42, 13, 32, 15, "result"], [42, 19, 32, 21], [43, 4, 33, 4], [43, 5, 33, 5], [44, 2, 34, 0], [44, 3, 34, 1], [44, 4, 34, 3], [44, 5, 34, 4], [45, 2, 35, 0], [45, 6, 35, 4, "__importDefault"], [45, 21, 35, 19], [45, 24, 35, 23], [45, 28, 35, 27], [45, 32, 35, 31], [45, 36, 35, 35], [45, 37, 35, 36, "__importDefault"], [45, 52, 35, 51], [45, 56, 35, 56], [45, 66, 35, 66, "mod"], [45, 69, 35, 69], [45, 71, 35, 71], [46, 4, 36, 4], [46, 11, 36, 12, "mod"], [46, 14, 36, 15], [46, 18, 36, 19, "mod"], [46, 21, 36, 22], [46, 22, 36, 23, "__esModule"], [46, 32, 36, 33], [46, 35, 36, 37, "mod"], [46, 38, 36, 40], [46, 41, 36, 43], [47, 6, 36, 45], [47, 15, 36, 54], [47, 17, 36, 56, "mod"], [48, 4, 36, 60], [48, 5, 36, 61], [49, 2, 37, 0], [49, 3, 37, 1], [50, 2, 38, 0, "Object"], [50, 8, 38, 6], [50, 9, 38, 7, "defineProperty"], [50, 23, 38, 21], [50, 24, 38, 22, "exports"], [50, 31, 38, 29], [50, 33, 38, 31], [50, 45, 38, 43], [50, 47, 38, 45], [51, 4, 38, 47, "value"], [51, 9, 38, 52], [51, 11, 38, 54], [52, 2, 38, 59], [52, 3, 38, 60], [52, 4, 38, 61], [53, 2, 39, 0, "exports"], [53, 9, 39, 7], [53, 10, 39, 8, "getStateFromPath"], [53, 26, 39, 24], [53, 29, 39, 27, "getStateFromPath"], [53, 45, 39, 43], [54, 2, 40, 0], [54, 8, 40, 6, "native_1"], [54, 16, 40, 14], [54, 19, 40, 17, "require"], [54, 26, 40, 24], [54, 27, 40, 24, "_dependencyMap"], [54, 41, 40, 24], [54, 72, 40, 51], [54, 73, 40, 52], [55, 2, 41, 0], [55, 8, 41, 6, "escape_string_regexp_1"], [55, 30, 41, 28], [55, 33, 41, 31, "__importDefault"], [55, 48, 41, 46], [55, 49, 41, 47, "require"], [55, 56, 41, 54], [55, 57, 41, 54, "_dependencyMap"], [55, 71, 41, 54], [55, 98, 41, 77], [55, 99, 41, 78], [55, 100, 41, 79], [56, 2, 42, 0], [56, 8, 42, 6, "findFocusedRoute_1"], [56, 26, 42, 24], [56, 29, 42, 27, "require"], [56, 36, 42, 34], [56, 37, 42, 34, "_dependencyMap"], [56, 51, 42, 34], [56, 76, 42, 55], [56, 77, 42, 56], [57, 2, 43, 0], [57, 8, 43, 6, "expo"], [57, 12, 43, 10], [57, 15, 43, 13, "__importStar"], [57, 27, 43, 25], [57, 28, 43, 26, "require"], [57, 35, 43, 33], [57, 36, 43, 33, "_dependencyMap"], [57, 50, 43, 33], [57, 81, 43, 60], [57, 82, 43, 61], [57, 83, 43, 62], [58, 2, 44, 0], [58, 8, 44, 6, "constants_1"], [58, 19, 44, 17], [58, 22, 44, 20, "require"], [58, 29, 44, 27], [58, 30, 44, 27, "_dependencyMap"], [58, 44, 44, 27], [58, 63, 44, 42], [58, 64, 44, 43], [59, 2, 45, 0], [60, 0, 46, 0], [61, 0, 47, 0], [62, 0, 48, 0], [63, 0, 49, 0], [64, 0, 50, 0], [65, 0, 51, 0], [66, 0, 52, 0], [67, 0, 53, 0], [68, 0, 54, 0], [69, 0, 55, 0], [70, 0, 56, 0], [71, 0, 57, 0], [72, 0, 58, 0], [73, 0, 59, 0], [74, 0, 60, 0], [75, 0, 61, 0], [76, 0, 62, 0], [77, 0, 63, 0], [78, 0, 64, 0], [79, 0, 65, 0], [80, 2, 66, 0], [80, 11, 66, 9, "getStateFromPath"], [80, 27, 66, 25, "getStateFromPath"], [80, 28, 66, 26, "path"], [80, 32, 66, 30], [80, 34, 66, 32, "options"], [80, 41, 66, 39], [81, 2, 67, 0], [82, 2, 68, 0, "segments"], [82, 10, 68, 8], [82, 13, 68, 11], [83, 2, 69, 0], [84, 2, 69, 0], [84, 4, 70, 2], [85, 4, 71, 4], [85, 10, 71, 10], [86, 6, 71, 12, "initialRoutes"], [86, 19, 71, 25], [87, 6, 71, 27, "configs"], [87, 13, 71, 34], [88, 6, 71, 36, "configWithRegexes"], [89, 4, 71, 54], [89, 5, 71, 55], [89, 8, 71, 58, "getConfigResources"], [89, 26, 71, 76], [89, 27, 71, 77, "options"], [89, 34, 71, 84], [90, 4, 72, 4], [91, 4, 73, 4, "segments"], [92, 4, 74, 4], [93, 4, 75, 4], [93, 5, 75, 5], [94, 4, 76, 4], [94, 10, 76, 10, "screens"], [94, 17, 76, 17], [94, 20, 76, 20, "options"], [94, 27, 76, 27], [94, 29, 76, 29, "screens"], [94, 36, 76, 36], [95, 4, 77, 4], [96, 4, 78, 4], [96, 10, 78, 10, "expoPath"], [96, 18, 78, 18], [96, 21, 78, 21, "expo"], [96, 25, 78, 25], [96, 26, 78, 26, "getUrlWithReactNavigationConcessions"], [96, 62, 78, 62], [96, 63, 78, 63, "path"], [96, 67, 78, 67], [96, 68, 78, 68], [97, 4, 79, 4], [98, 4, 80, 4], [99, 4, 81, 4], [99, 8, 81, 8, "remaining"], [99, 17, 81, 17], [99, 20, 81, 20, "expo"], [99, 24, 81, 24], [99, 25, 81, 25, "cleanPath"], [99, 34, 81, 34], [99, 35, 81, 35, "expoPath"], [99, 43, 81, 43], [99, 44, 81, 44, "nonstandardPathname"], [99, 63, 81, 63], [99, 64, 81, 64], [100, 4, 82, 4], [101, 4, 83, 4], [102, 4, 84, 4], [103, 4, 85, 4], [104, 4, 86, 4], [105, 4, 87, 4], [106, 4, 88, 4], [107, 4, 89, 4], [107, 10, 89, 10, "prefix"], [107, 16, 89, 16], [107, 19, 89, 19, "options"], [107, 26, 89, 26], [107, 28, 89, 28, "path"], [107, 32, 89, 32], [107, 34, 89, 34, "replace"], [107, 41, 89, 41], [107, 42, 89, 42], [107, 47, 89, 47], [107, 49, 89, 49], [107, 51, 89, 51], [107, 52, 89, 52], [107, 53, 89, 53], [107, 54, 89, 54], [108, 4, 90, 4], [108, 8, 90, 8, "prefix"], [108, 14, 90, 14], [108, 16, 90, 16], [109, 6, 91, 8], [110, 6, 92, 8], [110, 12, 92, 14, "normalizedPrefix"], [110, 28, 92, 30], [110, 31, 92, 33, "prefix"], [110, 37, 92, 39], [110, 38, 92, 40, "endsWith"], [110, 46, 92, 48], [110, 47, 92, 49], [110, 50, 92, 52], [110, 51, 92, 53], [110, 54, 92, 56, "prefix"], [110, 60, 92, 62], [110, 63, 92, 65], [110, 66, 92, 68, "prefix"], [110, 72, 92, 74], [110, 75, 92, 77], [111, 6, 93, 8], [112, 6, 94, 8], [112, 10, 94, 12], [112, 11, 94, 13, "remaining"], [112, 20, 94, 22], [112, 21, 94, 23, "startsWith"], [112, 31, 94, 33], [112, 32, 94, 34, "normalizedPrefix"], [112, 48, 94, 50], [112, 49, 94, 51], [112, 51, 94, 53], [113, 8, 95, 12], [113, 15, 95, 19, "undefined"], [113, 24, 95, 28], [114, 6, 96, 8], [115, 6, 97, 8], [116, 6, 98, 8, "remaining"], [116, 15, 98, 17], [116, 18, 98, 20, "remaining"], [116, 27, 98, 29], [116, 28, 98, 30, "replace"], [116, 35, 98, 37], [116, 36, 98, 38, "normalizedPrefix"], [116, 52, 98, 54], [116, 54, 98, 56], [116, 56, 98, 58], [116, 57, 98, 59], [117, 4, 99, 4], [118, 4, 100, 4], [118, 8, 100, 8, "screens"], [118, 15, 100, 15], [118, 20, 100, 20, "undefined"], [118, 29, 100, 29], [118, 31, 100, 31], [119, 6, 101, 8], [120, 6, 102, 8], [120, 12, 102, 14, "routes"], [120, 18, 102, 20], [120, 21, 102, 23, "remaining"], [120, 30, 102, 32], [120, 31, 103, 13, "split"], [120, 36, 103, 18], [120, 37, 103, 19], [120, 40, 103, 22], [120, 41, 103, 23], [120, 42, 104, 13, "filter"], [120, 48, 104, 19], [120, 49, 104, 20, "Boolean"], [120, 56, 104, 27], [120, 57, 104, 28], [120, 58, 105, 13, "map"], [120, 61, 105, 16], [120, 62, 105, 18, "segment"], [120, 69, 105, 25], [120, 73, 105, 30], [121, 8, 106, 12], [121, 14, 106, 18, "name"], [121, 18, 106, 22], [121, 21, 106, 25, "decodeURIComponent"], [121, 39, 106, 43], [121, 40, 106, 44, "segment"], [121, 47, 106, 51], [121, 48, 106, 52], [122, 8, 107, 12], [122, 15, 107, 19], [123, 10, 107, 21, "name"], [124, 8, 107, 26], [124, 9, 107, 27], [125, 6, 108, 8], [125, 7, 108, 9], [125, 8, 108, 10], [126, 6, 109, 8], [126, 10, 109, 12, "routes"], [126, 16, 109, 18], [126, 17, 109, 19, "length"], [126, 23, 109, 25], [126, 25, 109, 27], [127, 8, 110, 12], [127, 15, 110, 19, "createNestedStateObject"], [127, 38, 110, 42], [127, 39, 110, 43, "expoPath"], [127, 47, 110, 51], [127, 49, 110, 53, "routes"], [127, 55, 110, 59], [127, 57, 110, 61, "initialRoutes"], [127, 70, 110, 74], [127, 72, 110, 76], [127, 74, 110, 78], [127, 76, 110, 80, "expoPath"], [127, 84, 110, 88], [127, 85, 110, 89, "url"], [127, 88, 110, 92], [127, 89, 110, 93, "hash"], [127, 93, 110, 97], [127, 94, 110, 98], [128, 6, 111, 8], [129, 6, 112, 8], [129, 13, 112, 15, "undefined"], [129, 22, 112, 24], [130, 4, 113, 4], [131, 4, 114, 4], [131, 8, 114, 8, "remaining"], [131, 17, 114, 17], [131, 22, 114, 22], [131, 25, 114, 25], [131, 27, 114, 27], [132, 6, 115, 8], [133, 6, 116, 8], [134, 6, 117, 8], [135, 6, 118, 8], [135, 12, 118, 14, "match"], [135, 17, 118, 19], [135, 20, 118, 22, "expo"], [135, 24, 118, 26], [135, 25, 118, 27, "matchForEmptyPath"], [135, 42, 118, 44], [135, 43, 118, 45, "configWithRegexes"], [135, 60, 118, 62], [135, 61, 118, 63], [136, 6, 119, 8], [137, 6, 120, 8], [138, 6, 121, 8], [139, 6, 122, 8], [140, 6, 123, 8], [141, 6, 124, 8], [142, 6, 125, 8], [143, 6, 126, 8], [144, 6, 127, 8], [145, 6, 128, 8], [145, 10, 128, 12, "match"], [145, 15, 128, 17], [145, 17, 128, 19], [146, 8, 129, 12], [146, 15, 129, 19, "createNestedStateObject"], [146, 38, 129, 42], [146, 39, 129, 43, "expoPath"], [146, 47, 129, 51], [146, 49, 129, 53, "match"], [146, 54, 129, 58], [146, 55, 129, 59, "routeNames"], [146, 65, 129, 69], [146, 66, 129, 70, "map"], [146, 69, 129, 73], [146, 70, 129, 75, "name"], [146, 74, 129, 79], [146, 79, 129, 85], [147, 10, 129, 87, "name"], [148, 8, 129, 92], [148, 9, 129, 93], [148, 10, 129, 94], [148, 11, 129, 95], [148, 13, 129, 97, "initialRoutes"], [148, 26, 129, 110], [148, 28, 129, 112, "configs"], [148, 35, 129, 119], [148, 37, 129, 121, "expoPath"], [148, 45, 129, 129], [148, 46, 129, 130, "url"], [148, 49, 129, 133], [148, 50, 129, 134, "hash"], [148, 54, 129, 138], [148, 55, 129, 139], [149, 6, 130, 8], [150, 6, 131, 8], [150, 13, 131, 15, "undefined"], [150, 22, 131, 24], [151, 4, 132, 4], [152, 4, 133, 4], [152, 8, 133, 8, "result"], [152, 14, 133, 14], [153, 4, 134, 4], [153, 8, 134, 8, "current"], [153, 15, 134, 15], [154, 4, 135, 4], [155, 4, 136, 4], [156, 4, 137, 4], [156, 10, 137, 10], [157, 6, 137, 12, "routes"], [157, 12, 137, 18], [158, 6, 137, 20, "remainingPath"], [159, 4, 137, 34], [159, 5, 137, 35], [159, 8, 137, 38, "matchAgainstConfigs"], [159, 27, 137, 57], [159, 28, 137, 58, "remaining"], [159, 37, 137, 67], [159, 39, 137, 69, "configWithRegexes"], [159, 56, 137, 86], [159, 57, 137, 87], [160, 4, 138, 4], [160, 8, 138, 8, "routes"], [160, 14, 138, 14], [160, 19, 138, 19, "undefined"], [160, 28, 138, 28], [160, 30, 138, 30], [161, 6, 139, 8], [162, 6, 140, 8, "current"], [162, 13, 140, 15], [162, 16, 140, 18, "createNestedStateObject"], [162, 39, 140, 41], [162, 40, 140, 42, "expoPath"], [162, 48, 140, 50], [162, 50, 140, 52, "routes"], [162, 56, 140, 58], [162, 58, 140, 60, "initialRoutes"], [162, 71, 140, 73], [162, 73, 140, 75, "configs"], [162, 80, 140, 82], [162, 82, 140, 84, "expoPath"], [162, 90, 140, 92], [162, 91, 140, 93, "url"], [162, 94, 140, 96], [162, 95, 140, 97, "hash"], [162, 99, 140, 101], [162, 100, 140, 102], [163, 6, 141, 8, "remaining"], [163, 15, 141, 17], [163, 18, 141, 20, "remainingPath"], [163, 31, 141, 33], [164, 6, 142, 8, "result"], [164, 12, 142, 14], [164, 15, 142, 17, "current"], [164, 22, 142, 24], [165, 4, 143, 4], [166, 4, 144, 4], [166, 8, 144, 8, "current"], [166, 15, 144, 15], [166, 19, 144, 19], [166, 23, 144, 23], [166, 27, 144, 27, "result"], [166, 33, 144, 33], [166, 37, 144, 37], [166, 41, 144, 41], [166, 43, 144, 43], [167, 6, 145, 8], [167, 13, 145, 15, "undefined"], [167, 22, 145, 24], [168, 4, 146, 4], [169, 4, 147, 4], [169, 11, 147, 11, "result"], [169, 17, 147, 17], [170, 2, 148, 0], [171, 2, 149, 0], [172, 0, 150, 0], [173, 0, 151, 0], [174, 2, 152, 0], [174, 6, 152, 4, "cachedConfigResources"], [174, 27, 152, 25], [174, 30, 152, 28], [174, 31, 153, 4, "undefined"], [174, 40, 153, 13], [174, 42, 154, 4, "prepareConfigResources"], [174, 64, 154, 26], [174, 65, 154, 27], [174, 66, 154, 28], [174, 67, 155, 1], [175, 2, 156, 0], [175, 11, 156, 9, "getConfigResources"], [175, 29, 156, 27, "getConfigResources"], [175, 30, 156, 28, "options"], [175, 37, 156, 35], [176, 2, 157, 0], [177, 2, 158, 0, "previousSegments"], [178, 2, 159, 0], [179, 2, 159, 0], [179, 4, 160, 2], [180, 4, 161, 4], [181, 4, 162, 4], [182, 4, 163, 4, "cachedConfigResources"], [182, 25, 163, 25], [182, 28, 163, 28], [182, 29, 163, 29, "options"], [182, 36, 163, 36], [182, 38, 163, 38, "prepareConfigResources"], [182, 60, 163, 60], [182, 61, 163, 61, "options"], [182, 68, 163, 68], [182, 70, 163, 70, "previousSegments"], [182, 86, 163, 86], [182, 87, 163, 87], [182, 88, 163, 88], [183, 4, 164, 4], [184, 4, 165, 4], [185, 4, 166, 4], [185, 11, 166, 11, "cachedConfigResources"], [185, 32, 166, 32], [185, 33, 166, 33], [185, 34, 166, 34], [185, 35, 166, 35], [186, 2, 167, 0], [187, 2, 168, 0], [187, 11, 168, 9, "prepareConfigResources"], [187, 33, 168, 31, "prepareConfigResources"], [187, 34, 168, 32, "options"], [187, 41, 168, 39], [187, 43, 168, 41, "previousSegments"], [187, 59, 168, 57], [187, 61, 168, 59], [188, 4, 169, 4], [188, 8, 169, 8, "options"], [188, 15, 169, 15], [188, 17, 169, 17], [189, 6, 170, 8], [189, 7, 170, 9], [189, 8, 170, 10], [189, 10, 170, 12, "native_1"], [189, 18, 170, 20], [189, 19, 170, 21, "validatePathConfig"], [189, 37, 170, 39], [189, 39, 170, 41, "options"], [189, 46, 170, 48], [189, 47, 170, 49], [190, 4, 171, 4], [191, 4, 172, 4], [191, 10, 172, 10, "initialRoutes"], [191, 23, 172, 23], [191, 26, 172, 26, "getInitialRoutes"], [191, 42, 172, 42], [191, 43, 172, 43, "options"], [191, 50, 172, 50], [191, 51, 172, 51], [192, 4, 173, 4], [192, 10, 173, 10, "configs"], [192, 17, 173, 17], [192, 20, 173, 20, "getNormalizedConfigs"], [192, 40, 173, 40], [192, 41, 173, 41, "initialRoutes"], [192, 54, 173, 54], [192, 56, 173, 56, "options"], [192, 63, 173, 63], [192, 65, 173, 65, "screens"], [192, 72, 173, 72], [192, 74, 173, 74, "previousSegments"], [192, 90, 173, 90], [192, 91, 173, 91], [193, 4, 174, 4, "checkForDuplicatedConfigs"], [193, 29, 174, 29], [193, 30, 174, 30, "configs"], [193, 37, 174, 37], [193, 38, 174, 38], [194, 4, 175, 4], [194, 10, 175, 10, "configWithRegexes"], [194, 27, 175, 27], [194, 30, 175, 30, "getConfigsWithRegexes"], [194, 51, 175, 51], [194, 52, 175, 52, "configs"], [194, 59, 175, 59], [194, 60, 175, 60], [195, 4, 176, 4], [195, 11, 176, 11], [196, 6, 177, 8, "initialRoutes"], [196, 19, 177, 21], [197, 6, 178, 8, "configs"], [197, 13, 178, 15], [198, 6, 179, 8, "configWithRegexes"], [199, 4, 180, 4], [199, 5, 180, 5], [200, 2, 181, 0], [201, 2, 182, 0], [201, 11, 182, 9, "getInitialRoutes"], [201, 27, 182, 25, "getInitialRoutes"], [201, 28, 182, 26, "options"], [201, 35, 182, 33], [201, 37, 182, 35], [202, 4, 183, 4], [202, 10, 183, 10, "initialRoutes"], [202, 23, 183, 23], [202, 26, 183, 26], [202, 28, 183, 28], [203, 4, 184, 4], [203, 8, 184, 8, "options"], [203, 15, 184, 15], [203, 17, 184, 17, "initialRouteName"], [203, 33, 184, 33], [203, 35, 184, 35], [204, 6, 185, 8, "initialRoutes"], [204, 19, 185, 21], [204, 20, 185, 22, "push"], [204, 24, 185, 26], [204, 25, 185, 27], [205, 8, 186, 12, "initialRouteName"], [205, 24, 186, 28], [205, 26, 186, 30, "options"], [205, 33, 186, 37], [205, 34, 186, 38, "initialRouteName"], [205, 50, 186, 54], [206, 8, 187, 12, "parentScreens"], [206, 21, 187, 25], [206, 23, 187, 27], [207, 6, 188, 8], [207, 7, 188, 9], [207, 8, 188, 10], [208, 4, 189, 4], [209, 4, 190, 4], [209, 11, 190, 11, "initialRoutes"], [209, 24, 190, 24], [210, 2, 191, 0], [211, 2, 192, 0], [211, 11, 192, 9, "getNormalizedConfigs"], [211, 31, 192, 29, "getNormalizedConfigs"], [211, 32, 192, 30, "initialRoutes"], [211, 45, 192, 43], [211, 47, 192, 45, "screens"], [211, 54, 192, 52], [211, 57, 192, 55], [211, 58, 192, 56], [211, 59, 192, 57], [212, 2, 193, 0], [213, 2, 194, 0, "previousSegments"], [214, 2, 195, 0], [215, 2, 195, 0], [215, 4, 196, 2], [216, 4, 197, 4], [217, 4, 198, 4], [217, 11, 198, 11], [217, 13, 198, 13], [217, 14, 199, 9, "concat"], [217, 20, 199, 15], [217, 21, 199, 16], [217, 24, 199, 19, "Object"], [217, 30, 199, 25], [217, 31, 199, 26, "keys"], [217, 35, 199, 30], [217, 36, 199, 31, "screens"], [217, 43, 199, 38], [217, 44, 199, 39], [217, 45, 199, 40, "map"], [217, 48, 199, 43], [217, 49, 199, 45, "key"], [217, 52, 199, 48], [217, 56, 199, 53, "createNormalizedConfigs"], [217, 79, 199, 76], [217, 80, 199, 77, "key"], [217, 83, 199, 80], [217, 85, 199, 82, "screens"], [217, 92, 199, 89], [217, 94, 199, 91], [217, 96, 199, 93], [217, 98, 199, 95, "initialRoutes"], [217, 111, 199, 108], [217, 113, 199, 110], [217, 115, 199, 112], [217, 116, 199, 113], [217, 117, 199, 114], [217, 118, 199, 115], [217, 119, 200, 9, "map"], [217, 122, 200, 12], [217, 123, 200, 13, "expo"], [217, 127, 200, 17], [217, 128, 200, 18, "appendIsInitial"], [217, 143, 200, 33], [217, 144, 200, 34, "initialRoutes"], [217, 157, 200, 47], [217, 158, 200, 48], [217, 159, 200, 49], [217, 160, 201, 9, "sort"], [217, 164, 201, 13], [217, 165, 201, 14, "expo"], [217, 169, 201, 18], [217, 170, 201, 19, "getRouteConfigSorter"], [217, 190, 201, 39], [217, 191, 201, 40, "previousSegments"], [217, 207, 201, 56], [217, 208, 201, 57], [217, 209, 201, 58], [218, 4, 202, 4], [219, 4, 203, 4], [220, 4, 204, 4], [221, 4, 205, 4], [222, 4, 206, 4], [223, 4, 207, 4], [224, 4, 208, 4], [225, 4, 209, 4], [226, 4, 210, 4], [227, 4, 211, 4], [228, 4, 212, 4], [229, 4, 213, 4], [230, 4, 214, 4], [231, 4, 215, 4], [232, 4, 216, 4], [233, 4, 217, 4], [234, 4, 218, 4], [235, 4, 219, 4], [236, 4, 220, 4], [237, 4, 221, 4], [238, 4, 222, 4], [239, 4, 223, 4], [240, 4, 224, 4], [241, 4, 225, 4], [242, 4, 226, 4], [243, 4, 227, 4], [244, 4, 228, 4], [245, 4, 229, 4], [246, 4, 230, 4], [247, 4, 231, 4], [248, 4, 232, 4], [249, 4, 233, 4], [250, 4, 234, 4], [251, 4, 235, 4], [252, 4, 236, 4], [253, 4, 237, 4], [254, 4, 238, 4], [255, 4, 239, 4], [256, 4, 240, 4], [257, 4, 241, 4], [258, 4, 242, 4], [259, 4, 243, 4], [260, 4, 244, 4], [261, 4, 245, 4], [262, 4, 246, 4], [263, 2, 247, 0], [264, 2, 248, 0], [264, 11, 248, 9, "checkForDuplicatedConfigs"], [264, 36, 248, 34, "checkForDuplicatedConfigs"], [264, 37, 248, 35, "configs"], [264, 44, 248, 42], [264, 46, 248, 44], [265, 4, 249, 4], [266, 4, 250, 4, "configs"], [266, 11, 250, 11], [266, 12, 250, 12, "reduce"], [266, 18, 250, 18], [266, 19, 250, 19], [266, 20, 250, 20, "acc"], [266, 23, 250, 23], [266, 25, 250, 25, "config"], [266, 31, 250, 31], [266, 36, 250, 36], [267, 6, 251, 8], [267, 10, 251, 12, "acc"], [267, 13, 251, 15], [267, 14, 251, 16, "config"], [267, 20, 251, 22], [267, 21, 251, 23, "pattern"], [267, 28, 251, 30], [267, 29, 251, 31], [267, 31, 251, 33], [268, 8, 252, 12], [268, 14, 252, 18, "a"], [268, 15, 252, 19], [268, 18, 252, 22, "acc"], [268, 21, 252, 25], [268, 22, 252, 26, "config"], [268, 28, 252, 32], [268, 29, 252, 33, "pattern"], [268, 36, 252, 40], [268, 37, 252, 41], [268, 38, 252, 42, "routeNames"], [268, 48, 252, 52], [269, 8, 253, 12], [269, 14, 253, 18, "b"], [269, 15, 253, 19], [269, 18, 253, 22, "config"], [269, 24, 253, 28], [269, 25, 253, 29, "routeNames"], [269, 35, 253, 39], [270, 8, 254, 12], [271, 8, 255, 12], [272, 8, 256, 12], [272, 14, 256, 18, "intersects"], [272, 24, 256, 28], [272, 27, 256, 31, "a"], [272, 28, 256, 32], [272, 29, 256, 33, "length"], [272, 35, 256, 39], [272, 38, 256, 42, "b"], [272, 39, 256, 43], [272, 40, 256, 44, "length"], [272, 46, 256, 50], [272, 49, 256, 53, "b"], [272, 50, 256, 54], [272, 51, 256, 55, "every"], [272, 56, 256, 60], [272, 57, 256, 61], [272, 58, 256, 62, "it"], [272, 60, 256, 64], [272, 62, 256, 66, "i"], [272, 63, 256, 67], [272, 68, 256, 72, "a"], [272, 69, 256, 73], [272, 70, 256, 74, "i"], [272, 71, 256, 75], [272, 72, 256, 76], [272, 77, 256, 81, "it"], [272, 79, 256, 83], [272, 80, 256, 84], [272, 83, 256, 87, "a"], [272, 84, 256, 88], [272, 85, 256, 89, "every"], [272, 90, 256, 94], [272, 91, 256, 95], [272, 92, 256, 96, "it"], [272, 94, 256, 98], [272, 96, 256, 100, "i"], [272, 97, 256, 101], [272, 102, 256, 106, "b"], [272, 103, 256, 107], [272, 104, 256, 108, "i"], [272, 105, 256, 109], [272, 106, 256, 110], [272, 111, 256, 115, "it"], [272, 113, 256, 117], [272, 114, 256, 118], [273, 8, 257, 12], [273, 12, 257, 16], [273, 13, 257, 17, "intersects"], [273, 23, 257, 27], [273, 25, 257, 29], [274, 10, 258, 16], [274, 16, 258, 22], [274, 20, 258, 26, "Error"], [274, 25, 258, 31], [274, 26, 258, 32], [274, 91, 258, 97, "config"], [274, 97, 258, 103], [274, 98, 258, 104, "pattern"], [274, 105, 258, 111], [274, 128, 258, 134, "a"], [274, 129, 258, 135], [274, 130, 258, 136, "join"], [274, 134, 258, 140], [274, 135, 258, 141], [274, 140, 258, 146], [274, 141, 258, 147], [274, 151, 258, 157, "b"], [274, 152, 258, 158], [274, 153, 258, 159, "join"], [274, 157, 258, 163], [274, 158, 258, 164], [274, 163, 258, 169], [274, 164, 258, 170], [274, 236, 258, 242], [274, 237, 258, 243], [275, 8, 259, 12], [276, 6, 260, 8], [277, 6, 261, 8], [277, 13, 261, 15, "Object"], [277, 19, 261, 21], [277, 20, 261, 22, "assign"], [277, 26, 261, 28], [277, 27, 261, 29, "acc"], [277, 30, 261, 32], [277, 32, 261, 34], [278, 8, 262, 12], [278, 9, 262, 13, "config"], [278, 15, 262, 19], [278, 16, 262, 20, "pattern"], [278, 23, 262, 27], [278, 26, 262, 30, "config"], [279, 6, 263, 8], [279, 7, 263, 9], [279, 8, 263, 10], [280, 4, 264, 4], [280, 5, 264, 5], [280, 7, 264, 7], [280, 8, 264, 8], [280, 9, 264, 9], [280, 10, 264, 10], [281, 2, 265, 0], [282, 2, 266, 0], [282, 11, 266, 9, "getConfigsWithRegexes"], [282, 32, 266, 30, "getConfigsWithRegexes"], [282, 33, 266, 31, "configs"], [282, 40, 266, 38], [282, 42, 266, 40], [283, 4, 267, 4], [283, 11, 267, 11, "configs"], [283, 18, 267, 18], [283, 19, 267, 19, "map"], [283, 22, 267, 22], [283, 23, 267, 24, "c"], [283, 24, 267, 25], [283, 29, 267, 31], [284, 6, 268, 8], [284, 9, 268, 11, "c"], [284, 10, 268, 12], [285, 6, 269, 8], [286, 6, 270, 8], [287, 6, 271, 8], [288, 6, 272, 8, "regex"], [288, 11, 272, 13], [288, 13, 272, 15, "expo"], [288, 17, 272, 19], [288, 18, 272, 20, "configRegExp"], [288, 30, 272, 32], [288, 31, 272, 33, "c"], [288, 32, 272, 34], [289, 6, 273, 8], [290, 4, 274, 4], [290, 5, 274, 5], [290, 6, 274, 6], [290, 7, 274, 7], [291, 2, 275, 0], [292, 2, 276, 0], [292, 8, 276, 6, "joinPaths"], [292, 17, 276, 15], [292, 20, 276, 18, "joinPaths"], [292, 21, 276, 19], [292, 24, 276, 22, "paths"], [292, 29, 276, 27], [292, 34, 276, 32], [292, 36, 276, 34], [292, 37, 277, 5, "concat"], [292, 43, 277, 11], [292, 44, 277, 12], [292, 47, 277, 15, "paths"], [292, 52, 277, 20], [292, 53, 277, 21, "map"], [292, 56, 277, 24], [292, 57, 277, 26, "p"], [292, 58, 277, 27], [292, 62, 277, 32, "p"], [292, 63, 277, 33], [292, 64, 277, 34, "split"], [292, 69, 277, 39], [292, 70, 277, 40], [292, 73, 277, 43], [292, 74, 277, 44], [292, 75, 277, 45], [292, 76, 277, 46], [292, 77, 278, 5, "filter"], [292, 83, 278, 11], [292, 84, 278, 12, "Boolean"], [292, 91, 278, 19], [292, 92, 278, 20], [292, 93, 279, 5, "join"], [292, 97, 279, 9], [292, 98, 279, 10], [292, 101, 279, 13], [292, 102, 279, 14], [293, 2, 280, 0], [293, 8, 280, 6, "matchAgainstConfigs"], [293, 27, 280, 25], [293, 30, 280, 28, "matchAgainstConfigs"], [293, 31, 280, 29, "remaining"], [293, 40, 280, 38], [293, 42, 280, 40, "configs"], [293, 49, 280, 47], [293, 54, 280, 52], [294, 4, 281, 4], [294, 8, 281, 8, "routes"], [294, 14, 281, 14], [295, 4, 282, 4], [295, 8, 282, 8, "remainingPath"], [295, 21, 282, 21], [295, 24, 282, 24, "remaining"], [295, 33, 282, 33], [296, 4, 283, 4], [297, 4, 284, 4], [297, 10, 284, 10, "allParams"], [297, 19, 284, 19], [297, 22, 284, 22, "Object"], [297, 28, 284, 28], [297, 29, 284, 29, "create"], [297, 35, 284, 35], [297, 36, 284, 36], [297, 40, 284, 40], [297, 41, 284, 41], [298, 4, 285, 4], [299, 4, 286, 4], [300, 4, 287, 4], [300, 9, 287, 9], [300, 15, 287, 15, "config"], [300, 21, 287, 21], [300, 25, 287, 25, "configs"], [300, 32, 287, 32], [300, 34, 287, 34], [301, 6, 288, 8], [301, 10, 288, 12], [301, 11, 288, 13, "config"], [301, 17, 288, 19], [301, 18, 288, 20, "regex"], [301, 23, 288, 25], [301, 25, 288, 27], [302, 8, 289, 12], [303, 6, 290, 8], [304, 6, 291, 8], [304, 12, 291, 14, "match"], [304, 17, 291, 19], [304, 20, 291, 22, "remainingPath"], [304, 33, 291, 35], [304, 34, 291, 36, "match"], [304, 39, 291, 41], [304, 40, 291, 42, "config"], [304, 46, 291, 48], [304, 47, 291, 49, "regex"], [304, 52, 291, 54], [304, 53, 291, 55], [305, 6, 292, 8], [306, 6, 293, 8], [306, 10, 293, 12, "match"], [306, 15, 293, 17], [306, 17, 293, 19], [307, 8, 294, 12], [307, 14, 294, 18, "matchResult"], [307, 25, 294, 29], [307, 28, 294, 32, "config"], [307, 34, 294, 38], [307, 35, 294, 39, "pattern"], [307, 42, 294, 46], [307, 44, 294, 48, "split"], [307, 49, 294, 53], [307, 50, 294, 54], [307, 53, 294, 57], [307, 54, 294, 58], [307, 55, 294, 59, "reduce"], [307, 61, 294, 65], [307, 62, 294, 66], [307, 63, 294, 67, "acc"], [307, 66, 294, 70], [307, 68, 294, 72, "p"], [307, 69, 294, 73], [307, 71, 294, 75, "index"], [307, 76, 294, 80], [307, 81, 294, 85], [308, 10, 295, 16], [308, 14, 295, 20], [308, 15, 295, 21, "expo"], [308, 19, 295, 25], [308, 20, 295, 26, "isDynamicPart"], [308, 33, 295, 39], [308, 34, 295, 40, "p"], [308, 35, 295, 41], [308, 36, 295, 42], [308, 38, 295, 44], [309, 12, 296, 20], [309, 19, 296, 27, "acc"], [309, 22, 296, 30], [310, 10, 297, 16], [311, 10, 298, 16, "acc"], [311, 13, 298, 19], [311, 14, 298, 20, "pos"], [311, 17, 298, 23], [311, 21, 298, 27], [311, 22, 298, 28], [312, 10, 299, 16], [313, 10, 300, 16], [313, 16, 300, 22, "decodedParamSegment"], [313, 35, 300, 41], [313, 38, 300, 44, "expo"], [313, 42, 300, 48], [313, 43, 300, 49, "safelyDecodeURIComponent"], [313, 67, 300, 73], [314, 10, 301, 16], [315, 10, 302, 16], [316, 10, 303, 16, "match"], [316, 15, 303, 21], [316, 16, 303, 22], [316, 17, 303, 23, "acc"], [316, 20, 303, 26], [316, 21, 303, 27, "pos"], [316, 24, 303, 30], [316, 27, 303, 33], [316, 28, 303, 34], [316, 32, 303, 38], [316, 33, 303, 39], [317, 10, 304, 20], [318, 10, 304, 20], [318, 11, 305, 21, "replace"], [318, 18, 305, 28], [318, 19, 305, 29], [318, 24, 305, 34], [318, 26, 305, 36], [318, 28, 305, 38], [318, 29, 305, 39], [318, 30, 305, 40], [319, 10, 306, 16], [320, 10, 307, 16, "Object"], [320, 16, 307, 22], [320, 17, 307, 23, "assign"], [320, 23, 307, 29], [320, 24, 307, 30, "acc"], [320, 27, 307, 33], [320, 28, 307, 34, "matchedParams"], [320, 41, 307, 47], [320, 43, 307, 49], [321, 12, 308, 20], [321, 13, 308, 21, "p"], [321, 14, 308, 22], [321, 17, 308, 25, "Object"], [321, 23, 308, 31], [321, 24, 308, 32, "assign"], [321, 30, 308, 38], [321, 31, 308, 39, "acc"], [321, 34, 308, 42], [321, 35, 308, 43, "matchedParams"], [321, 48, 308, 56], [321, 49, 308, 57, "p"], [321, 50, 308, 58], [321, 51, 308, 59], [321, 55, 308, 63], [321, 56, 308, 64], [321, 57, 308, 65], [321, 59, 308, 67], [322, 14, 309, 24], [322, 15, 309, 25, "index"], [322, 20, 309, 30], [322, 23, 309, 33, "decodedParamSegment"], [323, 12, 310, 20], [323, 13, 310, 21], [324, 10, 311, 16], [324, 11, 311, 17], [324, 12, 311, 18], [325, 10, 312, 16], [325, 17, 312, 23, "acc"], [325, 20, 312, 26], [326, 8, 313, 12], [326, 9, 313, 13], [326, 11, 313, 15], [327, 10, 313, 17, "pos"], [327, 13, 313, 20], [327, 15, 313, 22], [327, 16, 313, 23], [327, 17, 313, 24], [328, 10, 313, 26, "matchedParams"], [328, 23, 313, 39], [328, 25, 313, 41], [328, 26, 313, 42], [329, 8, 313, 44], [329, 9, 313, 45], [329, 10, 313, 46], [330, 8, 314, 12], [330, 14, 314, 18, "matchedParams"], [330, 27, 314, 31], [330, 30, 314, 34, "matchResult"], [330, 41, 314, 45], [330, 42, 314, 46, "matchedParams"], [330, 55, 314, 59], [330, 59, 314, 63], [330, 60, 314, 64], [330, 61, 314, 65], [331, 8, 315, 12, "routes"], [331, 14, 315, 18], [331, 17, 315, 21, "config"], [331, 23, 315, 27], [331, 24, 315, 28, "routeNames"], [331, 34, 315, 38], [331, 35, 315, 39, "map"], [331, 38, 315, 42], [331, 39, 315, 44, "name"], [331, 43, 315, 48], [331, 47, 315, 53], [332, 10, 316, 16], [332, 16, 316, 22, "routeConfig"], [332, 27, 316, 33], [332, 30, 316, 36, "configs"], [332, 37, 316, 43], [332, 38, 316, 44, "find"], [332, 42, 316, 48], [332, 43, 316, 50, "c"], [332, 44, 316, 51], [332, 48, 316, 56], [333, 12, 317, 20], [334, 12, 318, 20], [334, 19, 318, 27, "c"], [334, 20, 318, 28], [334, 21, 318, 29, "screen"], [334, 27, 318, 35], [334, 32, 318, 40, "name"], [334, 36, 318, 44], [334, 40, 318, 48, "config"], [334, 46, 318, 54], [334, 47, 318, 55, "pattern"], [334, 54, 318, 62], [334, 55, 318, 63, "startsWith"], [334, 65, 318, 73], [334, 66, 318, 74, "c"], [334, 67, 318, 75], [334, 68, 318, 76, "pattern"], [334, 75, 318, 83], [334, 76, 318, 84], [335, 10, 319, 16], [335, 11, 319, 17], [335, 12, 319, 18], [336, 10, 320, 16], [337, 10, 321, 16], [337, 16, 321, 22, "normalizedPath"], [337, 30, 321, 36], [337, 33, 321, 39, "routeConfig"], [337, 44, 321, 50], [337, 46, 321, 52, "path"], [337, 50, 321, 56], [337, 51, 321, 57, "split"], [337, 56, 321, 62], [337, 57, 321, 63], [337, 60, 321, 66], [337, 61, 321, 67], [337, 62, 321, 68, "filter"], [337, 68, 321, 74], [337, 69, 321, 75, "Boolean"], [337, 76, 321, 82], [337, 77, 321, 83], [337, 78, 321, 84, "join"], [337, 82, 321, 88], [337, 83, 321, 89], [337, 86, 321, 92], [337, 87, 321, 93], [338, 10, 322, 16], [339, 10, 323, 16], [339, 16, 323, 22, "numInitialSegments"], [339, 34, 323, 40], [339, 37, 323, 43, "routeConfig"], [339, 48, 323, 54], [339, 50, 323, 56, "pattern"], [340, 10, 324, 20], [341, 10, 324, 20], [341, 11, 325, 21, "replace"], [341, 18, 325, 28], [341, 19, 325, 29], [341, 23, 325, 33, "RegExp"], [341, 29, 325, 39], [341, 30, 325, 40], [341, 33, 325, 43], [341, 34, 325, 44], [341, 35, 325, 45], [341, 37, 325, 47, "escape_string_regexp_1"], [341, 59, 325, 69], [341, 60, 325, 70, "default"], [341, 67, 325, 77], [341, 69, 325, 79, "normalizedPath"], [341, 83, 325, 93], [341, 84, 325, 94], [341, 87, 325, 97], [341, 88, 325, 98], [341, 90, 325, 100], [341, 92, 325, 102], [341, 93, 325, 103], [341, 95, 326, 22, "split"], [341, 100, 326, 27], [341, 101, 326, 28], [341, 104, 326, 31], [341, 105, 326, 32], [341, 106, 326, 33, "length"], [341, 112, 326, 39], [342, 10, 327, 16], [342, 16, 327, 22, "params"], [342, 22, 327, 28], [342, 25, 327, 31, "normalizedPath"], [342, 39, 327, 45], [342, 41, 328, 22, "split"], [342, 46, 328, 27], [342, 47, 328, 28], [342, 50, 328, 31], [342, 51, 328, 32], [342, 52, 329, 21, "reduce"], [342, 58, 329, 27], [342, 59, 329, 28], [342, 60, 329, 29, "acc"], [342, 63, 329, 32], [342, 65, 329, 34, "p"], [342, 66, 329, 35], [342, 68, 329, 37, "index"], [342, 73, 329, 42], [342, 78, 329, 47], [343, 12, 330, 20], [343, 16, 330, 24], [343, 17, 330, 25, "expo"], [343, 21, 330, 29], [343, 22, 330, 30, "isDynamicPart"], [343, 35, 330, 43], [343, 36, 330, 44, "p"], [343, 37, 330, 45], [343, 38, 330, 46], [343, 40, 330, 48], [344, 14, 331, 24], [344, 21, 331, 31, "acc"], [344, 24, 331, 34], [345, 12, 332, 20], [346, 12, 333, 20], [347, 12, 334, 20], [348, 12, 335, 20], [348, 18, 335, 26, "offset"], [348, 24, 335, 32], [348, 27, 335, 35, "numInitialSegments"], [348, 45, 335, 53], [348, 48, 335, 56, "numInitialSegments"], [348, 66, 335, 74], [348, 69, 335, 77], [348, 70, 335, 78], [348, 73, 335, 81], [348, 74, 335, 82], [349, 12, 336, 20], [350, 12, 337, 20], [351, 12, 338, 20], [351, 18, 338, 26, "value"], [351, 23, 338, 31], [351, 26, 338, 34, "expo"], [351, 30, 338, 38], [351, 31, 338, 39, "getParamValue"], [351, 44, 338, 52], [351, 45, 338, 53, "p"], [351, 46, 338, 54], [351, 48, 338, 56, "matchedParams"], [351, 61, 338, 69], [351, 62, 338, 70, "p"], [351, 63, 338, 71], [351, 64, 338, 72], [351, 67, 338, 75, "index"], [351, 72, 338, 80], [351, 75, 338, 83, "offset"], [351, 81, 338, 89], [351, 82, 338, 90], [351, 83, 338, 91], [352, 12, 339, 20], [353, 12, 340, 20], [353, 16, 340, 24, "value"], [353, 21, 340, 29], [353, 23, 340, 31], [354, 14, 341, 24], [355, 14, 342, 24], [356, 14, 343, 24], [356, 20, 343, 30, "key"], [356, 23, 343, 33], [356, 26, 343, 36, "expo"], [356, 30, 343, 40], [356, 31, 343, 41, "<PERSON><PERSON><PERSON>"], [356, 42, 343, 52], [356, 43, 343, 53, "p"], [356, 44, 343, 54], [356, 45, 343, 55], [357, 14, 344, 24], [358, 14, 345, 24, "acc"], [358, 17, 345, 27], [358, 18, 345, 28, "key"], [358, 21, 345, 31], [358, 22, 345, 32], [358, 25, 345, 35, "routeConfig"], [358, 36, 345, 46], [358, 38, 345, 48, "parse"], [358, 43, 345, 53], [358, 46, 345, 56, "key"], [358, 49, 345, 59], [358, 50, 345, 60], [358, 53, 345, 63, "routeConfig"], [358, 64, 345, 74], [358, 65, 345, 75, "parse"], [358, 70, 345, 80], [358, 71, 345, 81, "key"], [358, 74, 345, 84], [358, 75, 345, 85], [358, 76, 345, 86, "value"], [358, 81, 345, 91], [358, 82, 345, 92], [358, 85, 345, 95, "value"], [358, 90, 345, 100], [359, 12, 346, 20], [360, 12, 347, 20], [360, 19, 347, 27, "acc"], [360, 22, 347, 30], [361, 10, 348, 16], [361, 11, 348, 17], [361, 13, 348, 19], [361, 14, 348, 20], [361, 15, 348, 21], [361, 16, 348, 22], [362, 10, 349, 16], [362, 14, 349, 20, "params"], [362, 20, 349, 26], [362, 24, 349, 30, "Object"], [362, 30, 349, 36], [362, 31, 349, 37, "keys"], [362, 35, 349, 41], [362, 36, 349, 42, "params"], [362, 42, 349, 48], [362, 43, 349, 49], [362, 44, 349, 50, "length"], [362, 50, 349, 56], [362, 52, 349, 58], [363, 12, 350, 20, "Object"], [363, 18, 350, 26], [363, 19, 350, 27, "assign"], [363, 25, 350, 33], [363, 26, 350, 34, "allParams"], [363, 35, 350, 43], [363, 37, 350, 45, "params"], [363, 43, 350, 51], [363, 44, 350, 52], [364, 12, 351, 20], [364, 19, 351, 27], [365, 14, 351, 29, "name"], [365, 18, 351, 33], [366, 14, 351, 35, "params"], [367, 12, 351, 42], [367, 13, 351, 43], [368, 10, 352, 16], [369, 10, 353, 16], [369, 17, 353, 23], [370, 12, 353, 25, "name"], [371, 10, 353, 30], [371, 11, 353, 31], [372, 8, 354, 12], [372, 9, 354, 13], [372, 10, 354, 14], [373, 8, 355, 12, "remainingPath"], [373, 21, 355, 25], [373, 24, 355, 28, "remainingPath"], [373, 37, 355, 41], [373, 38, 355, 42, "replace"], [373, 45, 355, 49], [373, 46, 355, 50, "match"], [373, 51, 355, 55], [373, 52, 355, 56], [373, 53, 355, 57], [373, 54, 355, 58], [373, 56, 355, 60], [373, 58, 355, 62], [373, 59, 355, 63], [374, 8, 356, 12], [375, 6, 357, 8], [376, 4, 358, 4], [377, 4, 359, 4], [378, 4, 360, 4, "expo"], [378, 8, 360, 8], [378, 9, 360, 9, "populateParams"], [378, 23, 360, 23], [378, 24, 360, 24, "routes"], [378, 30, 360, 30], [378, 32, 360, 32, "allParams"], [378, 41, 360, 41], [378, 42, 360, 42], [379, 4, 361, 4], [380, 4, 362, 4], [380, 11, 362, 11], [381, 6, 362, 13, "routes"], [381, 12, 362, 19], [382, 6, 362, 21, "remainingPath"], [383, 4, 362, 35], [383, 5, 362, 36], [384, 2, 363, 0], [384, 3, 363, 1], [385, 2, 364, 0], [385, 8, 364, 6, "createNormalizedConfigs"], [385, 31, 364, 29], [385, 34, 364, 32, "createNormalizedConfigs"], [385, 35, 364, 33, "screen"], [385, 41, 364, 39], [385, 43, 364, 41, "routeConfig"], [385, 54, 364, 52], [385, 56, 364, 54, "routeNames"], [385, 66, 364, 64], [385, 69, 364, 67], [385, 71, 364, 69], [385, 73, 364, 71, "initials"], [385, 81, 364, 79], [385, 83, 364, 81, "parentScreens"], [385, 96, 364, 94], [385, 98, 364, 96, "parentPattern"], [385, 111, 364, 109], [385, 116, 364, 114], [386, 4, 365, 4], [386, 10, 365, 10, "configs"], [386, 17, 365, 17], [386, 20, 365, 20], [386, 22, 365, 22], [387, 4, 366, 4, "routeNames"], [387, 14, 366, 14], [387, 15, 366, 15, "push"], [387, 19, 366, 19], [387, 20, 366, 20, "screen"], [387, 26, 366, 26], [387, 27, 366, 27], [388, 4, 367, 4, "parentScreens"], [388, 17, 367, 17], [388, 18, 367, 18, "push"], [388, 22, 367, 22], [388, 23, 367, 23, "screen"], [388, 29, 367, 29], [388, 30, 367, 30], [389, 4, 368, 4], [390, 4, 369, 4], [390, 10, 369, 10, "config"], [390, 16, 369, 16], [390, 19, 369, 19, "routeConfig"], [390, 30, 369, 30], [390, 31, 369, 31, "screen"], [390, 37, 369, 37], [390, 38, 369, 38], [391, 4, 370, 4], [391, 8, 370, 8], [391, 15, 370, 15, "config"], [391, 21, 370, 21], [391, 26, 370, 26], [391, 34, 370, 34], [391, 36, 370, 36], [392, 6, 371, 8], [393, 6, 372, 8], [393, 12, 372, 14, "pattern"], [393, 19, 372, 21], [393, 22, 372, 24, "parentPattern"], [393, 35, 372, 37], [393, 38, 372, 40, "joinPaths"], [393, 47, 372, 49], [393, 48, 372, 50, "parentPattern"], [393, 61, 372, 63], [393, 63, 372, 65, "config"], [393, 69, 372, 71], [393, 70, 372, 72], [393, 73, 372, 75, "config"], [393, 79, 372, 81], [394, 6, 373, 8, "configs"], [394, 13, 373, 15], [394, 14, 373, 16, "push"], [394, 18, 373, 20], [394, 19, 373, 21, "createConfigItem"], [394, 35, 373, 37], [394, 36, 373, 38, "screen"], [394, 42, 373, 44], [394, 44, 373, 46, "routeNames"], [394, 54, 373, 56], [394, 56, 373, 58, "pattern"], [394, 63, 373, 65], [394, 65, 373, 67, "config"], [394, 71, 373, 73], [394, 72, 373, 74], [394, 73, 373, 75], [395, 4, 374, 4], [395, 5, 374, 5], [395, 11, 375, 9], [395, 15, 375, 13], [395, 22, 375, 20, "config"], [395, 28, 375, 26], [395, 33, 375, 31], [395, 41, 375, 39], [395, 43, 375, 41], [396, 6, 376, 8], [396, 10, 376, 12, "pattern"], [396, 17, 376, 19], [397, 6, 377, 8], [398, 6, 378, 8], [399, 6, 379, 8], [400, 6, 380, 8], [400, 10, 380, 12], [400, 17, 380, 19, "config"], [400, 23, 380, 25], [400, 24, 380, 26, "path"], [400, 28, 380, 30], [400, 33, 380, 35], [400, 41, 380, 43], [400, 43, 380, 45], [401, 8, 381, 12], [401, 12, 381, 16, "config"], [401, 18, 381, 22], [401, 19, 381, 23, "exact"], [401, 24, 381, 28], [401, 28, 381, 32, "config"], [401, 34, 381, 38], [401, 35, 381, 39, "path"], [401, 39, 381, 43], [401, 44, 381, 48, "undefined"], [401, 53, 381, 57], [401, 55, 381, 59], [402, 10, 382, 16], [402, 16, 382, 22], [402, 20, 382, 26, "Error"], [402, 25, 382, 31], [402, 26, 382, 32], [402, 176, 382, 182], [402, 177, 382, 183], [403, 8, 383, 12], [404, 8, 384, 12, "pattern"], [404, 15, 384, 19], [404, 18, 385, 16, "config"], [404, 24, 385, 22], [404, 25, 385, 23, "exact"], [404, 30, 385, 28], [404, 35, 385, 33], [404, 39, 385, 37], [404, 42, 386, 22, "joinPaths"], [404, 51, 386, 31], [404, 52, 386, 32, "parentPattern"], [404, 65, 386, 45], [404, 69, 386, 49], [404, 71, 386, 51], [404, 73, 386, 53, "config"], [404, 79, 386, 59], [404, 80, 386, 60, "path"], [404, 84, 386, 64], [404, 88, 386, 68], [404, 90, 386, 70], [404, 91, 386, 71], [404, 94, 387, 22, "config"], [404, 100, 387, 28], [404, 101, 387, 29, "path"], [404, 105, 387, 33], [404, 109, 387, 37], [404, 111, 387, 39], [405, 8, 388, 12], [405, 12, 388, 16, "screen"], [405, 18, 388, 22], [405, 23, 388, 27, "constants_1"], [405, 34, 388, 38], [405, 35, 388, 39, "INTERNAL_SLOT_NAME"], [405, 53, 388, 57], [405, 55, 388, 59], [406, 10, 389, 16, "configs"], [406, 17, 389, 23], [406, 18, 389, 24, "push"], [406, 22, 389, 28], [406, 23, 389, 29, "createConfigItem"], [406, 39, 389, 45], [406, 40, 389, 46, "screen"], [406, 46, 389, 52], [406, 48, 389, 54, "routeNames"], [406, 58, 389, 64], [406, 60, 389, 66, "pattern"], [406, 67, 389, 73], [406, 69, 389, 75, "config"], [406, 75, 389, 81], [406, 76, 389, 82, "path"], [406, 80, 389, 86], [406, 82, 389, 88, "config"], [406, 88, 389, 94], [406, 89, 389, 95, "parse"], [406, 94, 389, 100], [406, 96, 389, 102, "config"], [406, 102, 389, 108], [406, 103, 389, 109], [406, 104, 389, 110], [407, 8, 390, 12], [408, 6, 391, 8], [409, 6, 392, 8], [409, 10, 392, 12, "config"], [409, 16, 392, 18], [409, 17, 392, 19, "screens"], [409, 24, 392, 26], [409, 26, 392, 28], [410, 8, 393, 12], [411, 8, 394, 12], [411, 12, 394, 16, "config"], [411, 18, 394, 22], [411, 19, 394, 23, "initialRouteName"], [411, 35, 394, 39], [411, 37, 394, 41], [412, 10, 395, 16, "initials"], [412, 18, 395, 24], [412, 19, 395, 25, "push"], [412, 23, 395, 29], [412, 24, 395, 30], [413, 12, 396, 20, "initialRouteName"], [413, 28, 396, 36], [413, 30, 396, 38, "config"], [413, 36, 396, 44], [413, 37, 396, 45, "initialRouteName"], [413, 53, 396, 61], [414, 12, 397, 20, "parentScreens"], [415, 10, 398, 16], [415, 11, 398, 17], [415, 12, 398, 18], [416, 8, 399, 12], [417, 8, 400, 12, "Object"], [417, 14, 400, 18], [417, 15, 400, 19, "keys"], [417, 19, 400, 23], [417, 20, 400, 24, "config"], [417, 26, 400, 30], [417, 27, 400, 31, "screens"], [417, 34, 400, 38], [417, 35, 400, 39], [417, 36, 400, 40, "for<PERSON>ach"], [417, 43, 400, 47], [417, 44, 400, 49, "nestedConfig"], [417, 56, 400, 61], [417, 60, 400, 66], [418, 10, 401, 16], [418, 16, 401, 22, "result"], [418, 22, 401, 28], [418, 25, 401, 31, "createNormalizedConfigs"], [418, 48, 401, 54], [418, 49, 401, 55, "nestedConfig"], [418, 61, 401, 67], [418, 63, 401, 69, "config"], [418, 69, 401, 75], [418, 70, 401, 76, "screens"], [418, 77, 401, 83], [418, 79, 401, 85, "routeNames"], [418, 89, 401, 95], [418, 91, 401, 97, "initials"], [418, 99, 401, 105], [418, 101, 401, 107], [418, 102, 401, 108], [418, 105, 401, 111, "parentScreens"], [418, 118, 401, 124], [418, 119, 401, 125], [418, 121, 401, 127, "pattern"], [418, 128, 401, 134], [418, 132, 401, 138, "parentPattern"], [418, 145, 401, 151], [418, 146, 401, 152], [419, 10, 402, 16, "configs"], [419, 17, 402, 23], [419, 18, 402, 24, "push"], [419, 22, 402, 28], [419, 23, 402, 29], [419, 26, 402, 32, "result"], [419, 32, 402, 38], [419, 33, 402, 39], [420, 8, 403, 12], [420, 9, 403, 13], [420, 10, 403, 14], [421, 6, 404, 8], [422, 4, 405, 4], [423, 4, 406, 4, "routeNames"], [423, 14, 406, 14], [423, 15, 406, 15, "pop"], [423, 18, 406, 18], [423, 19, 406, 19], [423, 20, 406, 20], [424, 4, 407, 4], [424, 11, 407, 11, "configs"], [424, 18, 407, 18], [425, 2, 408, 0], [425, 3, 408, 1], [426, 2, 409, 0], [426, 8, 409, 6, "createConfigItem"], [426, 24, 409, 22], [426, 27, 409, 25, "createConfigItem"], [426, 28, 409, 26, "screen"], [426, 34, 409, 32], [426, 36, 409, 34, "routeNames"], [426, 46, 409, 44], [426, 48, 409, 46, "pattern"], [426, 55, 409, 53], [426, 57, 409, 55, "path"], [426, 61, 409, 59], [426, 63, 409, 61, "parse"], [426, 68, 409, 66], [426, 71, 409, 69, "undefined"], [426, 80, 409, 78], [426, 82, 409, 80, "config"], [426, 88, 409, 86], [426, 91, 409, 89], [426, 92, 409, 90], [426, 93, 409, 91], [426, 98, 409, 96], [427, 4, 410, 4], [428, 4, 411, 4, "pattern"], [428, 11, 411, 11], [428, 14, 411, 14, "pattern"], [428, 21, 411, 21], [428, 22, 411, 22, "split"], [428, 27, 411, 27], [428, 28, 411, 28], [428, 31, 411, 31], [428, 32, 411, 32], [428, 33, 411, 33, "filter"], [428, 39, 411, 39], [428, 40, 411, 40, "Boolean"], [428, 47, 411, 47], [428, 48, 411, 48], [428, 49, 411, 49, "join"], [428, 53, 411, 53], [428, 54, 411, 54], [428, 57, 411, 57], [428, 58, 411, 58], [429, 4, 412, 4], [430, 4, 413, 4], [430, 10, 413, 10, "regex"], [430, 15, 413, 15], [430, 18, 413, 18, "pattern"], [430, 25, 413, 25], [430, 28, 413, 28, "expo"], [430, 32, 413, 32], [430, 33, 413, 33, "routePatternToRegex"], [430, 52, 413, 52], [430, 53, 413, 53, "pattern"], [430, 60, 413, 60], [430, 61, 413, 61], [430, 64, 413, 64, "undefined"], [430, 73, 413, 73], [431, 4, 414, 4], [432, 4, 415, 4], [433, 4, 416, 4], [434, 4, 417, 4], [435, 4, 418, 4], [436, 4, 419, 4], [437, 4, 420, 4], [438, 4, 421, 4], [439, 4, 422, 4], [440, 4, 423, 4], [441, 4, 424, 4], [442, 4, 425, 4], [443, 4, 426, 4], [444, 4, 427, 4], [445, 4, 428, 4], [445, 11, 428, 11], [446, 6, 429, 8, "screen"], [446, 12, 429, 14], [447, 6, 430, 8, "regex"], [447, 11, 430, 13], [448, 6, 431, 8, "pattern"], [448, 13, 431, 15], [449, 6, 432, 8, "path"], [449, 10, 432, 12], [450, 6, 433, 8], [451, 6, 434, 8, "routeNames"], [451, 16, 434, 18], [451, 18, 434, 20], [451, 19, 434, 21], [451, 22, 434, 24, "routeNames"], [451, 32, 434, 34], [451, 33, 434, 35], [452, 6, 435, 8, "parse"], [452, 11, 435, 13], [453, 6, 436, 8], [454, 6, 437, 8], [454, 9, 437, 11, "expo"], [454, 13, 437, 15], [454, 14, 437, 16, "createConfig"], [454, 26, 437, 28], [454, 27, 437, 29, "screen"], [454, 33, 437, 35], [454, 35, 437, 37, "pattern"], [454, 42, 437, 44], [454, 44, 437, 46, "routeNames"], [454, 54, 437, 56], [454, 56, 437, 58, "config"], [454, 62, 437, 64], [455, 6, 438, 8], [456, 4, 439, 4], [456, 5, 439, 5], [457, 2, 440, 0], [457, 3, 440, 1], [458, 2, 441, 0], [458, 8, 441, 6, "findParseConfigForRoute"], [458, 31, 441, 29], [458, 34, 441, 32, "findParseConfigForRoute"], [458, 35, 441, 33, "routeName"], [458, 44, 441, 42], [458, 46, 441, 44, "flatConfig"], [458, 56, 441, 54], [458, 61, 441, 59], [459, 4, 442, 4], [459, 9, 442, 9], [459, 15, 442, 15, "config"], [459, 21, 442, 21], [459, 25, 442, 25, "flatConfig"], [459, 35, 442, 35], [459, 37, 442, 37], [460, 6, 443, 8], [460, 10, 443, 12, "routeName"], [460, 19, 443, 21], [460, 24, 443, 26, "config"], [460, 30, 443, 32], [460, 31, 443, 33, "routeNames"], [460, 41, 443, 43], [460, 42, 443, 44, "config"], [460, 48, 443, 50], [460, 49, 443, 51, "routeNames"], [460, 59, 443, 61], [460, 60, 443, 62, "length"], [460, 66, 443, 68], [460, 69, 443, 71], [460, 70, 443, 72], [460, 71, 443, 73], [460, 73, 443, 75], [461, 8, 444, 12], [461, 15, 444, 19, "config"], [461, 21, 444, 25], [461, 22, 444, 26, "parse"], [461, 27, 444, 31], [462, 6, 445, 8], [463, 4, 446, 4], [464, 4, 447, 4], [464, 11, 447, 11, "undefined"], [464, 20, 447, 20], [465, 2, 448, 0], [465, 3, 448, 1], [466, 2, 449, 0], [467, 2, 450, 0], [467, 8, 450, 6, "findInitialRoute"], [467, 24, 450, 22], [467, 27, 450, 25, "findInitialRoute"], [467, 28, 450, 26, "routeName"], [467, 37, 450, 35], [467, 39, 450, 37, "parentScreens"], [467, 52, 450, 50], [467, 54, 450, 52, "initialRoutes"], [467, 67, 450, 65], [467, 72, 450, 70], [468, 4, 451, 4], [468, 9, 451, 9], [468, 15, 451, 15, "config"], [468, 21, 451, 21], [468, 25, 451, 25, "initialRoutes"], [468, 38, 451, 38], [468, 40, 451, 40], [469, 6, 452, 8], [469, 10, 452, 12, "parentScreens"], [469, 23, 452, 25], [469, 24, 452, 26, "length"], [469, 30, 452, 32], [469, 35, 452, 37, "config"], [469, 41, 452, 43], [469, 42, 452, 44, "parentScreens"], [469, 55, 452, 57], [469, 56, 452, 58, "length"], [469, 62, 452, 64], [469, 64, 452, 66], [470, 8, 453, 12], [470, 12, 453, 16, "sameParents"], [470, 23, 453, 27], [470, 26, 453, 30], [470, 30, 453, 34], [471, 8, 454, 12], [471, 13, 454, 17], [471, 17, 454, 21, "i"], [471, 18, 454, 22], [471, 21, 454, 25], [471, 22, 454, 26], [471, 24, 454, 28, "i"], [471, 25, 454, 29], [471, 28, 454, 32, "parentScreens"], [471, 41, 454, 45], [471, 42, 454, 46, "length"], [471, 48, 454, 52], [471, 50, 454, 54, "i"], [471, 51, 454, 55], [471, 53, 454, 57], [471, 55, 454, 59], [472, 10, 455, 16], [472, 14, 455, 20, "parentScreens"], [472, 27, 455, 33], [472, 28, 455, 34, "i"], [472, 29, 455, 35], [472, 30, 455, 36], [472, 31, 455, 37, "localeCompare"], [472, 44, 455, 50], [472, 45, 455, 51, "config"], [472, 51, 455, 57], [472, 52, 455, 58, "parentScreens"], [472, 65, 455, 71], [472, 66, 455, 72, "i"], [472, 67, 455, 73], [472, 68, 455, 74], [472, 69, 455, 75], [472, 74, 455, 80], [472, 75, 455, 81], [472, 77, 455, 83], [473, 12, 456, 20, "sameParents"], [473, 23, 456, 31], [473, 26, 456, 34], [473, 31, 456, 39], [474, 12, 457, 20], [475, 10, 458, 16], [476, 8, 459, 12], [477, 8, 460, 12], [477, 12, 460, 16, "sameParents"], [477, 23, 460, 27], [477, 25, 460, 29], [478, 10, 461, 16], [478, 17, 461, 23, "routeName"], [478, 26, 461, 32], [478, 31, 461, 37, "config"], [478, 37, 461, 43], [478, 38, 461, 44, "initialRouteName"], [478, 54, 461, 60], [478, 57, 461, 63, "config"], [478, 63, 461, 69], [478, 64, 461, 70, "initialRouteName"], [478, 80, 461, 86], [478, 83, 461, 89, "undefined"], [478, 92, 461, 98], [479, 8, 462, 12], [480, 6, 463, 8], [481, 4, 464, 4], [482, 4, 465, 4], [482, 11, 465, 11, "undefined"], [482, 20, 465, 20], [483, 2, 466, 0], [483, 3, 466, 1], [484, 2, 467, 0], [485, 2, 468, 0], [486, 2, 469, 0], [486, 8, 469, 6, "createStateObject"], [486, 25, 469, 23], [486, 28, 469, 26, "createStateObject"], [486, 29, 469, 27, "initialRoute"], [486, 41, 469, 39], [486, 43, 469, 41, "route"], [486, 48, 469, 46], [486, 50, 469, 48, "isEmpty"], [486, 57, 469, 55], [486, 62, 469, 60], [487, 4, 470, 4], [487, 8, 470, 8, "isEmpty"], [487, 15, 470, 15], [487, 17, 470, 17], [488, 6, 471, 8], [488, 10, 471, 12, "initialRoute"], [488, 22, 471, 24], [488, 24, 471, 26], [489, 8, 472, 12], [489, 15, 472, 19], [490, 10, 473, 16, "index"], [490, 15, 473, 21], [490, 17, 473, 23], [490, 18, 473, 24], [491, 10, 474, 16, "routes"], [491, 16, 474, 22], [491, 18, 474, 24], [491, 19, 474, 25], [492, 12, 474, 27, "name"], [492, 16, 474, 31], [492, 18, 474, 33, "initialRoute"], [492, 30, 474, 45], [493, 12, 474, 47, "params"], [493, 18, 474, 53], [493, 20, 474, 55, "route"], [493, 25, 474, 60], [493, 26, 474, 61, "params"], [494, 10, 474, 68], [494, 11, 474, 69], [494, 13, 474, 71, "route"], [494, 18, 474, 76], [495, 8, 475, 12], [495, 9, 475, 13], [496, 6, 476, 8], [496, 7, 476, 9], [496, 13, 477, 13], [497, 8, 478, 12], [497, 15, 478, 19], [498, 10, 479, 16, "routes"], [498, 16, 479, 22], [498, 18, 479, 24], [498, 19, 479, 25, "route"], [498, 24, 479, 30], [499, 8, 480, 12], [499, 9, 480, 13], [500, 6, 481, 8], [501, 4, 482, 4], [501, 5, 482, 5], [501, 11, 483, 9], [502, 6, 484, 8], [502, 10, 484, 12, "initialRoute"], [502, 22, 484, 24], [502, 24, 484, 26], [503, 8, 485, 12], [503, 15, 485, 19], [504, 10, 486, 16, "index"], [504, 15, 486, 21], [504, 17, 486, 23], [504, 18, 486, 24], [505, 10, 487, 16, "routes"], [505, 16, 487, 22], [505, 18, 487, 24], [505, 19, 488, 20], [506, 12, 488, 22, "name"], [506, 16, 488, 26], [506, 18, 488, 28, "initialRoute"], [506, 30, 488, 40], [507, 12, 488, 42, "params"], [507, 18, 488, 48], [507, 20, 488, 50, "route"], [507, 25, 488, 55], [507, 26, 488, 56, "params"], [508, 10, 488, 63], [508, 11, 488, 64], [508, 13, 489, 20], [509, 12, 489, 22], [509, 15, 489, 25, "route"], [509, 20, 489, 30], [510, 12, 489, 32, "state"], [510, 17, 489, 37], [510, 19, 489, 39], [511, 14, 489, 41, "routes"], [511, 20, 489, 47], [511, 22, 489, 49], [512, 12, 489, 52], [513, 10, 489, 54], [513, 11, 489, 55], [514, 8, 491, 12], [514, 9, 491, 13], [515, 6, 492, 8], [515, 7, 492, 9], [515, 13, 493, 13], [516, 8, 494, 12], [516, 15, 494, 19], [517, 10, 495, 16, "routes"], [517, 16, 495, 22], [517, 18, 495, 24], [517, 19, 495, 25], [518, 12, 495, 27], [518, 15, 495, 30, "route"], [518, 20, 495, 35], [519, 12, 495, 37, "state"], [519, 17, 495, 42], [519, 19, 495, 44], [520, 14, 495, 46, "routes"], [520, 20, 495, 52], [520, 22, 495, 54], [521, 12, 495, 57], [522, 10, 495, 59], [522, 11, 495, 60], [523, 8, 496, 12], [523, 9, 496, 13], [524, 6, 497, 8], [525, 4, 498, 4], [526, 2, 499, 0], [526, 3, 499, 1], [527, 2, 500, 0], [527, 8, 500, 6, "createNestedStateObject"], [527, 31, 500, 29], [527, 34, 500, 32, "createNestedStateObject"], [527, 35, 500, 33], [528, 4, 500, 35, "path"], [528, 8, 500, 39], [529, 4, 500, 41], [529, 7, 500, 44, "expoURL"], [530, 2, 500, 52], [530, 3, 500, 53], [530, 5, 500, 55, "routes"], [530, 11, 500, 61], [530, 13, 500, 63, "initialRoutes"], [530, 26, 500, 76], [530, 28, 500, 78, "flatConfig"], [530, 38, 500, 88], [530, 40, 500, 90, "hash"], [530, 44, 500, 94], [530, 49, 500, 99], [531, 4, 501, 4], [531, 8, 501, 8, "route"], [531, 13, 501, 13], [531, 16, 501, 16, "routes"], [531, 22, 501, 22], [531, 23, 501, 23, "shift"], [531, 28, 501, 28], [531, 29, 501, 29], [531, 30, 501, 30], [532, 4, 502, 4], [532, 10, 502, 10, "parentScreens"], [532, 23, 502, 23], [532, 26, 502, 26], [532, 28, 502, 28], [533, 4, 503, 4], [533, 8, 503, 8, "initialRoute"], [533, 20, 503, 20], [533, 23, 503, 23, "findInitialRoute"], [533, 39, 503, 39], [533, 40, 503, 40, "route"], [533, 45, 503, 45], [533, 46, 503, 46, "name"], [533, 50, 503, 50], [533, 52, 503, 52, "parentScreens"], [533, 65, 503, 65], [533, 67, 503, 67, "initialRoutes"], [533, 80, 503, 80], [533, 81, 503, 81], [534, 4, 504, 4, "parentScreens"], [534, 17, 504, 17], [534, 18, 504, 18, "push"], [534, 22, 504, 22], [534, 23, 504, 23, "route"], [534, 28, 504, 28], [534, 29, 504, 29, "name"], [534, 33, 504, 33], [534, 34, 504, 34], [535, 4, 505, 4], [535, 10, 505, 10, "state"], [535, 15, 505, 15], [535, 18, 505, 18, "createStateObject"], [535, 35, 505, 35], [535, 36, 505, 36, "initialRoute"], [535, 48, 505, 48], [535, 50, 505, 50, "route"], [535, 55, 505, 55], [535, 57, 505, 57, "routes"], [535, 63, 505, 63], [535, 64, 505, 64, "length"], [535, 70, 505, 70], [535, 75, 505, 75], [535, 76, 505, 76], [535, 77, 505, 77], [536, 4, 506, 4], [536, 8, 506, 8, "routes"], [536, 14, 506, 14], [536, 15, 506, 15, "length"], [536, 21, 506, 21], [536, 24, 506, 24], [536, 25, 506, 25], [536, 27, 506, 27], [537, 6, 507, 8], [537, 10, 507, 12, "nestedState"], [537, 21, 507, 23], [537, 24, 507, 26, "state"], [537, 29, 507, 31], [538, 6, 508, 8], [538, 13, 508, 16, "route"], [538, 18, 508, 21], [538, 21, 508, 24, "routes"], [538, 27, 508, 30], [538, 28, 508, 31, "shift"], [538, 33, 508, 36], [538, 34, 508, 37], [538, 35, 508, 38], [538, 37, 508, 41], [539, 8, 509, 12, "initialRoute"], [539, 20, 509, 24], [539, 23, 509, 27, "findInitialRoute"], [539, 39, 509, 43], [539, 40, 509, 44, "route"], [539, 45, 509, 49], [539, 46, 509, 50, "name"], [539, 50, 509, 54], [539, 52, 509, 56, "parentScreens"], [539, 65, 509, 69], [539, 67, 509, 71, "initialRoutes"], [539, 80, 509, 84], [539, 81, 509, 85], [540, 8, 510, 12], [540, 14, 510, 18, "nestedStateIndex"], [540, 30, 510, 34], [540, 33, 510, 37, "nestedState"], [540, 44, 510, 48], [540, 45, 510, 49, "index"], [540, 50, 510, 54], [540, 54, 510, 58, "nestedState"], [540, 65, 510, 69], [540, 66, 510, 70, "routes"], [540, 72, 510, 76], [540, 73, 510, 77, "length"], [540, 79, 510, 83], [540, 82, 510, 86], [540, 83, 510, 87], [541, 8, 511, 12, "nestedState"], [541, 19, 511, 23], [541, 20, 511, 24, "routes"], [541, 26, 511, 30], [541, 27, 511, 31, "nestedStateIndex"], [541, 43, 511, 47], [541, 44, 511, 48], [541, 45, 511, 49, "state"], [541, 50, 511, 54], [541, 53, 511, 57, "createStateObject"], [541, 70, 511, 74], [541, 71, 511, 75, "initialRoute"], [541, 83, 511, 87], [541, 85, 511, 89, "route"], [541, 90, 511, 94], [541, 92, 511, 96, "routes"], [541, 98, 511, 102], [541, 99, 511, 103, "length"], [541, 105, 511, 109], [541, 110, 511, 114], [541, 111, 511, 115], [541, 112, 511, 116], [542, 8, 512, 12], [542, 12, 512, 16, "routes"], [542, 18, 512, 22], [542, 19, 512, 23, "length"], [542, 25, 512, 29], [542, 28, 512, 32], [542, 29, 512, 33], [542, 31, 512, 35], [543, 10, 513, 16, "nestedState"], [543, 21, 513, 27], [543, 24, 513, 30, "nestedState"], [543, 35, 513, 41], [543, 36, 513, 42, "routes"], [543, 42, 513, 48], [543, 43, 513, 49, "nestedStateIndex"], [543, 59, 513, 65], [543, 60, 513, 66], [543, 61, 513, 67, "state"], [543, 66, 513, 72], [544, 8, 514, 12], [545, 8, 515, 12, "parentScreens"], [545, 21, 515, 25], [545, 22, 515, 26, "push"], [545, 26, 515, 30], [545, 27, 515, 31, "route"], [545, 32, 515, 36], [545, 33, 515, 37, "name"], [545, 37, 515, 41], [545, 38, 515, 42], [546, 6, 516, 8], [547, 4, 517, 4], [548, 4, 518, 4, "route"], [548, 9, 518, 9], [548, 12, 518, 12], [548, 13, 518, 13], [548, 14, 518, 14], [548, 16, 518, 16, "findFocusedRoute_1"], [548, 34, 518, 34], [548, 35, 518, 35, "findFocusedRoute"], [548, 51, 518, 51], [548, 53, 518, 53, "state"], [548, 58, 518, 58], [548, 59, 518, 59], [549, 4, 519, 4], [550, 4, 520, 4, "route"], [550, 9, 520, 9], [550, 10, 520, 10, "path"], [550, 14, 520, 14], [550, 17, 520, 17, "expoURL"], [550, 24, 520, 24], [550, 25, 520, 25, "pathWithoutGroups"], [550, 42, 520, 42], [551, 4, 521, 4], [552, 4, 522, 4], [553, 4, 523, 4], [554, 4, 524, 4], [555, 4, 525, 4], [555, 10, 525, 10, "params"], [555, 16, 525, 16], [555, 19, 525, 19, "expo"], [555, 23, 525, 23], [555, 24, 525, 24, "parseQueryParams"], [555, 40, 525, 40], [555, 41, 525, 41, "path"], [555, 45, 525, 45], [555, 47, 525, 47, "route"], [555, 52, 525, 52], [555, 54, 525, 54, "flatConfig"], [555, 64, 525, 64], [555, 67, 525, 67, "findParseConfigForRoute"], [555, 90, 525, 90], [555, 91, 525, 91, "route"], [555, 96, 525, 96], [555, 97, 525, 97, "name"], [555, 101, 525, 101], [555, 103, 525, 103, "flatConfig"], [555, 113, 525, 113], [555, 114, 525, 114], [555, 117, 525, 117, "undefined"], [555, 126, 525, 126], [555, 128, 525, 128, "hash"], [555, 132, 525, 132], [555, 133, 525, 133], [556, 4, 526, 4], [557, 4, 527, 4], [558, 4, 528, 4], [559, 4, 529, 4], [559, 8, 529, 8, "params"], [559, 14, 529, 14], [559, 16, 529, 16], [560, 6, 530, 8, "route"], [560, 11, 530, 13], [560, 12, 530, 14, "params"], [560, 18, 530, 20], [560, 21, 530, 23], [561, 8, 530, 25], [561, 11, 530, 28, "route"], [561, 16, 530, 33], [561, 17, 530, 34, "params"], [561, 23, 530, 40], [562, 8, 530, 42], [562, 11, 530, 45, "params"], [563, 6, 530, 52], [563, 7, 530, 53], [564, 4, 531, 4], [565, 4, 532, 4], [566, 4, 533, 4], [566, 11, 533, 11, "state"], [566, 16, 533, 16], [567, 2, 534, 0], [567, 3, 534, 1], [568, 2, 535, 0], [569, 2, 536, 0], [570, 2, 537, 0], [571, 2, 538, 0], [572, 2, 539, 0], [573, 2, 540, 0], [574, 2, 541, 0], [575, 2, 542, 0], [576, 2, 543, 0], [577, 2, 544, 0], [578, 2, 545, 0], [579, 2, 546, 0], [580, 2, 547, 0], [581, 2, 548, 0], [582, 0, 548, 0], [582, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "desc.get", "ownKeys", "getStateFromPath", "remaining.split.filter.map$argument_0", "match.routeNames.map$argument_0", "getConfigResources", "prepareConfigResources", "getInitialRoutes", "getNormalizedConfigs", "Object.keys.map$argument_0", "checkForDuplicatedConfigs", "configs.reduce$argument_0", "b.every$argument_0", "a.every$argument_0", "getConfigsWithRegexes", "configs.map$argument_0", "joinPaths", "paths.map$argument_0", "matchAgainstConfigs", "config.pattern.split.reduce$argument_0", "config.routeNames.map$argument_0", "configs.find$argument_0", "normalizedPath.split.reduce$argument_0", "createNormalizedConfigs", "Object.keys.forEach$argument_0", "createConfigItem", "findParseConfigForRoute", "findInitialRoute", "createStateObject", "createNestedStateObject"], "mappings": "AAA;0ECC;sCCI,2BD;CDG,KC;CDG;gFCC;CDE,IC;CDE;mDCC;kBEC;gDFC;SEI;KFE;CDQ;wDCC;CDE;AI6B;iBCuC;SDG;0EEqB,oBF;CJmB;AOQ;CPW;AQC;CRa;ASC;CTS;AUC;4CCO,qED;CVgD;AYC;mBCE;6DCM,sBD,YE,sBF;KDQ;CZC;AgBC;uBCC;MDO;ChBC;kBkBC;yBCC,mBD;clBE;4BoBC;kECc;aDmB;2CEE;iDCC;iBDG;4BEU;iBFmB;aFM;CpBS;gCyBC;gDCoC;aDG;CzBK;yB2BC;C3B+B;gC4BC;C5BO;yB6BE;C7BgB;0B8BG;C9B8B;gC+BC;C/BkC"}}, "type": "js/module"}]}