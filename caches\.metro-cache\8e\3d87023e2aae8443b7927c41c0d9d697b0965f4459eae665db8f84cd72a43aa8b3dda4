{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@egjs/hammerjs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 291}, "end": {"line": 6, "column": 36, "index": 327}}], "key": "I5Lt2ouU6D9a2C2V4SJv4GWe8Fg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/findNodeHandle", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "2RP7Dp//wHy/IL2D3/pkDbKjgUI=", "exportNames": ["*"]}}, {"name": "../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 375}, "end": {"line": 8, "column": 33, "index": 408}}], "key": "ISRoyBmrsYyTcSqLDCBIFNoRZWE=", "exportNames": ["*"]}}, {"name": "./constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 409}, "end": {"line": 9, "column": 39, "index": 448}}], "key": "waDaw5D7vDr2hRFu0z1BqRCTzP4=", "exportNames": ["*"]}}, {"name": "./NodeManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 449}, "end": {"line": 10, "column": 45, "index": 494}}], "key": "krMbHBNX+RO/bw1gHRLBNgemf/A=", "exportNames": ["*"]}}, {"name": "../ghQueueMicrotask", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 495}, "end": {"line": 11, "column": 55, "index": 550}}], "key": "Ty3ERJQ4RajY8XDWg1+a8wq7RdE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _hammerjs = _interopRequireDefault(require(_dependencyMap[1], \"@egjs/hammerjs\"));\n  var _findNodeHandle = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/findNodeHandle\"));\n  var _State = require(_dependencyMap[3], \"../State\");\n  var _constants = require(_dependencyMap[4], \"./constants\");\n  var NodeManager = _interopRequireWildcard(require(_dependencyMap[5], \"./NodeManager\"));\n  var _ghQueueMicrotask = require(_dependencyMap[6], \"../ghQueueMicrotask\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n\n  /* eslint-disable eslint-comments/no-unlimited-disable */\n\n  /* eslint-disable */\n\n  // TODO(TS) Replace with HammerInput if https://github.com/DefinitelyTyped/DefinitelyTyped/pull/50438/files is merged\n\n  let gestureInstances = 0;\n  class GestureHandler {\n    get id() {\n      return `${this.name}${this.gestureInstance}`;\n    } // a simple way to check if GestureHandler is NativeViewGestureHandler, since importing it\n    // here to use instanceof would cause import cycle\n\n    get isNative() {\n      return false;\n    }\n    get isDiscrete() {\n      return false;\n    }\n    get shouldEnableGestureOnSetup() {\n      throw new Error('Must override GestureHandler.shouldEnableGestureOnSetup');\n    }\n    constructor() {\n      _defineProperty(this, \"handlerTag\", void 0);\n      _defineProperty(this, \"isGestureRunning\", false);\n      _defineProperty(this, \"view\", null);\n      _defineProperty(this, \"hasCustomActivationCriteria\", void 0);\n      _defineProperty(this, \"hasGestureFailed\", false);\n      _defineProperty(this, \"hammer\", null);\n      _defineProperty(this, \"initialRotation\", null);\n      _defineProperty(this, \"__initialX\", void 0);\n      _defineProperty(this, \"__initialY\", void 0);\n      _defineProperty(this, \"config\", {});\n      _defineProperty(this, \"previousState\", _State.State.UNDETERMINED);\n      _defineProperty(this, \"pendingGestures\", {});\n      _defineProperty(this, \"oldState\", _State.State.UNDETERMINED);\n      _defineProperty(this, \"lastSentState\", null);\n      _defineProperty(this, \"gestureInstance\", void 0);\n      _defineProperty(this, \"_stillWaiting\", void 0);\n      _defineProperty(this, \"propsRef\", void 0);\n      _defineProperty(this, \"ref\", void 0);\n      _defineProperty(this, \"clearSelfAsPending\", () => {\n        if (Array.isArray(this.config.waitFor)) {\n          for (const gesture of this.config.waitFor) {\n            gesture.removePendingGesture(this.id);\n          }\n        }\n      });\n      _defineProperty(this, \"destroy\", () => {\n        this.clearSelfAsPending();\n        if (this.hammer) {\n          this.hammer.stop(false);\n          this.hammer.destroy();\n        }\n        this.hammer = null;\n      });\n      _defineProperty(this, \"isPointInView\", ({\n        x,\n        y\n      }) => {\n        // @ts-ignore FIXME(TS)\n        const rect = this.view.getBoundingClientRect();\n        const pointerInside = x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom;\n        return pointerInside;\n      });\n      _defineProperty(this, \"sendEvent\", nativeEvent => {\n        const {\n          onGestureHandlerEvent,\n          onGestureHandlerStateChange\n        } = this.propsRef.current;\n        const event = this.transformEventData(nativeEvent);\n        invokeNullableMethod(onGestureHandlerEvent, event);\n        if (this.lastSentState !== event.nativeEvent.state) {\n          this.lastSentState = event.nativeEvent.state;\n          invokeNullableMethod(onGestureHandlerStateChange, event);\n        }\n      });\n      _defineProperty(this, \"sync\", () => {\n        const gesture = this.hammer.get(this.name);\n        if (!gesture) return;\n        const enable = (recognizer, inputData) => {\n          if (!this.config.enabled) {\n            this.isGestureRunning = false;\n            this.hasGestureFailed = false;\n            return false;\n          } // Prevent events before the system is ready.\n\n          if (!inputData || !recognizer.options || typeof inputData.maxPointers === 'undefined') {\n            return this.shouldEnableGestureOnSetup;\n          }\n          if (this.hasGestureFailed) {\n            return false;\n          }\n          if (!this.isDiscrete) {\n            if (this.isGestureRunning) {\n              return true;\n            } // The built-in hammer.js \"waitFor\" doesn't work across multiple views.\n            // Only process if there are views to wait for.\n\n            this._stillWaiting = this._getPendingGestures(); // This gesture should continue waiting.\n\n            if (this._stillWaiting.length) {\n              // Check to see if one of the gestures you're waiting for has started.\n              // If it has then the gesture should fail.\n              for (const gesture of this._stillWaiting) {\n                // When the target gesture has started, this gesture must force fail.\n                if (!gesture.isDiscrete && gesture.isGestureRunning) {\n                  this.hasGestureFailed = true;\n                  this.isGestureRunning = false;\n                  return false;\n                }\n              } // This gesture shouldn't start until the others have finished.\n\n              return false;\n            }\n          } // Use default behaviour\n\n          if (!this.hasCustomActivationCriteria) {\n            return true;\n          }\n          const deltaRotation = this.initialRotation == null ? 0 : inputData.rotation - this.initialRotation; // @ts-ignore FIXME(TS)\n\n          const {\n            success,\n            failed\n          } = this.isGestureEnabledForEvent(this.getConfig(), recognizer, {\n            ...inputData,\n            deltaRotation\n          });\n          if (failed) {\n            this.simulateCancelEvent(inputData);\n            this.hasGestureFailed = true;\n          }\n          return success;\n        };\n        const params = this.getHammerConfig(); // @ts-ignore FIXME(TS)\n\n        gesture.set({\n          ...params,\n          enable\n        });\n      });\n      this.gestureInstance = gestureInstances++;\n      this.hasCustomActivationCriteria = false;\n    }\n    getConfig() {\n      return this.config;\n    }\n    onWaitingEnded(_gesture) {}\n    removePendingGesture(id) {\n      delete this.pendingGestures[id];\n    }\n    addPendingGesture(gesture) {\n      this.pendingGestures[gesture.id] = gesture;\n    }\n    isGestureEnabledForEvent(_config, _recognizer, _event) {\n      return {\n        success: true\n      };\n    }\n    get NativeGestureClass() {\n      throw new Error('Must override GestureHandler.NativeGestureClass');\n    }\n    updateHasCustomActivationCriteria(_config) {\n      return true;\n    }\n    updateGestureConfig({\n      enabled = true,\n      ...props\n    }) {\n      this.clearSelfAsPending();\n      this.config = this.ensureConfig({\n        enabled,\n        ...props\n      });\n      this.hasCustomActivationCriteria = this.updateHasCustomActivationCriteria(this.config);\n      if (Array.isArray(this.config.waitFor)) {\n        for (const gesture of this.config.waitFor) {\n          gesture.addPendingGesture(this);\n        }\n      }\n      if (this.hammer) {\n        this.sync();\n      }\n      return this.config;\n    }\n    getState(type) {\n      // @ts-ignore TODO(TS) check if this is needed\n      if (type == 0) {\n        return 0;\n      }\n      return _constants.EventMap[type];\n    }\n    transformEventData(event) {\n      const {\n        eventType,\n        maxPointers: numberOfPointers\n      } = event; // const direction = DirectionMap[ev.direction];\n\n      const changedTouch = event.changedPointers[0];\n      const pointerInside = this.isPointInView({\n        x: changedTouch.clientX,\n        y: changedTouch.clientY\n      }); // TODO(TS) Remove cast after https://github.com/DefinitelyTyped/DefinitelyTyped/pull/50966 is merged.\n\n      const state = this.getState(eventType);\n      if (state !== this.previousState) {\n        this.oldState = this.previousState;\n        this.previousState = state;\n      }\n      return {\n        nativeEvent: {\n          numberOfPointers,\n          state,\n          pointerInside,\n          ...this.transformNativeEvent(event),\n          // onHandlerStateChange only\n          handlerTag: this.handlerTag,\n          target: this.ref,\n          // send oldState only when the state was changed, or is different than ACTIVE\n          // GestureDetector relies on the presence of `oldState` to differentiate between\n          // update events and state change events\n          oldState: state !== this.previousState || state != 4 ? this.oldState : undefined\n        },\n        timeStamp: Date.now()\n      };\n    }\n    transformNativeEvent(_event) {\n      return {};\n    }\n    cancelPendingGestures(event) {\n      for (const gesture of Object.values(this.pendingGestures)) {\n        if (gesture && gesture.isGestureRunning) {\n          gesture.hasGestureFailed = true;\n          gesture.cancelEvent(event);\n        }\n      }\n    }\n    notifyPendingGestures() {\n      for (const gesture of Object.values(this.pendingGestures)) {\n        if (gesture) {\n          gesture.onWaitingEnded(this);\n        }\n      }\n    } // FIXME event is undefined in runtime when firstly invoked (see Draggable example), check other functions taking event as input\n\n    onGestureEnded(event) {\n      this.isGestureRunning = false;\n      this.cancelPendingGestures(event);\n    }\n    forceInvalidate(event) {\n      if (this.isGestureRunning) {\n        this.hasGestureFailed = true;\n        this.cancelEvent(event);\n      }\n    }\n    cancelEvent(event) {\n      this.notifyPendingGestures();\n      this.sendEvent({\n        ...event,\n        eventType: _hammerjs.default.INPUT_CANCEL,\n        isFinal: true\n      });\n      this.onGestureEnded(event);\n    }\n    onRawEvent({\n      isFirst\n    }) {\n      if (isFirst) {\n        this.hasGestureFailed = false;\n      }\n    }\n    shouldUseTouchEvents(config) {\n      var _config$simultaneousH, _config$simultaneousH2;\n      return (_config$simultaneousH = (_config$simultaneousH2 = config.simultaneousHandlers) === null || _config$simultaneousH2 === void 0 ? void 0 : _config$simultaneousH2.some(handler => handler.isNative)) !== null && _config$simultaneousH !== void 0 ? _config$simultaneousH : false;\n    }\n    setView(ref, propsRef) {\n      if (ref == null) {\n        this.destroy();\n        this.view = null;\n        return;\n      } // @ts-ignore window doesn't exist on global type as we don't want to use Node types\n\n      const SUPPORTS_TOUCH = 'ontouchstart' in window;\n      this.propsRef = propsRef;\n      this.ref = ref;\n      this.view = (0, _findNodeHandle.default)(ref); // When the browser starts handling the gesture (e.g. scrolling), it sends a pointercancel event and stops\n      // sending additional pointer events. This is not the case with touch events, so if the gesture is simultaneous\n      // with a NativeGestureHandler, we need to check if touch events are supported and use them if possible.\n\n      this.hammer = SUPPORTS_TOUCH && this.shouldUseTouchEvents(this.config) ? new _hammerjs.default.Manager(this.view, {\n        inputClass: _hammerjs.default.TouchInput\n      }) : new _hammerjs.default.Manager(this.view);\n      this.oldState = _State.State.UNDETERMINED;\n      this.previousState = _State.State.UNDETERMINED;\n      this.lastSentState = null;\n      const {\n        NativeGestureClass\n      } = this; // @ts-ignore TODO(TS)\n\n      const gesture = new NativeGestureClass(this.getHammerConfig());\n      this.hammer.add(gesture);\n      this.hammer.on('hammer.input', ev => {\n        if (!this.config.enabled) {\n          this.hasGestureFailed = false;\n          this.isGestureRunning = false;\n          return;\n        }\n        this.onRawEvent(ev); // TODO: Bacon: Check against something other than null\n        // The isFirst value is not called when the first rotation is calculated.\n\n        if (this.initialRotation === null && ev.rotation !== 0) {\n          this.initialRotation = ev.rotation;\n        }\n        if (ev.isFinal) {\n          // in favor of a willFail otherwise the last frame of the gesture will be captured.\n          setTimeout(() => {\n            this.initialRotation = null;\n            this.hasGestureFailed = false;\n          });\n        }\n      });\n      this.setupEvents();\n      this.sync();\n    }\n    setupEvents() {\n      // TODO(TS) Hammer types aren't exactly that what we get in runtime\n      if (!this.isDiscrete) {\n        this.hammer.on(`${this.name}start`, event => this.onStart(event));\n        this.hammer.on(`${this.name}end ${this.name}cancel`, event => {\n          this.onGestureEnded(event);\n        });\n      }\n      this.hammer.on(this.name, ev => this.onGestureActivated(ev)); // TODO(TS) remove cast after https://github.com/DefinitelyTyped/DefinitelyTyped/pull/50438 is merged\n    }\n    onStart({\n      deltaX,\n      deltaY,\n      rotation\n    }) {\n      // Reset the state for the next gesture\n      this.oldState = _State.State.UNDETERMINED;\n      this.previousState = _State.State.UNDETERMINED;\n      this.lastSentState = null;\n      this.isGestureRunning = true;\n      this.__initialX = deltaX;\n      this.__initialY = deltaY;\n      this.initialRotation = rotation;\n    }\n    onGestureActivated(ev) {\n      this.sendEvent(ev);\n    }\n    onSuccess() {}\n    _getPendingGestures() {\n      if (Array.isArray(this.config.waitFor) && this.config.waitFor.length) {\n        // Get the list of gestures that this gesture is still waiting for.\n        // Use `=== false` in case a ref that isn't a gesture handler is used.\n        const stillWaiting = this.config.waitFor.filter(({\n          hasGestureFailed\n        }) => hasGestureFailed === false);\n        return stillWaiting;\n      }\n      return [];\n    }\n    getHammerConfig() {\n      const pointers = this.config.minPointers === this.config.maxPointers ? this.config.minPointers : 0;\n      return {\n        pointers\n      };\n    }\n    simulateCancelEvent(_inputData) {} // Validate the props\n\n    ensureConfig(config) {\n      const props = {\n        ...config\n      }; // TODO(TS) We use ! to assert that if property is present then value is not empty (null, undefined)\n\n      if ('minDist' in config) {\n        props.minDist = config.minDist;\n        props.minDistSq = props.minDist * props.minDist;\n      }\n      if ('minVelocity' in config) {\n        props.minVelocity = config.minVelocity;\n        props.minVelocitySq = props.minVelocity * props.minVelocity;\n      }\n      if ('maxDist' in config) {\n        props.maxDist = config.maxDist;\n        props.maxDistSq = config.maxDist * config.maxDist;\n      }\n      if ('waitFor' in config) {\n        props.waitFor = asArray(config.waitFor).map(({\n          handlerTag\n        }) => NodeManager.getHandler(handlerTag)).filter(v => v);\n      } else {\n        props.waitFor = null;\n      }\n      if ('simultaneousHandlers' in config) {\n        const shouldUseTouchEvents = this.shouldUseTouchEvents(this.config);\n        props.simultaneousHandlers = asArray(config.simultaneousHandlers).map(handler => {\n          if (typeof handler === 'number') {\n            return NodeManager.getHandler(handler);\n          } else {\n            return NodeManager.getHandler(handler.handlerTag);\n          }\n        }).filter(v => v);\n        if (shouldUseTouchEvents !== this.shouldUseTouchEvents(props)) {\n          (0, _ghQueueMicrotask.ghQueueMicrotask)(() => {\n            // if the undelying event API needs to be changed, we need to unmount and mount\n            // the hammer instance again.\n            this.destroy();\n            this.setView(this.ref, this.propsRef);\n          });\n        }\n      } else {\n        props.simultaneousHandlers = null;\n      }\n      const configProps = ['minPointers', 'maxPointers', 'minDist', 'maxDist', 'maxDistSq', 'minVelocitySq', 'minDistSq', 'minVelocity', 'failOffsetXStart', 'failOffsetYStart', 'failOffsetXEnd', 'failOffsetYEnd', 'activeOffsetXStart', 'activeOffsetXEnd', 'activeOffsetYStart', 'activeOffsetYEnd'];\n      configProps.forEach(prop => {\n        if (typeof props[prop] === 'undefined') {\n          props[prop] = Number.NaN;\n        }\n      });\n      return props; // TODO(TS) how to convince TS that props are filled?\n    }\n  } // TODO(TS) investigate this method\n  // Used for sending data to a callback or AnimatedEvent\n\n  function invokeNullableMethod(method, event) {\n    if (method) {\n      if (typeof method === 'function') {\n        method(event);\n      } else {\n        // For use with reanimated's AnimatedEvent\n        if ('__getHandler' in method && typeof method.__getHandler === 'function') {\n          const handler = method.__getHandler();\n          invokeNullableMethod(handler, event);\n        } else {\n          if ('__nodeConfig' in method) {\n            const {\n              argMapping\n            } = method.__nodeConfig;\n            if (Array.isArray(argMapping)) {\n              for (const [index, [key, value]] of argMapping.entries()) {\n                if (key in event.nativeEvent) {\n                  // @ts-ignore fix method type\n                  const nativeValue = event.nativeEvent[key];\n                  if (value && value.setValue) {\n                    // Reanimated API\n                    value.setValue(nativeValue);\n                  } else {\n                    // RN Animated API\n                    method.__nodeConfig.argMapping[index] = [key, nativeValue];\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  function asArray(value) {\n    // TODO(TS) use config.waitFor type\n    return value == null ? [] : Array.isArray(value) ? value : [value];\n  }\n  var _default = exports.default = GestureHandler;\n});", "lineCount": 494, "map": [[7, 2, 6, 0], [7, 6, 6, 0, "_hammerjs"], [7, 15, 6, 0], [7, 18, 6, 0, "_interopRequireDefault"], [7, 40, 6, 0], [7, 41, 6, 0, "require"], [7, 48, 6, 0], [7, 49, 6, 0, "_dependencyMap"], [7, 63, 6, 0], [8, 2, 6, 36], [8, 6, 6, 36, "_findNodeHandle"], [8, 21, 6, 36], [8, 24, 6, 36, "_interopRequireDefault"], [8, 46, 6, 36], [8, 47, 6, 36, "require"], [8, 54, 6, 36], [8, 55, 6, 36, "_dependencyMap"], [8, 69, 6, 36], [9, 2, 8, 0], [9, 6, 8, 0, "_State"], [9, 12, 8, 0], [9, 15, 8, 0, "require"], [9, 22, 8, 0], [9, 23, 8, 0, "_dependencyMap"], [9, 37, 8, 0], [10, 2, 9, 0], [10, 6, 9, 0, "_constants"], [10, 16, 9, 0], [10, 19, 9, 0, "require"], [10, 26, 9, 0], [10, 27, 9, 0, "_dependencyMap"], [10, 41, 9, 0], [11, 2, 10, 0], [11, 6, 10, 0, "NodeManager"], [11, 17, 10, 0], [11, 20, 10, 0, "_interopRequireWildcard"], [11, 43, 10, 0], [11, 44, 10, 0, "require"], [11, 51, 10, 0], [11, 52, 10, 0, "_dependencyMap"], [11, 66, 10, 0], [12, 2, 11, 0], [12, 6, 11, 0, "_ghQueueMicrotask"], [12, 23, 11, 0], [12, 26, 11, 0, "require"], [12, 33, 11, 0], [12, 34, 11, 0, "_dependencyMap"], [12, 48, 11, 0], [13, 2, 11, 55], [13, 11, 11, 55, "_interopRequireWildcard"], [13, 35, 11, 55, "e"], [13, 36, 11, 55], [13, 38, 11, 55, "t"], [13, 39, 11, 55], [13, 68, 11, 55, "WeakMap"], [13, 75, 11, 55], [13, 81, 11, 55, "r"], [13, 82, 11, 55], [13, 89, 11, 55, "WeakMap"], [13, 96, 11, 55], [13, 100, 11, 55, "n"], [13, 101, 11, 55], [13, 108, 11, 55, "WeakMap"], [13, 115, 11, 55], [13, 127, 11, 55, "_interopRequireWildcard"], [13, 150, 11, 55], [13, 162, 11, 55, "_interopRequireWildcard"], [13, 163, 11, 55, "e"], [13, 164, 11, 55], [13, 166, 11, 55, "t"], [13, 167, 11, 55], [13, 176, 11, 55, "t"], [13, 177, 11, 55], [13, 181, 11, 55, "e"], [13, 182, 11, 55], [13, 186, 11, 55, "e"], [13, 187, 11, 55], [13, 188, 11, 55, "__esModule"], [13, 198, 11, 55], [13, 207, 11, 55, "e"], [13, 208, 11, 55], [13, 214, 11, 55, "o"], [13, 215, 11, 55], [13, 217, 11, 55, "i"], [13, 218, 11, 55], [13, 220, 11, 55, "f"], [13, 221, 11, 55], [13, 226, 11, 55, "__proto__"], [13, 235, 11, 55], [13, 243, 11, 55, "default"], [13, 250, 11, 55], [13, 252, 11, 55, "e"], [13, 253, 11, 55], [13, 270, 11, 55, "e"], [13, 271, 11, 55], [13, 294, 11, 55, "e"], [13, 295, 11, 55], [13, 320, 11, 55, "e"], [13, 321, 11, 55], [13, 330, 11, 55, "f"], [13, 331, 11, 55], [13, 337, 11, 55, "o"], [13, 338, 11, 55], [13, 341, 11, 55, "t"], [13, 342, 11, 55], [13, 345, 11, 55, "n"], [13, 346, 11, 55], [13, 349, 11, 55, "r"], [13, 350, 11, 55], [13, 358, 11, 55, "o"], [13, 359, 11, 55], [13, 360, 11, 55, "has"], [13, 363, 11, 55], [13, 364, 11, 55, "e"], [13, 365, 11, 55], [13, 375, 11, 55, "o"], [13, 376, 11, 55], [13, 377, 11, 55, "get"], [13, 380, 11, 55], [13, 381, 11, 55, "e"], [13, 382, 11, 55], [13, 385, 11, 55, "o"], [13, 386, 11, 55], [13, 387, 11, 55, "set"], [13, 390, 11, 55], [13, 391, 11, 55, "e"], [13, 392, 11, 55], [13, 394, 11, 55, "f"], [13, 395, 11, 55], [13, 411, 11, 55, "t"], [13, 412, 11, 55], [13, 416, 11, 55, "e"], [13, 417, 11, 55], [13, 433, 11, 55, "t"], [13, 434, 11, 55], [13, 441, 11, 55, "hasOwnProperty"], [13, 455, 11, 55], [13, 456, 11, 55, "call"], [13, 460, 11, 55], [13, 461, 11, 55, "e"], [13, 462, 11, 55], [13, 464, 11, 55, "t"], [13, 465, 11, 55], [13, 472, 11, 55, "i"], [13, 473, 11, 55], [13, 477, 11, 55, "o"], [13, 478, 11, 55], [13, 481, 11, 55, "Object"], [13, 487, 11, 55], [13, 488, 11, 55, "defineProperty"], [13, 502, 11, 55], [13, 507, 11, 55, "Object"], [13, 513, 11, 55], [13, 514, 11, 55, "getOwnPropertyDescriptor"], [13, 538, 11, 55], [13, 539, 11, 55, "e"], [13, 540, 11, 55], [13, 542, 11, 55, "t"], [13, 543, 11, 55], [13, 550, 11, 55, "i"], [13, 551, 11, 55], [13, 552, 11, 55, "get"], [13, 555, 11, 55], [13, 559, 11, 55, "i"], [13, 560, 11, 55], [13, 561, 11, 55, "set"], [13, 564, 11, 55], [13, 568, 11, 55, "o"], [13, 569, 11, 55], [13, 570, 11, 55, "f"], [13, 571, 11, 55], [13, 573, 11, 55, "t"], [13, 574, 11, 55], [13, 576, 11, 55, "i"], [13, 577, 11, 55], [13, 581, 11, 55, "f"], [13, 582, 11, 55], [13, 583, 11, 55, "t"], [13, 584, 11, 55], [13, 588, 11, 55, "e"], [13, 589, 11, 55], [13, 590, 11, 55, "t"], [13, 591, 11, 55], [13, 602, 11, 55, "f"], [13, 603, 11, 55], [13, 608, 11, 55, "e"], [13, 609, 11, 55], [13, 611, 11, 55, "t"], [13, 612, 11, 55], [14, 2, 1, 0], [14, 11, 1, 9, "_defineProperty"], [14, 26, 1, 24, "_defineProperty"], [14, 27, 1, 25, "obj"], [14, 30, 1, 28], [14, 32, 1, 30, "key"], [14, 35, 1, 33], [14, 37, 1, 35, "value"], [14, 42, 1, 40], [14, 44, 1, 42], [15, 4, 1, 44], [15, 8, 1, 48, "key"], [15, 11, 1, 51], [15, 15, 1, 55, "obj"], [15, 18, 1, 58], [15, 20, 1, 60], [16, 6, 1, 62, "Object"], [16, 12, 1, 68], [16, 13, 1, 69, "defineProperty"], [16, 27, 1, 83], [16, 28, 1, 84, "obj"], [16, 31, 1, 87], [16, 33, 1, 89, "key"], [16, 36, 1, 92], [16, 38, 1, 94], [17, 8, 1, 96, "value"], [17, 13, 1, 101], [17, 15, 1, 103, "value"], [17, 20, 1, 108], [18, 8, 1, 110, "enumerable"], [18, 18, 1, 120], [18, 20, 1, 122], [18, 24, 1, 126], [19, 8, 1, 128, "configurable"], [19, 20, 1, 140], [19, 22, 1, 142], [19, 26, 1, 146], [20, 8, 1, 148, "writable"], [20, 16, 1, 156], [20, 18, 1, 158], [21, 6, 1, 163], [21, 7, 1, 164], [21, 8, 1, 165], [22, 4, 1, 167], [22, 5, 1, 168], [22, 11, 1, 174], [23, 6, 1, 176, "obj"], [23, 9, 1, 179], [23, 10, 1, 180, "key"], [23, 13, 1, 183], [23, 14, 1, 184], [23, 17, 1, 187, "value"], [23, 22, 1, 192], [24, 4, 1, 194], [25, 4, 1, 196], [25, 11, 1, 203, "obj"], [25, 14, 1, 206], [26, 2, 1, 208], [28, 2, 3, 0], [30, 2, 5, 0], [32, 2, 11, 56], [34, 2, 13, 0], [34, 6, 13, 4, "gestureInstances"], [34, 22, 13, 20], [34, 25, 13, 23], [34, 26, 13, 24], [35, 2, 15, 0], [35, 8, 15, 6, "Gesture<PERSON>andler"], [35, 22, 15, 20], [35, 23, 15, 21], [36, 4, 16, 2], [36, 8, 16, 6, "id"], [36, 10, 16, 8, "id"], [36, 11, 16, 8], [36, 13, 16, 11], [37, 6, 17, 4], [37, 13, 17, 11], [37, 16, 17, 14], [37, 20, 17, 18], [37, 21, 17, 19, "name"], [37, 25, 17, 23], [37, 28, 17, 26], [37, 32, 17, 30], [37, 33, 17, 31, "gestureInstance"], [37, 48, 17, 46], [37, 50, 17, 48], [38, 4, 18, 2], [38, 5, 18, 3], [38, 6, 18, 4], [39, 4, 19, 2], [41, 4, 22, 2], [41, 8, 22, 6, "isNative"], [41, 16, 22, 14, "isNative"], [41, 17, 22, 14], [41, 19, 22, 17], [42, 6, 23, 4], [42, 13, 23, 11], [42, 18, 23, 16], [43, 4, 24, 2], [44, 4, 26, 2], [44, 8, 26, 6, "isDiscrete"], [44, 18, 26, 16, "isDiscrete"], [44, 19, 26, 16], [44, 21, 26, 19], [45, 6, 27, 4], [45, 13, 27, 11], [45, 18, 27, 16], [46, 4, 28, 2], [47, 4, 30, 2], [47, 8, 30, 6, "shouldEnableGestureOnSetup"], [47, 34, 30, 32, "shouldEnableGestureOnSetup"], [47, 35, 30, 32], [47, 37, 30, 35], [48, 6, 31, 4], [48, 12, 31, 10], [48, 16, 31, 14, "Error"], [48, 21, 31, 19], [48, 22, 31, 20], [48, 79, 31, 77], [48, 80, 31, 78], [49, 4, 32, 2], [50, 4, 34, 2, "constructor"], [50, 15, 34, 13, "constructor"], [50, 16, 34, 13], [50, 18, 34, 16], [51, 6, 35, 4, "_defineProperty"], [51, 21, 35, 19], [51, 22, 35, 20], [51, 26, 35, 24], [51, 28, 35, 26], [51, 40, 35, 38], [51, 42, 35, 40], [51, 47, 35, 45], [51, 48, 35, 46], [51, 49, 35, 47], [52, 6, 37, 4, "_defineProperty"], [52, 21, 37, 19], [52, 22, 37, 20], [52, 26, 37, 24], [52, 28, 37, 26], [52, 46, 37, 44], [52, 48, 37, 46], [52, 53, 37, 51], [52, 54, 37, 52], [53, 6, 39, 4, "_defineProperty"], [53, 21, 39, 19], [53, 22, 39, 20], [53, 26, 39, 24], [53, 28, 39, 26], [53, 34, 39, 32], [53, 36, 39, 34], [53, 40, 39, 38], [53, 41, 39, 39], [54, 6, 41, 4, "_defineProperty"], [54, 21, 41, 19], [54, 22, 41, 20], [54, 26, 41, 24], [54, 28, 41, 26], [54, 57, 41, 55], [54, 59, 41, 57], [54, 64, 41, 62], [54, 65, 41, 63], [54, 66, 41, 64], [55, 6, 43, 4, "_defineProperty"], [55, 21, 43, 19], [55, 22, 43, 20], [55, 26, 43, 24], [55, 28, 43, 26], [55, 46, 43, 44], [55, 48, 43, 46], [55, 53, 43, 51], [55, 54, 43, 52], [56, 6, 45, 4, "_defineProperty"], [56, 21, 45, 19], [56, 22, 45, 20], [56, 26, 45, 24], [56, 28, 45, 26], [56, 36, 45, 34], [56, 38, 45, 36], [56, 42, 45, 40], [56, 43, 45, 41], [57, 6, 47, 4, "_defineProperty"], [57, 21, 47, 19], [57, 22, 47, 20], [57, 26, 47, 24], [57, 28, 47, 26], [57, 45, 47, 43], [57, 47, 47, 45], [57, 51, 47, 49], [57, 52, 47, 50], [58, 6, 49, 4, "_defineProperty"], [58, 21, 49, 19], [58, 22, 49, 20], [58, 26, 49, 24], [58, 28, 49, 26], [58, 40, 49, 38], [58, 42, 49, 40], [58, 47, 49, 45], [58, 48, 49, 46], [58, 49, 49, 47], [59, 6, 51, 4, "_defineProperty"], [59, 21, 51, 19], [59, 22, 51, 20], [59, 26, 51, 24], [59, 28, 51, 26], [59, 40, 51, 38], [59, 42, 51, 40], [59, 47, 51, 45], [59, 48, 51, 46], [59, 49, 51, 47], [60, 6, 53, 4, "_defineProperty"], [60, 21, 53, 19], [60, 22, 53, 20], [60, 26, 53, 24], [60, 28, 53, 26], [60, 36, 53, 34], [60, 38, 53, 36], [60, 39, 53, 37], [60, 40, 53, 38], [60, 41, 53, 39], [61, 6, 55, 4, "_defineProperty"], [61, 21, 55, 19], [61, 22, 55, 20], [61, 26, 55, 24], [61, 28, 55, 26], [61, 43, 55, 41], [61, 45, 55, 43, "State"], [61, 57, 55, 48], [61, 58, 55, 49, "UNDETERMINED"], [61, 70, 55, 61], [61, 71, 55, 62], [62, 6, 57, 4, "_defineProperty"], [62, 21, 57, 19], [62, 22, 57, 20], [62, 26, 57, 24], [62, 28, 57, 26], [62, 45, 57, 43], [62, 47, 57, 45], [62, 48, 57, 46], [62, 49, 57, 47], [62, 50, 57, 48], [63, 6, 59, 4, "_defineProperty"], [63, 21, 59, 19], [63, 22, 59, 20], [63, 26, 59, 24], [63, 28, 59, 26], [63, 38, 59, 36], [63, 40, 59, 38, "State"], [63, 52, 59, 43], [63, 53, 59, 44, "UNDETERMINED"], [63, 65, 59, 56], [63, 66, 59, 57], [64, 6, 61, 4, "_defineProperty"], [64, 21, 61, 19], [64, 22, 61, 20], [64, 26, 61, 24], [64, 28, 61, 26], [64, 43, 61, 41], [64, 45, 61, 43], [64, 49, 61, 47], [64, 50, 61, 48], [65, 6, 63, 4, "_defineProperty"], [65, 21, 63, 19], [65, 22, 63, 20], [65, 26, 63, 24], [65, 28, 63, 26], [65, 45, 63, 43], [65, 47, 63, 45], [65, 52, 63, 50], [65, 53, 63, 51], [65, 54, 63, 52], [66, 6, 65, 4, "_defineProperty"], [66, 21, 65, 19], [66, 22, 65, 20], [66, 26, 65, 24], [66, 28, 65, 26], [66, 43, 65, 41], [66, 45, 65, 43], [66, 50, 65, 48], [66, 51, 65, 49], [66, 52, 65, 50], [67, 6, 67, 4, "_defineProperty"], [67, 21, 67, 19], [67, 22, 67, 20], [67, 26, 67, 24], [67, 28, 67, 26], [67, 38, 67, 36], [67, 40, 67, 38], [67, 45, 67, 43], [67, 46, 67, 44], [67, 47, 67, 45], [68, 6, 69, 4, "_defineProperty"], [68, 21, 69, 19], [68, 22, 69, 20], [68, 26, 69, 24], [68, 28, 69, 26], [68, 33, 69, 31], [68, 35, 69, 33], [68, 40, 69, 38], [68, 41, 69, 39], [68, 42, 69, 40], [69, 6, 71, 4, "_defineProperty"], [69, 21, 71, 19], [69, 22, 71, 20], [69, 26, 71, 24], [69, 28, 71, 26], [69, 48, 71, 46], [69, 50, 71, 48], [69, 56, 71, 54], [70, 8, 72, 6], [70, 12, 72, 10, "Array"], [70, 17, 72, 15], [70, 18, 72, 16, "isArray"], [70, 25, 72, 23], [70, 26, 72, 24], [70, 30, 72, 28], [70, 31, 72, 29, "config"], [70, 37, 72, 35], [70, 38, 72, 36, "waitFor"], [70, 45, 72, 43], [70, 46, 72, 44], [70, 48, 72, 46], [71, 10, 73, 8], [71, 15, 73, 13], [71, 21, 73, 19, "gesture"], [71, 28, 73, 26], [71, 32, 73, 30], [71, 36, 73, 34], [71, 37, 73, 35, "config"], [71, 43, 73, 41], [71, 44, 73, 42, "waitFor"], [71, 51, 73, 49], [71, 53, 73, 51], [72, 12, 74, 10, "gesture"], [72, 19, 74, 17], [72, 20, 74, 18, "removePendingGesture"], [72, 40, 74, 38], [72, 41, 74, 39], [72, 45, 74, 43], [72, 46, 74, 44, "id"], [72, 48, 74, 46], [72, 49, 74, 47], [73, 10, 75, 8], [74, 8, 76, 6], [75, 6, 77, 4], [75, 7, 77, 5], [75, 8, 77, 6], [76, 6, 79, 4, "_defineProperty"], [76, 21, 79, 19], [76, 22, 79, 20], [76, 26, 79, 24], [76, 28, 79, 26], [76, 37, 79, 35], [76, 39, 79, 37], [76, 45, 79, 43], [77, 8, 80, 6], [77, 12, 80, 10], [77, 13, 80, 11, "clearSelfAsPending"], [77, 31, 80, 29], [77, 32, 80, 30], [77, 33, 80, 31], [78, 8, 82, 6], [78, 12, 82, 10], [78, 16, 82, 14], [78, 17, 82, 15, "hammer"], [78, 23, 82, 21], [78, 25, 82, 23], [79, 10, 83, 8], [79, 14, 83, 12], [79, 15, 83, 13, "hammer"], [79, 21, 83, 19], [79, 22, 83, 20, "stop"], [79, 26, 83, 24], [79, 27, 83, 25], [79, 32, 83, 30], [79, 33, 83, 31], [80, 10, 84, 8], [80, 14, 84, 12], [80, 15, 84, 13, "hammer"], [80, 21, 84, 19], [80, 22, 84, 20, "destroy"], [80, 29, 84, 27], [80, 30, 84, 28], [80, 31, 84, 29], [81, 8, 85, 6], [82, 8, 87, 6], [82, 12, 87, 10], [82, 13, 87, 11, "hammer"], [82, 19, 87, 17], [82, 22, 87, 20], [82, 26, 87, 24], [83, 6, 88, 4], [83, 7, 88, 5], [83, 8, 88, 6], [84, 6, 90, 4, "_defineProperty"], [84, 21, 90, 19], [84, 22, 90, 20], [84, 26, 90, 24], [84, 28, 90, 26], [84, 43, 90, 41], [84, 45, 90, 43], [84, 46, 90, 44], [85, 8, 91, 6, "x"], [85, 9, 91, 7], [86, 8, 92, 6, "y"], [87, 6, 93, 4], [87, 7, 93, 5], [87, 12, 93, 10], [88, 8, 94, 6], [89, 8, 95, 6], [89, 14, 95, 12, "rect"], [89, 18, 95, 16], [89, 21, 95, 19], [89, 25, 95, 23], [89, 26, 95, 24, "view"], [89, 30, 95, 28], [89, 31, 95, 29, "getBoundingClientRect"], [89, 52, 95, 50], [89, 53, 95, 51], [89, 54, 95, 52], [90, 8, 96, 6], [90, 14, 96, 12, "pointerInside"], [90, 27, 96, 25], [90, 30, 96, 28, "x"], [90, 31, 96, 29], [90, 35, 96, 33, "rect"], [90, 39, 96, 37], [90, 40, 96, 38, "left"], [90, 44, 96, 42], [90, 48, 96, 46, "x"], [90, 49, 96, 47], [90, 53, 96, 51, "rect"], [90, 57, 96, 55], [90, 58, 96, 56, "right"], [90, 63, 96, 61], [90, 67, 96, 65, "y"], [90, 68, 96, 66], [90, 72, 96, 70, "rect"], [90, 76, 96, 74], [90, 77, 96, 75, "top"], [90, 80, 96, 78], [90, 84, 96, 82, "y"], [90, 85, 96, 83], [90, 89, 96, 87, "rect"], [90, 93, 96, 91], [90, 94, 96, 92, "bottom"], [90, 100, 96, 98], [91, 8, 97, 6], [91, 15, 97, 13, "pointerInside"], [91, 28, 97, 26], [92, 6, 98, 4], [92, 7, 98, 5], [92, 8, 98, 6], [93, 6, 100, 4, "_defineProperty"], [93, 21, 100, 19], [93, 22, 100, 20], [93, 26, 100, 24], [93, 28, 100, 26], [93, 39, 100, 37], [93, 41, 100, 39, "nativeEvent"], [93, 52, 100, 50], [93, 56, 100, 54], [94, 8, 101, 6], [94, 14, 101, 12], [95, 10, 102, 8, "onGestureHandlerEvent"], [95, 31, 102, 29], [96, 10, 103, 8, "onGestureHandlerStateChange"], [97, 8, 104, 6], [97, 9, 104, 7], [97, 12, 104, 10], [97, 16, 104, 14], [97, 17, 104, 15, "propsRef"], [97, 25, 104, 23], [97, 26, 104, 24, "current"], [97, 33, 104, 31], [98, 8, 105, 6], [98, 14, 105, 12, "event"], [98, 19, 105, 17], [98, 22, 105, 20], [98, 26, 105, 24], [98, 27, 105, 25, "transformEventData"], [98, 45, 105, 43], [98, 46, 105, 44, "nativeEvent"], [98, 57, 105, 55], [98, 58, 105, 56], [99, 8, 106, 6, "invokeNullableMethod"], [99, 28, 106, 26], [99, 29, 106, 27, "onGestureHandlerEvent"], [99, 50, 106, 48], [99, 52, 106, 50, "event"], [99, 57, 106, 55], [99, 58, 106, 56], [100, 8, 108, 6], [100, 12, 108, 10], [100, 16, 108, 14], [100, 17, 108, 15, "lastSentState"], [100, 30, 108, 28], [100, 35, 108, 33, "event"], [100, 40, 108, 38], [100, 41, 108, 39, "nativeEvent"], [100, 52, 108, 50], [100, 53, 108, 51, "state"], [100, 58, 108, 56], [100, 60, 108, 58], [101, 10, 109, 8], [101, 14, 109, 12], [101, 15, 109, 13, "lastSentState"], [101, 28, 109, 26], [101, 31, 109, 29, "event"], [101, 36, 109, 34], [101, 37, 109, 35, "nativeEvent"], [101, 48, 109, 46], [101, 49, 109, 47, "state"], [101, 54, 109, 52], [102, 10, 110, 8, "invokeNullableMethod"], [102, 30, 110, 28], [102, 31, 110, 29, "onGestureHandlerStateChange"], [102, 58, 110, 56], [102, 60, 110, 58, "event"], [102, 65, 110, 63], [102, 66, 110, 64], [103, 8, 111, 6], [104, 6, 112, 4], [104, 7, 112, 5], [104, 8, 112, 6], [105, 6, 114, 4, "_defineProperty"], [105, 21, 114, 19], [105, 22, 114, 20], [105, 26, 114, 24], [105, 28, 114, 26], [105, 34, 114, 32], [105, 36, 114, 34], [105, 42, 114, 40], [106, 8, 115, 6], [106, 14, 115, 12, "gesture"], [106, 21, 115, 19], [106, 24, 115, 22], [106, 28, 115, 26], [106, 29, 115, 27, "hammer"], [106, 35, 115, 33], [106, 36, 115, 34, "get"], [106, 39, 115, 37], [106, 40, 115, 38], [106, 44, 115, 42], [106, 45, 115, 43, "name"], [106, 49, 115, 47], [106, 50, 115, 48], [107, 8, 116, 6], [107, 12, 116, 10], [107, 13, 116, 11, "gesture"], [107, 20, 116, 18], [107, 22, 116, 20], [108, 8, 118, 6], [108, 14, 118, 12, "enable"], [108, 20, 118, 18], [108, 23, 118, 21, "enable"], [108, 24, 118, 22, "recognizer"], [108, 34, 118, 32], [108, 36, 118, 34, "inputData"], [108, 45, 118, 43], [108, 50, 118, 48], [109, 10, 119, 8], [109, 14, 119, 12], [109, 15, 119, 13], [109, 19, 119, 17], [109, 20, 119, 18, "config"], [109, 26, 119, 24], [109, 27, 119, 25, "enabled"], [109, 34, 119, 32], [109, 36, 119, 34], [110, 12, 120, 10], [110, 16, 120, 14], [110, 17, 120, 15, "isGestureRunning"], [110, 33, 120, 31], [110, 36, 120, 34], [110, 41, 120, 39], [111, 12, 121, 10], [111, 16, 121, 14], [111, 17, 121, 15, "hasGestureFailed"], [111, 33, 121, 31], [111, 36, 121, 34], [111, 41, 121, 39], [112, 12, 122, 10], [112, 19, 122, 17], [112, 24, 122, 22], [113, 10, 123, 8], [113, 11, 123, 9], [113, 12, 123, 10], [115, 10, 126, 8], [115, 14, 126, 12], [115, 15, 126, 13, "inputData"], [115, 24, 126, 22], [115, 28, 126, 26], [115, 29, 126, 27, "recognizer"], [115, 39, 126, 37], [115, 40, 126, 38, "options"], [115, 47, 126, 45], [115, 51, 126, 49], [115, 58, 126, 56, "inputData"], [115, 67, 126, 65], [115, 68, 126, 66, "maxPointers"], [115, 79, 126, 77], [115, 84, 126, 82], [115, 95, 126, 93], [115, 97, 126, 95], [116, 12, 127, 10], [116, 19, 127, 17], [116, 23, 127, 21], [116, 24, 127, 22, "shouldEnableGestureOnSetup"], [116, 50, 127, 48], [117, 10, 128, 8], [118, 10, 130, 8], [118, 14, 130, 12], [118, 18, 130, 16], [118, 19, 130, 17, "hasGestureFailed"], [118, 35, 130, 33], [118, 37, 130, 35], [119, 12, 131, 10], [119, 19, 131, 17], [119, 24, 131, 22], [120, 10, 132, 8], [121, 10, 134, 8], [121, 14, 134, 12], [121, 15, 134, 13], [121, 19, 134, 17], [121, 20, 134, 18, "isDiscrete"], [121, 30, 134, 28], [121, 32, 134, 30], [122, 12, 135, 10], [122, 16, 135, 14], [122, 20, 135, 18], [122, 21, 135, 19, "isGestureRunning"], [122, 37, 135, 35], [122, 39, 135, 37], [123, 14, 136, 12], [123, 21, 136, 19], [123, 25, 136, 23], [124, 12, 137, 10], [124, 13, 137, 11], [124, 14, 137, 12], [125, 12, 138, 10], [127, 12, 141, 10], [127, 16, 141, 14], [127, 17, 141, 15, "_stillWaiting"], [127, 30, 141, 28], [127, 33, 141, 31], [127, 37, 141, 35], [127, 38, 141, 36, "_getPendingGestures"], [127, 57, 141, 55], [127, 58, 141, 56], [127, 59, 141, 57], [127, 60, 141, 58], [127, 61, 141, 59], [129, 12, 143, 10], [129, 16, 143, 14], [129, 20, 143, 18], [129, 21, 143, 19, "_stillWaiting"], [129, 34, 143, 32], [129, 35, 143, 33, "length"], [129, 41, 143, 39], [129, 43, 143, 41], [130, 14, 144, 12], [131, 14, 145, 12], [132, 14, 146, 12], [132, 19, 146, 17], [132, 25, 146, 23, "gesture"], [132, 32, 146, 30], [132, 36, 146, 34], [132, 40, 146, 38], [132, 41, 146, 39, "_stillWaiting"], [132, 54, 146, 52], [132, 56, 146, 54], [133, 16, 147, 14], [134, 16, 148, 14], [134, 20, 148, 18], [134, 21, 148, 19, "gesture"], [134, 28, 148, 26], [134, 29, 148, 27, "isDiscrete"], [134, 39, 148, 37], [134, 43, 148, 41, "gesture"], [134, 50, 148, 48], [134, 51, 148, 49, "isGestureRunning"], [134, 67, 148, 65], [134, 69, 148, 67], [135, 18, 149, 16], [135, 22, 149, 20], [135, 23, 149, 21, "hasGestureFailed"], [135, 39, 149, 37], [135, 42, 149, 40], [135, 46, 149, 44], [136, 18, 150, 16], [136, 22, 150, 20], [136, 23, 150, 21, "isGestureRunning"], [136, 39, 150, 37], [136, 42, 150, 40], [136, 47, 150, 45], [137, 18, 151, 16], [137, 25, 151, 23], [137, 30, 151, 28], [138, 16, 152, 14], [139, 14, 153, 12], [139, 15, 153, 13], [139, 16, 153, 14], [141, 14, 156, 12], [141, 21, 156, 19], [141, 26, 156, 24], [142, 12, 157, 10], [143, 10, 158, 8], [143, 11, 158, 9], [143, 12, 158, 10], [145, 10, 161, 8], [145, 14, 161, 12], [145, 15, 161, 13], [145, 19, 161, 17], [145, 20, 161, 18, "hasCustomActivationCriteria"], [145, 47, 161, 45], [145, 49, 161, 47], [146, 12, 162, 10], [146, 19, 162, 17], [146, 23, 162, 21], [147, 10, 163, 8], [148, 10, 165, 8], [148, 16, 165, 14, "deltaRotation"], [148, 29, 165, 27], [148, 32, 165, 30], [148, 36, 165, 34], [148, 37, 165, 35, "initialRotation"], [148, 52, 165, 50], [148, 56, 165, 54], [148, 60, 165, 58], [148, 63, 165, 61], [148, 64, 165, 62], [148, 67, 165, 65, "inputData"], [148, 76, 165, 74], [148, 77, 165, 75, "rotation"], [148, 85, 165, 83], [148, 88, 165, 86], [148, 92, 165, 90], [148, 93, 165, 91, "initialRotation"], [148, 108, 165, 106], [148, 109, 165, 107], [148, 110, 165, 108], [150, 10, 167, 8], [150, 16, 167, 14], [151, 12, 168, 10, "success"], [151, 19, 168, 17], [152, 12, 169, 10, "failed"], [153, 10, 170, 8], [153, 11, 170, 9], [153, 14, 170, 12], [153, 18, 170, 16], [153, 19, 170, 17, "isGestureEnabledForEvent"], [153, 43, 170, 41], [153, 44, 170, 42], [153, 48, 170, 46], [153, 49, 170, 47, "getConfig"], [153, 58, 170, 56], [153, 59, 170, 57], [153, 60, 170, 58], [153, 62, 170, 60, "recognizer"], [153, 72, 170, 70], [153, 74, 170, 72], [154, 12, 170, 74], [154, 15, 170, 77, "inputData"], [154, 24, 170, 86], [155, 12, 171, 10, "deltaRotation"], [156, 10, 172, 8], [156, 11, 172, 9], [156, 12, 172, 10], [157, 10, 174, 8], [157, 14, 174, 12, "failed"], [157, 20, 174, 18], [157, 22, 174, 20], [158, 12, 175, 10], [158, 16, 175, 14], [158, 17, 175, 15, "simulateCancelEvent"], [158, 36, 175, 34], [158, 37, 175, 35, "inputData"], [158, 46, 175, 44], [158, 47, 175, 45], [159, 12, 176, 10], [159, 16, 176, 14], [159, 17, 176, 15, "hasGestureFailed"], [159, 33, 176, 31], [159, 36, 176, 34], [159, 40, 176, 38], [160, 10, 177, 8], [161, 10, 179, 8], [161, 17, 179, 15, "success"], [161, 24, 179, 22], [162, 8, 180, 6], [162, 9, 180, 7], [163, 8, 182, 6], [163, 14, 182, 12, "params"], [163, 20, 182, 18], [163, 23, 182, 21], [163, 27, 182, 25], [163, 28, 182, 26, "getHammerConfig"], [163, 43, 182, 41], [163, 44, 182, 42], [163, 45, 182, 43], [163, 46, 182, 44], [163, 47, 182, 45], [165, 8, 184, 6, "gesture"], [165, 15, 184, 13], [165, 16, 184, 14, "set"], [165, 19, 184, 17], [165, 20, 184, 18], [166, 10, 184, 20], [166, 13, 184, 23, "params"], [166, 19, 184, 29], [167, 10, 185, 8, "enable"], [168, 8, 186, 6], [168, 9, 186, 7], [168, 10, 186, 8], [169, 6, 187, 4], [169, 7, 187, 5], [169, 8, 187, 6], [170, 6, 189, 4], [170, 10, 189, 8], [170, 11, 189, 9, "gestureInstance"], [170, 26, 189, 24], [170, 29, 189, 27, "gestureInstances"], [170, 45, 189, 43], [170, 47, 189, 45], [171, 6, 190, 4], [171, 10, 190, 8], [171, 11, 190, 9, "hasCustomActivationCriteria"], [171, 38, 190, 36], [171, 41, 190, 39], [171, 46, 190, 44], [172, 4, 191, 2], [173, 4, 193, 2, "getConfig"], [173, 13, 193, 11, "getConfig"], [173, 14, 193, 11], [173, 16, 193, 14], [174, 6, 194, 4], [174, 13, 194, 11], [174, 17, 194, 15], [174, 18, 194, 16, "config"], [174, 24, 194, 22], [175, 4, 195, 2], [176, 4, 197, 2, "onWaitingEnded"], [176, 18, 197, 16, "onWaitingEnded"], [176, 19, 197, 17, "_gesture"], [176, 27, 197, 25], [176, 29, 197, 27], [176, 30, 197, 28], [177, 4, 199, 2, "removePendingGesture"], [177, 24, 199, 22, "removePendingGesture"], [177, 25, 199, 23, "id"], [177, 27, 199, 25], [177, 29, 199, 27], [178, 6, 200, 4], [178, 13, 200, 11], [178, 17, 200, 15], [178, 18, 200, 16, "pendingGestures"], [178, 33, 200, 31], [178, 34, 200, 32, "id"], [178, 36, 200, 34], [178, 37, 200, 35], [179, 4, 201, 2], [180, 4, 203, 2, "addPendingGesture"], [180, 21, 203, 19, "addPendingGesture"], [180, 22, 203, 20, "gesture"], [180, 29, 203, 27], [180, 31, 203, 29], [181, 6, 204, 4], [181, 10, 204, 8], [181, 11, 204, 9, "pendingGestures"], [181, 26, 204, 24], [181, 27, 204, 25, "gesture"], [181, 34, 204, 32], [181, 35, 204, 33, "id"], [181, 37, 204, 35], [181, 38, 204, 36], [181, 41, 204, 39, "gesture"], [181, 48, 204, 46], [182, 4, 205, 2], [183, 4, 207, 2, "isGestureEnabledForEvent"], [183, 28, 207, 26, "isGestureEnabledForEvent"], [183, 29, 207, 27, "_config"], [183, 36, 207, 34], [183, 38, 207, 36, "_recognizer"], [183, 49, 207, 47], [183, 51, 207, 49, "_event"], [183, 57, 207, 55], [183, 59, 207, 57], [184, 6, 208, 4], [184, 13, 208, 11], [185, 8, 209, 6, "success"], [185, 15, 209, 13], [185, 17, 209, 15], [186, 6, 210, 4], [186, 7, 210, 5], [187, 4, 211, 2], [188, 4, 213, 2], [188, 8, 213, 6, "NativeGestureClass"], [188, 26, 213, 24, "NativeGestureClass"], [188, 27, 213, 24], [188, 29, 213, 27], [189, 6, 214, 4], [189, 12, 214, 10], [189, 16, 214, 14, "Error"], [189, 21, 214, 19], [189, 22, 214, 20], [189, 71, 214, 69], [189, 72, 214, 70], [190, 4, 215, 2], [191, 4, 217, 2, "updateHasCustomActivationCriteria"], [191, 37, 217, 35, "updateHasCustomActivationCriteria"], [191, 38, 217, 36, "_config"], [191, 45, 217, 43], [191, 47, 217, 45], [192, 6, 218, 4], [192, 13, 218, 11], [192, 17, 218, 15], [193, 4, 219, 2], [194, 4, 221, 2, "updateGestureConfig"], [194, 23, 221, 21, "updateGestureConfig"], [194, 24, 221, 22], [195, 6, 222, 4, "enabled"], [195, 13, 222, 11], [195, 16, 222, 14], [195, 20, 222, 18], [196, 6, 223, 4], [196, 9, 223, 7, "props"], [197, 4, 224, 2], [197, 5, 224, 3], [197, 7, 224, 5], [198, 6, 225, 4], [198, 10, 225, 8], [198, 11, 225, 9, "clearSelfAsPending"], [198, 29, 225, 27], [198, 30, 225, 28], [198, 31, 225, 29], [199, 6, 226, 4], [199, 10, 226, 8], [199, 11, 226, 9, "config"], [199, 17, 226, 15], [199, 20, 226, 18], [199, 24, 226, 22], [199, 25, 226, 23, "ensureConfig"], [199, 37, 226, 35], [199, 38, 226, 36], [200, 8, 227, 6, "enabled"], [200, 15, 227, 13], [201, 8, 228, 6], [201, 11, 228, 9, "props"], [202, 6, 229, 4], [202, 7, 229, 5], [202, 8, 229, 6], [203, 6, 230, 4], [203, 10, 230, 8], [203, 11, 230, 9, "hasCustomActivationCriteria"], [203, 38, 230, 36], [203, 41, 230, 39], [203, 45, 230, 43], [203, 46, 230, 44, "updateHasCustomActivationCriteria"], [203, 79, 230, 77], [203, 80, 230, 78], [203, 84, 230, 82], [203, 85, 230, 83, "config"], [203, 91, 230, 89], [203, 92, 230, 90], [204, 6, 232, 4], [204, 10, 232, 8, "Array"], [204, 15, 232, 13], [204, 16, 232, 14, "isArray"], [204, 23, 232, 21], [204, 24, 232, 22], [204, 28, 232, 26], [204, 29, 232, 27, "config"], [204, 35, 232, 33], [204, 36, 232, 34, "waitFor"], [204, 43, 232, 41], [204, 44, 232, 42], [204, 46, 232, 44], [205, 8, 233, 6], [205, 13, 233, 11], [205, 19, 233, 17, "gesture"], [205, 26, 233, 24], [205, 30, 233, 28], [205, 34, 233, 32], [205, 35, 233, 33, "config"], [205, 41, 233, 39], [205, 42, 233, 40, "waitFor"], [205, 49, 233, 47], [205, 51, 233, 49], [206, 10, 234, 8, "gesture"], [206, 17, 234, 15], [206, 18, 234, 16, "addPendingGesture"], [206, 35, 234, 33], [206, 36, 234, 34], [206, 40, 234, 38], [206, 41, 234, 39], [207, 8, 235, 6], [208, 6, 236, 4], [209, 6, 238, 4], [209, 10, 238, 8], [209, 14, 238, 12], [209, 15, 238, 13, "hammer"], [209, 21, 238, 19], [209, 23, 238, 21], [210, 8, 239, 6], [210, 12, 239, 10], [210, 13, 239, 11, "sync"], [210, 17, 239, 15], [210, 18, 239, 16], [210, 19, 239, 17], [211, 6, 240, 4], [212, 6, 242, 4], [212, 13, 242, 11], [212, 17, 242, 15], [212, 18, 242, 16, "config"], [212, 24, 242, 22], [213, 4, 243, 2], [214, 4, 245, 2, "getState"], [214, 12, 245, 10, "getState"], [214, 13, 245, 11, "type"], [214, 17, 245, 15], [214, 19, 245, 17], [215, 6, 246, 4], [216, 6, 247, 4], [216, 10, 247, 8, "type"], [216, 14, 247, 12], [216, 18, 247, 16], [216, 19, 247, 17], [216, 21, 247, 19], [217, 8, 248, 6], [217, 15, 248, 13], [217, 16, 248, 14], [218, 6, 249, 4], [219, 6, 251, 4], [219, 13, 251, 11, "EventMap"], [219, 32, 251, 19], [219, 33, 251, 20, "type"], [219, 37, 251, 24], [219, 38, 251, 25], [220, 4, 252, 2], [221, 4, 254, 2, "transformEventData"], [221, 22, 254, 20, "transformEventData"], [221, 23, 254, 21, "event"], [221, 28, 254, 26], [221, 30, 254, 28], [222, 6, 255, 4], [222, 12, 255, 10], [223, 8, 256, 6, "eventType"], [223, 17, 256, 15], [224, 8, 257, 6, "maxPointers"], [224, 19, 257, 17], [224, 21, 257, 19, "numberOfPointers"], [225, 6, 258, 4], [225, 7, 258, 5], [225, 10, 258, 8, "event"], [225, 15, 258, 13], [225, 16, 258, 14], [225, 17, 258, 15], [227, 6, 260, 4], [227, 12, 260, 10, "changedTouch"], [227, 24, 260, 22], [227, 27, 260, 25, "event"], [227, 32, 260, 30], [227, 33, 260, 31, "changedPointers"], [227, 48, 260, 46], [227, 49, 260, 47], [227, 50, 260, 48], [227, 51, 260, 49], [228, 6, 261, 4], [228, 12, 261, 10, "pointerInside"], [228, 25, 261, 23], [228, 28, 261, 26], [228, 32, 261, 30], [228, 33, 261, 31, "isPointInView"], [228, 46, 261, 44], [228, 47, 261, 45], [229, 8, 262, 6, "x"], [229, 9, 262, 7], [229, 11, 262, 9, "changedTouch"], [229, 23, 262, 21], [229, 24, 262, 22, "clientX"], [229, 31, 262, 29], [230, 8, 263, 6, "y"], [230, 9, 263, 7], [230, 11, 263, 9, "changedTouch"], [230, 23, 263, 21], [230, 24, 263, 22, "clientY"], [231, 6, 264, 4], [231, 7, 264, 5], [231, 8, 264, 6], [231, 9, 264, 7], [231, 10, 264, 8], [233, 6, 266, 4], [233, 12, 266, 10, "state"], [233, 17, 266, 15], [233, 20, 266, 18], [233, 24, 266, 22], [233, 25, 266, 23, "getState"], [233, 33, 266, 31], [233, 34, 266, 32, "eventType"], [233, 43, 266, 41], [233, 44, 266, 42], [234, 6, 268, 4], [234, 10, 268, 8, "state"], [234, 15, 268, 13], [234, 20, 268, 18], [234, 24, 268, 22], [234, 25, 268, 23, "previousState"], [234, 38, 268, 36], [234, 40, 268, 38], [235, 8, 269, 6], [235, 12, 269, 10], [235, 13, 269, 11, "oldState"], [235, 21, 269, 19], [235, 24, 269, 22], [235, 28, 269, 26], [235, 29, 269, 27, "previousState"], [235, 42, 269, 40], [236, 8, 270, 6], [236, 12, 270, 10], [236, 13, 270, 11, "previousState"], [236, 26, 270, 24], [236, 29, 270, 27, "state"], [236, 34, 270, 32], [237, 6, 271, 4], [238, 6, 273, 4], [238, 13, 273, 11], [239, 8, 274, 6, "nativeEvent"], [239, 19, 274, 17], [239, 21, 274, 19], [240, 10, 275, 8, "numberOfPointers"], [240, 26, 275, 24], [241, 10, 276, 8, "state"], [241, 15, 276, 13], [242, 10, 277, 8, "pointerInside"], [242, 23, 277, 21], [243, 10, 278, 8], [243, 13, 278, 11], [243, 17, 278, 15], [243, 18, 278, 16, "transformNativeEvent"], [243, 38, 278, 36], [243, 39, 278, 37, "event"], [243, 44, 278, 42], [243, 45, 278, 43], [244, 10, 279, 8], [245, 10, 280, 8, "handlerTag"], [245, 20, 280, 18], [245, 22, 280, 20], [245, 26, 280, 24], [245, 27, 280, 25, "handlerTag"], [245, 37, 280, 35], [246, 10, 281, 8, "target"], [246, 16, 281, 14], [246, 18, 281, 16], [246, 22, 281, 20], [246, 23, 281, 21, "ref"], [246, 26, 281, 24], [247, 10, 282, 8], [248, 10, 283, 8], [249, 10, 284, 8], [250, 10, 285, 8, "oldState"], [250, 18, 285, 16], [250, 20, 285, 18, "state"], [250, 25, 285, 23], [250, 30, 285, 28], [250, 34, 285, 32], [250, 35, 285, 33, "previousState"], [250, 48, 285, 46], [250, 52, 285, 50, "state"], [250, 57, 285, 55], [250, 61, 285, 59], [250, 62, 285, 60], [250, 65, 285, 63], [250, 69, 285, 67], [250, 70, 285, 68, "oldState"], [250, 78, 285, 76], [250, 81, 285, 79, "undefined"], [251, 8, 286, 6], [251, 9, 286, 7], [252, 8, 287, 6, "timeStamp"], [252, 17, 287, 15], [252, 19, 287, 17, "Date"], [252, 23, 287, 21], [252, 24, 287, 22, "now"], [252, 27, 287, 25], [252, 28, 287, 26], [253, 6, 288, 4], [253, 7, 288, 5], [254, 4, 289, 2], [255, 4, 291, 2, "transformNativeEvent"], [255, 24, 291, 22, "transformNativeEvent"], [255, 25, 291, 23, "_event"], [255, 31, 291, 29], [255, 33, 291, 31], [256, 6, 292, 4], [256, 13, 292, 11], [256, 14, 292, 12], [256, 15, 292, 13], [257, 4, 293, 2], [258, 4, 295, 2, "cancelPendingGestures"], [258, 25, 295, 23, "cancelPendingGestures"], [258, 26, 295, 24, "event"], [258, 31, 295, 29], [258, 33, 295, 31], [259, 6, 296, 4], [259, 11, 296, 9], [259, 17, 296, 15, "gesture"], [259, 24, 296, 22], [259, 28, 296, 26, "Object"], [259, 34, 296, 32], [259, 35, 296, 33, "values"], [259, 41, 296, 39], [259, 42, 296, 40], [259, 46, 296, 44], [259, 47, 296, 45, "pendingGestures"], [259, 62, 296, 60], [259, 63, 296, 61], [259, 65, 296, 63], [260, 8, 297, 6], [260, 12, 297, 10, "gesture"], [260, 19, 297, 17], [260, 23, 297, 21, "gesture"], [260, 30, 297, 28], [260, 31, 297, 29, "isGestureRunning"], [260, 47, 297, 45], [260, 49, 297, 47], [261, 10, 298, 8, "gesture"], [261, 17, 298, 15], [261, 18, 298, 16, "hasGestureFailed"], [261, 34, 298, 32], [261, 37, 298, 35], [261, 41, 298, 39], [262, 10, 299, 8, "gesture"], [262, 17, 299, 15], [262, 18, 299, 16, "cancelEvent"], [262, 29, 299, 27], [262, 30, 299, 28, "event"], [262, 35, 299, 33], [262, 36, 299, 34], [263, 8, 300, 6], [264, 6, 301, 4], [265, 4, 302, 2], [266, 4, 304, 2, "notifyPendingGestures"], [266, 25, 304, 23, "notifyPendingGestures"], [266, 26, 304, 23], [266, 28, 304, 26], [267, 6, 305, 4], [267, 11, 305, 9], [267, 17, 305, 15, "gesture"], [267, 24, 305, 22], [267, 28, 305, 26, "Object"], [267, 34, 305, 32], [267, 35, 305, 33, "values"], [267, 41, 305, 39], [267, 42, 305, 40], [267, 46, 305, 44], [267, 47, 305, 45, "pendingGestures"], [267, 62, 305, 60], [267, 63, 305, 61], [267, 65, 305, 63], [268, 8, 306, 6], [268, 12, 306, 10, "gesture"], [268, 19, 306, 17], [268, 21, 306, 19], [269, 10, 307, 8, "gesture"], [269, 17, 307, 15], [269, 18, 307, 16, "onWaitingEnded"], [269, 32, 307, 30], [269, 33, 307, 31], [269, 37, 307, 35], [269, 38, 307, 36], [270, 8, 308, 6], [271, 6, 309, 4], [272, 4, 310, 2], [272, 5, 310, 3], [272, 6, 310, 4], [274, 4, 313, 2, "onGestureEnded"], [274, 18, 313, 16, "onGestureEnded"], [274, 19, 313, 17, "event"], [274, 24, 313, 22], [274, 26, 313, 24], [275, 6, 314, 4], [275, 10, 314, 8], [275, 11, 314, 9, "isGestureRunning"], [275, 27, 314, 25], [275, 30, 314, 28], [275, 35, 314, 33], [276, 6, 315, 4], [276, 10, 315, 8], [276, 11, 315, 9, "cancelPendingGestures"], [276, 32, 315, 30], [276, 33, 315, 31, "event"], [276, 38, 315, 36], [276, 39, 315, 37], [277, 4, 316, 2], [278, 4, 318, 2, "forceInvalidate"], [278, 19, 318, 17, "forceInvalidate"], [278, 20, 318, 18, "event"], [278, 25, 318, 23], [278, 27, 318, 25], [279, 6, 319, 4], [279, 10, 319, 8], [279, 14, 319, 12], [279, 15, 319, 13, "isGestureRunning"], [279, 31, 319, 29], [279, 33, 319, 31], [280, 8, 320, 6], [280, 12, 320, 10], [280, 13, 320, 11, "hasGestureFailed"], [280, 29, 320, 27], [280, 32, 320, 30], [280, 36, 320, 34], [281, 8, 321, 6], [281, 12, 321, 10], [281, 13, 321, 11, "cancelEvent"], [281, 24, 321, 22], [281, 25, 321, 23, "event"], [281, 30, 321, 28], [281, 31, 321, 29], [282, 6, 322, 4], [283, 4, 323, 2], [284, 4, 325, 2, "cancelEvent"], [284, 15, 325, 13, "cancelEvent"], [284, 16, 325, 14, "event"], [284, 21, 325, 19], [284, 23, 325, 21], [285, 6, 326, 4], [285, 10, 326, 8], [285, 11, 326, 9, "notifyPendingGestures"], [285, 32, 326, 30], [285, 33, 326, 31], [285, 34, 326, 32], [286, 6, 327, 4], [286, 10, 327, 8], [286, 11, 327, 9, "sendEvent"], [286, 20, 327, 18], [286, 21, 327, 19], [287, 8, 327, 21], [287, 11, 327, 24, "event"], [287, 16, 327, 29], [288, 8, 328, 6, "eventType"], [288, 17, 328, 15], [288, 19, 328, 17, "Hammer"], [288, 36, 328, 23], [288, 37, 328, 24, "INPUT_CANCEL"], [288, 49, 328, 36], [289, 8, 329, 6, "isFinal"], [289, 15, 329, 13], [289, 17, 329, 15], [290, 6, 330, 4], [290, 7, 330, 5], [290, 8, 330, 6], [291, 6, 331, 4], [291, 10, 331, 8], [291, 11, 331, 9, "onGestureEnded"], [291, 25, 331, 23], [291, 26, 331, 24, "event"], [291, 31, 331, 29], [291, 32, 331, 30], [292, 4, 332, 2], [293, 4, 334, 2, "onRawEvent"], [293, 14, 334, 12, "onRawEvent"], [293, 15, 334, 13], [294, 6, 335, 4, "<PERSON><PERSON><PERSON><PERSON>"], [295, 4, 336, 2], [295, 5, 336, 3], [295, 7, 336, 5], [296, 6, 337, 4], [296, 10, 337, 8, "<PERSON><PERSON><PERSON><PERSON>"], [296, 17, 337, 15], [296, 19, 337, 17], [297, 8, 338, 6], [297, 12, 338, 10], [297, 13, 338, 11, "hasGestureFailed"], [297, 29, 338, 27], [297, 32, 338, 30], [297, 37, 338, 35], [298, 6, 339, 4], [299, 4, 340, 2], [300, 4, 342, 2, "shouldUseTouchEvents"], [300, 24, 342, 22, "shouldUseTouchEvents"], [300, 25, 342, 23, "config"], [300, 31, 342, 29], [300, 33, 342, 31], [301, 6, 343, 4], [301, 10, 343, 8, "_config$simultaneousH"], [301, 31, 343, 29], [301, 33, 343, 31, "_config$simultaneousH2"], [301, 55, 343, 53], [302, 6, 345, 4], [302, 13, 345, 11], [302, 14, 345, 12, "_config$simultaneousH"], [302, 35, 345, 33], [302, 38, 345, 36], [302, 39, 345, 37, "_config$simultaneousH2"], [302, 61, 345, 59], [302, 64, 345, 62, "config"], [302, 70, 345, 68], [302, 71, 345, 69, "simultaneousHandlers"], [302, 91, 345, 89], [302, 97, 345, 95], [302, 101, 345, 99], [302, 105, 345, 103, "_config$simultaneousH2"], [302, 127, 345, 125], [302, 132, 345, 130], [302, 137, 345, 135], [302, 138, 345, 136], [302, 141, 345, 139], [302, 146, 345, 144], [302, 147, 345, 145], [302, 150, 345, 148, "_config$simultaneousH2"], [302, 172, 345, 170], [302, 173, 345, 171, "some"], [302, 177, 345, 175], [302, 178, 345, 176, "handler"], [302, 185, 345, 183], [302, 189, 345, 187, "handler"], [302, 196, 345, 194], [302, 197, 345, 195, "isNative"], [302, 205, 345, 203], [302, 206, 345, 204], [302, 212, 345, 210], [302, 216, 345, 214], [302, 220, 345, 218, "_config$simultaneousH"], [302, 241, 345, 239], [302, 246, 345, 244], [302, 251, 345, 249], [302, 252, 345, 250], [302, 255, 345, 253, "_config$simultaneousH"], [302, 276, 345, 274], [302, 279, 345, 277], [302, 284, 345, 282], [303, 4, 346, 2], [304, 4, 348, 2, "<PERSON><PERSON><PERSON><PERSON>"], [304, 11, 348, 9, "<PERSON><PERSON><PERSON><PERSON>"], [304, 12, 348, 10, "ref"], [304, 15, 348, 13], [304, 17, 348, 15, "propsRef"], [304, 25, 348, 23], [304, 27, 348, 25], [305, 6, 349, 4], [305, 10, 349, 8, "ref"], [305, 13, 349, 11], [305, 17, 349, 15], [305, 21, 349, 19], [305, 23, 349, 21], [306, 8, 350, 6], [306, 12, 350, 10], [306, 13, 350, 11, "destroy"], [306, 20, 350, 18], [306, 21, 350, 19], [306, 22, 350, 20], [307, 8, 351, 6], [307, 12, 351, 10], [307, 13, 351, 11, "view"], [307, 17, 351, 15], [307, 20, 351, 18], [307, 24, 351, 22], [308, 8, 352, 6], [309, 6, 353, 4], [309, 7, 353, 5], [309, 8, 353, 6], [311, 6, 356, 4], [311, 12, 356, 10, "SUPPORTS_TOUCH"], [311, 26, 356, 24], [311, 29, 356, 28], [311, 43, 356, 42], [311, 47, 356, 46, "window"], [311, 53, 356, 53], [312, 6, 357, 4], [312, 10, 357, 8], [312, 11, 357, 9, "propsRef"], [312, 19, 357, 17], [312, 22, 357, 20, "propsRef"], [312, 30, 357, 28], [313, 6, 358, 4], [313, 10, 358, 8], [313, 11, 358, 9, "ref"], [313, 14, 358, 12], [313, 17, 358, 15, "ref"], [313, 20, 358, 18], [314, 6, 359, 4], [314, 10, 359, 8], [314, 11, 359, 9, "view"], [314, 15, 359, 13], [314, 18, 359, 16], [314, 22, 359, 16, "findNodeHandle"], [314, 45, 359, 30], [314, 47, 359, 31, "ref"], [314, 50, 359, 34], [314, 51, 359, 35], [314, 52, 359, 36], [314, 53, 359, 37], [315, 6, 360, 4], [316, 6, 361, 4], [318, 6, 363, 4], [318, 10, 363, 8], [318, 11, 363, 9, "hammer"], [318, 17, 363, 15], [318, 20, 363, 18, "SUPPORTS_TOUCH"], [318, 34, 363, 32], [318, 38, 363, 36], [318, 42, 363, 40], [318, 43, 363, 41, "shouldUseTouchEvents"], [318, 63, 363, 61], [318, 64, 363, 62], [318, 68, 363, 66], [318, 69, 363, 67, "config"], [318, 75, 363, 73], [318, 76, 363, 74], [318, 79, 363, 77], [318, 83, 363, 81, "Hammer"], [318, 100, 363, 87], [318, 101, 363, 88, "Manager"], [318, 108, 363, 95], [318, 109, 363, 96], [318, 113, 363, 100], [318, 114, 363, 101, "view"], [318, 118, 363, 105], [318, 120, 363, 107], [319, 8, 364, 6, "inputClass"], [319, 18, 364, 16], [319, 20, 364, 18, "Hammer"], [319, 37, 364, 24], [319, 38, 364, 25, "TouchInput"], [320, 6, 365, 4], [320, 7, 365, 5], [320, 8, 365, 6], [320, 11, 365, 9], [320, 15, 365, 13, "Hammer"], [320, 32, 365, 19], [320, 33, 365, 20, "Manager"], [320, 40, 365, 27], [320, 41, 365, 28], [320, 45, 365, 32], [320, 46, 365, 33, "view"], [320, 50, 365, 37], [320, 51, 365, 38], [321, 6, 366, 4], [321, 10, 366, 8], [321, 11, 366, 9, "oldState"], [321, 19, 366, 17], [321, 22, 366, 20, "State"], [321, 34, 366, 25], [321, 35, 366, 26, "UNDETERMINED"], [321, 47, 366, 38], [322, 6, 367, 4], [322, 10, 367, 8], [322, 11, 367, 9, "previousState"], [322, 24, 367, 22], [322, 27, 367, 25, "State"], [322, 39, 367, 30], [322, 40, 367, 31, "UNDETERMINED"], [322, 52, 367, 43], [323, 6, 368, 4], [323, 10, 368, 8], [323, 11, 368, 9, "lastSentState"], [323, 24, 368, 22], [323, 27, 368, 25], [323, 31, 368, 29], [324, 6, 369, 4], [324, 12, 369, 10], [325, 8, 370, 6, "NativeGestureClass"], [326, 6, 371, 4], [326, 7, 371, 5], [326, 10, 371, 8], [326, 14, 371, 12], [326, 15, 371, 13], [326, 16, 371, 14], [328, 6, 373, 4], [328, 12, 373, 10, "gesture"], [328, 19, 373, 17], [328, 22, 373, 20], [328, 26, 373, 24, "NativeGestureClass"], [328, 44, 373, 42], [328, 45, 373, 43], [328, 49, 373, 47], [328, 50, 373, 48, "getHammerConfig"], [328, 65, 373, 63], [328, 66, 373, 64], [328, 67, 373, 65], [328, 68, 373, 66], [329, 6, 374, 4], [329, 10, 374, 8], [329, 11, 374, 9, "hammer"], [329, 17, 374, 15], [329, 18, 374, 16, "add"], [329, 21, 374, 19], [329, 22, 374, 20, "gesture"], [329, 29, 374, 27], [329, 30, 374, 28], [330, 6, 375, 4], [330, 10, 375, 8], [330, 11, 375, 9, "hammer"], [330, 17, 375, 15], [330, 18, 375, 16, "on"], [330, 20, 375, 18], [330, 21, 375, 19], [330, 35, 375, 33], [330, 37, 375, 35, "ev"], [330, 39, 375, 37], [330, 43, 375, 41], [331, 8, 376, 6], [331, 12, 376, 10], [331, 13, 376, 11], [331, 17, 376, 15], [331, 18, 376, 16, "config"], [331, 24, 376, 22], [331, 25, 376, 23, "enabled"], [331, 32, 376, 30], [331, 34, 376, 32], [332, 10, 377, 8], [332, 14, 377, 12], [332, 15, 377, 13, "hasGestureFailed"], [332, 31, 377, 29], [332, 34, 377, 32], [332, 39, 377, 37], [333, 10, 378, 8], [333, 14, 378, 12], [333, 15, 378, 13, "isGestureRunning"], [333, 31, 378, 29], [333, 34, 378, 32], [333, 39, 378, 37], [334, 10, 379, 8], [335, 8, 380, 6], [336, 8, 382, 6], [336, 12, 382, 10], [336, 13, 382, 11, "onRawEvent"], [336, 23, 382, 21], [336, 24, 382, 22, "ev"], [336, 26, 382, 24], [336, 27, 382, 25], [336, 28, 382, 26], [336, 29, 382, 27], [337, 8, 383, 6], [339, 8, 385, 6], [339, 12, 385, 10], [339, 16, 385, 14], [339, 17, 385, 15, "initialRotation"], [339, 32, 385, 30], [339, 37, 385, 35], [339, 41, 385, 39], [339, 45, 385, 43, "ev"], [339, 47, 385, 45], [339, 48, 385, 46, "rotation"], [339, 56, 385, 54], [339, 61, 385, 59], [339, 62, 385, 60], [339, 64, 385, 62], [340, 10, 386, 8], [340, 14, 386, 12], [340, 15, 386, 13, "initialRotation"], [340, 30, 386, 28], [340, 33, 386, 31, "ev"], [340, 35, 386, 33], [340, 36, 386, 34, "rotation"], [340, 44, 386, 42], [341, 8, 387, 6], [342, 8, 389, 6], [342, 12, 389, 10, "ev"], [342, 14, 389, 12], [342, 15, 389, 13, "isFinal"], [342, 22, 389, 20], [342, 24, 389, 22], [343, 10, 390, 8], [344, 10, 391, 8, "setTimeout"], [344, 20, 391, 18], [344, 21, 391, 19], [344, 27, 391, 25], [345, 12, 392, 10], [345, 16, 392, 14], [345, 17, 392, 15, "initialRotation"], [345, 32, 392, 30], [345, 35, 392, 33], [345, 39, 392, 37], [346, 12, 393, 10], [346, 16, 393, 14], [346, 17, 393, 15, "hasGestureFailed"], [346, 33, 393, 31], [346, 36, 393, 34], [346, 41, 393, 39], [347, 10, 394, 8], [347, 11, 394, 9], [347, 12, 394, 10], [348, 8, 395, 6], [349, 6, 396, 4], [349, 7, 396, 5], [349, 8, 396, 6], [350, 6, 397, 4], [350, 10, 397, 8], [350, 11, 397, 9, "setupEvents"], [350, 22, 397, 20], [350, 23, 397, 21], [350, 24, 397, 22], [351, 6, 398, 4], [351, 10, 398, 8], [351, 11, 398, 9, "sync"], [351, 15, 398, 13], [351, 16, 398, 14], [351, 17, 398, 15], [352, 4, 399, 2], [353, 4, 401, 2, "setupEvents"], [353, 15, 401, 13, "setupEvents"], [353, 16, 401, 13], [353, 18, 401, 16], [354, 6, 402, 4], [355, 6, 403, 4], [355, 10, 403, 8], [355, 11, 403, 9], [355, 15, 403, 13], [355, 16, 403, 14, "isDiscrete"], [355, 26, 403, 24], [355, 28, 403, 26], [356, 8, 404, 6], [356, 12, 404, 10], [356, 13, 404, 11, "hammer"], [356, 19, 404, 17], [356, 20, 404, 18, "on"], [356, 22, 404, 20], [356, 23, 404, 21], [356, 26, 404, 24], [356, 30, 404, 28], [356, 31, 404, 29, "name"], [356, 35, 404, 33], [356, 42, 404, 40], [356, 44, 404, 42, "event"], [356, 49, 404, 47], [356, 53, 404, 51], [356, 57, 404, 55], [356, 58, 404, 56, "onStart"], [356, 65, 404, 63], [356, 66, 404, 64, "event"], [356, 71, 404, 69], [356, 72, 404, 70], [356, 73, 404, 71], [357, 8, 405, 6], [357, 12, 405, 10], [357, 13, 405, 11, "hammer"], [357, 19, 405, 17], [357, 20, 405, 18, "on"], [357, 22, 405, 20], [357, 23, 405, 21], [357, 26, 405, 24], [357, 30, 405, 28], [357, 31, 405, 29, "name"], [357, 35, 405, 33], [357, 42, 405, 40], [357, 46, 405, 44], [357, 47, 405, 45, "name"], [357, 51, 405, 49], [357, 59, 405, 57], [357, 61, 405, 59, "event"], [357, 66, 405, 64], [357, 70, 405, 68], [358, 10, 406, 8], [358, 14, 406, 12], [358, 15, 406, 13, "onGestureEnded"], [358, 29, 406, 27], [358, 30, 406, 28, "event"], [358, 35, 406, 33], [358, 36, 406, 34], [359, 8, 407, 6], [359, 9, 407, 7], [359, 10, 407, 8], [360, 6, 408, 4], [361, 6, 410, 4], [361, 10, 410, 8], [361, 11, 410, 9, "hammer"], [361, 17, 410, 15], [361, 18, 410, 16, "on"], [361, 20, 410, 18], [361, 21, 410, 19], [361, 25, 410, 23], [361, 26, 410, 24, "name"], [361, 30, 410, 28], [361, 32, 410, 30, "ev"], [361, 34, 410, 32], [361, 38, 410, 36], [361, 42, 410, 40], [361, 43, 410, 41, "onGestureActivated"], [361, 61, 410, 59], [361, 62, 410, 60, "ev"], [361, 64, 410, 62], [361, 65, 410, 63], [361, 66, 410, 64], [361, 67, 410, 65], [361, 68, 410, 66], [362, 4, 411, 2], [363, 4, 413, 2, "onStart"], [363, 11, 413, 9, "onStart"], [363, 12, 413, 10], [364, 6, 414, 4, "deltaX"], [364, 12, 414, 10], [365, 6, 415, 4, "deltaY"], [365, 12, 415, 10], [366, 6, 416, 4, "rotation"], [367, 4, 417, 2], [367, 5, 417, 3], [367, 7, 417, 5], [368, 6, 418, 4], [369, 6, 419, 4], [369, 10, 419, 8], [369, 11, 419, 9, "oldState"], [369, 19, 419, 17], [369, 22, 419, 20, "State"], [369, 34, 419, 25], [369, 35, 419, 26, "UNDETERMINED"], [369, 47, 419, 38], [370, 6, 420, 4], [370, 10, 420, 8], [370, 11, 420, 9, "previousState"], [370, 24, 420, 22], [370, 27, 420, 25, "State"], [370, 39, 420, 30], [370, 40, 420, 31, "UNDETERMINED"], [370, 52, 420, 43], [371, 6, 421, 4], [371, 10, 421, 8], [371, 11, 421, 9, "lastSentState"], [371, 24, 421, 22], [371, 27, 421, 25], [371, 31, 421, 29], [372, 6, 422, 4], [372, 10, 422, 8], [372, 11, 422, 9, "isGestureRunning"], [372, 27, 422, 25], [372, 30, 422, 28], [372, 34, 422, 32], [373, 6, 423, 4], [373, 10, 423, 8], [373, 11, 423, 9, "__initialX"], [373, 21, 423, 19], [373, 24, 423, 22, "deltaX"], [373, 30, 423, 28], [374, 6, 424, 4], [374, 10, 424, 8], [374, 11, 424, 9, "__initialY"], [374, 21, 424, 19], [374, 24, 424, 22, "deltaY"], [374, 30, 424, 28], [375, 6, 425, 4], [375, 10, 425, 8], [375, 11, 425, 9, "initialRotation"], [375, 26, 425, 24], [375, 29, 425, 27, "rotation"], [375, 37, 425, 35], [376, 4, 426, 2], [377, 4, 428, 2, "onGestureActivated"], [377, 22, 428, 20, "onGestureActivated"], [377, 23, 428, 21, "ev"], [377, 25, 428, 23], [377, 27, 428, 25], [378, 6, 429, 4], [378, 10, 429, 8], [378, 11, 429, 9, "sendEvent"], [378, 20, 429, 18], [378, 21, 429, 19, "ev"], [378, 23, 429, 21], [378, 24, 429, 22], [379, 4, 430, 2], [380, 4, 432, 2, "onSuccess"], [380, 13, 432, 11, "onSuccess"], [380, 14, 432, 11], [380, 16, 432, 14], [380, 17, 432, 15], [381, 4, 434, 2, "_getPendingGestures"], [381, 23, 434, 21, "_getPendingGestures"], [381, 24, 434, 21], [381, 26, 434, 24], [382, 6, 435, 4], [382, 10, 435, 8, "Array"], [382, 15, 435, 13], [382, 16, 435, 14, "isArray"], [382, 23, 435, 21], [382, 24, 435, 22], [382, 28, 435, 26], [382, 29, 435, 27, "config"], [382, 35, 435, 33], [382, 36, 435, 34, "waitFor"], [382, 43, 435, 41], [382, 44, 435, 42], [382, 48, 435, 46], [382, 52, 435, 50], [382, 53, 435, 51, "config"], [382, 59, 435, 57], [382, 60, 435, 58, "waitFor"], [382, 67, 435, 65], [382, 68, 435, 66, "length"], [382, 74, 435, 72], [382, 76, 435, 74], [383, 8, 436, 6], [384, 8, 437, 6], [385, 8, 438, 6], [385, 14, 438, 12, "stillWaiting"], [385, 26, 438, 24], [385, 29, 438, 27], [385, 33, 438, 31], [385, 34, 438, 32, "config"], [385, 40, 438, 38], [385, 41, 438, 39, "waitFor"], [385, 48, 438, 46], [385, 49, 438, 47, "filter"], [385, 55, 438, 53], [385, 56, 438, 54], [385, 57, 438, 55], [386, 10, 439, 8, "hasGestureFailed"], [387, 8, 440, 6], [387, 9, 440, 7], [387, 14, 440, 12, "hasGestureFailed"], [387, 30, 440, 28], [387, 35, 440, 33], [387, 40, 440, 38], [387, 41, 440, 39], [388, 8, 441, 6], [388, 15, 441, 13, "stillWaiting"], [388, 27, 441, 25], [389, 6, 442, 4], [390, 6, 444, 4], [390, 13, 444, 11], [390, 15, 444, 13], [391, 4, 445, 2], [392, 4, 447, 2, "getHammerConfig"], [392, 19, 447, 17, "getHammerConfig"], [392, 20, 447, 17], [392, 22, 447, 20], [393, 6, 448, 4], [393, 12, 448, 10, "pointers"], [393, 20, 448, 18], [393, 23, 448, 21], [393, 27, 448, 25], [393, 28, 448, 26, "config"], [393, 34, 448, 32], [393, 35, 448, 33, "minPointers"], [393, 46, 448, 44], [393, 51, 448, 49], [393, 55, 448, 53], [393, 56, 448, 54, "config"], [393, 62, 448, 60], [393, 63, 448, 61, "maxPointers"], [393, 74, 448, 72], [393, 77, 448, 75], [393, 81, 448, 79], [393, 82, 448, 80, "config"], [393, 88, 448, 86], [393, 89, 448, 87, "minPointers"], [393, 100, 448, 98], [393, 103, 448, 101], [393, 104, 448, 102], [394, 6, 449, 4], [394, 13, 449, 11], [395, 8, 450, 6, "pointers"], [396, 6, 451, 4], [396, 7, 451, 5], [397, 4, 452, 2], [398, 4, 454, 2, "simulateCancelEvent"], [398, 23, 454, 21, "simulateCancelEvent"], [398, 24, 454, 22, "_inputData"], [398, 34, 454, 32], [398, 36, 454, 34], [398, 37, 454, 35], [398, 38, 454, 36], [398, 39, 454, 37], [400, 4, 457, 2, "ensureConfig"], [400, 16, 457, 14, "ensureConfig"], [400, 17, 457, 15, "config"], [400, 23, 457, 21], [400, 25, 457, 23], [401, 6, 458, 4], [401, 12, 458, 10, "props"], [401, 17, 458, 15], [401, 20, 458, 18], [402, 8, 458, 20], [402, 11, 458, 23, "config"], [403, 6, 459, 4], [403, 7, 459, 5], [403, 8, 459, 6], [403, 9, 459, 7], [405, 6, 461, 4], [405, 10, 461, 8], [405, 19, 461, 17], [405, 23, 461, 21, "config"], [405, 29, 461, 27], [405, 31, 461, 29], [406, 8, 462, 6, "props"], [406, 13, 462, 11], [406, 14, 462, 12, "minDist"], [406, 21, 462, 19], [406, 24, 462, 22, "config"], [406, 30, 462, 28], [406, 31, 462, 29, "minDist"], [406, 38, 462, 36], [407, 8, 463, 6, "props"], [407, 13, 463, 11], [407, 14, 463, 12, "minDistSq"], [407, 23, 463, 21], [407, 26, 463, 24, "props"], [407, 31, 463, 29], [407, 32, 463, 30, "minDist"], [407, 39, 463, 37], [407, 42, 463, 40, "props"], [407, 47, 463, 45], [407, 48, 463, 46, "minDist"], [407, 55, 463, 53], [408, 6, 464, 4], [409, 6, 466, 4], [409, 10, 466, 8], [409, 23, 466, 21], [409, 27, 466, 25, "config"], [409, 33, 466, 31], [409, 35, 466, 33], [410, 8, 467, 6, "props"], [410, 13, 467, 11], [410, 14, 467, 12, "minVelocity"], [410, 25, 467, 23], [410, 28, 467, 26, "config"], [410, 34, 467, 32], [410, 35, 467, 33, "minVelocity"], [410, 46, 467, 44], [411, 8, 468, 6, "props"], [411, 13, 468, 11], [411, 14, 468, 12, "minVelocitySq"], [411, 27, 468, 25], [411, 30, 468, 28, "props"], [411, 35, 468, 33], [411, 36, 468, 34, "minVelocity"], [411, 47, 468, 45], [411, 50, 468, 48, "props"], [411, 55, 468, 53], [411, 56, 468, 54, "minVelocity"], [411, 67, 468, 65], [412, 6, 469, 4], [413, 6, 471, 4], [413, 10, 471, 8], [413, 19, 471, 17], [413, 23, 471, 21, "config"], [413, 29, 471, 27], [413, 31, 471, 29], [414, 8, 472, 6, "props"], [414, 13, 472, 11], [414, 14, 472, 12, "maxDist"], [414, 21, 472, 19], [414, 24, 472, 22, "config"], [414, 30, 472, 28], [414, 31, 472, 29, "maxDist"], [414, 38, 472, 36], [415, 8, 473, 6, "props"], [415, 13, 473, 11], [415, 14, 473, 12, "maxDistSq"], [415, 23, 473, 21], [415, 26, 473, 24, "config"], [415, 32, 473, 30], [415, 33, 473, 31, "maxDist"], [415, 40, 473, 38], [415, 43, 473, 41, "config"], [415, 49, 473, 47], [415, 50, 473, 48, "maxDist"], [415, 57, 473, 55], [416, 6, 474, 4], [417, 6, 476, 4], [417, 10, 476, 8], [417, 19, 476, 17], [417, 23, 476, 21, "config"], [417, 29, 476, 27], [417, 31, 476, 29], [418, 8, 477, 6, "props"], [418, 13, 477, 11], [418, 14, 477, 12, "waitFor"], [418, 21, 477, 19], [418, 24, 477, 22, "asArray"], [418, 31, 477, 29], [418, 32, 477, 30, "config"], [418, 38, 477, 36], [418, 39, 477, 37, "waitFor"], [418, 46, 477, 44], [418, 47, 477, 45], [418, 48, 477, 46, "map"], [418, 51, 477, 49], [418, 52, 477, 50], [418, 53, 477, 51], [419, 10, 478, 8, "handlerTag"], [420, 8, 479, 6], [420, 9, 479, 7], [420, 14, 479, 12, "NodeManager"], [420, 25, 479, 23], [420, 26, 479, 24, "<PERSON><PERSON><PERSON><PERSON>"], [420, 36, 479, 34], [420, 37, 479, 35, "handlerTag"], [420, 47, 479, 45], [420, 48, 479, 46], [420, 49, 479, 47], [420, 50, 479, 48, "filter"], [420, 56, 479, 54], [420, 57, 479, 55, "v"], [420, 58, 479, 56], [420, 62, 479, 60, "v"], [420, 63, 479, 61], [420, 64, 479, 62], [421, 6, 480, 4], [421, 7, 480, 5], [421, 13, 480, 11], [422, 8, 481, 6, "props"], [422, 13, 481, 11], [422, 14, 481, 12, "waitFor"], [422, 21, 481, 19], [422, 24, 481, 22], [422, 28, 481, 26], [423, 6, 482, 4], [424, 6, 484, 4], [424, 10, 484, 8], [424, 32, 484, 30], [424, 36, 484, 34, "config"], [424, 42, 484, 40], [424, 44, 484, 42], [425, 8, 485, 6], [425, 14, 485, 12, "shouldUseTouchEvents"], [425, 34, 485, 32], [425, 37, 485, 35], [425, 41, 485, 39], [425, 42, 485, 40, "shouldUseTouchEvents"], [425, 62, 485, 60], [425, 63, 485, 61], [425, 67, 485, 65], [425, 68, 485, 66, "config"], [425, 74, 485, 72], [425, 75, 485, 73], [426, 8, 486, 6, "props"], [426, 13, 486, 11], [426, 14, 486, 12, "simultaneousHandlers"], [426, 34, 486, 32], [426, 37, 486, 35, "asArray"], [426, 44, 486, 42], [426, 45, 486, 43, "config"], [426, 51, 486, 49], [426, 52, 486, 50, "simultaneousHandlers"], [426, 72, 486, 70], [426, 73, 486, 71], [426, 74, 486, 72, "map"], [426, 77, 486, 75], [426, 78, 486, 76, "handler"], [426, 85, 486, 83], [426, 89, 486, 87], [427, 10, 487, 8], [427, 14, 487, 12], [427, 21, 487, 19, "handler"], [427, 28, 487, 26], [427, 33, 487, 31], [427, 41, 487, 39], [427, 43, 487, 41], [428, 12, 488, 10], [428, 19, 488, 17, "NodeManager"], [428, 30, 488, 28], [428, 31, 488, 29, "<PERSON><PERSON><PERSON><PERSON>"], [428, 41, 488, 39], [428, 42, 488, 40, "handler"], [428, 49, 488, 47], [428, 50, 488, 48], [429, 10, 489, 8], [429, 11, 489, 9], [429, 17, 489, 15], [430, 12, 490, 10], [430, 19, 490, 17, "NodeManager"], [430, 30, 490, 28], [430, 31, 490, 29, "<PERSON><PERSON><PERSON><PERSON>"], [430, 41, 490, 39], [430, 42, 490, 40, "handler"], [430, 49, 490, 47], [430, 50, 490, 48, "handlerTag"], [430, 60, 490, 58], [430, 61, 490, 59], [431, 10, 491, 8], [432, 8, 492, 6], [432, 9, 492, 7], [432, 10, 492, 8], [432, 11, 492, 9, "filter"], [432, 17, 492, 15], [432, 18, 492, 16, "v"], [432, 19, 492, 17], [432, 23, 492, 21, "v"], [432, 24, 492, 22], [432, 25, 492, 23], [433, 8, 494, 6], [433, 12, 494, 10, "shouldUseTouchEvents"], [433, 32, 494, 30], [433, 37, 494, 35], [433, 41, 494, 39], [433, 42, 494, 40, "shouldUseTouchEvents"], [433, 62, 494, 60], [433, 63, 494, 61, "props"], [433, 68, 494, 66], [433, 69, 494, 67], [433, 71, 494, 69], [434, 10, 495, 8], [434, 14, 495, 8, "ghQueueMicrotask"], [434, 48, 495, 24], [434, 50, 495, 25], [434, 56, 495, 31], [435, 12, 496, 10], [436, 12, 497, 10], [437, 12, 498, 10], [437, 16, 498, 14], [437, 17, 498, 15, "destroy"], [437, 24, 498, 22], [437, 25, 498, 23], [437, 26, 498, 24], [438, 12, 499, 10], [438, 16, 499, 14], [438, 17, 499, 15, "<PERSON><PERSON><PERSON><PERSON>"], [438, 24, 499, 22], [438, 25, 499, 23], [438, 29, 499, 27], [438, 30, 499, 28, "ref"], [438, 33, 499, 31], [438, 35, 499, 33], [438, 39, 499, 37], [438, 40, 499, 38, "propsRef"], [438, 48, 499, 46], [438, 49, 499, 47], [439, 10, 500, 8], [439, 11, 500, 9], [439, 12, 500, 10], [440, 8, 501, 6], [441, 6, 502, 4], [441, 7, 502, 5], [441, 13, 502, 11], [442, 8, 503, 6, "props"], [442, 13, 503, 11], [442, 14, 503, 12, "simultaneousHandlers"], [442, 34, 503, 32], [442, 37, 503, 35], [442, 41, 503, 39], [443, 6, 504, 4], [444, 6, 506, 4], [444, 12, 506, 10, "configProps"], [444, 23, 506, 21], [444, 26, 506, 24], [444, 27, 506, 25], [444, 40, 506, 38], [444, 42, 506, 40], [444, 55, 506, 53], [444, 57, 506, 55], [444, 66, 506, 64], [444, 68, 506, 66], [444, 77, 506, 75], [444, 79, 506, 77], [444, 90, 506, 88], [444, 92, 506, 90], [444, 107, 506, 105], [444, 109, 506, 107], [444, 120, 506, 118], [444, 122, 506, 120], [444, 135, 506, 133], [444, 137, 506, 135], [444, 155, 506, 153], [444, 157, 506, 155], [444, 175, 506, 173], [444, 177, 506, 175], [444, 193, 506, 191], [444, 195, 506, 193], [444, 211, 506, 209], [444, 213, 506, 211], [444, 233, 506, 231], [444, 235, 506, 233], [444, 253, 506, 251], [444, 255, 506, 253], [444, 275, 506, 273], [444, 277, 506, 275], [444, 295, 506, 293], [444, 296, 506, 294], [445, 6, 507, 4, "configProps"], [445, 17, 507, 15], [445, 18, 507, 16, "for<PERSON>ach"], [445, 25, 507, 23], [445, 26, 507, 24, "prop"], [445, 30, 507, 28], [445, 34, 507, 32], [446, 8, 508, 6], [446, 12, 508, 10], [446, 19, 508, 17, "props"], [446, 24, 508, 22], [446, 25, 508, 23, "prop"], [446, 29, 508, 27], [446, 30, 508, 28], [446, 35, 508, 33], [446, 46, 508, 44], [446, 48, 508, 46], [447, 10, 509, 8, "props"], [447, 15, 509, 13], [447, 16, 509, 14, "prop"], [447, 20, 509, 18], [447, 21, 509, 19], [447, 24, 509, 22, "Number"], [447, 30, 509, 28], [447, 31, 509, 29, "NaN"], [447, 34, 509, 32], [448, 8, 510, 6], [449, 6, 511, 4], [449, 7, 511, 5], [449, 8, 511, 6], [450, 6, 512, 4], [450, 13, 512, 11, "props"], [450, 18, 512, 16], [450, 19, 512, 17], [450, 20, 512, 18], [451, 4, 513, 2], [452, 2, 515, 0], [452, 3, 515, 1], [452, 4, 515, 2], [453, 2, 516, 0], [455, 2, 519, 0], [455, 11, 519, 9, "invokeNullableMethod"], [455, 31, 519, 29, "invokeNullableMethod"], [455, 32, 519, 30, "method"], [455, 38, 519, 36], [455, 40, 519, 38, "event"], [455, 45, 519, 43], [455, 47, 519, 45], [456, 4, 520, 2], [456, 8, 520, 6, "method"], [456, 14, 520, 12], [456, 16, 520, 14], [457, 6, 521, 4], [457, 10, 521, 8], [457, 17, 521, 15, "method"], [457, 23, 521, 21], [457, 28, 521, 26], [457, 38, 521, 36], [457, 40, 521, 38], [458, 8, 522, 6, "method"], [458, 14, 522, 12], [458, 15, 522, 13, "event"], [458, 20, 522, 18], [458, 21, 522, 19], [459, 6, 523, 4], [459, 7, 523, 5], [459, 13, 523, 11], [460, 8, 524, 6], [461, 8, 525, 6], [461, 12, 525, 10], [461, 26, 525, 24], [461, 30, 525, 28, "method"], [461, 36, 525, 34], [461, 40, 525, 38], [461, 47, 525, 45, "method"], [461, 53, 525, 51], [461, 54, 525, 52, "__<PERSON><PERSON><PERSON><PERSON>"], [461, 66, 525, 64], [461, 71, 525, 69], [461, 81, 525, 79], [461, 83, 525, 81], [462, 10, 526, 8], [462, 16, 526, 14, "handler"], [462, 23, 526, 21], [462, 26, 526, 24, "method"], [462, 32, 526, 30], [462, 33, 526, 31, "__<PERSON><PERSON><PERSON><PERSON>"], [462, 45, 526, 43], [462, 46, 526, 44], [462, 47, 526, 45], [463, 10, 528, 8, "invokeNullableMethod"], [463, 30, 528, 28], [463, 31, 528, 29, "handler"], [463, 38, 528, 36], [463, 40, 528, 38, "event"], [463, 45, 528, 43], [463, 46, 528, 44], [464, 8, 529, 6], [464, 9, 529, 7], [464, 15, 529, 13], [465, 10, 530, 8], [465, 14, 530, 12], [465, 28, 530, 26], [465, 32, 530, 30, "method"], [465, 38, 530, 36], [465, 40, 530, 38], [466, 12, 531, 10], [466, 18, 531, 16], [467, 14, 532, 12, "arg<PERSON><PERSON><PERSON>"], [468, 12, 533, 10], [468, 13, 533, 11], [468, 16, 533, 14, "method"], [468, 22, 533, 20], [468, 23, 533, 21, "__nodeConfig"], [468, 35, 533, 33], [469, 12, 535, 10], [469, 16, 535, 14, "Array"], [469, 21, 535, 19], [469, 22, 535, 20, "isArray"], [469, 29, 535, 27], [469, 30, 535, 28, "arg<PERSON><PERSON><PERSON>"], [469, 40, 535, 38], [469, 41, 535, 39], [469, 43, 535, 41], [470, 14, 536, 12], [470, 19, 536, 17], [470, 25, 536, 23], [470, 26, 536, 24, "index"], [470, 31, 536, 29], [470, 33, 536, 31], [470, 34, 536, 32, "key"], [470, 37, 536, 35], [470, 39, 536, 37, "value"], [470, 44, 536, 42], [470, 45, 536, 43], [470, 46, 536, 44], [470, 50, 536, 48, "arg<PERSON><PERSON><PERSON>"], [470, 60, 536, 58], [470, 61, 536, 59, "entries"], [470, 68, 536, 66], [470, 69, 536, 67], [470, 70, 536, 68], [470, 72, 536, 70], [471, 16, 537, 14], [471, 20, 537, 18, "key"], [471, 23, 537, 21], [471, 27, 537, 25, "event"], [471, 32, 537, 30], [471, 33, 537, 31, "nativeEvent"], [471, 44, 537, 42], [471, 46, 537, 44], [472, 18, 538, 16], [473, 18, 539, 16], [473, 24, 539, 22, "nativeValue"], [473, 35, 539, 33], [473, 38, 539, 36, "event"], [473, 43, 539, 41], [473, 44, 539, 42, "nativeEvent"], [473, 55, 539, 53], [473, 56, 539, 54, "key"], [473, 59, 539, 57], [473, 60, 539, 58], [474, 18, 541, 16], [474, 22, 541, 20, "value"], [474, 27, 541, 25], [474, 31, 541, 29, "value"], [474, 36, 541, 34], [474, 37, 541, 35, "setValue"], [474, 45, 541, 43], [474, 47, 541, 45], [475, 20, 542, 18], [476, 20, 543, 18, "value"], [476, 25, 543, 23], [476, 26, 543, 24, "setValue"], [476, 34, 543, 32], [476, 35, 543, 33, "nativeValue"], [476, 46, 543, 44], [476, 47, 543, 45], [477, 18, 544, 16], [477, 19, 544, 17], [477, 25, 544, 23], [478, 20, 545, 18], [479, 20, 546, 18, "method"], [479, 26, 546, 24], [479, 27, 546, 25, "__nodeConfig"], [479, 39, 546, 37], [479, 40, 546, 38, "arg<PERSON><PERSON><PERSON>"], [479, 50, 546, 48], [479, 51, 546, 49, "index"], [479, 56, 546, 54], [479, 57, 546, 55], [479, 60, 546, 58], [479, 61, 546, 59, "key"], [479, 64, 546, 62], [479, 66, 546, 64, "nativeValue"], [479, 77, 546, 75], [479, 78, 546, 76], [480, 18, 547, 16], [481, 16, 548, 14], [482, 14, 549, 12], [483, 12, 550, 10], [484, 10, 551, 8], [485, 8, 552, 6], [486, 6, 553, 4], [487, 4, 554, 2], [488, 2, 555, 0], [489, 2, 557, 0], [489, 11, 557, 9, "asArray"], [489, 18, 557, 16, "asArray"], [489, 19, 557, 17, "value"], [489, 24, 557, 22], [489, 26, 557, 24], [490, 4, 558, 2], [491, 4, 559, 2], [491, 11, 559, 9, "value"], [491, 16, 559, 14], [491, 20, 559, 18], [491, 24, 559, 22], [491, 27, 559, 25], [491, 29, 559, 27], [491, 32, 559, 30, "Array"], [491, 37, 559, 35], [491, 38, 559, 36, "isArray"], [491, 45, 559, 43], [491, 46, 559, 44, "value"], [491, 51, 559, 49], [491, 52, 559, 50], [491, 55, 559, 53, "value"], [491, 60, 559, 58], [491, 63, 559, 61], [491, 64, 559, 62, "value"], [491, 69, 559, 67], [491, 70, 559, 68], [492, 2, 560, 0], [493, 2, 560, 1], [493, 6, 560, 1, "_default"], [493, 14, 560, 1], [493, 17, 560, 1, "exports"], [493, 24, 560, 1], [493, 25, 560, 1, "default"], [493, 32, 560, 1], [493, 35, 562, 15, "Gesture<PERSON>andler"], [493, 49, 562, 29], [494, 0, 562, 29], [494, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "Gesture<PERSON>andler", "get__id", "get__isNative", "get__isDiscrete", "get__shouldEnableGestureOnSetup", "constructor", "_defineProperty$argument_2", "enable", "getConfig", "onWaitingEnded", "removePendingGesture", "addPendingGesture", "isGestureEnabledForEvent", "get__NativeGestureClass", "updateHasCustomActivationCriteria", "updateGestureConfig", "getState", "transformEventData", "transformNativeEvent", "cancelPendingGestures", "notifyPendingGestures", "onGestureEnded", "forceInvalidate", "cancelEvent", "onRawEvent", "shouldUseTouchEvents", "_config$simultaneousH2.some$argument_0", "<PERSON><PERSON><PERSON><PERSON>", "hammer.on$argument_1", "setTimeout$argument_0", "setupEvents", "onStart", "onGestureActivated", "onSuccess", "_getPendingGestures", "config.waitFor.filter$argument_0", "getHammerConfig", "simulateCancelEvent", "ensureConfig", "asArray.map$argument_0", "asArray.map.filter$argument_0", "ghQueueMicrotask$argument_0", "configProps.forEach$argument_0", "invokeNullableMethod", "asArray"], "mappings": "AAA,iNC;ACc;ECC;GDE;EEI;GFE;EGE;GHE;EIE;GJE;EKE;gDCqC;KDM;qCCE;KDS;2CCE;KDQ;uCCE;KDY;kCCE;qBCI;OD8D;KDO;GLI;EQE;GRE;ESE,2BT;EUE;GVE;EWE;GXE;EYE;GZI;EaE;GbE;EcE;GdE;EeE;GfsB;EgBE;GhBO;EiBE;GjBmC;EkBE;GlBE;EmBE;GnBO;EoBE;GpBM;EqBG;GrBG;EsBE;GtBK;EuBE;GvBO;EwBE;GxBM;EyBE;gLCG,2BD;GzBC;E2BE;mCC2B;mBCgB;SDG;KDE;G3BG;E8BE;0CFG,4BE;2DFC;OEE;8BFG,iCE;G9BC;E+BE;G/Ba;EgCE;GhCE;EiCE,cjC;EkCE;sDCI;sCDE;GlCK;EoCE;GpCK;EqCE,kCrC;EsCG;kDCoB;8CDE,SE,MF;4ECO;ODM,SE,MF;yBGG;SHK;wBIO;KJI;GtCE;CDE;A4CI;C5CoC;A6CE;C7CG"}}, "type": "js/module"}]}