{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../unitlessNumbers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 242}, "end": {"line": 13, "column": 50, "index": 292}}], "key": "W0D7xSWAg9kc7AVkvML4oJh7bN4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _unitlessNumbers = _interopRequireDefault(require(_dependencyMap[1], \"../unitlessNumbers\"));\n  /* eslint-disable */\n\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * From React 16.0.0\n   * \n   */\n\n  /**\n   * Convert a value into the proper css writable value. The style name `name`\n   * should be logical (no hyphens), as specified\n   * in `CSSProperty.isUnitlessNumber`.\n   *\n   * @param {string} name CSS property name such as `topMargin`.\n   * @param {*} value CSS property value such as `10px`.\n   * @return {string} Normalized style value with dimensions applied.\n   */\n  function dangerousStyleValue(name, value, isCustomProperty) {\n    // Note that we've removed escapeTextForBrowser() calls here since the\n    // whole string will be escaped when the attribute is injected into\n    // the markup. If you provide unsafe user data here they can inject\n    // arbitrary CSS which may be problematic (I couldn't repro this):\n    // https://www.owasp.org/index.php/XSS_Filter_Evasion_Cheat_Sheet\n    // http://www.thespanner.co.uk/2007/11/26/ultimate-xss-css-injection/\n    // This is not an XSS hole but instead a potential CSS injection issue\n    // which has lead to a greater discussion about how we're going to\n    // trust URLs moving forward. See #2115901\n\n    var isEmpty = value == null || typeof value === 'boolean' || value === '';\n    if (isEmpty) {\n      return '';\n    }\n    if (!isCustomProperty && typeof value === 'number' && value !== 0 && !(_unitlessNumbers.default.hasOwnProperty(name) && _unitlessNumbers.default[name])) {\n      return value + 'px'; // Presumes implicit 'px' suffix for unitless numbers\n    }\n    return ('' + value).trim();\n  }\n  var _default = exports.default = dangerousStyleValue;\n});", "lineCount": 50, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "_unitlessNumbers"], [7, 22, 13, 0], [7, 25, 13, 0, "_interopRequireDefault"], [7, 47, 13, 0], [7, 48, 13, 0, "require"], [7, 55, 13, 0], [7, 56, 13, 0, "_dependencyMap"], [7, 70, 13, 0], [8, 2, 1, 0], [10, 2, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [14, 0, 7, 0], [15, 0, 8, 0], [16, 0, 9, 0], [17, 0, 10, 0], [18, 0, 11, 0], [20, 2, 15, 0], [21, 0, 16, 0], [22, 0, 17, 0], [23, 0, 18, 0], [24, 0, 19, 0], [25, 0, 20, 0], [26, 0, 21, 0], [27, 0, 22, 0], [28, 0, 23, 0], [29, 2, 24, 0], [29, 11, 24, 9, "dangerousStyleValue"], [29, 30, 24, 28, "dangerousStyleValue"], [29, 31, 24, 29, "name"], [29, 35, 24, 33], [29, 37, 24, 35, "value"], [29, 42, 24, 40], [29, 44, 24, 42, "isCustomProperty"], [29, 60, 24, 58], [29, 62, 24, 60], [30, 4, 25, 2], [31, 4, 26, 2], [32, 4, 27, 2], [33, 4, 28, 2], [34, 4, 29, 2], [35, 4, 30, 2], [36, 4, 31, 2], [37, 4, 32, 2], [38, 4, 33, 2], [40, 4, 35, 2], [40, 8, 35, 6, "isEmpty"], [40, 15, 35, 13], [40, 18, 35, 16, "value"], [40, 23, 35, 21], [40, 27, 35, 25], [40, 31, 35, 29], [40, 35, 35, 33], [40, 42, 35, 40, "value"], [40, 47, 35, 45], [40, 52, 35, 50], [40, 61, 35, 59], [40, 65, 35, 63, "value"], [40, 70, 35, 68], [40, 75, 35, 73], [40, 77, 35, 75], [41, 4, 36, 2], [41, 8, 36, 6, "isEmpty"], [41, 15, 36, 13], [41, 17, 36, 15], [42, 6, 37, 4], [42, 13, 37, 11], [42, 15, 37, 13], [43, 4, 38, 2], [44, 4, 39, 2], [44, 8, 39, 6], [44, 9, 39, 7, "isCustomProperty"], [44, 25, 39, 23], [44, 29, 39, 27], [44, 36, 39, 34, "value"], [44, 41, 39, 39], [44, 46, 39, 44], [44, 54, 39, 52], [44, 58, 39, 56, "value"], [44, 63, 39, 61], [44, 68, 39, 66], [44, 69, 39, 67], [44, 73, 39, 71], [44, 75, 39, 73, "isUnitlessNumber"], [44, 99, 39, 89], [44, 100, 39, 90, "hasOwnProperty"], [44, 114, 39, 104], [44, 115, 39, 105, "name"], [44, 119, 39, 109], [44, 120, 39, 110], [44, 124, 39, 114, "isUnitlessNumber"], [44, 148, 39, 130], [44, 149, 39, 131, "name"], [44, 153, 39, 135], [44, 154, 39, 136], [44, 155, 39, 137], [44, 157, 39, 139], [45, 6, 40, 4], [45, 13, 40, 11, "value"], [45, 18, 40, 16], [45, 21, 40, 19], [45, 25, 40, 23], [45, 26, 40, 24], [45, 27, 40, 25], [46, 4, 41, 2], [47, 4, 42, 2], [47, 11, 42, 9], [47, 12, 42, 10], [47, 14, 42, 12], [47, 17, 42, 15, "value"], [47, 22, 42, 20], [47, 24, 42, 22, "trim"], [47, 28, 42, 26], [47, 29, 42, 27], [47, 30, 42, 28], [48, 2, 43, 0], [49, 2, 43, 1], [49, 6, 43, 1, "_default"], [49, 14, 43, 1], [49, 17, 43, 1, "exports"], [49, 24, 43, 1], [49, 25, 43, 1, "default"], [49, 32, 43, 1], [49, 35, 44, 15, "dangerousStyleValue"], [49, 54, 44, 34], [50, 0, 44, 34], [50, 3]], "functionMap": {"names": ["<global>", "dangerousStyleValue"], "mappings": "AAA;ACuB;CDmB"}}, "type": "js/module"}]}