{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./useNavigation.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 51, "index": 98}}], "key": "QYUBGacr5qSJ4R+u3laZK0wRG3s=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useIsFocused = useIsFocused;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _useNavigation = require(_dependencyMap[1], \"./useNavigation.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Hook to get the current focus state of the screen. Returns a `true` if screen is focused, otherwise `false`.\n   * This can be used if a component needs to render something based on the focus state.\n   */\n  function useIsFocused() {\n    var navigation = (0, _useNavigation.useNavigation)();\n    var subscribe = React.useCallback(callback => {\n      var unsubscribeFocus = navigation.addListener('focus', callback);\n      var unsubscribeBlur = navigation.addListener('blur', callback);\n      return () => {\n        unsubscribeFocus();\n        unsubscribeBlur();\n      };\n    }, [navigation]);\n    var value = React.useSyncExternalStore(subscribe, navigation.isFocused, navigation.isFocused);\n    return value;\n  }\n});", "lineCount": 28, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useIsFocused"], [7, 22, 1, 13], [7, 25, 1, 13, "useIsFocused"], [7, 37, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_useNavigation"], [9, 20, 4, 0], [9, 23, 4, 0, "require"], [9, 30, 4, 0], [9, 31, 4, 0, "_dependencyMap"], [9, 45, 4, 0], [10, 2, 4, 51], [10, 11, 4, 51, "_interopRequireWildcard"], [10, 35, 4, 51, "e"], [10, 36, 4, 51], [10, 38, 4, 51, "t"], [10, 39, 4, 51], [10, 68, 4, 51, "WeakMap"], [10, 75, 4, 51], [10, 81, 4, 51, "r"], [10, 82, 4, 51], [10, 89, 4, 51, "WeakMap"], [10, 96, 4, 51], [10, 100, 4, 51, "n"], [10, 101, 4, 51], [10, 108, 4, 51, "WeakMap"], [10, 115, 4, 51], [10, 127, 4, 51, "_interopRequireWildcard"], [10, 150, 4, 51], [10, 162, 4, 51, "_interopRequireWildcard"], [10, 163, 4, 51, "e"], [10, 164, 4, 51], [10, 166, 4, 51, "t"], [10, 167, 4, 51], [10, 176, 4, 51, "t"], [10, 177, 4, 51], [10, 181, 4, 51, "e"], [10, 182, 4, 51], [10, 186, 4, 51, "e"], [10, 187, 4, 51], [10, 188, 4, 51, "__esModule"], [10, 198, 4, 51], [10, 207, 4, 51, "e"], [10, 208, 4, 51], [10, 214, 4, 51, "o"], [10, 215, 4, 51], [10, 217, 4, 51, "i"], [10, 218, 4, 51], [10, 220, 4, 51, "f"], [10, 221, 4, 51], [10, 226, 4, 51, "__proto__"], [10, 235, 4, 51], [10, 243, 4, 51, "default"], [10, 250, 4, 51], [10, 252, 4, 51, "e"], [10, 253, 4, 51], [10, 270, 4, 51, "e"], [10, 271, 4, 51], [10, 294, 4, 51, "e"], [10, 295, 4, 51], [10, 320, 4, 51, "e"], [10, 321, 4, 51], [10, 330, 4, 51, "f"], [10, 331, 4, 51], [10, 337, 4, 51, "o"], [10, 338, 4, 51], [10, 341, 4, 51, "t"], [10, 342, 4, 51], [10, 345, 4, 51, "n"], [10, 346, 4, 51], [10, 349, 4, 51, "r"], [10, 350, 4, 51], [10, 358, 4, 51, "o"], [10, 359, 4, 51], [10, 360, 4, 51, "has"], [10, 363, 4, 51], [10, 364, 4, 51, "e"], [10, 365, 4, 51], [10, 375, 4, 51, "o"], [10, 376, 4, 51], [10, 377, 4, 51, "get"], [10, 380, 4, 51], [10, 381, 4, 51, "e"], [10, 382, 4, 51], [10, 385, 4, 51, "o"], [10, 386, 4, 51], [10, 387, 4, 51, "set"], [10, 390, 4, 51], [10, 391, 4, 51, "e"], [10, 392, 4, 51], [10, 394, 4, 51, "f"], [10, 395, 4, 51], [10, 409, 4, 51, "_t"], [10, 411, 4, 51], [10, 415, 4, 51, "e"], [10, 416, 4, 51], [10, 432, 4, 51, "_t"], [10, 434, 4, 51], [10, 441, 4, 51, "hasOwnProperty"], [10, 455, 4, 51], [10, 456, 4, 51, "call"], [10, 460, 4, 51], [10, 461, 4, 51, "e"], [10, 462, 4, 51], [10, 464, 4, 51, "_t"], [10, 466, 4, 51], [10, 473, 4, 51, "i"], [10, 474, 4, 51], [10, 478, 4, 51, "o"], [10, 479, 4, 51], [10, 482, 4, 51, "Object"], [10, 488, 4, 51], [10, 489, 4, 51, "defineProperty"], [10, 503, 4, 51], [10, 508, 4, 51, "Object"], [10, 514, 4, 51], [10, 515, 4, 51, "getOwnPropertyDescriptor"], [10, 539, 4, 51], [10, 540, 4, 51, "e"], [10, 541, 4, 51], [10, 543, 4, 51, "_t"], [10, 545, 4, 51], [10, 552, 4, 51, "i"], [10, 553, 4, 51], [10, 554, 4, 51, "get"], [10, 557, 4, 51], [10, 561, 4, 51, "i"], [10, 562, 4, 51], [10, 563, 4, 51, "set"], [10, 566, 4, 51], [10, 570, 4, 51, "o"], [10, 571, 4, 51], [10, 572, 4, 51, "f"], [10, 573, 4, 51], [10, 575, 4, 51, "_t"], [10, 577, 4, 51], [10, 579, 4, 51, "i"], [10, 580, 4, 51], [10, 584, 4, 51, "f"], [10, 585, 4, 51], [10, 586, 4, 51, "_t"], [10, 588, 4, 51], [10, 592, 4, 51, "e"], [10, 593, 4, 51], [10, 594, 4, 51, "_t"], [10, 596, 4, 51], [10, 607, 4, 51, "f"], [10, 608, 4, 51], [10, 613, 4, 51, "e"], [10, 614, 4, 51], [10, 616, 4, 51, "t"], [10, 617, 4, 51], [11, 2, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [14, 0, 9, 0], [15, 2, 10, 7], [15, 11, 10, 16, "useIsFocused"], [15, 23, 10, 28, "useIsFocused"], [15, 24, 10, 28], [15, 26, 10, 31], [16, 4, 11, 2], [16, 8, 11, 8, "navigation"], [16, 18, 11, 18], [16, 21, 11, 21], [16, 25, 11, 21, "useNavigation"], [16, 53, 11, 34], [16, 55, 11, 35], [16, 56, 11, 36], [17, 4, 12, 2], [17, 8, 12, 8, "subscribe"], [17, 17, 12, 17], [17, 20, 12, 20, "React"], [17, 25, 12, 25], [17, 26, 12, 26, "useCallback"], [17, 37, 12, 37], [17, 38, 12, 38, "callback"], [17, 46, 12, 46], [17, 50, 12, 50], [18, 6, 13, 4], [18, 10, 13, 10, "unsubscribeFocus"], [18, 26, 13, 26], [18, 29, 13, 29, "navigation"], [18, 39, 13, 39], [18, 40, 13, 40, "addListener"], [18, 51, 13, 51], [18, 52, 13, 52], [18, 59, 13, 59], [18, 61, 13, 61, "callback"], [18, 69, 13, 69], [18, 70, 13, 70], [19, 6, 14, 4], [19, 10, 14, 10, "unsubscribeBlur"], [19, 25, 14, 25], [19, 28, 14, 28, "navigation"], [19, 38, 14, 38], [19, 39, 14, 39, "addListener"], [19, 50, 14, 50], [19, 51, 14, 51], [19, 57, 14, 57], [19, 59, 14, 59, "callback"], [19, 67, 14, 67], [19, 68, 14, 68], [20, 6, 15, 4], [20, 13, 15, 11], [20, 19, 15, 17], [21, 8, 16, 6, "unsubscribeFocus"], [21, 24, 16, 22], [21, 25, 16, 23], [21, 26, 16, 24], [22, 8, 17, 6, "unsubscribeBlur"], [22, 23, 17, 21], [22, 24, 17, 22], [22, 25, 17, 23], [23, 6, 18, 4], [23, 7, 18, 5], [24, 4, 19, 2], [24, 5, 19, 3], [24, 7, 19, 5], [24, 8, 19, 6, "navigation"], [24, 18, 19, 16], [24, 19, 19, 17], [24, 20, 19, 18], [25, 4, 20, 2], [25, 8, 20, 8, "value"], [25, 13, 20, 13], [25, 16, 20, 16, "React"], [25, 21, 20, 21], [25, 22, 20, 22, "useSyncExternalStore"], [25, 42, 20, 42], [25, 43, 20, 43, "subscribe"], [25, 52, 20, 52], [25, 54, 20, 54, "navigation"], [25, 64, 20, 64], [25, 65, 20, 65, "isFocused"], [25, 74, 20, 74], [25, 76, 20, 76, "navigation"], [25, 86, 20, 86], [25, 87, 20, 87, "isFocused"], [25, 96, 20, 96], [25, 97, 20, 97], [26, 4, 21, 2], [26, 11, 21, 9, "value"], [26, 16, 21, 14], [27, 2, 22, 0], [28, 0, 22, 1], [28, 3]], "functionMap": {"names": ["<global>", "useIsFocused", "subscribe", "<anonymous>"], "mappings": "AAA;OCS;sCCE;WCG;KDG;GDC;CDG"}}, "type": "js/module"}]}