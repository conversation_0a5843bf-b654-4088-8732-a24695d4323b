{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectSpread2", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 65, "index": 65}}], "key": "SfRhzMj3Ex6qA89WTFEUm9Lj49A=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 276}, "end": {"line": 12, "column": 31, "index": 307}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.VirtualizedListCellContextProvider = VirtualizedListCellContextProvider;\n  exports.VirtualizedListContext = void 0;\n  exports.VirtualizedListContextProvider = VirtualizedListContextProvider;\n  exports.VirtualizedListContextResetter = VirtualizedListContextResetter;\n  var _objectSpread2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectSpread2\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var React = _react;\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   * @format\n   */\n\n  var __DEV__ = process.env.NODE_ENV !== 'production';\n  var VirtualizedListContext = exports.VirtualizedListContext = /*#__PURE__*/React.createContext(null);\n  if (__DEV__) {\n    VirtualizedListContext.displayName = 'VirtualizedListContext';\n  }\n\n  /**\n   * Resets the context. Intended for use by portal-like components (e.g. Modal).\n   */\n  function VirtualizedListContextResetter(_ref) {\n    var children = _ref.children;\n    return /*#__PURE__*/React.createElement(VirtualizedListContext.Provider, {\n      value: null\n    }, children);\n  }\n\n  /**\n   * Sets the context with memoization. Intended to be used by `VirtualizedList`.\n   */\n  function VirtualizedListContextProvider(_ref2) {\n    var children = _ref2.children,\n      value = _ref2.value;\n    // Avoid setting a newly created context object if the values are identical.\n    var context = (0, _react.useMemo)(() => ({\n      cellKey: null,\n      getScrollMetrics: value.getScrollMetrics,\n      horizontal: value.horizontal,\n      getOutermostParentListRef: value.getOutermostParentListRef,\n      registerAsNestedChild: value.registerAsNestedChild,\n      unregisterAsNestedChild: value.unregisterAsNestedChild\n    }), [value.getScrollMetrics, value.horizontal, value.getOutermostParentListRef, value.registerAsNestedChild, value.unregisterAsNestedChild]);\n    return /*#__PURE__*/React.createElement(VirtualizedListContext.Provider, {\n      value: context\n    }, children);\n  }\n\n  /**\n   * Sets the `cellKey`. Intended to be used by `VirtualizedList` for each cell.\n   */\n  function VirtualizedListCellContextProvider(_ref3) {\n    var cellKey = _ref3.cellKey,\n      children = _ref3.children;\n    // Avoid setting a newly created context object if the values are identical.\n    var currContext = (0, _react.useContext)(VirtualizedListContext);\n    var context = (0, _react.useMemo)(() => currContext == null ? null : (0, _objectSpread2.default)((0, _objectSpread2.default)({}, currContext), {}, {\n      cellKey\n    }), [currContext, cellKey]);\n    return /*#__PURE__*/React.createElement(VirtualizedListContext.Provider, {\n      value: context\n    }, children);\n  }\n});", "lineCount": 75, "map": [[10, 2, 1, 0], [10, 6, 1, 0, "_objectSpread2"], [10, 20, 1, 0], [10, 23, 1, 0, "_interopRequireDefault"], [10, 45, 1, 0], [10, 46, 1, 0, "require"], [10, 53, 1, 0], [10, 54, 1, 0, "_dependencyMap"], [10, 68, 1, 0], [11, 2, 12, 0], [11, 6, 12, 0, "_react"], [11, 12, 12, 0], [11, 15, 12, 0, "_interopRequireWildcard"], [11, 38, 12, 0], [11, 39, 12, 0, "require"], [11, 46, 12, 0], [11, 47, 12, 0, "_dependencyMap"], [11, 61, 12, 0], [12, 2, 12, 31], [12, 6, 12, 31, "React"], [12, 11, 12, 31], [12, 14, 12, 31, "_react"], [12, 20, 12, 31], [13, 2, 12, 31], [13, 11, 12, 31, "_interopRequireWildcard"], [13, 35, 12, 31, "e"], [13, 36, 12, 31], [13, 38, 12, 31, "t"], [13, 39, 12, 31], [13, 68, 12, 31, "WeakMap"], [13, 75, 12, 31], [13, 81, 12, 31, "r"], [13, 82, 12, 31], [13, 89, 12, 31, "WeakMap"], [13, 96, 12, 31], [13, 100, 12, 31, "n"], [13, 101, 12, 31], [13, 108, 12, 31, "WeakMap"], [13, 115, 12, 31], [13, 127, 12, 31, "_interopRequireWildcard"], [13, 150, 12, 31], [13, 162, 12, 31, "_interopRequireWildcard"], [13, 163, 12, 31, "e"], [13, 164, 12, 31], [13, 166, 12, 31, "t"], [13, 167, 12, 31], [13, 176, 12, 31, "t"], [13, 177, 12, 31], [13, 181, 12, 31, "e"], [13, 182, 12, 31], [13, 186, 12, 31, "e"], [13, 187, 12, 31], [13, 188, 12, 31, "__esModule"], [13, 198, 12, 31], [13, 207, 12, 31, "e"], [13, 208, 12, 31], [13, 214, 12, 31, "o"], [13, 215, 12, 31], [13, 217, 12, 31, "i"], [13, 218, 12, 31], [13, 220, 12, 31, "f"], [13, 221, 12, 31], [13, 226, 12, 31, "__proto__"], [13, 235, 12, 31], [13, 243, 12, 31, "default"], [13, 250, 12, 31], [13, 252, 12, 31, "e"], [13, 253, 12, 31], [13, 270, 12, 31, "e"], [13, 271, 12, 31], [13, 294, 12, 31, "e"], [13, 295, 12, 31], [13, 320, 12, 31, "e"], [13, 321, 12, 31], [13, 330, 12, 31, "f"], [13, 331, 12, 31], [13, 337, 12, 31, "o"], [13, 338, 12, 31], [13, 341, 12, 31, "t"], [13, 342, 12, 31], [13, 345, 12, 31, "n"], [13, 346, 12, 31], [13, 349, 12, 31, "r"], [13, 350, 12, 31], [13, 358, 12, 31, "o"], [13, 359, 12, 31], [13, 360, 12, 31, "has"], [13, 363, 12, 31], [13, 364, 12, 31, "e"], [13, 365, 12, 31], [13, 375, 12, 31, "o"], [13, 376, 12, 31], [13, 377, 12, 31, "get"], [13, 380, 12, 31], [13, 381, 12, 31, "e"], [13, 382, 12, 31], [13, 385, 12, 31, "o"], [13, 386, 12, 31], [13, 387, 12, 31, "set"], [13, 390, 12, 31], [13, 391, 12, 31, "e"], [13, 392, 12, 31], [13, 394, 12, 31, "f"], [13, 395, 12, 31], [13, 411, 12, 31, "t"], [13, 412, 12, 31], [13, 416, 12, 31, "e"], [13, 417, 12, 31], [13, 433, 12, 31, "t"], [13, 434, 12, 31], [13, 441, 12, 31, "hasOwnProperty"], [13, 455, 12, 31], [13, 456, 12, 31, "call"], [13, 460, 12, 31], [13, 461, 12, 31, "e"], [13, 462, 12, 31], [13, 464, 12, 31, "t"], [13, 465, 12, 31], [13, 472, 12, 31, "i"], [13, 473, 12, 31], [13, 477, 12, 31, "o"], [13, 478, 12, 31], [13, 481, 12, 31, "Object"], [13, 487, 12, 31], [13, 488, 12, 31, "defineProperty"], [13, 502, 12, 31], [13, 507, 12, 31, "Object"], [13, 513, 12, 31], [13, 514, 12, 31, "getOwnPropertyDescriptor"], [13, 538, 12, 31], [13, 539, 12, 31, "e"], [13, 540, 12, 31], [13, 542, 12, 31, "t"], [13, 543, 12, 31], [13, 550, 12, 31, "i"], [13, 551, 12, 31], [13, 552, 12, 31, "get"], [13, 555, 12, 31], [13, 559, 12, 31, "i"], [13, 560, 12, 31], [13, 561, 12, 31, "set"], [13, 564, 12, 31], [13, 568, 12, 31, "o"], [13, 569, 12, 31], [13, 570, 12, 31, "f"], [13, 571, 12, 31], [13, 573, 12, 31, "t"], [13, 574, 12, 31], [13, 576, 12, 31, "i"], [13, 577, 12, 31], [13, 581, 12, 31, "f"], [13, 582, 12, 31], [13, 583, 12, 31, "t"], [13, 584, 12, 31], [13, 588, 12, 31, "e"], [13, 589, 12, 31], [13, 590, 12, 31, "t"], [13, 591, 12, 31], [13, 602, 12, 31, "f"], [13, 603, 12, 31], [13, 608, 12, 31, "e"], [13, 609, 12, 31], [13, 611, 12, 31, "t"], [13, 612, 12, 31], [14, 2, 2, 0], [15, 0, 3, 0], [16, 0, 4, 0], [17, 0, 5, 0], [18, 0, 6, 0], [19, 0, 7, 0], [20, 0, 8, 0], [21, 0, 9, 0], [22, 0, 10, 0], [24, 2, 14, 0], [24, 6, 14, 4, "__DEV__"], [24, 13, 14, 11], [24, 16, 14, 14, "process"], [24, 23, 14, 21], [24, 24, 14, 22, "env"], [24, 27, 14, 25], [24, 28, 14, 26, "NODE_ENV"], [24, 36, 14, 34], [24, 41, 14, 39], [24, 53, 14, 51], [25, 2, 15, 7], [25, 6, 15, 11, "VirtualizedListContext"], [25, 28, 15, 33], [25, 31, 15, 33, "exports"], [25, 38, 15, 33], [25, 39, 15, 33, "VirtualizedListContext"], [25, 61, 15, 33], [25, 64, 15, 36], [25, 77, 15, 49, "React"], [25, 82, 15, 54], [25, 83, 15, 55, "createContext"], [25, 96, 15, 68], [25, 97, 15, 69], [25, 101, 15, 73], [25, 102, 15, 74], [26, 2, 16, 0], [26, 6, 16, 4, "__DEV__"], [26, 13, 16, 11], [26, 15, 16, 13], [27, 4, 17, 2, "VirtualizedListContext"], [27, 26, 17, 24], [27, 27, 17, 25, "displayName"], [27, 38, 17, 36], [27, 41, 17, 39], [27, 65, 17, 63], [28, 2, 18, 0], [30, 2, 20, 0], [31, 0, 21, 0], [32, 0, 22, 0], [33, 2, 23, 7], [33, 11, 23, 16, "VirtualizedListContextResetter"], [33, 41, 23, 46, "VirtualizedListContextResetter"], [33, 42, 23, 47, "_ref"], [33, 46, 23, 51], [33, 48, 23, 53], [34, 4, 24, 2], [34, 8, 24, 6, "children"], [34, 16, 24, 14], [34, 19, 24, 17, "_ref"], [34, 23, 24, 21], [34, 24, 24, 22, "children"], [34, 32, 24, 30], [35, 4, 25, 2], [35, 11, 25, 9], [35, 24, 25, 22, "React"], [35, 29, 25, 27], [35, 30, 25, 28, "createElement"], [35, 43, 25, 41], [35, 44, 25, 42, "VirtualizedListContext"], [35, 66, 25, 64], [35, 67, 25, 65, "Provider"], [35, 75, 25, 73], [35, 77, 25, 75], [36, 6, 26, 4, "value"], [36, 11, 26, 9], [36, 13, 26, 11], [37, 4, 27, 2], [37, 5, 27, 3], [37, 7, 27, 5, "children"], [37, 15, 27, 13], [37, 16, 27, 14], [38, 2, 28, 0], [40, 2, 30, 0], [41, 0, 31, 0], [42, 0, 32, 0], [43, 2, 33, 7], [43, 11, 33, 16, "VirtualizedListContextProvider"], [43, 41, 33, 46, "VirtualizedListContextProvider"], [43, 42, 33, 47, "_ref2"], [43, 47, 33, 52], [43, 49, 33, 54], [44, 4, 34, 2], [44, 8, 34, 6, "children"], [44, 16, 34, 14], [44, 19, 34, 17, "_ref2"], [44, 24, 34, 22], [44, 25, 34, 23, "children"], [44, 33, 34, 31], [45, 6, 35, 4, "value"], [45, 11, 35, 9], [45, 14, 35, 12, "_ref2"], [45, 19, 35, 17], [45, 20, 35, 18, "value"], [45, 25, 35, 23], [46, 4, 36, 2], [47, 4, 37, 2], [47, 8, 37, 6, "context"], [47, 15, 37, 13], [47, 18, 37, 16], [47, 22, 37, 16, "useMemo"], [47, 36, 37, 23], [47, 38, 37, 24], [47, 45, 37, 31], [48, 6, 38, 4, "cellKey"], [48, 13, 38, 11], [48, 15, 38, 13], [48, 19, 38, 17], [49, 6, 39, 4, "getScrollMetrics"], [49, 22, 39, 20], [49, 24, 39, 22, "value"], [49, 29, 39, 27], [49, 30, 39, 28, "getScrollMetrics"], [49, 46, 39, 44], [50, 6, 40, 4, "horizontal"], [50, 16, 40, 14], [50, 18, 40, 16, "value"], [50, 23, 40, 21], [50, 24, 40, 22, "horizontal"], [50, 34, 40, 32], [51, 6, 41, 4, "getOutermostParentListRef"], [51, 31, 41, 29], [51, 33, 41, 31, "value"], [51, 38, 41, 36], [51, 39, 41, 37, "getOutermostParentListRef"], [51, 64, 41, 62], [52, 6, 42, 4, "registerAsNestedChild"], [52, 27, 42, 25], [52, 29, 42, 27, "value"], [52, 34, 42, 32], [52, 35, 42, 33, "registerAsNestedChild"], [52, 56, 42, 54], [53, 6, 43, 4, "unregisterAsNestedChild"], [53, 29, 43, 27], [53, 31, 43, 29, "value"], [53, 36, 43, 34], [53, 37, 43, 35, "unregisterAsNestedChild"], [54, 4, 44, 2], [54, 5, 44, 3], [54, 6, 44, 4], [54, 8, 44, 6], [54, 9, 44, 7, "value"], [54, 14, 44, 12], [54, 15, 44, 13, "getScrollMetrics"], [54, 31, 44, 29], [54, 33, 44, 31, "value"], [54, 38, 44, 36], [54, 39, 44, 37, "horizontal"], [54, 49, 44, 47], [54, 51, 44, 49, "value"], [54, 56, 44, 54], [54, 57, 44, 55, "getOutermostParentListRef"], [54, 82, 44, 80], [54, 84, 44, 82, "value"], [54, 89, 44, 87], [54, 90, 44, 88, "registerAsNestedChild"], [54, 111, 44, 109], [54, 113, 44, 111, "value"], [54, 118, 44, 116], [54, 119, 44, 117, "unregisterAsNestedChild"], [54, 142, 44, 140], [54, 143, 44, 141], [54, 144, 44, 142], [55, 4, 45, 2], [55, 11, 45, 9], [55, 24, 45, 22, "React"], [55, 29, 45, 27], [55, 30, 45, 28, "createElement"], [55, 43, 45, 41], [55, 44, 45, 42, "VirtualizedListContext"], [55, 66, 45, 64], [55, 67, 45, 65, "Provider"], [55, 75, 45, 73], [55, 77, 45, 75], [56, 6, 46, 4, "value"], [56, 11, 46, 9], [56, 13, 46, 11, "context"], [57, 4, 47, 2], [57, 5, 47, 3], [57, 7, 47, 5, "children"], [57, 15, 47, 13], [57, 16, 47, 14], [58, 2, 48, 0], [60, 2, 50, 0], [61, 0, 51, 0], [62, 0, 52, 0], [63, 2, 53, 7], [63, 11, 53, 16, "VirtualizedListCellContextProvider"], [63, 45, 53, 50, "VirtualizedListCellContextProvider"], [63, 46, 53, 51, "_ref3"], [63, 51, 53, 56], [63, 53, 53, 58], [64, 4, 54, 2], [64, 8, 54, 6, "cellKey"], [64, 15, 54, 13], [64, 18, 54, 16, "_ref3"], [64, 23, 54, 21], [64, 24, 54, 22, "cellKey"], [64, 31, 54, 29], [65, 6, 55, 4, "children"], [65, 14, 55, 12], [65, 17, 55, 15, "_ref3"], [65, 22, 55, 20], [65, 23, 55, 21, "children"], [65, 31, 55, 29], [66, 4, 56, 2], [67, 4, 57, 2], [67, 8, 57, 6, "currContext"], [67, 19, 57, 17], [67, 22, 57, 20], [67, 26, 57, 20, "useContext"], [67, 43, 57, 30], [67, 45, 57, 31, "VirtualizedListContext"], [67, 67, 57, 53], [67, 68, 57, 54], [68, 4, 58, 2], [68, 8, 58, 6, "context"], [68, 15, 58, 13], [68, 18, 58, 16], [68, 22, 58, 16, "useMemo"], [68, 36, 58, 23], [68, 38, 58, 24], [68, 44, 58, 30, "currContext"], [68, 55, 58, 41], [68, 59, 58, 45], [68, 63, 58, 49], [68, 66, 58, 52], [68, 70, 58, 56], [68, 73, 58, 59], [68, 77, 58, 59, "_objectSpread"], [68, 99, 58, 72], [68, 101, 58, 73], [68, 105, 58, 73, "_objectSpread"], [68, 127, 58, 86], [68, 129, 58, 87], [68, 130, 58, 88], [68, 131, 58, 89], [68, 133, 58, 91, "currContext"], [68, 144, 58, 102], [68, 145, 58, 103], [68, 147, 58, 105], [68, 148, 58, 106], [68, 149, 58, 107], [68, 151, 58, 109], [69, 6, 59, 4, "cellKey"], [70, 4, 60, 2], [70, 5, 60, 3], [70, 6, 60, 4], [70, 8, 60, 6], [70, 9, 60, 7, "currContext"], [70, 20, 60, 18], [70, 22, 60, 20, "cellKey"], [70, 29, 60, 27], [70, 30, 60, 28], [70, 31, 60, 29], [71, 4, 61, 2], [71, 11, 61, 9], [71, 24, 61, 22, "React"], [71, 29, 61, 27], [71, 30, 61, 28, "createElement"], [71, 43, 61, 41], [71, 44, 61, 42, "VirtualizedListContext"], [71, 66, 61, 64], [71, 67, 61, 65, "Provider"], [71, 75, 61, 73], [71, 77, 61, 75], [72, 6, 62, 4, "value"], [72, 11, 62, 9], [72, 13, 62, 11, "context"], [73, 4, 63, 2], [73, 5, 63, 3], [73, 7, 63, 5, "children"], [73, 15, 63, 13], [73, 16, 63, 14], [74, 2, 64, 0], [75, 0, 64, 1], [75, 3]], "functionMap": {"names": ["<global>", "VirtualizedListContextResetter", "VirtualizedListContextProvider", "useMemo$argument_0", "VirtualizedListCellContextProvider"], "mappings": "AAA;OCsB;CDK;OEK;wBCI;IDO;CFI;OIK;wBDK;ICE"}}, "type": "js/module"}]}