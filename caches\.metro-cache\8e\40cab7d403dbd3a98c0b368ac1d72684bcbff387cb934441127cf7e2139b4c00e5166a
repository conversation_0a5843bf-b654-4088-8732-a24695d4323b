{"dependencies": [{"name": "./StackClient", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 38, "index": 278}, "end": {"line": 7, "column": 62, "index": 302}}], "key": "FcJwGGC40FvU0RZnEsQ/SfnBSCE=", "exportNames": ["*"]}}, {"name": "../views/Screen", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 17, "index": 361}, "end": {"line": 9, "column": 43, "index": 387}}], "key": "CvVDy33sFAANwe0yWc+ZvurKuCc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Stack = void 0;\n  const StackClient_1 = __importDefault(require(_dependencyMap[0], \"./StackClient\"));\n  exports.Stack = StackClient_1.default;\n  const Screen_1 = require(_dependencyMap[1], \"../views/Screen\");\n  StackClient_1.default.Screen = Screen_1.Screen;\n  exports.default = StackClient_1.default;\n});", "lineCount": 18, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [4, 6, 2, 4, "__importDefault"], [4, 21, 2, 19], [4, 24, 2, 23], [4, 28, 2, 27], [4, 32, 2, 31], [4, 36, 2, 35], [4, 37, 2, 36, "__importDefault"], [4, 52, 2, 51], [4, 56, 2, 56], [4, 66, 2, 66, "mod"], [4, 69, 2, 69], [4, 71, 2, 71], [5, 4, 3, 4], [5, 11, 3, 12, "mod"], [5, 14, 3, 15], [5, 18, 3, 19, "mod"], [5, 21, 3, 22], [5, 22, 3, 23, "__esModule"], [5, 32, 3, 33], [5, 35, 3, 37, "mod"], [5, 38, 3, 40], [5, 41, 3, 43], [6, 6, 3, 45], [6, 15, 3, 54], [6, 17, 3, 56, "mod"], [7, 4, 3, 60], [7, 5, 3, 61], [8, 2, 4, 0], [8, 3, 4, 1], [9, 2, 5, 0, "Object"], [9, 8, 5, 6], [9, 9, 5, 7, "defineProperty"], [9, 23, 5, 21], [9, 24, 5, 22, "exports"], [9, 31, 5, 29], [9, 33, 5, 31], [9, 45, 5, 43], [9, 47, 5, 45], [10, 4, 5, 47, "value"], [10, 9, 5, 52], [10, 11, 5, 54], [11, 2, 5, 59], [11, 3, 5, 60], [11, 4, 5, 61], [12, 2, 6, 0, "exports"], [12, 9, 6, 7], [12, 10, 6, 8, "<PERSON><PERSON>"], [12, 15, 6, 13], [12, 18, 6, 16], [12, 23, 6, 21], [12, 24, 6, 22], [13, 2, 7, 0], [13, 8, 7, 6, "StackClient_1"], [13, 21, 7, 19], [13, 24, 7, 22, "__importDefault"], [13, 39, 7, 37], [13, 40, 7, 38, "require"], [13, 47, 7, 45], [13, 48, 7, 45, "_dependencyMap"], [13, 62, 7, 45], [13, 82, 7, 61], [13, 83, 7, 62], [13, 84, 7, 63], [14, 2, 8, 0, "exports"], [14, 9, 8, 7], [14, 10, 8, 8, "<PERSON><PERSON>"], [14, 15, 8, 13], [14, 18, 8, 16, "StackClient_1"], [14, 31, 8, 29], [14, 32, 8, 30, "default"], [14, 39, 8, 37], [15, 2, 9, 0], [15, 8, 9, 6, "Screen_1"], [15, 16, 9, 14], [15, 19, 9, 17, "require"], [15, 26, 9, 24], [15, 27, 9, 24, "_dependencyMap"], [15, 41, 9, 24], [15, 63, 9, 42], [15, 64, 9, 43], [16, 2, 10, 0, "StackClient_1"], [16, 15, 10, 13], [16, 16, 10, 14, "default"], [16, 23, 10, 21], [16, 24, 10, 22, "Screen"], [16, 30, 10, 28], [16, 33, 10, 31, "Screen_1"], [16, 41, 10, 39], [16, 42, 10, 40, "Screen"], [16, 48, 10, 46], [17, 2, 11, 0, "exports"], [17, 9, 11, 7], [17, 10, 11, 8, "default"], [17, 17, 11, 15], [17, 20, 11, 18, "StackClient_1"], [17, 33, 11, 31], [17, 34, 11, 32, "default"], [17, 41, 11, 39], [18, 0, 11, 40], [18, 3]], "functionMap": {"names": ["<global>", "<anonymous>"], "mappings": "AAA;wDCC;CDE"}}, "type": "js/module"}]}