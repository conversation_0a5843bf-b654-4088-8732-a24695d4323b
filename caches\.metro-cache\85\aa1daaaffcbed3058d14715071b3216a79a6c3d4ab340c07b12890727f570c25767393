{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./createHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 44, "index": 44}}], "key": "j9sUgJL2drnBoAedJuo4/l2ILqw=", "exportNames": ["*"]}}, {"name": "./gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 45}, "end": {"line": 2, "column": 65, "index": 110}}], "key": "M3YJtGPnWOlAL/cGsCkMRGpSLhc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.longPressHandlerName = exports.longPressGestureHandlerProps = exports.LongPressGestureHandler = void 0;\n  var _createHandler = _interopRequireDefault(require(_dependencyMap[1], \"./createHandler\"));\n  var _gestureHandlerCommon = require(_dependencyMap[2], \"./gestureHandlerCommon\");\n  const longPressGestureHandlerProps = exports.longPressGestureHandlerProps = ['minDurationMs', 'maxDist', 'numberOfPointers'];\n  const longPressHandlerName = exports.longPressHandlerName = 'LongPressGestureHandler';\n  /**\n   * @deprecated LongPressGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.LongPress()` instead.\n   */\n\n  /**\n   * @deprecated LongPressGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.LongPress()` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\n  const LongPressGestureHandler = exports.LongPressGestureHandler = (0, _createHandler.default)({\n    name: longPressHandlerName,\n    allowedProps: [..._gestureHandlerCommon.baseGestureHandlerProps, ...longPressGestureHandlerProps],\n    config: {\n      shouldCancelWhenOutside: true\n    }\n  });\n});", "lineCount": 26, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_createHandler"], [7, 20, 1, 0], [7, 23, 1, 0, "_interopRequireDefault"], [7, 45, 1, 0], [7, 46, 1, 0, "require"], [7, 53, 1, 0], [7, 54, 1, 0, "_dependencyMap"], [7, 68, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 27, 2, 0], [8, 30, 2, 0, "require"], [8, 37, 2, 0], [8, 38, 2, 0, "_dependencyMap"], [8, 52, 2, 0], [9, 2, 3, 7], [9, 8, 3, 13, "longPressGestureHandlerProps"], [9, 36, 3, 41], [9, 39, 3, 41, "exports"], [9, 46, 3, 41], [9, 47, 3, 41, "longPressGestureHandlerProps"], [9, 75, 3, 41], [9, 78, 3, 44], [9, 79, 3, 45], [9, 94, 3, 60], [9, 96, 3, 62], [9, 105, 3, 71], [9, 107, 3, 73], [9, 125, 3, 91], [9, 126, 3, 92], [10, 2, 4, 7], [10, 8, 4, 13, "longPressHandlerName"], [10, 28, 4, 33], [10, 31, 4, 33, "exports"], [10, 38, 4, 33], [10, 39, 4, 33, "longPressHandlerName"], [10, 59, 4, 33], [10, 62, 4, 36], [10, 87, 4, 61], [11, 2, 5, 0], [12, 0, 6, 0], [13, 0, 7, 0], [15, 2, 9, 0], [16, 0, 10, 0], [17, 0, 11, 0], [18, 2, 12, 0], [19, 2, 13, 7], [19, 8, 13, 13, "LongPressGestureHandler"], [19, 31, 13, 36], [19, 34, 13, 36, "exports"], [19, 41, 13, 36], [19, 42, 13, 36, "LongPressGestureHandler"], [19, 65, 13, 36], [19, 68, 13, 39], [19, 72, 13, 39, "createHandler"], [19, 94, 13, 52], [19, 96, 13, 53], [20, 4, 14, 2, "name"], [20, 8, 14, 6], [20, 10, 14, 8, "longPressHandlerName"], [20, 30, 14, 28], [21, 4, 15, 2, "allowedProps"], [21, 16, 15, 14], [21, 18, 15, 16], [21, 19, 15, 17], [21, 22, 15, 20, "baseGestureHandlerProps"], [21, 67, 15, 43], [21, 69, 15, 45], [21, 72, 15, 48, "longPressGestureHandlerProps"], [21, 100, 15, 76], [21, 101, 15, 77], [22, 4, 16, 2, "config"], [22, 10, 16, 8], [22, 12, 16, 10], [23, 6, 17, 4, "shouldCancelWhenOutside"], [23, 29, 17, 27], [23, 31, 17, 29], [24, 4, 18, 2], [25, 2, 19, 0], [25, 3, 19, 1], [25, 4, 19, 2], [26, 0, 19, 3], [26, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}