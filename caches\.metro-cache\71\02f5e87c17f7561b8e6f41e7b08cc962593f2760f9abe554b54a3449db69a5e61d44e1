{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../Image/Image", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 38}}], "key": "x+0sfJh3/nzfUxnJ5JIXsJmNMus=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "./LogBoxButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 42}}], "key": "M6ofQu070ZUTf+Oq+Zz+7FQEgjs=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "./LogBoxImages/close.png", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 39, "column": 18}, "end": {"line": 39, "column": 53}}], "key": "XdH+uXlhufl1K0w18U6SkZPQCuk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = LogBoxNotificationDismissButton;\n  var _View = _interopRequireDefault(require(_dependencyMap[1], \"../../Components/View/View\"));\n  var _Image = _interopRequireDefault(require(_dependencyMap[2], \"../../Image/Image\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[3], \"../../StyleSheet/StyleSheet\"));\n  var _LogBoxButton = _interopRequireDefault(require(_dependencyMap[4], \"./LogBoxButton\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[5], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[6], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[7], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native/Libraries/LogBox/UI/LogBoxNotificationDismissButton.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function LogBoxNotificationDismissButton(props) {\n    return (0, _jsxRuntime.jsx)(_View.default, {\n      style: styles.container,\n      children: (0, _jsxRuntime.jsx)(_LogBoxButton.default, {\n        id: props.id,\n        backgroundColor: {\n          default: LogBoxStyle.getTextColor(0.3),\n          pressed: LogBoxStyle.getTextColor(0.5)\n        },\n        hitSlop: {\n          top: 12,\n          right: 10,\n          bottom: 12,\n          left: 10\n        },\n        onPress: props.onPress,\n        style: styles.press,\n        children: (0, _jsxRuntime.jsx)(_Image.default, {\n          source: require(_dependencyMap[8], \"./LogBoxImages/close.png\"),\n          style: styles.image\n        })\n      })\n    });\n  }\n  var styles = _StyleSheet.default.create({\n    container: {\n      alignSelf: 'center',\n      flexDirection: 'row',\n      flexGrow: 0,\n      flexShrink: 0,\n      flexBasis: 'auto',\n      marginLeft: 5\n    },\n    press: {\n      height: 20,\n      width: 20,\n      borderRadius: 25,\n      alignSelf: 'flex-end',\n      alignItems: 'center',\n      justifyContent: 'center'\n    },\n    image: {\n      height: 8,\n      width: 8,\n      tintColor: LogBoxStyle.getBackgroundColor(1)\n    }\n  });\n});", "lineCount": 63, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_View"], [7, 11, 11, 0], [7, 14, 11, 0, "_interopRequireDefault"], [7, 36, 11, 0], [7, 37, 11, 0, "require"], [7, 44, 11, 0], [7, 45, 11, 0, "_dependencyMap"], [7, 59, 11, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_Image"], [8, 12, 12, 0], [8, 15, 12, 0, "_interopRequireDefault"], [8, 37, 12, 0], [8, 38, 12, 0, "require"], [8, 45, 12, 0], [8, 46, 12, 0, "_dependencyMap"], [8, 60, 12, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_StyleSheet"], [9, 17, 13, 0], [9, 20, 13, 0, "_interopRequireDefault"], [9, 42, 13, 0], [9, 43, 13, 0, "require"], [9, 50, 13, 0], [9, 51, 13, 0, "_dependencyMap"], [9, 65, 13, 0], [10, 2, 14, 0], [10, 6, 14, 0, "_LogBoxButton"], [10, 19, 14, 0], [10, 22, 14, 0, "_interopRequireDefault"], [10, 44, 14, 0], [10, 45, 14, 0, "require"], [10, 52, 14, 0], [10, 53, 14, 0, "_dependencyMap"], [10, 67, 14, 0], [11, 2, 15, 0], [11, 6, 15, 0, "LogBoxStyle"], [11, 17, 15, 0], [11, 20, 15, 0, "_interopRequireWildcard"], [11, 43, 15, 0], [11, 44, 15, 0, "require"], [11, 51, 15, 0], [11, 52, 15, 0, "_dependencyMap"], [11, 66, 15, 0], [12, 2, 16, 0], [12, 6, 16, 0, "React"], [12, 11, 16, 0], [12, 14, 16, 0, "_interopRequireWildcard"], [12, 37, 16, 0], [12, 38, 16, 0, "require"], [12, 45, 16, 0], [12, 46, 16, 0, "_dependencyMap"], [12, 60, 16, 0], [13, 2, 16, 31], [13, 6, 16, 31, "_jsxRuntime"], [13, 17, 16, 31], [13, 20, 16, 31, "require"], [13, 27, 16, 31], [13, 28, 16, 31, "_dependencyMap"], [13, 42, 16, 31], [14, 2, 16, 31], [14, 6, 16, 31, "_jsxFileName"], [14, 18, 16, 31], [15, 2, 16, 31], [15, 11, 16, 31, "_interopRequireWildcard"], [15, 35, 16, 31, "e"], [15, 36, 16, 31], [15, 38, 16, 31, "t"], [15, 39, 16, 31], [15, 68, 16, 31, "WeakMap"], [15, 75, 16, 31], [15, 81, 16, 31, "r"], [15, 82, 16, 31], [15, 89, 16, 31, "WeakMap"], [15, 96, 16, 31], [15, 100, 16, 31, "n"], [15, 101, 16, 31], [15, 108, 16, 31, "WeakMap"], [15, 115, 16, 31], [15, 127, 16, 31, "_interopRequireWildcard"], [15, 150, 16, 31], [15, 162, 16, 31, "_interopRequireWildcard"], [15, 163, 16, 31, "e"], [15, 164, 16, 31], [15, 166, 16, 31, "t"], [15, 167, 16, 31], [15, 176, 16, 31, "t"], [15, 177, 16, 31], [15, 181, 16, 31, "e"], [15, 182, 16, 31], [15, 186, 16, 31, "e"], [15, 187, 16, 31], [15, 188, 16, 31, "__esModule"], [15, 198, 16, 31], [15, 207, 16, 31, "e"], [15, 208, 16, 31], [15, 214, 16, 31, "o"], [15, 215, 16, 31], [15, 217, 16, 31, "i"], [15, 218, 16, 31], [15, 220, 16, 31, "f"], [15, 221, 16, 31], [15, 226, 16, 31, "__proto__"], [15, 235, 16, 31], [15, 243, 16, 31, "default"], [15, 250, 16, 31], [15, 252, 16, 31, "e"], [15, 253, 16, 31], [15, 270, 16, 31, "e"], [15, 271, 16, 31], [15, 294, 16, 31, "e"], [15, 295, 16, 31], [15, 320, 16, 31, "e"], [15, 321, 16, 31], [15, 330, 16, 31, "f"], [15, 331, 16, 31], [15, 337, 16, 31, "o"], [15, 338, 16, 31], [15, 341, 16, 31, "t"], [15, 342, 16, 31], [15, 345, 16, 31, "n"], [15, 346, 16, 31], [15, 349, 16, 31, "r"], [15, 350, 16, 31], [15, 358, 16, 31, "o"], [15, 359, 16, 31], [15, 360, 16, 31, "has"], [15, 363, 16, 31], [15, 364, 16, 31, "e"], [15, 365, 16, 31], [15, 375, 16, 31, "o"], [15, 376, 16, 31], [15, 377, 16, 31, "get"], [15, 380, 16, 31], [15, 381, 16, 31, "e"], [15, 382, 16, 31], [15, 385, 16, 31, "o"], [15, 386, 16, 31], [15, 387, 16, 31, "set"], [15, 390, 16, 31], [15, 391, 16, 31, "e"], [15, 392, 16, 31], [15, 394, 16, 31, "f"], [15, 395, 16, 31], [15, 409, 16, 31, "_t"], [15, 411, 16, 31], [15, 415, 16, 31, "e"], [15, 416, 16, 31], [15, 432, 16, 31, "_t"], [15, 434, 16, 31], [15, 441, 16, 31, "hasOwnProperty"], [15, 455, 16, 31], [15, 456, 16, 31, "call"], [15, 460, 16, 31], [15, 461, 16, 31, "e"], [15, 462, 16, 31], [15, 464, 16, 31, "_t"], [15, 466, 16, 31], [15, 473, 16, 31, "i"], [15, 474, 16, 31], [15, 478, 16, 31, "o"], [15, 479, 16, 31], [15, 482, 16, 31, "Object"], [15, 488, 16, 31], [15, 489, 16, 31, "defineProperty"], [15, 503, 16, 31], [15, 508, 16, 31, "Object"], [15, 514, 16, 31], [15, 515, 16, 31, "getOwnPropertyDescriptor"], [15, 539, 16, 31], [15, 540, 16, 31, "e"], [15, 541, 16, 31], [15, 543, 16, 31, "_t"], [15, 545, 16, 31], [15, 552, 16, 31, "i"], [15, 553, 16, 31], [15, 554, 16, 31, "get"], [15, 557, 16, 31], [15, 561, 16, 31, "i"], [15, 562, 16, 31], [15, 563, 16, 31, "set"], [15, 566, 16, 31], [15, 570, 16, 31, "o"], [15, 571, 16, 31], [15, 572, 16, 31, "f"], [15, 573, 16, 31], [15, 575, 16, 31, "_t"], [15, 577, 16, 31], [15, 579, 16, 31, "i"], [15, 580, 16, 31], [15, 584, 16, 31, "f"], [15, 585, 16, 31], [15, 586, 16, 31, "_t"], [15, 588, 16, 31], [15, 592, 16, 31, "e"], [15, 593, 16, 31], [15, 594, 16, 31, "_t"], [15, 596, 16, 31], [15, 607, 16, 31, "f"], [15, 608, 16, 31], [15, 613, 16, 31, "e"], [15, 614, 16, 31], [15, 616, 16, 31, "t"], [15, 617, 16, 31], [16, 2, 18, 15], [16, 11, 18, 24, "LogBoxNotificationDismissButton"], [16, 42, 18, 55, "LogBoxNotificationDismissButton"], [16, 43, 18, 56, "props"], [16, 48, 21, 1], [16, 50, 21, 15], [17, 4, 22, 2], [17, 11, 23, 4], [17, 15, 23, 4, "_jsxRuntime"], [17, 26, 23, 4], [17, 27, 23, 4, "jsx"], [17, 30, 23, 4], [17, 32, 23, 5, "_View"], [17, 37, 23, 5], [17, 38, 23, 5, "default"], [17, 45, 23, 9], [18, 6, 23, 10, "style"], [18, 11, 23, 15], [18, 13, 23, 17, "styles"], [18, 19, 23, 23], [18, 20, 23, 24, "container"], [18, 29, 23, 34], [19, 6, 23, 34, "children"], [19, 14, 23, 34], [19, 16, 24, 6], [19, 20, 24, 6, "_jsxRuntime"], [19, 31, 24, 6], [19, 32, 24, 6, "jsx"], [19, 35, 24, 6], [19, 37, 24, 7, "_LogBoxButton"], [19, 50, 24, 7], [19, 51, 24, 7, "default"], [19, 58, 24, 19], [20, 8, 25, 8, "id"], [20, 10, 25, 10], [20, 12, 25, 12, "props"], [20, 17, 25, 17], [20, 18, 25, 18, "id"], [20, 20, 25, 21], [21, 8, 26, 8, "backgroundColor"], [21, 23, 26, 23], [21, 25, 26, 25], [22, 10, 27, 10, "default"], [22, 17, 27, 17], [22, 19, 27, 19, "LogBoxStyle"], [22, 30, 27, 30], [22, 31, 27, 31, "getTextColor"], [22, 43, 27, 43], [22, 44, 27, 44], [22, 47, 27, 47], [22, 48, 27, 48], [23, 10, 28, 10, "pressed"], [23, 17, 28, 17], [23, 19, 28, 19, "LogBoxStyle"], [23, 30, 28, 30], [23, 31, 28, 31, "getTextColor"], [23, 43, 28, 43], [23, 44, 28, 44], [23, 47, 28, 47], [24, 8, 29, 8], [24, 9, 29, 10], [25, 8, 30, 8, "hitSlop"], [25, 15, 30, 15], [25, 17, 30, 17], [26, 10, 31, 10, "top"], [26, 13, 31, 13], [26, 15, 31, 15], [26, 17, 31, 17], [27, 10, 32, 10, "right"], [27, 15, 32, 15], [27, 17, 32, 17], [27, 19, 32, 19], [28, 10, 33, 10, "bottom"], [28, 16, 33, 16], [28, 18, 33, 18], [28, 20, 33, 20], [29, 10, 34, 10, "left"], [29, 14, 34, 14], [29, 16, 34, 16], [30, 8, 35, 8], [30, 9, 35, 10], [31, 8, 36, 8, "onPress"], [31, 15, 36, 15], [31, 17, 36, 17, "props"], [31, 22, 36, 22], [31, 23, 36, 23, "onPress"], [31, 30, 36, 31], [32, 8, 37, 8, "style"], [32, 13, 37, 13], [32, 15, 37, 15, "styles"], [32, 21, 37, 21], [32, 22, 37, 22, "press"], [32, 27, 37, 28], [33, 8, 37, 28, "children"], [33, 16, 37, 28], [33, 18, 38, 8], [33, 22, 38, 8, "_jsxRuntime"], [33, 33, 38, 8], [33, 34, 38, 8, "jsx"], [33, 37, 38, 8], [33, 39, 38, 9, "_Image"], [33, 45, 38, 9], [33, 46, 38, 9, "default"], [33, 53, 38, 14], [34, 10, 39, 10, "source"], [34, 16, 39, 16], [34, 18, 39, 18, "require"], [34, 25, 39, 25], [34, 26, 39, 25, "_dependencyMap"], [34, 40, 39, 25], [34, 71, 39, 52], [34, 72, 39, 54], [35, 10, 40, 10, "style"], [35, 15, 40, 15], [35, 17, 40, 17, "styles"], [35, 23, 40, 23], [35, 24, 40, 24, "image"], [36, 8, 40, 30], [36, 9, 41, 9], [37, 6, 41, 10], [37, 7, 42, 20], [38, 4, 42, 21], [38, 5, 43, 10], [38, 6, 43, 11], [39, 2, 45, 0], [40, 2, 47, 0], [40, 6, 47, 6, "styles"], [40, 12, 47, 12], [40, 15, 47, 15, "StyleSheet"], [40, 34, 47, 25], [40, 35, 47, 26, "create"], [40, 41, 47, 32], [40, 42, 47, 33], [41, 4, 48, 2, "container"], [41, 13, 48, 11], [41, 15, 48, 13], [42, 6, 49, 4, "alignSelf"], [42, 15, 49, 13], [42, 17, 49, 15], [42, 25, 49, 23], [43, 6, 50, 4, "flexDirection"], [43, 19, 50, 17], [43, 21, 50, 19], [43, 26, 50, 24], [44, 6, 51, 4, "flexGrow"], [44, 14, 51, 12], [44, 16, 51, 14], [44, 17, 51, 15], [45, 6, 52, 4, "flexShrink"], [45, 16, 52, 14], [45, 18, 52, 16], [45, 19, 52, 17], [46, 6, 53, 4, "flexBasis"], [46, 15, 53, 13], [46, 17, 53, 15], [46, 23, 53, 21], [47, 6, 54, 4, "marginLeft"], [47, 16, 54, 14], [47, 18, 54, 16], [48, 4, 55, 2], [48, 5, 55, 3], [49, 4, 56, 2, "press"], [49, 9, 56, 7], [49, 11, 56, 9], [50, 6, 57, 4, "height"], [50, 12, 57, 10], [50, 14, 57, 12], [50, 16, 57, 14], [51, 6, 58, 4, "width"], [51, 11, 58, 9], [51, 13, 58, 11], [51, 15, 58, 13], [52, 6, 59, 4, "borderRadius"], [52, 18, 59, 16], [52, 20, 59, 18], [52, 22, 59, 20], [53, 6, 60, 4, "alignSelf"], [53, 15, 60, 13], [53, 17, 60, 15], [53, 27, 60, 25], [54, 6, 61, 4, "alignItems"], [54, 16, 61, 14], [54, 18, 61, 16], [54, 26, 61, 24], [55, 6, 62, 4, "justifyContent"], [55, 20, 62, 18], [55, 22, 62, 20], [56, 4, 63, 2], [56, 5, 63, 3], [57, 4, 64, 2, "image"], [57, 9, 64, 7], [57, 11, 64, 9], [58, 6, 65, 4, "height"], [58, 12, 65, 10], [58, 14, 65, 12], [58, 15, 65, 13], [59, 6, 66, 4, "width"], [59, 11, 66, 9], [59, 13, 66, 11], [59, 14, 66, 12], [60, 6, 67, 4, "tintColor"], [60, 15, 67, 13], [60, 17, 67, 15, "LogBoxStyle"], [60, 28, 67, 26], [60, 29, 67, 27, "getBackgroundColor"], [60, 47, 67, 45], [60, 48, 67, 46], [60, 49, 67, 47], [61, 4, 68, 2], [62, 2, 69, 0], [62, 3, 69, 1], [62, 4, 69, 2], [63, 0, 69, 3], [63, 3]], "functionMap": {"names": ["<global>", "LogBoxNotificationDismissButton"], "mappings": "AAA;eCiB;CD2B"}}, "type": "js/module"}]}