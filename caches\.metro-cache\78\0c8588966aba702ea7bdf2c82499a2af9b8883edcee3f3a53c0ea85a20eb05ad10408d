{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./createHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 44, "index": 44}}], "key": "j9sUgJL2drnBoAedJuo4/l2ILqw=", "exportNames": ["*"]}}, {"name": "./gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 45}, "end": {"line": 2, "column": 65, "index": 110}}], "key": "M3YJtGPnWOlAL/cGsCkMRGpSLhc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.rotationHandlerName = exports.RotationGestureHandler = void 0;\n  var _createHandler = _interopRequireDefault(require(_dependencyMap[1], \"./createHandler\"));\n  var _gestureHandlerCommon = require(_dependencyMap[2], \"./gestureHandlerCommon\");\n  /**\n   * @deprecated RotationGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Rotation()` instead.\n   */\n\n  const rotationHandlerName = exports.rotationHandlerName = 'RotationGestureHandler';\n  /**\n   * @deprecated RotationGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Rotation()` instead.\n   */\n\n  /**\n   * @deprecated RotationGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Rotation()` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\n  const RotationGestureHandler = exports.RotationGestureHandler = (0, _createHandler.default)({\n    name: rotationHandlerName,\n    allowedProps: _gestureHandlerCommon.baseGestureHandlerProps,\n    config: {}\n  });\n});", "lineCount": 27, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_createHandler"], [7, 20, 1, 0], [7, 23, 1, 0, "_interopRequireDefault"], [7, 45, 1, 0], [7, 46, 1, 0, "require"], [7, 53, 1, 0], [7, 54, 1, 0, "_dependencyMap"], [7, 68, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 27, 2, 0], [8, 30, 2, 0, "require"], [8, 37, 2, 0], [8, 38, 2, 0, "_dependencyMap"], [8, 52, 2, 0], [9, 2, 3, 0], [10, 0, 4, 0], [11, 0, 5, 0], [13, 2, 7, 7], [13, 8, 7, 13, "rotationHandlerName"], [13, 27, 7, 32], [13, 30, 7, 32, "exports"], [13, 37, 7, 32], [13, 38, 7, 32, "rotationHandlerName"], [13, 57, 7, 32], [13, 60, 7, 35], [13, 84, 7, 59], [14, 2, 8, 0], [15, 0, 9, 0], [16, 0, 10, 0], [18, 2, 12, 0], [19, 0, 13, 0], [20, 0, 14, 0], [21, 2, 15, 0], [22, 2, 16, 7], [22, 8, 16, 13, "RotationGestureHandler"], [22, 30, 16, 35], [22, 33, 16, 35, "exports"], [22, 40, 16, 35], [22, 41, 16, 35, "RotationGestureHandler"], [22, 63, 16, 35], [22, 66, 16, 38], [22, 70, 16, 38, "createHandler"], [22, 92, 16, 51], [22, 94, 16, 52], [23, 4, 17, 2, "name"], [23, 8, 17, 6], [23, 10, 17, 8, "rotationHandlerName"], [23, 29, 17, 27], [24, 4, 18, 2, "allowedProps"], [24, 16, 18, 14], [24, 18, 18, 16, "baseGestureHandlerProps"], [24, 63, 18, 39], [25, 4, 19, 2, "config"], [25, 10, 19, 8], [25, 12, 19, 10], [25, 13, 19, 11], [26, 2, 20, 0], [26, 3, 20, 1], [26, 4, 20, 2], [27, 0, 20, 3], [27, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}