{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 42, "index": 57}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../animation/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 58}, "end": {"line": 4, "column": 58, "index": 116}}], "key": "6GAoNhzQ7+ZSX+WBszeRuj9gSFc=", "exportNames": ["*"]}}, {"name": "../commonTypes.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 117}, "end": {"line": 5, "column": 54, "index": 171}}], "key": "9j6OaBzi0V5srVAX3iTMRrWOBnc=", "exportNames": ["*"]}}, {"name": "../core.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 172}, "end": {"line": 6, "column": 68, "index": 240}}], "key": "t9lN+rBifYCuaIC+E0heKxRioMA=", "exportNames": ["*"]}}, {"name": "../errors.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 241}, "end": {"line": 7, "column": 47, "index": 288}}], "key": "hqwpWRawU/ruYp+nBkn/8IqEHoU=", "exportNames": ["*"]}}, {"name": "../PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 289}, "end": {"line": 8, "column": 63, "index": 352}}], "key": "iJ0YgfbcPgrclB5t1J5j2jedwxA=", "exportNames": ["*"]}}, {"name": "../processBoxShadow.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 353}, "end": {"line": 9, "column": 58, "index": 411}}], "key": "/4O2z86Wm5PMUTe020ro8DvHbsg=", "exportNames": ["*"]}}, {"name": "../updateProps/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 412}, "end": {"line": 10, "column": 78, "index": 490}}], "key": "qbpfy52wS90DxIiWiB1QiooQv/k=", "exportNames": ["*"]}}, {"name": "../ViewDescriptorsSet.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 491}, "end": {"line": 11, "column": 66, "index": 557}}], "key": "5rJBe2sTOa1QjbC233zArbM4pZs=", "exportNames": ["*"]}}, {"name": "./useSharedValue.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 558}, "end": {"line": 12, "column": 53, "index": 611}}], "key": "DVYfvqRexdAwKhee+lmO6Kkit04=", "exportNames": ["*"]}}, {"name": "./utils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 612}, "end": {"line": 13, "column": 97, "index": 709}}], "key": "NIaSEHO1E48gsZc7jH9Ex1xTHgE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedStyle = useAnimatedStyle;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _index = require(_dependencyMap[1], \"../animation/index.js\");\n  var _commonTypes = require(_dependencyMap[2], \"../commonTypes.js\");\n  var _core = require(_dependencyMap[3], \"../core.js\");\n  var _errors = require(_dependencyMap[4], \"../errors.js\");\n  var _PlatformChecker = require(_dependencyMap[5], \"../PlatformChecker.js\");\n  var _processBoxShadow = require(_dependencyMap[6], \"../processBoxShadow.js\");\n  var _index2 = require(_dependencyMap[7], \"../updateProps/index.js\");\n  var _ViewDescriptorsSet = require(_dependencyMap[8], \"../ViewDescriptorsSet.js\");\n  var _useSharedValue = require(_dependencyMap[9], \"./useSharedValue.js\");\n  var _utils = require(_dependencyMap[10], \"./utils.js\");\n  const SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n  const _worklet_15514483176912_init_data = {\n    code: \"function prepareAnimation_reactNativeReanimated_useAnimatedStyleJs1(frameTimestamp,animatedProp,lastAnimation,lastValue){const prepareAnimation_reactNativeReanimated_useAnimatedStyleJs1=this._recur;if(Array.isArray(animatedProp)){animatedProp.forEach(function(prop,index){prepareAnimation_reactNativeReanimated_useAnimatedStyleJs1(frameTimestamp,prop,lastAnimation&&lastAnimation[index],lastValue&&lastValue[index]);});}if(typeof animatedProp==='object'&&animatedProp.onFrame){const animation=animatedProp;let value=animation.current;if(lastValue!==undefined&&lastValue!==null){if(typeof lastValue==='object'){if(lastValue.value!==undefined){value=lastValue.value;}else if(lastValue.onFrame!==undefined){if((lastAnimation===null||lastAnimation===void 0?void 0:lastAnimation.current)!==undefined){value=lastAnimation.current;}else if((lastValue===null||lastValue===void 0?void 0:lastValue.current)!==undefined){value=lastValue.current;}}}else{value=lastValue;}}animation.callStart=function(timestamp){animation.onStart(animation,value,timestamp,lastAnimation);};animation.callStart(frameTimestamp);animation.callStart=null;}else if(typeof animatedProp==='object'){Object.keys(animatedProp).forEach(function(key){return prepareAnimation_reactNativeReanimated_useAnimatedStyleJs1(frameTimestamp,animatedProp[key],lastAnimation&&lastAnimation[key],lastValue&&lastValue[key]);});}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedStyle.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"prepareAnimation_reactNativeReanimated_useAnimatedStyleJs1\\\",\\\"frameTimestamp\\\",\\\"animatedProp\\\",\\\"lastAnimation\\\",\\\"lastValue\\\",\\\"_recur\\\",\\\"Array\\\",\\\"isArray\\\",\\\"forEach\\\",\\\"prop\\\",\\\"index\\\",\\\"onFrame\\\",\\\"animation\\\",\\\"value\\\",\\\"current\\\",\\\"undefined\\\",\\\"callStart\\\",\\\"timestamp\\\",\\\"onStart\\\",\\\"Object\\\",\\\"keys\\\",\\\"key\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedStyle.js\\\"],\\\"mappings\\\":\\\"AAcA,SAAAA,0DAAwDA,CAAAC,cAAwB,CAAEC,YAAA,CAAAC,aAAA,CAAAC,SAAA,QAAAJ,0DAAA,MAAAK,MAAA,CAGhF,GAAIC,KAAK,CAACC,OAAO,CAACL,YAAY,CAAC,CAAE,CAC/BA,YAAY,CAACM,OAAO,CAAC,SAACC,IAAI,CAAEC,KAAK,CAAK,CACpCV,0DAAwD,CAAAC,cAAc,CAAKQ,IAAG,CAAAN,aAAa,EAAAA,aAAiB,CAAAO,KAAA,EAAAN,SAAA,EAAAA,SAAA,CAAAM,KAAA,GAC9G,CAAC,CAAC,CAEJ,CACA,GAAI,MAAO,CAAAR,YAAY,GAAK,QAAQ,EAAIA,YAAY,CAACS,OAAO,CAAE,CAC5D,KAAM,CAAAC,SAAS,CAAGV,YAAY,CAC9B,GAAI,CAAAW,KAAK,CAAGD,SAAS,CAACE,OAAO,CAC7B,GAAIV,SAAS,GAAKW,SAAS,EAAIX,SAAS,GAAK,IAAI,CAAE,CACjD,GAAI,MAAO,CAAAA,SAAS,GAAK,QAAQ,CAAE,CACjC,GAAIA,SAAS,CAACS,KAAK,GAAKE,SAAS,CAAE,CAEjCF,KAAK,CAAGT,SAAS,CAACS,KAAK,CACzB,CAAC,IAAM,IAAIT,SAAS,CAACO,OAAO,GAAKI,SAAS,CAAE,CAC1C,GAAI,CAAAZ,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEW,OAAO,IAAKC,SAAS,CAAE,CAExCF,KAAK,CAAGV,aAAa,CAACW,OAAO,CAC/B,CAAC,IAAM,IAAI,CAAAV,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEU,OAAO,IAAKC,SAAS,CAAE,CAE3CF,KAAK,CAAGT,SAAS,CAACU,OAAO,CAC3B,CACF,CACF,CAAC,IAAM,CAELD,KAAK,CAAGT,SAAS,CACnB,CACF,CACAQ,SAAS,CAACI,SAAS,CAAG,SAAAC,SAAS,CAAI,CACjCL,SAAS,CAACM,OAAO,CAACN,SAAS,CAAEC,KAAK,CAAEI,SAAS,CAAEd,aAAa,CAAC,CAC/D,CAAC,CACDS,SAAS,CAACI,SAAS,CAACf,cAAc,CAAC,CACnCW,SAAS,CAACI,SAAS,CAAG,IAAI,CAC5B,CAAC,IAAM,IAAI,MAAO,CAAAd,YAAY,GAAK,QAAQ,CAAE,CAE3CiB,MAAM,CAACC,IAAI,CAAClB,YAAY,CAAC,CAACM,OAAO,CAAC,SAAAa,GAAG,QAAI,CAAArB,0DAAoD,CAAAC,cAAiB,CAAAC,YAAkB,CAAAmB,GAAE,EAAAlB,aAAa,EAASA,aAAM,CAAAkB,GAAA,EAAAjB,SAAA,EAAAA,SAAA,CAAAiB,GAAA,KAAC,CACjK,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const prepareAnimation = function () {\n    const _e = [new global.Error(), 1, -27];\n    const prepareAnimation = function (frameTimestamp, animatedProp, lastAnimation, lastValue) {\n      if (Array.isArray(animatedProp)) {\n        animatedProp.forEach((prop, index) => {\n          prepareAnimation(frameTimestamp, prop, lastAnimation && lastAnimation[index], lastValue && lastValue[index]);\n        });\n        // return animatedProp;\n      }\n      if (typeof animatedProp === 'object' && animatedProp.onFrame) {\n        const animation = animatedProp;\n        let value = animation.current;\n        if (lastValue !== undefined && lastValue !== null) {\n          if (typeof lastValue === 'object') {\n            if (lastValue.value !== undefined) {\n              // previously it was a shared value\n              value = lastValue.value;\n            } else if (lastValue.onFrame !== undefined) {\n              if (lastAnimation?.current !== undefined) {\n                // it was an animation before, copy its state\n                value = lastAnimation.current;\n              } else if (lastValue?.current !== undefined) {\n                // it was initialized\n                value = lastValue.current;\n              }\n            }\n          } else {\n            // previously it was a plain value, just set it as starting point\n            value = lastValue;\n          }\n        }\n        animation.callStart = timestamp => {\n          animation.onStart(animation, value, timestamp, lastAnimation);\n        };\n        animation.callStart(frameTimestamp);\n        animation.callStart = null;\n      } else if (typeof animatedProp === 'object') {\n        // it is an object\n        Object.keys(animatedProp).forEach(key => prepareAnimation(frameTimestamp, animatedProp[key], lastAnimation && lastAnimation[key], lastValue && lastValue[key]));\n      }\n    };\n    prepareAnimation.__closure = {};\n    prepareAnimation.__workletHash = 15514483176912;\n    prepareAnimation.__initData = _worklet_15514483176912_init_data;\n    prepareAnimation.__stackDetails = _e;\n    return prepareAnimation;\n  }();\n  const _worklet_7950007328399_init_data = {\n    code: \"function runAnimations_reactNativeReanimated_useAnimatedStyleJs2(animation,timestamp,key,result,animationsActive,forceCopyAnimation){const runAnimations_reactNativeReanimated_useAnimatedStyleJs2=this._recur;if(!animationsActive.value){return true;}if(Array.isArray(animation)){result[key]=[];let allFinished=true;forceCopyAnimation=key==='boxShadow';animation.forEach(function(entry,index){if(!runAnimations_reactNativeReanimated_useAnimatedStyleJs2(entry,timestamp,index,result[key],animationsActive,forceCopyAnimation)){allFinished=false;}});return allFinished;}else if(typeof animation==='object'&&animation.onFrame){let finished=true;if(!animation.finished){if(animation.callStart){animation.callStart(timestamp);animation.callStart=null;}finished=animation.onFrame(animation,timestamp);animation.timestamp=timestamp;if(finished){animation.finished=true;animation.callback&&animation.callback(true);}}if(forceCopyAnimation){result[key]={...animation.current};}else{result[key]=animation.current;}return finished;}else if(typeof animation==='object'){result[key]={};let allFinished=true;Object.keys(animation).forEach(function(k){if(!runAnimations_reactNativeReanimated_useAnimatedStyleJs2(animation[k],timestamp,k,result[key],animationsActive,forceCopyAnimation)){allFinished=false;}});return allFinished;}else{result[key]=animation;return true;}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedStyle.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"runAnimations_reactNativeReanimated_useAnimatedStyleJs2\\\",\\\"animation\\\",\\\"timestamp\\\",\\\"key\\\",\\\"result\\\",\\\"animationsActive\\\",\\\"forceCopyAnimation\\\",\\\"_recur\\\",\\\"value\\\",\\\"Array\\\",\\\"isArray\\\",\\\"allFinished\\\",\\\"forEach\\\",\\\"entry\\\",\\\"index\\\",\\\"onFrame\\\",\\\"finished\\\",\\\"callStart\\\",\\\"callback\\\",\\\"current\\\",\\\"Object\\\",\\\"keys\\\",\\\"k\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedStyle.js\\\"],\\\"mappings\\\":\\\"AAuDA,SAAAA,uDAA0DA,CAAAC,SAAA,CAAgBC,SAAE,CAAAC,GAAA,CAAAC,MAAoB,CAAAC,gBAAA,CAAAC,kBAAA,QAAAN,uDAAA,MAAAO,MAAA,CAG9F,GAAI,CAACF,gBAAgB,CAACG,KAAK,CAAE,CAC3B,MAAO,KAAI,CACb,CACA,GAAIC,KAAK,CAACC,OAAO,CAACT,SAAS,CAAC,CAAE,CAC5BG,MAAM,CAACD,GAAG,CAAC,CAAG,EAAE,CAChB,GAAI,CAAAQ,WAAW,CAAG,IAAI,CACtBL,kBAAkB,CAAGH,GAAG,GAAK,WAAW,CACxCF,SAAS,CAACW,OAAO,CAAC,SAACC,KAAK,CAAEC,KAAK,CAAK,CAClC,GAAI,CAACd,uDAAoD,CAAAa,KAAA,CAAAX,SAAkB,CAAAY,KAAA,CAAAV,MAAA,CAAkBD,GAAG,EAAAE,gBAAA,CAAAC,kBAAA,GAC9FK,WAAW,CAAG,KAAK,CACrB,CACF,CAAC,CAAC,CACF,MAAO,CAAAA,WAAW,CACpB,CAAC,IAAM,IAAI,MAAO,CAAAV,SAAS,GAAK,QAAQ,EAAIA,SAAS,CAACc,OAAO,CAAE,CAC7D,GAAI,CAAAC,QAAQ,CAAG,IAAI,CACnB,GAAI,CAACf,SAAS,CAACe,QAAQ,CAAE,CACvB,GAAIf,SAAS,CAACgB,SAAS,CAAE,CACvBhB,SAAS,CAACgB,SAAS,CAACf,SAAS,CAAC,CAC9BD,SAAS,CAACgB,SAAS,CAAG,IAAI,CAC5B,CACAD,QAAQ,CAAGf,SAAS,CAACc,OAAO,CAACd,SAAS,CAAEC,SAAS,CAAC,CAClDD,SAAS,CAACC,SAAS,CAAGA,SAAS,CAC/B,GAAIc,QAAQ,CAAE,CACZf,SAAS,CAACe,QAAQ,CAAG,IAAI,CACzBf,SAAS,CAACiB,QAAQ,EAAIjB,SAAS,CAACiB,QAAQ,CAAC,IAAmB,CAAC,CAC/D,CACF,CAMA,GAAIZ,kBAAkB,CAAE,CACtBF,MAAM,CAACD,GAAG,CAAC,CAAG,CACZ,GAAGF,SAAS,CAACkB,OACf,CAAC,CACH,CAAC,IAAM,CACLf,MAAM,CAACD,GAAG,CAAC,CAAGF,SAAS,CAACkB,OAAO,CACjC,CACA,MAAO,CAAAH,QAAQ,CACjB,CAAC,IAAM,IAAI,MAAO,CAAAf,SAAS,GAAK,QAAQ,CAAE,CACxCG,MAAM,CAACD,GAAG,CAAC,CAAG,CAAC,CAAC,CAChB,GAAI,CAAAQ,WAAW,CAAG,IAAI,CACtBS,MAAM,CAACC,IAAI,CAACpB,SAAS,CAAC,CAACW,OAAO,CAAC,SAAAU,CAAC,CAAI,CAClC,GAAI,CAACtB,uDAAuD,CAAAC,SAAA,CAAAqB,CAAA,EAAApB,SAAkB,CAAAoB,CAAA,CAAAlB,MAAA,CAAkBD,GAAG,EAAAE,gBAAA,CAAAC,kBAAA,GACjGK,WAAW,CAAG,KAAK,CACrB,CACF,CAAC,CAAC,CACF,MAAO,CAAAA,WAAW,CACpB,CAAC,IAAM,CACLP,MAAM,CAACD,GAAG,CAAC,CAAGF,SAAS,CACvB,MAAO,KAAI,CACb,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const runAnimations = function () {\n    const _e = [new global.Error(), 1, -27];\n    const runAnimations = function (animation, timestamp, key, result, animationsActive, forceCopyAnimation) {\n      if (!animationsActive.value) {\n        return true;\n      }\n      if (Array.isArray(animation)) {\n        result[key] = [];\n        let allFinished = true;\n        forceCopyAnimation = key === 'boxShadow';\n        animation.forEach((entry, index) => {\n          if (!runAnimations(entry, timestamp, index, result[key], animationsActive, forceCopyAnimation)) {\n            allFinished = false;\n          }\n        });\n        return allFinished;\n      } else if (typeof animation === 'object' && animation.onFrame) {\n        let finished = true;\n        if (!animation.finished) {\n          if (animation.callStart) {\n            animation.callStart(timestamp);\n            animation.callStart = null;\n          }\n          finished = animation.onFrame(animation, timestamp);\n          animation.timestamp = timestamp;\n          if (finished) {\n            animation.finished = true;\n            animation.callback && animation.callback(true /* finished */);\n          }\n        }\n        /*\n         * If `animation.current` is a boxShadow object, spread its properties into a new object\n         * to avoid modifying the original reference. This ensures when `newValues` has a nested color prop, it stays unparsed\n         * in rgba format, allowing the animation to run correctly.\n         */\n        if (forceCopyAnimation) {\n          result[key] = {\n            ...animation.current\n          };\n        } else {\n          result[key] = animation.current;\n        }\n        return finished;\n      } else if (typeof animation === 'object') {\n        result[key] = {};\n        let allFinished = true;\n        Object.keys(animation).forEach(k => {\n          if (!runAnimations(animation[k], timestamp, k, result[key], animationsActive, forceCopyAnimation)) {\n            allFinished = false;\n          }\n        });\n        return allFinished;\n      } else {\n        result[key] = animation;\n        return true;\n      }\n    };\n    runAnimations.__closure = {};\n    runAnimations.__workletHash = 7950007328399;\n    runAnimations.__initData = _worklet_7950007328399_init_data;\n    runAnimations.__stackDetails = _e;\n    return runAnimations;\n  }();\n  const _worklet_9037738663389_init_data = {\n    code: \"function styleUpdater_reactNativeReanimated_useAnimatedStyleJs3(viewDescriptors,updater,state,animationsActive,isAnimatedProps=false){const{SHOULD_BE_USE_WEB,processBoxShadow,isAnimated,prepareAnimation,runAnimations,updateProps,shallowEqual}=this.__closure;var _state$animations,_updater;const animations=(_state$animations=state.animations)!==null&&_state$animations!==void 0?_state$animations:{};const newValues=(_updater=updater())!==null&&_updater!==void 0?_updater:{};const oldValues=state.last;const nonAnimatedNewValues={};let hasAnimations=false;let frameTimestamp;let hasNonAnimatedValues=false;if(!SHOULD_BE_USE_WEB&&newValues.boxShadow){processBoxShadow(newValues);}for(const key in newValues){const value=newValues[key];if(isAnimated(value)){frameTimestamp=global.__frameTimestamp||global._getAnimationTimestamp();prepareAnimation(frameTimestamp,value,animations[key],oldValues[key]);animations[key]=value;hasAnimations=true;}else{hasNonAnimatedValues=true;nonAnimatedNewValues[key]=value;delete animations[key];}}if(hasAnimations){const frame=function(timestamp){const{animations:animations,last:last,isAnimationCancelled:isAnimationCancelled}=state;if(isAnimationCancelled){state.isAnimationRunning=false;return;}const updates={};let allFinished=true;for(const propName in animations){const finished=runAnimations(animations[propName],timestamp,propName,updates,animationsActive);if(finished){if(Array.isArray(updates[propName])){updates[propName].forEach(function(obj){for(const prop in obj){last[propName][prop]=obj[prop];}});}else{last[propName]=updates[propName];}delete animations[propName];}else{allFinished=false;}}if(updates){updateProps(viewDescriptors,updates);}if(!allFinished){requestAnimationFrame(frame);}else{state.isAnimationRunning=false;}};state.animations=animations;if(!state.isAnimationRunning){state.isAnimationCancelled=false;state.isAnimationRunning=true;frame(frameTimestamp);}if(hasNonAnimatedValues){updateProps(viewDescriptors,nonAnimatedNewValues);}}else{state.isAnimationCancelled=true;state.animations=[];if(!shallowEqual(oldValues,newValues)){updateProps(viewDescriptors,newValues,isAnimatedProps);}}state.last=newValues;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedStyle.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"styleUpdater_reactNativeReanimated_useAnimatedStyleJs3\\\",\\\"viewDescriptors\\\",\\\"updater\\\",\\\"state\\\",\\\"animationsActive\\\",\\\"isAnimatedProps\\\",\\\"SHOULD_BE_USE_WEB\\\",\\\"processBoxShadow\\\",\\\"isAnimated\\\",\\\"prepareAnimation\\\",\\\"runAnimations\\\",\\\"updateProps\\\",\\\"shallowEqual\\\",\\\"__closure\\\",\\\"_state$animations\\\",\\\"_updater\\\",\\\"animations\\\",\\\"newValues\\\",\\\"oldValues\\\",\\\"last\\\",\\\"nonAnimatedNewValues\\\",\\\"hasAnimations\\\",\\\"frameTimestamp\\\",\\\"hasNonAnimatedValues\\\",\\\"boxShadow\\\",\\\"key\\\",\\\"value\\\",\\\"global\\\",\\\"__frameTimestamp\\\",\\\"_getAnimationTimestamp\\\",\\\"frame\\\",\\\"timestamp\\\",\\\"isAnimationCancelled\\\",\\\"isAnimationRunning\\\",\\\"updates\\\",\\\"allFinished\\\",\\\"propName\\\",\\\"finished\\\",\\\"Array\\\",\\\"isArray\\\",\\\"forEach\\\",\\\"obj\\\",\\\"prop\\\",\\\"requestAnimationFrame\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedStyle.js\\\"],\\\"mappings\\\":\\\"AAgHA,SAAAA,sDAAuDA,CAAAC,eAAkB,CAAAC,OAAA,CAAeC,KAAG,CAAKC,gBAAE,CAAAC,eAAA,cAAAC,iBAAA,CAAAC,gBAAA,CAAAC,UAAA,CAAAC,gBAAA,CAAAC,aAAA,CAAAC,WAAA,CAAAC,YAAA,OAAAC,SAAA,KAAAC,iBAAA,CAAAC,QAAA,CAGhG,KAAM,CAAAC,UAAU,EAAAF,iBAAA,CAAGX,KAAK,CAACa,UAAU,UAAAF,iBAAA,UAAAA,iBAAA,CAAI,CAAC,CAAC,CACzC,KAAM,CAAAG,SAAS,EAAAF,QAAA,CAAGb,OAAO,CAAC,CAAC,UAAAa,QAAA,UAAAA,QAAA,CAAI,CAAC,CAAC,CACjC,KAAM,CAAAG,SAAS,CAAGf,KAAK,CAACgB,IAAI,CAC5B,KAAM,CAAAC,oBAAoB,CAAG,CAAC,CAAC,CAC/B,GAAI,CAAAC,aAAa,CAAG,KAAK,CACzB,GAAI,CAAAC,cAAc,CAClB,GAAI,CAAAC,oBAAoB,CAAG,KAAK,CAChC,GAAI,CAACjB,iBAAiB,EAAIW,SAAS,CAACO,SAAS,CAAE,CAC7CjB,gBAAgB,CAACU,SAAS,CAAC,CAC7B,CACA,IAAK,KAAM,CAAAQ,GAAG,GAAI,CAAAR,SAAS,CAAE,CAC3B,KAAM,CAAAS,KAAK,CAAGT,SAAS,CAACQ,GAAG,CAAC,CAC5B,GAAIjB,UAAU,CAACkB,KAAK,CAAC,CAAE,CACrBJ,cAAc,CAAGK,MAAM,CAACC,gBAAgB,EAAID,MAAM,CAACE,sBAAsB,CAAC,CAAC,CAC3EpB,gBAAgB,CAACa,cAAc,CAAEI,KAAK,CAAEV,UAAU,CAACS,GAAG,CAAC,CAAEP,SAAS,CAACO,GAAG,CAAC,CAAC,CACxET,UAAU,CAACS,GAAG,CAAC,CAAGC,KAAK,CACvBL,aAAa,CAAG,IAAI,CACtB,CAAC,IAAM,CACLE,oBAAoB,CAAG,IAAI,CAC3BH,oBAAoB,CAACK,GAAG,CAAC,CAAGC,KAAK,CACjC,MAAO,CAAAV,UAAU,CAACS,GAAG,CAAC,CACxB,CACF,CACA,GAAIJ,aAAa,CAAE,CACjB,KAAM,CAAAS,KAAK,CAAG,QAAAA,CAAAC,SAAS,CAAI,CAEzB,KAAM,CACJf,UAAU,CAAVA,UAAU,CACVG,IAAI,CAAJA,IAAI,CACJa,oBAAA,CAAAA,oBACF,CAAC,CAAG7B,KAAK,CACT,GAAI6B,oBAAoB,CAAE,CACxB7B,KAAK,CAAC8B,kBAAkB,CAAG,KAAK,CAChC,OACF,CACA,KAAM,CAAAC,OAAO,CAAG,CAAC,CAAC,CAClB,GAAI,CAAAC,WAAW,CAAG,IAAI,CACtB,IAAK,KAAM,CAAAC,QAAQ,GAAI,CAAApB,UAAU,CAAE,CACjC,KAAM,CAAAqB,QAAQ,CAAG3B,aAAa,CAACM,UAAU,CAACoB,QAAQ,CAAC,CAAEL,SAAS,CAAEK,QAAQ,CAAEF,OAAO,CAAE9B,gBAAgB,CAAC,CACpG,GAAIiC,QAAQ,CAAE,CAOZ,GAAIC,KAAK,CAACC,OAAO,CAACL,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAE,CACpCF,OAAO,CAACE,QAAQ,CAAC,CAACI,OAAO,CAAC,SAAAC,GAAG,CAAI,CAC/B,IAAK,KAAM,CAAAC,IAAI,GAAI,CAAAD,GAAG,CAAE,CACtBtB,IAAI,CAACiB,QAAQ,CAAC,CAACM,IAAI,CAAC,CAAGD,GAAG,CAACC,IAAI,CAAC,CAClC,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACLvB,IAAI,CAACiB,QAAQ,CAAC,CAAGF,OAAO,CAACE,QAAQ,CAAC,CACpC,CACA,MAAO,CAAApB,UAAU,CAACoB,QAAQ,CAAC,CAC7B,CAAC,IAAM,CACLD,WAAW,CAAG,KAAK,CACrB,CACF,CACA,GAAID,OAAO,CAAE,CACXvB,WAAW,CAACV,eAAe,CAAEiC,OAAO,CAAC,CACvC,CACA,GAAI,CAACC,WAAW,CAAE,CAChBQ,qBAAqB,CAACb,KAAK,CAAC,CAC9B,CAAC,IAAM,CACL3B,KAAK,CAAC8B,kBAAkB,CAAG,KAAK,CAClC,CACF,CAAC,CACD9B,KAAK,CAACa,UAAU,CAAGA,UAAU,CAC7B,GAAI,CAACb,KAAK,CAAC8B,kBAAkB,CAAE,CAC7B9B,KAAK,CAAC6B,oBAAoB,CAAG,KAAK,CAClC7B,KAAK,CAAC8B,kBAAkB,CAAG,IAAI,CAC/BH,KAAK,CAACR,cAAc,CAAC,CACvB,CACA,GAAIC,oBAAoB,CAAE,CACxBZ,WAAW,CAACV,eAAe,CAAEmB,oBAAoB,CAAC,CACpD,CACF,CAAC,IAAM,CACLjB,KAAK,CAAC6B,oBAAoB,CAAG,IAAI,CACjC7B,KAAK,CAACa,UAAU,CAAG,EAAE,CACrB,GAAI,CAACJ,YAAY,CAACM,SAAS,CAAED,SAAS,CAAC,CAAE,CACvCN,WAAW,CAACV,eAAe,CAAEgB,SAAS,CAAEZ,eAAe,CAAC,CAC1D,CACF,CACAF,KAAK,CAACgB,IAAI,CAAGF,SAAS,CACxB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const styleUpdater = function () {\n    const _e = [new global.Error(), -8, -27];\n    const styleUpdater = function (viewDescriptors, updater, state, animationsActive, isAnimatedProps = false) {\n      const animations = state.animations ?? {};\n      const newValues = updater() ?? {};\n      const oldValues = state.last;\n      const nonAnimatedNewValues = {};\n      let hasAnimations = false;\n      let frameTimestamp;\n      let hasNonAnimatedValues = false;\n      if (!SHOULD_BE_USE_WEB && newValues.boxShadow) {\n        (0, _processBoxShadow.processBoxShadow)(newValues);\n      }\n      for (const key in newValues) {\n        const value = newValues[key];\n        if ((0, _utils.isAnimated)(value)) {\n          frameTimestamp = global.__frameTimestamp || global._getAnimationTimestamp();\n          prepareAnimation(frameTimestamp, value, animations[key], oldValues[key]);\n          animations[key] = value;\n          hasAnimations = true;\n        } else {\n          hasNonAnimatedValues = true;\n          nonAnimatedNewValues[key] = value;\n          delete animations[key];\n        }\n      }\n      if (hasAnimations) {\n        const frame = timestamp => {\n          // eslint-disable-next-line @typescript-eslint/no-shadow\n          const {\n            animations,\n            last,\n            isAnimationCancelled\n          } = state;\n          if (isAnimationCancelled) {\n            state.isAnimationRunning = false;\n            return;\n          }\n          const updates = {};\n          let allFinished = true;\n          for (const propName in animations) {\n            const finished = runAnimations(animations[propName], timestamp, propName, updates, animationsActive);\n            if (finished) {\n              /**\n               * If the animated prop is an array, we need to directly set each\n               * property (manually spread it). This prevents issues where the color\n               * prop might be incorrectly linked with its `toValue` and `current`\n               * states, causing abrupt transitions or 'jumps' in animation states.\n               */\n              if (Array.isArray(updates[propName])) {\n                updates[propName].forEach(obj => {\n                  for (const prop in obj) {\n                    last[propName][prop] = obj[prop];\n                  }\n                });\n              } else {\n                last[propName] = updates[propName];\n              }\n              delete animations[propName];\n            } else {\n              allFinished = false;\n            }\n          }\n          if (updates) {\n            (0, _index2.updateProps)(viewDescriptors, updates);\n          }\n          if (!allFinished) {\n            requestAnimationFrame(frame);\n          } else {\n            state.isAnimationRunning = false;\n          }\n        };\n        state.animations = animations;\n        if (!state.isAnimationRunning) {\n          state.isAnimationCancelled = false;\n          state.isAnimationRunning = true;\n          frame(frameTimestamp);\n        }\n        if (hasNonAnimatedValues) {\n          (0, _index2.updateProps)(viewDescriptors, nonAnimatedNewValues);\n        }\n      } else {\n        state.isAnimationCancelled = true;\n        state.animations = [];\n        if (!(0, _utils.shallowEqual)(oldValues, newValues)) {\n          (0, _index2.updateProps)(viewDescriptors, newValues, isAnimatedProps);\n        }\n      }\n      state.last = newValues;\n    };\n    styleUpdater.__closure = {\n      SHOULD_BE_USE_WEB,\n      processBoxShadow: _processBoxShadow.processBoxShadow,\n      isAnimated: _utils.isAnimated,\n      prepareAnimation,\n      runAnimations,\n      updateProps: _index2.updateProps,\n      shallowEqual: _utils.shallowEqual\n    };\n    styleUpdater.__workletHash = 9037738663389;\n    styleUpdater.__initData = _worklet_9037738663389_init_data;\n    styleUpdater.__stackDetails = _e;\n    return styleUpdater;\n  }();\n  const _worklet_10872013740208_init_data = {\n    code: \"function jestStyleUpdater_reactNativeReanimated_useAnimatedStyleJs4(viewDescriptors,updater,state,animationsActive,animatedValues,adapters){const{isAnimated,prepareAnimation,runAnimations,updatePropsJestWrapper,shallowEqual}=this.__closure;var _state$animations,_updater;const animations=(_state$animations=state.animations)!==null&&_state$animations!==void 0?_state$animations:{};const newValues=(_updater=updater())!==null&&_updater!==void 0?_updater:{};const oldValues=state.last;let hasAnimations=false;let frameTimestamp;Object.keys(animations).forEach(function(key){const value=newValues[key];if(!isAnimated(value)){delete animations[key];}});Object.keys(newValues).forEach(function(key){const value=newValues[key];if(isAnimated(value)){frameTimestamp=global.__frameTimestamp||global._getAnimationTimestamp();prepareAnimation(frameTimestamp,value,animations[key],oldValues[key]);animations[key]=value;hasAnimations=true;}});function frame(timestamp){const{animations:animations,last:last,isAnimationCancelled:isAnimationCancelled}=state;if(isAnimationCancelled){state.isAnimationRunning=false;return;}const updates={};let allFinished=true;Object.keys(animations).forEach(function(propName){const finished=runAnimations(animations[propName],timestamp,propName,updates,animationsActive);if(finished){last[propName]=updates[propName];delete animations[propName];}else{allFinished=false;}});if(Object.keys(updates).length){updatePropsJestWrapper(viewDescriptors,updates,animatedValues,adapters);}if(!allFinished){requestAnimationFrame(frame);}else{state.isAnimationRunning=false;}}if(hasAnimations){state.animations=animations;if(!state.isAnimationRunning){state.isAnimationCancelled=false;state.isAnimationRunning=true;frame(frameTimestamp);}}else{state.isAnimationCancelled=true;state.animations=[];}state.last=newValues;if(!shallowEqual(oldValues,newValues)){updatePropsJestWrapper(viewDescriptors,newValues,animatedValues,adapters);}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedStyle.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"jestStyleUpdater_reactNativeReanimated_useAnimatedStyleJs4\\\",\\\"viewDescriptors\\\",\\\"updater\\\",\\\"state\\\",\\\"animationsActive\\\",\\\"animatedValues\\\",\\\"adapters\\\",\\\"isAnimated\\\",\\\"prepareAnimation\\\",\\\"runAnimations\\\",\\\"updatePropsJestWrapper\\\",\\\"shallowEqual\\\",\\\"__closure\\\",\\\"_state$animations\\\",\\\"_updater\\\",\\\"animations\\\",\\\"newValues\\\",\\\"oldValues\\\",\\\"last\\\",\\\"hasAnimations\\\",\\\"frameTimestamp\\\",\\\"Object\\\",\\\"keys\\\",\\\"forEach\\\",\\\"key\\\",\\\"value\\\",\\\"global\\\",\\\"__frameTimestamp\\\",\\\"_getAnimationTimestamp\\\",\\\"frame\\\",\\\"timestamp\\\",\\\"isAnimationCancelled\\\",\\\"isAnimationRunning\\\",\\\"updates\\\",\\\"allFinished\\\",\\\"propName\\\",\\\"finished\\\",\\\"length\\\",\\\"requestAnimationFrame\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedStyle.js\\\"],\\\"mappings\\\":\\\"AA0MA,SAAAA,0DAA2DA,CAAAC,eAAkB,CAAAC,OAAc,CAAEC,KAAA,CAAAC,gBAAU,CAAAC,cAAA,CAAAC,QAAA,QAAAC,UAAA,CAAAC,gBAAA,CAAAC,aAAA,CAAAC,sBAAA,CAAAC,YAAA,OAAAC,SAAA,KAAAC,iBAAA,CAAAC,QAAA,CAGrG,KAAM,CAAAC,UAAU,EAAAF,iBAAA,CAAGV,KAAK,CAACY,UAAU,UAAAF,iBAAA,UAAAA,iBAAA,CAAI,CAAC,CAAC,CACzC,KAAM,CAAAG,SAAS,EAAAF,QAAA,CAAGZ,OAAO,CAAC,CAAC,UAAAY,QAAA,UAAAA,QAAA,CAAI,CAAC,CAAC,CACjC,KAAM,CAAAG,SAAS,CAAGd,KAAK,CAACe,IAAI,CAG5B,GAAI,CAAAC,aAAa,CAAG,KAAK,CACzB,GAAI,CAAAC,cAAc,CAClBC,MAAM,CAACC,IAAI,CAACP,UAAU,CAAC,CAACQ,OAAO,CAAC,SAAAC,GAAG,CAAI,CACrC,KAAM,CAAAC,KAAK,CAAGT,SAAS,CAACQ,GAAG,CAAC,CAC5B,GAAI,CAACjB,UAAU,CAACkB,KAAK,CAAC,CAAE,CACtB,MAAO,CAAAV,UAAU,CAACS,GAAG,CAAC,CACxB,CACF,CAAC,CAAC,CACFH,MAAM,CAACC,IAAI,CAACN,SAAS,CAAC,CAACO,OAAO,CAAC,SAAAC,GAAG,CAAI,CACpC,KAAM,CAAAC,KAAK,CAAGT,SAAS,CAACQ,GAAG,CAAC,CAC5B,GAAIjB,UAAU,CAACkB,KAAK,CAAC,CAAE,CACrBL,cAAc,CAAGM,MAAM,CAACC,gBAAgB,EAAID,MAAM,CAACE,sBAAsB,CAAC,CAAC,CAC3EpB,gBAAgB,CAACY,cAAc,CAAEK,KAAK,CAAEV,UAAU,CAACS,GAAG,CAAC,CAAEP,SAAS,CAACO,GAAG,CAAC,CAAC,CACxET,UAAU,CAACS,GAAG,CAAC,CAAGC,KAAK,CACvBN,aAAa,CAAG,IAAI,CACtB,CACF,CAAC,CAAC,CACF,QAAS,CAAAU,KAAKA,CAACC,SAAS,CAAE,CAExB,KAAM,CACJf,UAAU,CAAVA,UAAU,CACVG,IAAI,CAAJA,IAAI,CACJa,oBAAA,CAAAA,oBACF,CAAC,CAAG5B,KAAK,CACT,GAAI4B,oBAAoB,CAAE,CACxB5B,KAAK,CAAC6B,kBAAkB,CAAG,KAAK,CAChC,OACF,CACA,KAAM,CAAAC,OAAO,CAAG,CAAC,CAAC,CAClB,GAAI,CAAAC,WAAW,CAAG,IAAI,CACtBb,MAAM,CAACC,IAAI,CAACP,UAAU,CAAC,CAACQ,OAAO,CAAC,SAAAY,QAAQ,CAAI,CAC1C,KAAM,CAAAC,QAAQ,CAAG3B,aAAa,CAACM,UAAU,CAACoB,QAAQ,CAAC,CAAEL,SAAS,CAAEK,QAAQ,CAAEF,OAAO,CAAE7B,gBAAgB,CAAC,CACpG,GAAIgC,QAAQ,CAAE,CACZlB,IAAI,CAACiB,QAAQ,CAAC,CAAGF,OAAO,CAACE,QAAQ,CAAC,CAClC,MAAO,CAAApB,UAAU,CAACoB,QAAQ,CAAC,CAC7B,CAAC,IAAM,CACLD,WAAW,CAAG,KAAK,CACrB,CACF,CAAC,CAAC,CACF,GAAIb,MAAM,CAACC,IAAI,CAACW,OAAO,CAAC,CAACI,MAAM,CAAE,CAC/B3B,sBAAsB,CAACT,eAAe,CAAEgC,OAAO,CAAE5B,cAAc,CAAEC,QAAQ,CAAC,CAC5E,CACA,GAAI,CAAC4B,WAAW,CAAE,CAChBI,qBAAqB,CAACT,KAAK,CAAC,CAC9B,CAAC,IAAM,CACL1B,KAAK,CAAC6B,kBAAkB,CAAG,KAAK,CAClC,CACF,CACA,GAAIb,aAAa,CAAE,CACjBhB,KAAK,CAACY,UAAU,CAAGA,UAAU,CAC7B,GAAI,CAACZ,KAAK,CAAC6B,kBAAkB,CAAE,CAC7B7B,KAAK,CAAC4B,oBAAoB,CAAG,KAAK,CAClC5B,KAAK,CAAC6B,kBAAkB,CAAG,IAAI,CAC/BH,KAAK,CAACT,cAAc,CAAC,CACvB,CACF,CAAC,IAAM,CACLjB,KAAK,CAAC4B,oBAAoB,CAAG,IAAI,CACjC5B,KAAK,CAACY,UAAU,CAAG,EAAE,CACvB,CAGAZ,KAAK,CAACe,IAAI,CAAGF,SAAS,CACtB,GAAI,CAACL,YAAY,CAACM,SAAS,CAAED,SAAS,CAAC,CAAE,CACvCN,sBAAsB,CAACT,eAAe,CAAEe,SAAS,CAAEX,cAAc,CAAEC,QAAQ,CAAC,CAC9E,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const jestStyleUpdater = function () {\n    const _e = [new global.Error(), -6, -27];\n    const jestStyleUpdater = function (viewDescriptors, updater, state, animationsActive, animatedValues, adapters) {\n      const animations = state.animations ?? {};\n      const newValues = updater() ?? {};\n      const oldValues = state.last;\n\n      // extract animated props\n      let hasAnimations = false;\n      let frameTimestamp;\n      Object.keys(animations).forEach(key => {\n        const value = newValues[key];\n        if (!(0, _utils.isAnimated)(value)) {\n          delete animations[key];\n        }\n      });\n      Object.keys(newValues).forEach(key => {\n        const value = newValues[key];\n        if ((0, _utils.isAnimated)(value)) {\n          frameTimestamp = global.__frameTimestamp || global._getAnimationTimestamp();\n          prepareAnimation(frameTimestamp, value, animations[key], oldValues[key]);\n          animations[key] = value;\n          hasAnimations = true;\n        }\n      });\n      function frame(timestamp) {\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        const {\n          animations,\n          last,\n          isAnimationCancelled\n        } = state;\n        if (isAnimationCancelled) {\n          state.isAnimationRunning = false;\n          return;\n        }\n        const updates = {};\n        let allFinished = true;\n        Object.keys(animations).forEach(propName => {\n          const finished = runAnimations(animations[propName], timestamp, propName, updates, animationsActive);\n          if (finished) {\n            last[propName] = updates[propName];\n            delete animations[propName];\n          } else {\n            allFinished = false;\n          }\n        });\n        if (Object.keys(updates).length) {\n          (0, _index2.updatePropsJestWrapper)(viewDescriptors, updates, animatedValues, adapters);\n        }\n        if (!allFinished) {\n          requestAnimationFrame(frame);\n        } else {\n          state.isAnimationRunning = false;\n        }\n      }\n      if (hasAnimations) {\n        state.animations = animations;\n        if (!state.isAnimationRunning) {\n          state.isAnimationCancelled = false;\n          state.isAnimationRunning = true;\n          frame(frameTimestamp);\n        }\n      } else {\n        state.isAnimationCancelled = true;\n        state.animations = [];\n      }\n\n      // calculate diff\n      state.last = newValues;\n      if (!(0, _utils.shallowEqual)(oldValues, newValues)) {\n        (0, _index2.updatePropsJestWrapper)(viewDescriptors, newValues, animatedValues, adapters);\n      }\n    };\n    jestStyleUpdater.__closure = {\n      isAnimated: _utils.isAnimated,\n      prepareAnimation,\n      runAnimations,\n      updatePropsJestWrapper: _index2.updatePropsJestWrapper,\n      shallowEqual: _utils.shallowEqual\n    };\n    jestStyleUpdater.__workletHash = 10872013740208;\n    jestStyleUpdater.__initData = _worklet_10872013740208_init_data;\n    jestStyleUpdater.__stackDetails = _e;\n    return jestStyleUpdater;\n  }(); // check for invalid usage of shared values in returned object\n  function checkSharedValueUsage(prop, currentKey) {\n    if (Array.isArray(prop)) {\n      // if it's an array (i.ex. transform) validate all its elements\n      for (const element of prop) {\n        checkSharedValueUsage(element, currentKey);\n      }\n    } else if (typeof prop === 'object' && prop !== null && prop.value === undefined) {\n      // if it's a nested object, run validation for all its props\n      for (const key of Object.keys(prop)) {\n        checkSharedValueUsage(prop[key], key);\n      }\n    } else if (currentKey !== undefined && typeof prop === 'object' && prop !== null && prop.value !== undefined) {\n      // if shared value is passed instead of its value, throw an error\n      throw new _errors.ReanimatedError(`Invalid value passed to \\`${currentKey}\\`, maybe you forgot to use \\`.value\\`?`);\n    }\n  }\n\n  /**\n   * Lets you create a styles object, similar to StyleSheet styles, which can be\n   * animated using shared values.\n   *\n   * @param updater - A function returning an object with style properties you\n   *   want to animate.\n   * @param dependencies - An optional array of dependencies. Only relevant when\n   *   using Reanimated without the Babel plugin on the Web.\n   * @returns An animated style object which has to be passed to the `style`\n   *   property of an Animated component you want to animate.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedStyle\n   */\n  // You cannot pass Shared Values to `useAnimatedStyle` directly.\n  // @ts-expect-error This overload is required by our API.\n  const _worklet_15993059429066_init_data = {\n    code: \"function reactNativeReanimated_useAnimatedStyleJs5(){const{updater,adaptersArray}=this.__closure;const newValues=updater();adaptersArray.forEach(function(adapter){adapter(newValues);});return newValues;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedStyle.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useAnimatedStyleJs5\\\",\\\"updater\\\",\\\"adaptersArray\\\",\\\"__closure\\\",\\\"newValues\\\",\\\"forEach\\\",\\\"adapter\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedStyle.js\\\"],\\\"mappings\\\":\\\"AA6WkB,SAAAA,yCAAMA,CAAA,QAAAC,OAAA,CAAAC,aAAA,OAAAC,SAAA,CAGhB,KAAM,CAAAC,SAAS,CAAGH,OAAO,CAAC,CAAC,CAC3BC,aAAa,CAACG,OAAO,CAAC,SAAAC,OAAO,CAAI,CAC/BA,OAAO,CAACF,SAAS,CAAC,CACpB,CAAC,CAAC,CACF,MAAO,CAAAA,SAAS,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_17205083532123_init_data = {\n    code: \"function reactNativeReanimated_useAnimatedStyleJs6(){const{jestStyleUpdater,shareableViewDescriptors,updater,remoteState,areAnimationsActive,jestAnimatedValues,adaptersArray}=this.__closure;jestStyleUpdater(shareableViewDescriptors,updater,remoteState,areAnimationsActive,jestAnimatedValues,adaptersArray);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedStyle.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useAnimatedStyleJs6\\\",\\\"jestStyleUpdater\\\",\\\"shareableViewDescriptors\\\",\\\"updater\\\",\\\"remoteState\\\",\\\"areAnimationsActive\\\",\\\"jestAnimatedValues\\\",\\\"adaptersArray\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedStyle.js\\\"],\\\"mappings\\\":\\\"AAwXY,SAAAA,yCAAMA,CAAA,QAAAC,gBAAA,CAAAC,wBAAA,CAAAC,OAAA,CAAAC,WAAA,CAAAC,mBAAA,CAAAC,kBAAA,CAAAC,aAAA,OAAAC,SAAA,CAGVP,gBAAgB,CAACC,wBAAwB,CAAEC,OAAO,CAAEC,WAAW,CAAEC,mBAAmB,CAAEC,kBAAkB,CAAEC,aAAa,CAAC,CAC1H\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_5297059117402_init_data = {\n    code: \"function reactNativeReanimated_useAnimatedStyleJs7(){const{styleUpdater,shareableViewDescriptors,updaterFn,remoteState,areAnimationsActive,isAnimatedProps}=this.__closure;styleUpdater(shareableViewDescriptors,updaterFn,remoteState,areAnimationsActive,isAnimatedProps);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedStyle.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useAnimatedStyleJs7\\\",\\\"styleUpdater\\\",\\\"shareableViewDescriptors\\\",\\\"updaterFn\\\",\\\"remoteState\\\",\\\"areAnimationsActive\\\",\\\"isAnimatedProps\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedStyle.js\\\"],\\\"mappings\\\":\\\"AA8XY,SAAAA,yCAAMA,CAAA,QAAAC,YAAA,CAAAC,wBAAA,CAAAC,SAAA,CAAAC,WAAA,CAAAC,mBAAA,CAAAC,eAAA,OAAAC,SAAA,CAGVN,YAAY,CAACC,wBAAwB,CAAEC,SAAS,CAAEC,WAAW,CAAEC,mBAAmB,CAAEC,eAAe,CAAC,CACtG\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function useAnimatedStyle(updater, dependencies, adapters, isAnimatedProps = false) {\n    const animatedUpdaterData = (0, _react.useRef)(null);\n    let inputs = Object.values(updater.__closure ?? {});\n    if (SHOULD_BE_USE_WEB) {\n      if (!inputs.length && dependencies?.length) {\n        // let web work without a Babel plugin\n        inputs = dependencies;\n      }\n      if (__DEV__ && !inputs.length && !dependencies && !(0, _commonTypes.isWorkletFunction)(updater)) {\n        throw new _errors.ReanimatedError(`\\`useAnimatedStyle\\` was used without a dependency array or Babel plugin. Please explicitly pass a dependency array, or enable the Babel plugin.\nFor more, see the docs: \\`https://docs.swmansion.com/react-native-reanimated/docs/guides/web-support#web-without-the-babel-plugin\\`.`);\n      }\n    }\n    const adaptersArray = adapters ? Array.isArray(adapters) ? adapters : [adapters] : [];\n    const adaptersHash = adapters ? (0, _utils.buildWorkletsHash)(adaptersArray) : null;\n    const areAnimationsActive = (0, _useSharedValue.useSharedValue)(true);\n    const jestAnimatedValues = (0, _react.useRef)({});\n\n    // build dependencies\n    if (!dependencies) {\n      dependencies = [...inputs, updater.__workletHash];\n    } else {\n      dependencies.push(updater.__workletHash);\n    }\n    adaptersHash && dependencies.push(adaptersHash);\n    if (!animatedUpdaterData.current) {\n      const initialStyle = (0, _index.initialUpdaterRun)(updater);\n      if (__DEV__) {\n        (0, _utils.validateAnimatedStyles)(initialStyle);\n      }\n      animatedUpdaterData.current = {\n        initial: {\n          value: initialStyle,\n          updater\n        },\n        remoteState: (0, _core.makeShareable)({\n          last: initialStyle,\n          animations: {},\n          isAnimationCancelled: false,\n          isAnimationRunning: false\n        }),\n        viewDescriptors: (0, _ViewDescriptorsSet.makeViewDescriptorsSet)()\n      };\n    }\n    const {\n      initial,\n      remoteState,\n      viewDescriptors\n    } = animatedUpdaterData.current;\n    const shareableViewDescriptors = viewDescriptors.shareableViewDescriptors;\n    dependencies.push(shareableViewDescriptors);\n    (0, _react.useEffect)(() => {\n      let fun;\n      let updaterFn = updater;\n      if (adapters) {\n        updaterFn = function () {\n          const _e = [new global.Error(), -3, -27];\n          const reactNativeReanimated_useAnimatedStyleJs5 = function () {\n            const newValues = updater();\n            adaptersArray.forEach(adapter => {\n              adapter(newValues);\n            });\n            return newValues;\n          };\n          reactNativeReanimated_useAnimatedStyleJs5.__closure = {\n            updater,\n            adaptersArray\n          };\n          reactNativeReanimated_useAnimatedStyleJs5.__workletHash = 15993059429066;\n          reactNativeReanimated_useAnimatedStyleJs5.__initData = _worklet_15993059429066_init_data;\n          reactNativeReanimated_useAnimatedStyleJs5.__stackDetails = _e;\n          return reactNativeReanimated_useAnimatedStyleJs5;\n        }();\n      }\n      if ((0, _PlatformChecker.isJest)()) {\n        fun = function () {\n          const _e = [new global.Error(), -8, -27];\n          const reactNativeReanimated_useAnimatedStyleJs6 = function () {\n            jestStyleUpdater(shareableViewDescriptors, updater, remoteState, areAnimationsActive, jestAnimatedValues, adaptersArray);\n          };\n          reactNativeReanimated_useAnimatedStyleJs6.__closure = {\n            jestStyleUpdater,\n            shareableViewDescriptors,\n            updater,\n            remoteState,\n            areAnimationsActive,\n            jestAnimatedValues,\n            adaptersArray\n          };\n          reactNativeReanimated_useAnimatedStyleJs6.__workletHash = 17205083532123;\n          reactNativeReanimated_useAnimatedStyleJs6.__initData = _worklet_17205083532123_init_data;\n          reactNativeReanimated_useAnimatedStyleJs6.__stackDetails = _e;\n          return reactNativeReanimated_useAnimatedStyleJs6;\n        }();\n      } else {\n        fun = function () {\n          const _e = [new global.Error(), -7, -27];\n          const reactNativeReanimated_useAnimatedStyleJs7 = function () {\n            styleUpdater(shareableViewDescriptors, updaterFn, remoteState, areAnimationsActive, isAnimatedProps);\n          };\n          reactNativeReanimated_useAnimatedStyleJs7.__closure = {\n            styleUpdater,\n            shareableViewDescriptors,\n            updaterFn,\n            remoteState,\n            areAnimationsActive,\n            isAnimatedProps\n          };\n          reactNativeReanimated_useAnimatedStyleJs7.__workletHash = 5297059117402;\n          reactNativeReanimated_useAnimatedStyleJs7.__initData = _worklet_5297059117402_init_data;\n          reactNativeReanimated_useAnimatedStyleJs7.__stackDetails = _e;\n          return reactNativeReanimated_useAnimatedStyleJs7;\n        }();\n      }\n      const mapperId = (0, _core.startMapper)(fun, inputs);\n      return () => {\n        (0, _core.stopMapper)(mapperId);\n      };\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, dependencies);\n    (0, _react.useEffect)(() => {\n      areAnimationsActive.value = true;\n      return () => {\n        areAnimationsActive.value = false;\n      };\n    }, [areAnimationsActive]);\n    checkSharedValueUsage(initial.value);\n    const animatedStyleHandle = (0, _react.useRef)(null);\n    if (!animatedStyleHandle.current) {\n      animatedStyleHandle.current = (0, _PlatformChecker.isJest)() ? {\n        viewDescriptors,\n        initial,\n        jestAnimatedValues\n      } : {\n        viewDescriptors,\n        initial\n      };\n    }\n    return animatedStyleHandle.current;\n  }\n});", "lineCount": 533, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedStyle"], [7, 26, 1, 13], [7, 29, 1, 13, "useAnimatedStyle"], [7, 45, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_react"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_index"], [9, 12, 4, 0], [9, 15, 4, 0, "require"], [9, 22, 4, 0], [9, 23, 4, 0, "_dependencyMap"], [9, 37, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_commonTypes"], [10, 18, 5, 0], [10, 21, 5, 0, "require"], [10, 28, 5, 0], [10, 29, 5, 0, "_dependencyMap"], [10, 43, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_core"], [11, 11, 6, 0], [11, 14, 6, 0, "require"], [11, 21, 6, 0], [11, 22, 6, 0, "_dependencyMap"], [11, 36, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_errors"], [12, 13, 7, 0], [12, 16, 7, 0, "require"], [12, 23, 7, 0], [12, 24, 7, 0, "_dependencyMap"], [12, 38, 7, 0], [13, 2, 8, 0], [13, 6, 8, 0, "_PlatformChecker"], [13, 22, 8, 0], [13, 25, 8, 0, "require"], [13, 32, 8, 0], [13, 33, 8, 0, "_dependencyMap"], [13, 47, 8, 0], [14, 2, 9, 0], [14, 6, 9, 0, "_processBoxShadow"], [14, 23, 9, 0], [14, 26, 9, 0, "require"], [14, 33, 9, 0], [14, 34, 9, 0, "_dependencyMap"], [14, 48, 9, 0], [15, 2, 10, 0], [15, 6, 10, 0, "_index2"], [15, 13, 10, 0], [15, 16, 10, 0, "require"], [15, 23, 10, 0], [15, 24, 10, 0, "_dependencyMap"], [15, 38, 10, 0], [16, 2, 11, 0], [16, 6, 11, 0, "_ViewDescriptorsSet"], [16, 25, 11, 0], [16, 28, 11, 0, "require"], [16, 35, 11, 0], [16, 36, 11, 0, "_dependencyMap"], [16, 50, 11, 0], [17, 2, 12, 0], [17, 6, 12, 0, "_useSharedValue"], [17, 21, 12, 0], [17, 24, 12, 0, "require"], [17, 31, 12, 0], [17, 32, 12, 0, "_dependencyMap"], [17, 46, 12, 0], [18, 2, 13, 0], [18, 6, 13, 0, "_utils"], [18, 12, 13, 0], [18, 15, 13, 0, "require"], [18, 22, 13, 0], [18, 23, 13, 0, "_dependencyMap"], [18, 37, 13, 0], [19, 2, 14, 0], [19, 8, 14, 6, "SHOULD_BE_USE_WEB"], [19, 25, 14, 23], [19, 28, 14, 26], [19, 32, 14, 26, "shouldBeUseWeb"], [19, 63, 14, 40], [19, 65, 14, 41], [19, 66, 14, 42], [20, 2, 14, 43], [20, 8, 14, 43, "_worklet_15514483176912_init_data"], [20, 41, 14, 43], [21, 4, 14, 43, "code"], [21, 8, 14, 43], [22, 4, 14, 43, "location"], [22, 12, 14, 43], [23, 4, 14, 43, "sourceMap"], [23, 13, 14, 43], [24, 4, 14, 43, "version"], [24, 11, 14, 43], [25, 2, 14, 43], [26, 2, 14, 43], [26, 8, 14, 43, "prepareAnimation"], [26, 24, 14, 43], [26, 27, 15, 0], [27, 4, 15, 0], [27, 10, 15, 0, "_e"], [27, 12, 15, 0], [27, 20, 15, 0, "global"], [27, 26, 15, 0], [27, 27, 15, 0, "Error"], [27, 32, 15, 0], [28, 4, 15, 0], [28, 10, 15, 0, "prepareAnimation"], [28, 26, 15, 0], [28, 38, 15, 0, "prepareAnimation"], [28, 39, 15, 26, "frameTimestamp"], [28, 53, 15, 40], [28, 55, 15, 42, "animatedProp"], [28, 67, 15, 54], [28, 69, 15, 56, "lastAnimation"], [28, 82, 15, 69], [28, 84, 15, 71, "lastValue"], [28, 93, 15, 80], [28, 95, 15, 82], [29, 6, 18, 2], [29, 10, 18, 6, "Array"], [29, 15, 18, 11], [29, 16, 18, 12, "isArray"], [29, 23, 18, 19], [29, 24, 18, 20, "animatedProp"], [29, 36, 18, 32], [29, 37, 18, 33], [29, 39, 18, 35], [30, 8, 19, 4, "animatedProp"], [30, 20, 19, 16], [30, 21, 19, 17, "for<PERSON>ach"], [30, 28, 19, 24], [30, 29, 19, 25], [30, 30, 19, 26, "prop"], [30, 34, 19, 30], [30, 36, 19, 32, "index"], [30, 41, 19, 37], [30, 46, 19, 42], [31, 10, 20, 6, "prepareAnimation"], [31, 26, 20, 22], [31, 27, 20, 23, "frameTimestamp"], [31, 41, 20, 37], [31, 43, 20, 39, "prop"], [31, 47, 20, 43], [31, 49, 20, 45, "lastAnimation"], [31, 62, 20, 58], [31, 66, 20, 62, "lastAnimation"], [31, 79, 20, 75], [31, 80, 20, 76, "index"], [31, 85, 20, 81], [31, 86, 20, 82], [31, 88, 20, 84, "lastValue"], [31, 97, 20, 93], [31, 101, 20, 97, "lastValue"], [31, 110, 20, 106], [31, 111, 20, 107, "index"], [31, 116, 20, 112], [31, 117, 20, 113], [31, 118, 20, 114], [32, 8, 21, 4], [32, 9, 21, 5], [32, 10, 21, 6], [33, 8, 22, 4], [34, 6, 23, 2], [35, 6, 24, 2], [35, 10, 24, 6], [35, 17, 24, 13, "animatedProp"], [35, 29, 24, 25], [35, 34, 24, 30], [35, 42, 24, 38], [35, 46, 24, 42, "animatedProp"], [35, 58, 24, 54], [35, 59, 24, 55, "onFrame"], [35, 66, 24, 62], [35, 68, 24, 64], [36, 8, 25, 4], [36, 14, 25, 10, "animation"], [36, 23, 25, 19], [36, 26, 25, 22, "animatedProp"], [36, 38, 25, 34], [37, 8, 26, 4], [37, 12, 26, 8, "value"], [37, 17, 26, 13], [37, 20, 26, 16, "animation"], [37, 29, 26, 25], [37, 30, 26, 26, "current"], [37, 37, 26, 33], [38, 8, 27, 4], [38, 12, 27, 8, "lastValue"], [38, 21, 27, 17], [38, 26, 27, 22, "undefined"], [38, 35, 27, 31], [38, 39, 27, 35, "lastValue"], [38, 48, 27, 44], [38, 53, 27, 49], [38, 57, 27, 53], [38, 59, 27, 55], [39, 10, 28, 6], [39, 14, 28, 10], [39, 21, 28, 17, "lastValue"], [39, 30, 28, 26], [39, 35, 28, 31], [39, 43, 28, 39], [39, 45, 28, 41], [40, 12, 29, 8], [40, 16, 29, 12, "lastValue"], [40, 25, 29, 21], [40, 26, 29, 22, "value"], [40, 31, 29, 27], [40, 36, 29, 32, "undefined"], [40, 45, 29, 41], [40, 47, 29, 43], [41, 14, 30, 10], [42, 14, 31, 10, "value"], [42, 19, 31, 15], [42, 22, 31, 18, "lastValue"], [42, 31, 31, 27], [42, 32, 31, 28, "value"], [42, 37, 31, 33], [43, 12, 32, 8], [43, 13, 32, 9], [43, 19, 32, 15], [43, 23, 32, 19, "lastValue"], [43, 32, 32, 28], [43, 33, 32, 29, "onFrame"], [43, 40, 32, 36], [43, 45, 32, 41, "undefined"], [43, 54, 32, 50], [43, 56, 32, 52], [44, 14, 33, 10], [44, 18, 33, 14, "lastAnimation"], [44, 31, 33, 27], [44, 33, 33, 29, "current"], [44, 40, 33, 36], [44, 45, 33, 41, "undefined"], [44, 54, 33, 50], [44, 56, 33, 52], [45, 16, 34, 12], [46, 16, 35, 12, "value"], [46, 21, 35, 17], [46, 24, 35, 20, "lastAnimation"], [46, 37, 35, 33], [46, 38, 35, 34, "current"], [46, 45, 35, 41], [47, 14, 36, 10], [47, 15, 36, 11], [47, 21, 36, 17], [47, 25, 36, 21, "lastValue"], [47, 34, 36, 30], [47, 36, 36, 32, "current"], [47, 43, 36, 39], [47, 48, 36, 44, "undefined"], [47, 57, 36, 53], [47, 59, 36, 55], [48, 16, 37, 12], [49, 16, 38, 12, "value"], [49, 21, 38, 17], [49, 24, 38, 20, "lastValue"], [49, 33, 38, 29], [49, 34, 38, 30, "current"], [49, 41, 38, 37], [50, 14, 39, 10], [51, 12, 40, 8], [52, 10, 41, 6], [52, 11, 41, 7], [52, 17, 41, 13], [53, 12, 42, 8], [54, 12, 43, 8, "value"], [54, 17, 43, 13], [54, 20, 43, 16, "lastValue"], [54, 29, 43, 25], [55, 10, 44, 6], [56, 8, 45, 4], [57, 8, 46, 4, "animation"], [57, 17, 46, 13], [57, 18, 46, 14, "callStart"], [57, 27, 46, 23], [57, 30, 46, 26, "timestamp"], [57, 39, 46, 35], [57, 43, 46, 39], [58, 10, 47, 6, "animation"], [58, 19, 47, 15], [58, 20, 47, 16, "onStart"], [58, 27, 47, 23], [58, 28, 47, 24, "animation"], [58, 37, 47, 33], [58, 39, 47, 35, "value"], [58, 44, 47, 40], [58, 46, 47, 42, "timestamp"], [58, 55, 47, 51], [58, 57, 47, 53, "lastAnimation"], [58, 70, 47, 66], [58, 71, 47, 67], [59, 8, 48, 4], [59, 9, 48, 5], [60, 8, 49, 4, "animation"], [60, 17, 49, 13], [60, 18, 49, 14, "callStart"], [60, 27, 49, 23], [60, 28, 49, 24, "frameTimestamp"], [60, 42, 49, 38], [60, 43, 49, 39], [61, 8, 50, 4, "animation"], [61, 17, 50, 13], [61, 18, 50, 14, "callStart"], [61, 27, 50, 23], [61, 30, 50, 26], [61, 34, 50, 30], [62, 6, 51, 2], [62, 7, 51, 3], [62, 13, 51, 9], [62, 17, 51, 13], [62, 24, 51, 20, "animatedProp"], [62, 36, 51, 32], [62, 41, 51, 37], [62, 49, 51, 45], [62, 51, 51, 47], [63, 8, 52, 4], [64, 8, 53, 4, "Object"], [64, 14, 53, 10], [64, 15, 53, 11, "keys"], [64, 19, 53, 15], [64, 20, 53, 16, "animatedProp"], [64, 32, 53, 28], [64, 33, 53, 29], [64, 34, 53, 30, "for<PERSON>ach"], [64, 41, 53, 37], [64, 42, 53, 38, "key"], [64, 45, 53, 41], [64, 49, 53, 45, "prepareAnimation"], [64, 65, 53, 61], [64, 66, 53, 62, "frameTimestamp"], [64, 80, 53, 76], [64, 82, 53, 78, "animatedProp"], [64, 94, 53, 90], [64, 95, 53, 91, "key"], [64, 98, 53, 94], [64, 99, 53, 95], [64, 101, 53, 97, "lastAnimation"], [64, 114, 53, 110], [64, 118, 53, 114, "lastAnimation"], [64, 131, 53, 127], [64, 132, 53, 128, "key"], [64, 135, 53, 131], [64, 136, 53, 132], [64, 138, 53, 134, "lastValue"], [64, 147, 53, 143], [64, 151, 53, 147, "lastValue"], [64, 160, 53, 156], [64, 161, 53, 157, "key"], [64, 164, 53, 160], [64, 165, 53, 161], [64, 166, 53, 162], [64, 167, 53, 163], [65, 6, 54, 2], [66, 4, 55, 0], [66, 5, 55, 1], [67, 4, 55, 1, "prepareAnimation"], [67, 20, 55, 1], [67, 21, 55, 1, "__closure"], [67, 30, 55, 1], [68, 4, 55, 1, "prepareAnimation"], [68, 20, 55, 1], [68, 21, 55, 1, "__workletHash"], [68, 34, 55, 1], [69, 4, 55, 1, "prepareAnimation"], [69, 20, 55, 1], [69, 21, 55, 1, "__initData"], [69, 31, 55, 1], [69, 34, 55, 1, "_worklet_15514483176912_init_data"], [69, 67, 55, 1], [70, 4, 55, 1, "prepareAnimation"], [70, 20, 55, 1], [70, 21, 55, 1, "__stackDetails"], [70, 35, 55, 1], [70, 38, 55, 1, "_e"], [70, 40, 55, 1], [71, 4, 55, 1], [71, 11, 55, 1, "prepareAnimation"], [71, 27, 55, 1], [72, 2, 55, 1], [72, 3, 15, 0], [73, 2, 15, 0], [73, 8, 15, 0, "_worklet_7950007328399_init_data"], [73, 40, 15, 0], [74, 4, 15, 0, "code"], [74, 8, 15, 0], [75, 4, 15, 0, "location"], [75, 12, 15, 0], [76, 4, 15, 0, "sourceMap"], [76, 13, 15, 0], [77, 4, 15, 0, "version"], [77, 11, 15, 0], [78, 2, 15, 0], [79, 2, 15, 0], [79, 8, 15, 0, "runAnimations"], [79, 21, 15, 0], [79, 24, 56, 0], [80, 4, 56, 0], [80, 10, 56, 0, "_e"], [80, 12, 56, 0], [80, 20, 56, 0, "global"], [80, 26, 56, 0], [80, 27, 56, 0, "Error"], [80, 32, 56, 0], [81, 4, 56, 0], [81, 10, 56, 0, "runAnimations"], [81, 23, 56, 0], [81, 35, 56, 0, "runAnimations"], [81, 36, 56, 23, "animation"], [81, 45, 56, 32], [81, 47, 56, 34, "timestamp"], [81, 56, 56, 43], [81, 58, 56, 45, "key"], [81, 61, 56, 48], [81, 63, 56, 50, "result"], [81, 69, 56, 56], [81, 71, 56, 58, "animationsActive"], [81, 87, 56, 74], [81, 89, 56, 76, "forceCopyAnimation"], [81, 107, 56, 94], [81, 109, 56, 96], [82, 6, 59, 2], [82, 10, 59, 6], [82, 11, 59, 7, "animationsActive"], [82, 27, 59, 23], [82, 28, 59, 24, "value"], [82, 33, 59, 29], [82, 35, 59, 31], [83, 8, 60, 4], [83, 15, 60, 11], [83, 19, 60, 15], [84, 6, 61, 2], [85, 6, 62, 2], [85, 10, 62, 6, "Array"], [85, 15, 62, 11], [85, 16, 62, 12, "isArray"], [85, 23, 62, 19], [85, 24, 62, 20, "animation"], [85, 33, 62, 29], [85, 34, 62, 30], [85, 36, 62, 32], [86, 8, 63, 4, "result"], [86, 14, 63, 10], [86, 15, 63, 11, "key"], [86, 18, 63, 14], [86, 19, 63, 15], [86, 22, 63, 18], [86, 24, 63, 20], [87, 8, 64, 4], [87, 12, 64, 8, "allFinished"], [87, 23, 64, 19], [87, 26, 64, 22], [87, 30, 64, 26], [88, 8, 65, 4, "forceCopyAnimation"], [88, 26, 65, 22], [88, 29, 65, 25, "key"], [88, 32, 65, 28], [88, 37, 65, 33], [88, 48, 65, 44], [89, 8, 66, 4, "animation"], [89, 17, 66, 13], [89, 18, 66, 14, "for<PERSON>ach"], [89, 25, 66, 21], [89, 26, 66, 22], [89, 27, 66, 23, "entry"], [89, 32, 66, 28], [89, 34, 66, 30, "index"], [89, 39, 66, 35], [89, 44, 66, 40], [90, 10, 67, 6], [90, 14, 67, 10], [90, 15, 67, 11, "runAnimations"], [90, 28, 67, 24], [90, 29, 67, 25, "entry"], [90, 34, 67, 30], [90, 36, 67, 32, "timestamp"], [90, 45, 67, 41], [90, 47, 67, 43, "index"], [90, 52, 67, 48], [90, 54, 67, 50, "result"], [90, 60, 67, 56], [90, 61, 67, 57, "key"], [90, 64, 67, 60], [90, 65, 67, 61], [90, 67, 67, 63, "animationsActive"], [90, 83, 67, 79], [90, 85, 67, 81, "forceCopyAnimation"], [90, 103, 67, 99], [90, 104, 67, 100], [90, 106, 67, 102], [91, 12, 68, 8, "allFinished"], [91, 23, 68, 19], [91, 26, 68, 22], [91, 31, 68, 27], [92, 10, 69, 6], [93, 8, 70, 4], [93, 9, 70, 5], [93, 10, 70, 6], [94, 8, 71, 4], [94, 15, 71, 11, "allFinished"], [94, 26, 71, 22], [95, 6, 72, 2], [95, 7, 72, 3], [95, 13, 72, 9], [95, 17, 72, 13], [95, 24, 72, 20, "animation"], [95, 33, 72, 29], [95, 38, 72, 34], [95, 46, 72, 42], [95, 50, 72, 46, "animation"], [95, 59, 72, 55], [95, 60, 72, 56, "onFrame"], [95, 67, 72, 63], [95, 69, 72, 65], [96, 8, 73, 4], [96, 12, 73, 8, "finished"], [96, 20, 73, 16], [96, 23, 73, 19], [96, 27, 73, 23], [97, 8, 74, 4], [97, 12, 74, 8], [97, 13, 74, 9, "animation"], [97, 22, 74, 18], [97, 23, 74, 19, "finished"], [97, 31, 74, 27], [97, 33, 74, 29], [98, 10, 75, 6], [98, 14, 75, 10, "animation"], [98, 23, 75, 19], [98, 24, 75, 20, "callStart"], [98, 33, 75, 29], [98, 35, 75, 31], [99, 12, 76, 8, "animation"], [99, 21, 76, 17], [99, 22, 76, 18, "callStart"], [99, 31, 76, 27], [99, 32, 76, 28, "timestamp"], [99, 41, 76, 37], [99, 42, 76, 38], [100, 12, 77, 8, "animation"], [100, 21, 77, 17], [100, 22, 77, 18, "callStart"], [100, 31, 77, 27], [100, 34, 77, 30], [100, 38, 77, 34], [101, 10, 78, 6], [102, 10, 79, 6, "finished"], [102, 18, 79, 14], [102, 21, 79, 17, "animation"], [102, 30, 79, 26], [102, 31, 79, 27, "onFrame"], [102, 38, 79, 34], [102, 39, 79, 35, "animation"], [102, 48, 79, 44], [102, 50, 79, 46, "timestamp"], [102, 59, 79, 55], [102, 60, 79, 56], [103, 10, 80, 6, "animation"], [103, 19, 80, 15], [103, 20, 80, 16, "timestamp"], [103, 29, 80, 25], [103, 32, 80, 28, "timestamp"], [103, 41, 80, 37], [104, 10, 81, 6], [104, 14, 81, 10, "finished"], [104, 22, 81, 18], [104, 24, 81, 20], [105, 12, 82, 8, "animation"], [105, 21, 82, 17], [105, 22, 82, 18, "finished"], [105, 30, 82, 26], [105, 33, 82, 29], [105, 37, 82, 33], [106, 12, 83, 8, "animation"], [106, 21, 83, 17], [106, 22, 83, 18, "callback"], [106, 30, 83, 26], [106, 34, 83, 30, "animation"], [106, 43, 83, 39], [106, 44, 83, 40, "callback"], [106, 52, 83, 48], [106, 53, 83, 49], [106, 57, 83, 53], [106, 58, 83, 54], [106, 72, 83, 68], [106, 73, 83, 69], [107, 10, 84, 6], [108, 8, 85, 4], [109, 8, 86, 4], [110, 0, 87, 0], [111, 0, 88, 0], [112, 0, 89, 0], [113, 0, 90, 0], [114, 8, 91, 4], [114, 12, 91, 8, "forceCopyAnimation"], [114, 30, 91, 26], [114, 32, 91, 28], [115, 10, 92, 6, "result"], [115, 16, 92, 12], [115, 17, 92, 13, "key"], [115, 20, 92, 16], [115, 21, 92, 17], [115, 24, 92, 20], [116, 12, 93, 8], [116, 15, 93, 11, "animation"], [116, 24, 93, 20], [116, 25, 93, 21, "current"], [117, 10, 94, 6], [117, 11, 94, 7], [118, 8, 95, 4], [118, 9, 95, 5], [118, 15, 95, 11], [119, 10, 96, 6, "result"], [119, 16, 96, 12], [119, 17, 96, 13, "key"], [119, 20, 96, 16], [119, 21, 96, 17], [119, 24, 96, 20, "animation"], [119, 33, 96, 29], [119, 34, 96, 30, "current"], [119, 41, 96, 37], [120, 8, 97, 4], [121, 8, 98, 4], [121, 15, 98, 11, "finished"], [121, 23, 98, 19], [122, 6, 99, 2], [122, 7, 99, 3], [122, 13, 99, 9], [122, 17, 99, 13], [122, 24, 99, 20, "animation"], [122, 33, 99, 29], [122, 38, 99, 34], [122, 46, 99, 42], [122, 48, 99, 44], [123, 8, 100, 4, "result"], [123, 14, 100, 10], [123, 15, 100, 11, "key"], [123, 18, 100, 14], [123, 19, 100, 15], [123, 22, 100, 18], [123, 23, 100, 19], [123, 24, 100, 20], [124, 8, 101, 4], [124, 12, 101, 8, "allFinished"], [124, 23, 101, 19], [124, 26, 101, 22], [124, 30, 101, 26], [125, 8, 102, 4, "Object"], [125, 14, 102, 10], [125, 15, 102, 11, "keys"], [125, 19, 102, 15], [125, 20, 102, 16, "animation"], [125, 29, 102, 25], [125, 30, 102, 26], [125, 31, 102, 27, "for<PERSON>ach"], [125, 38, 102, 34], [125, 39, 102, 35, "k"], [125, 40, 102, 36], [125, 44, 102, 40], [126, 10, 103, 6], [126, 14, 103, 10], [126, 15, 103, 11, "runAnimations"], [126, 28, 103, 24], [126, 29, 103, 25, "animation"], [126, 38, 103, 34], [126, 39, 103, 35, "k"], [126, 40, 103, 36], [126, 41, 103, 37], [126, 43, 103, 39, "timestamp"], [126, 52, 103, 48], [126, 54, 103, 50, "k"], [126, 55, 103, 51], [126, 57, 103, 53, "result"], [126, 63, 103, 59], [126, 64, 103, 60, "key"], [126, 67, 103, 63], [126, 68, 103, 64], [126, 70, 103, 66, "animationsActive"], [126, 86, 103, 82], [126, 88, 103, 84, "forceCopyAnimation"], [126, 106, 103, 102], [126, 107, 103, 103], [126, 109, 103, 105], [127, 12, 104, 8, "allFinished"], [127, 23, 104, 19], [127, 26, 104, 22], [127, 31, 104, 27], [128, 10, 105, 6], [129, 8, 106, 4], [129, 9, 106, 5], [129, 10, 106, 6], [130, 8, 107, 4], [130, 15, 107, 11, "allFinished"], [130, 26, 107, 22], [131, 6, 108, 2], [131, 7, 108, 3], [131, 13, 108, 9], [132, 8, 109, 4, "result"], [132, 14, 109, 10], [132, 15, 109, 11, "key"], [132, 18, 109, 14], [132, 19, 109, 15], [132, 22, 109, 18, "animation"], [132, 31, 109, 27], [133, 8, 110, 4], [133, 15, 110, 11], [133, 19, 110, 15], [134, 6, 111, 2], [135, 4, 112, 0], [135, 5, 112, 1], [136, 4, 112, 1, "runAnimations"], [136, 17, 112, 1], [136, 18, 112, 1, "__closure"], [136, 27, 112, 1], [137, 4, 112, 1, "runAnimations"], [137, 17, 112, 1], [137, 18, 112, 1, "__workletHash"], [137, 31, 112, 1], [138, 4, 112, 1, "runAnimations"], [138, 17, 112, 1], [138, 18, 112, 1, "__initData"], [138, 28, 112, 1], [138, 31, 112, 1, "_worklet_7950007328399_init_data"], [138, 63, 112, 1], [139, 4, 112, 1, "runAnimations"], [139, 17, 112, 1], [139, 18, 112, 1, "__stackDetails"], [139, 32, 112, 1], [139, 35, 112, 1, "_e"], [139, 37, 112, 1], [140, 4, 112, 1], [140, 11, 112, 1, "runAnimations"], [140, 24, 112, 1], [141, 2, 112, 1], [141, 3, 56, 0], [142, 2, 56, 0], [142, 8, 56, 0, "_worklet_9037738663389_init_data"], [142, 40, 56, 0], [143, 4, 56, 0, "code"], [143, 8, 56, 0], [144, 4, 56, 0, "location"], [144, 12, 56, 0], [145, 4, 56, 0, "sourceMap"], [145, 13, 56, 0], [146, 4, 56, 0, "version"], [146, 11, 56, 0], [147, 2, 56, 0], [148, 2, 56, 0], [148, 8, 56, 0, "styleUpdater"], [148, 20, 56, 0], [148, 23, 113, 0], [149, 4, 113, 0], [149, 10, 113, 0, "_e"], [149, 12, 113, 0], [149, 20, 113, 0, "global"], [149, 26, 113, 0], [149, 27, 113, 0, "Error"], [149, 32, 113, 0], [150, 4, 113, 0], [150, 10, 113, 0, "styleUpdater"], [150, 22, 113, 0], [150, 34, 113, 0, "styleUpdater"], [150, 35, 113, 22, "viewDescriptors"], [150, 50, 113, 37], [150, 52, 113, 39, "updater"], [150, 59, 113, 46], [150, 61, 113, 48, "state"], [150, 66, 113, 53], [150, 68, 113, 55, "animationsActive"], [150, 84, 113, 71], [150, 86, 113, 73, "isAnimatedProps"], [150, 101, 113, 88], [150, 104, 113, 91], [150, 109, 113, 96], [150, 111, 113, 98], [151, 6, 116, 2], [151, 12, 116, 8, "animations"], [151, 22, 116, 18], [151, 25, 116, 21, "state"], [151, 30, 116, 26], [151, 31, 116, 27, "animations"], [151, 41, 116, 37], [151, 45, 116, 41], [151, 46, 116, 42], [151, 47, 116, 43], [152, 6, 117, 2], [152, 12, 117, 8, "newValues"], [152, 21, 117, 17], [152, 24, 117, 20, "updater"], [152, 31, 117, 27], [152, 32, 117, 28], [152, 33, 117, 29], [152, 37, 117, 33], [152, 38, 117, 34], [152, 39, 117, 35], [153, 6, 118, 2], [153, 12, 118, 8, "oldValues"], [153, 21, 118, 17], [153, 24, 118, 20, "state"], [153, 29, 118, 25], [153, 30, 118, 26, "last"], [153, 34, 118, 30], [154, 6, 119, 2], [154, 12, 119, 8, "nonAnimatedNewValues"], [154, 32, 119, 28], [154, 35, 119, 31], [154, 36, 119, 32], [154, 37, 119, 33], [155, 6, 120, 2], [155, 10, 120, 6, "hasAnimations"], [155, 23, 120, 19], [155, 26, 120, 22], [155, 31, 120, 27], [156, 6, 121, 2], [156, 10, 121, 6, "frameTimestamp"], [156, 24, 121, 20], [157, 6, 122, 2], [157, 10, 122, 6, "hasNonAnimatedValues"], [157, 30, 122, 26], [157, 33, 122, 29], [157, 38, 122, 34], [158, 6, 123, 2], [158, 10, 123, 6], [158, 11, 123, 7, "SHOULD_BE_USE_WEB"], [158, 28, 123, 24], [158, 32, 123, 28, "newValues"], [158, 41, 123, 37], [158, 42, 123, 38, "boxShadow"], [158, 51, 123, 47], [158, 53, 123, 49], [159, 8, 124, 4], [159, 12, 124, 4, "processBoxShadow"], [159, 46, 124, 20], [159, 48, 124, 21, "newValues"], [159, 57, 124, 30], [159, 58, 124, 31], [160, 6, 125, 2], [161, 6, 126, 2], [161, 11, 126, 7], [161, 17, 126, 13, "key"], [161, 20, 126, 16], [161, 24, 126, 20, "newValues"], [161, 33, 126, 29], [161, 35, 126, 31], [162, 8, 127, 4], [162, 14, 127, 10, "value"], [162, 19, 127, 15], [162, 22, 127, 18, "newValues"], [162, 31, 127, 27], [162, 32, 127, 28, "key"], [162, 35, 127, 31], [162, 36, 127, 32], [163, 8, 128, 4], [163, 12, 128, 8], [163, 16, 128, 8, "isAnimated"], [163, 33, 128, 18], [163, 35, 128, 19, "value"], [163, 40, 128, 24], [163, 41, 128, 25], [163, 43, 128, 27], [164, 10, 129, 6, "frameTimestamp"], [164, 24, 129, 20], [164, 27, 129, 23, "global"], [164, 33, 129, 29], [164, 34, 129, 30, "__frameTimestamp"], [164, 50, 129, 46], [164, 54, 129, 50, "global"], [164, 60, 129, 56], [164, 61, 129, 57, "_getAnimationTimestamp"], [164, 83, 129, 79], [164, 84, 129, 80], [164, 85, 129, 81], [165, 10, 130, 6, "prepareAnimation"], [165, 26, 130, 22], [165, 27, 130, 23, "frameTimestamp"], [165, 41, 130, 37], [165, 43, 130, 39, "value"], [165, 48, 130, 44], [165, 50, 130, 46, "animations"], [165, 60, 130, 56], [165, 61, 130, 57, "key"], [165, 64, 130, 60], [165, 65, 130, 61], [165, 67, 130, 63, "oldValues"], [165, 76, 130, 72], [165, 77, 130, 73, "key"], [165, 80, 130, 76], [165, 81, 130, 77], [165, 82, 130, 78], [166, 10, 131, 6, "animations"], [166, 20, 131, 16], [166, 21, 131, 17, "key"], [166, 24, 131, 20], [166, 25, 131, 21], [166, 28, 131, 24, "value"], [166, 33, 131, 29], [167, 10, 132, 6, "hasAnimations"], [167, 23, 132, 19], [167, 26, 132, 22], [167, 30, 132, 26], [168, 8, 133, 4], [168, 9, 133, 5], [168, 15, 133, 11], [169, 10, 134, 6, "hasNonAnimatedValues"], [169, 30, 134, 26], [169, 33, 134, 29], [169, 37, 134, 33], [170, 10, 135, 6, "nonAnimatedNewValues"], [170, 30, 135, 26], [170, 31, 135, 27, "key"], [170, 34, 135, 30], [170, 35, 135, 31], [170, 38, 135, 34, "value"], [170, 43, 135, 39], [171, 10, 136, 6], [171, 17, 136, 13, "animations"], [171, 27, 136, 23], [171, 28, 136, 24, "key"], [171, 31, 136, 27], [171, 32, 136, 28], [172, 8, 137, 4], [173, 6, 138, 2], [174, 6, 139, 2], [174, 10, 139, 6, "hasAnimations"], [174, 23, 139, 19], [174, 25, 139, 21], [175, 8, 140, 4], [175, 14, 140, 10, "frame"], [175, 19, 140, 15], [175, 22, 140, 18, "timestamp"], [175, 31, 140, 27], [175, 35, 140, 31], [176, 10, 141, 6], [177, 10, 142, 6], [177, 16, 142, 12], [178, 12, 143, 8, "animations"], [178, 22, 143, 18], [179, 12, 144, 8, "last"], [179, 16, 144, 12], [180, 12, 145, 8, "isAnimationCancelled"], [181, 10, 146, 6], [181, 11, 146, 7], [181, 14, 146, 10, "state"], [181, 19, 146, 15], [182, 10, 147, 6], [182, 14, 147, 10, "isAnimationCancelled"], [182, 34, 147, 30], [182, 36, 147, 32], [183, 12, 148, 8, "state"], [183, 17, 148, 13], [183, 18, 148, 14, "isAnimationRunning"], [183, 36, 148, 32], [183, 39, 148, 35], [183, 44, 148, 40], [184, 12, 149, 8], [185, 10, 150, 6], [186, 10, 151, 6], [186, 16, 151, 12, "updates"], [186, 23, 151, 19], [186, 26, 151, 22], [186, 27, 151, 23], [186, 28, 151, 24], [187, 10, 152, 6], [187, 14, 152, 10, "allFinished"], [187, 25, 152, 21], [187, 28, 152, 24], [187, 32, 152, 28], [188, 10, 153, 6], [188, 15, 153, 11], [188, 21, 153, 17, "propName"], [188, 29, 153, 25], [188, 33, 153, 29, "animations"], [188, 43, 153, 39], [188, 45, 153, 41], [189, 12, 154, 8], [189, 18, 154, 14, "finished"], [189, 26, 154, 22], [189, 29, 154, 25, "runAnimations"], [189, 42, 154, 38], [189, 43, 154, 39, "animations"], [189, 53, 154, 49], [189, 54, 154, 50, "propName"], [189, 62, 154, 58], [189, 63, 154, 59], [189, 65, 154, 61, "timestamp"], [189, 74, 154, 70], [189, 76, 154, 72, "propName"], [189, 84, 154, 80], [189, 86, 154, 82, "updates"], [189, 93, 154, 89], [189, 95, 154, 91, "animationsActive"], [189, 111, 154, 107], [189, 112, 154, 108], [190, 12, 155, 8], [190, 16, 155, 12, "finished"], [190, 24, 155, 20], [190, 26, 155, 22], [191, 14, 156, 10], [192, 0, 157, 0], [193, 0, 158, 0], [194, 0, 159, 0], [195, 0, 160, 0], [196, 0, 161, 0], [197, 14, 162, 10], [197, 18, 162, 14, "Array"], [197, 23, 162, 19], [197, 24, 162, 20, "isArray"], [197, 31, 162, 27], [197, 32, 162, 28, "updates"], [197, 39, 162, 35], [197, 40, 162, 36, "propName"], [197, 48, 162, 44], [197, 49, 162, 45], [197, 50, 162, 46], [197, 52, 162, 48], [198, 16, 163, 12, "updates"], [198, 23, 163, 19], [198, 24, 163, 20, "propName"], [198, 32, 163, 28], [198, 33, 163, 29], [198, 34, 163, 30, "for<PERSON>ach"], [198, 41, 163, 37], [198, 42, 163, 38, "obj"], [198, 45, 163, 41], [198, 49, 163, 45], [199, 18, 164, 14], [199, 23, 164, 19], [199, 29, 164, 25, "prop"], [199, 33, 164, 29], [199, 37, 164, 33, "obj"], [199, 40, 164, 36], [199, 42, 164, 38], [200, 20, 165, 16, "last"], [200, 24, 165, 20], [200, 25, 165, 21, "propName"], [200, 33, 165, 29], [200, 34, 165, 30], [200, 35, 165, 31, "prop"], [200, 39, 165, 35], [200, 40, 165, 36], [200, 43, 165, 39, "obj"], [200, 46, 165, 42], [200, 47, 165, 43, "prop"], [200, 51, 165, 47], [200, 52, 165, 48], [201, 18, 166, 14], [202, 16, 167, 12], [202, 17, 167, 13], [202, 18, 167, 14], [203, 14, 168, 10], [203, 15, 168, 11], [203, 21, 168, 17], [204, 16, 169, 12, "last"], [204, 20, 169, 16], [204, 21, 169, 17, "propName"], [204, 29, 169, 25], [204, 30, 169, 26], [204, 33, 169, 29, "updates"], [204, 40, 169, 36], [204, 41, 169, 37, "propName"], [204, 49, 169, 45], [204, 50, 169, 46], [205, 14, 170, 10], [206, 14, 171, 10], [206, 21, 171, 17, "animations"], [206, 31, 171, 27], [206, 32, 171, 28, "propName"], [206, 40, 171, 36], [206, 41, 171, 37], [207, 12, 172, 8], [207, 13, 172, 9], [207, 19, 172, 15], [208, 14, 173, 10, "allFinished"], [208, 25, 173, 21], [208, 28, 173, 24], [208, 33, 173, 29], [209, 12, 174, 8], [210, 10, 175, 6], [211, 10, 176, 6], [211, 14, 176, 10, "updates"], [211, 21, 176, 17], [211, 23, 176, 19], [212, 12, 177, 8], [212, 16, 177, 8, "updateProps"], [212, 35, 177, 19], [212, 37, 177, 20, "viewDescriptors"], [212, 52, 177, 35], [212, 54, 177, 37, "updates"], [212, 61, 177, 44], [212, 62, 177, 45], [213, 10, 178, 6], [214, 10, 179, 6], [214, 14, 179, 10], [214, 15, 179, 11, "allFinished"], [214, 26, 179, 22], [214, 28, 179, 24], [215, 12, 180, 8, "requestAnimationFrame"], [215, 33, 180, 29], [215, 34, 180, 30, "frame"], [215, 39, 180, 35], [215, 40, 180, 36], [216, 10, 181, 6], [216, 11, 181, 7], [216, 17, 181, 13], [217, 12, 182, 8, "state"], [217, 17, 182, 13], [217, 18, 182, 14, "isAnimationRunning"], [217, 36, 182, 32], [217, 39, 182, 35], [217, 44, 182, 40], [218, 10, 183, 6], [219, 8, 184, 4], [219, 9, 184, 5], [220, 8, 185, 4, "state"], [220, 13, 185, 9], [220, 14, 185, 10, "animations"], [220, 24, 185, 20], [220, 27, 185, 23, "animations"], [220, 37, 185, 33], [221, 8, 186, 4], [221, 12, 186, 8], [221, 13, 186, 9, "state"], [221, 18, 186, 14], [221, 19, 186, 15, "isAnimationRunning"], [221, 37, 186, 33], [221, 39, 186, 35], [222, 10, 187, 6, "state"], [222, 15, 187, 11], [222, 16, 187, 12, "isAnimationCancelled"], [222, 36, 187, 32], [222, 39, 187, 35], [222, 44, 187, 40], [223, 10, 188, 6, "state"], [223, 15, 188, 11], [223, 16, 188, 12, "isAnimationRunning"], [223, 34, 188, 30], [223, 37, 188, 33], [223, 41, 188, 37], [224, 10, 189, 6, "frame"], [224, 15, 189, 11], [224, 16, 189, 12, "frameTimestamp"], [224, 30, 189, 26], [224, 31, 189, 27], [225, 8, 190, 4], [226, 8, 191, 4], [226, 12, 191, 8, "hasNonAnimatedValues"], [226, 32, 191, 28], [226, 34, 191, 30], [227, 10, 192, 6], [227, 14, 192, 6, "updateProps"], [227, 33, 192, 17], [227, 35, 192, 18, "viewDescriptors"], [227, 50, 192, 33], [227, 52, 192, 35, "nonAnimatedNewValues"], [227, 72, 192, 55], [227, 73, 192, 56], [228, 8, 193, 4], [229, 6, 194, 2], [229, 7, 194, 3], [229, 13, 194, 9], [230, 8, 195, 4, "state"], [230, 13, 195, 9], [230, 14, 195, 10, "isAnimationCancelled"], [230, 34, 195, 30], [230, 37, 195, 33], [230, 41, 195, 37], [231, 8, 196, 4, "state"], [231, 13, 196, 9], [231, 14, 196, 10, "animations"], [231, 24, 196, 20], [231, 27, 196, 23], [231, 29, 196, 25], [232, 8, 197, 4], [232, 12, 197, 8], [232, 13, 197, 9], [232, 17, 197, 9, "shallowEqual"], [232, 36, 197, 21], [232, 38, 197, 22, "oldValues"], [232, 47, 197, 31], [232, 49, 197, 33, "newValues"], [232, 58, 197, 42], [232, 59, 197, 43], [232, 61, 197, 45], [233, 10, 198, 6], [233, 14, 198, 6, "updateProps"], [233, 33, 198, 17], [233, 35, 198, 18, "viewDescriptors"], [233, 50, 198, 33], [233, 52, 198, 35, "newValues"], [233, 61, 198, 44], [233, 63, 198, 46, "isAnimatedProps"], [233, 78, 198, 61], [233, 79, 198, 62], [234, 8, 199, 4], [235, 6, 200, 2], [236, 6, 201, 2, "state"], [236, 11, 201, 7], [236, 12, 201, 8, "last"], [236, 16, 201, 12], [236, 19, 201, 15, "newValues"], [236, 28, 201, 24], [237, 4, 202, 0], [237, 5, 202, 1], [238, 4, 202, 1, "styleUpdater"], [238, 16, 202, 1], [238, 17, 202, 1, "__closure"], [238, 26, 202, 1], [239, 6, 202, 1, "SHOULD_BE_USE_WEB"], [239, 23, 202, 1], [240, 6, 202, 1, "processBoxShadow"], [240, 22, 202, 1], [240, 24, 124, 4, "processBoxShadow"], [240, 58, 124, 20], [241, 6, 124, 20, "isAnimated"], [241, 16, 124, 20], [241, 18, 128, 8, "isAnimated"], [241, 35, 128, 18], [242, 6, 128, 18, "prepareAnimation"], [242, 22, 128, 18], [243, 6, 128, 18, "runAnimations"], [243, 19, 128, 18], [244, 6, 128, 18, "updateProps"], [244, 17, 128, 18], [244, 19, 177, 8, "updateProps"], [244, 38, 177, 19], [245, 6, 177, 19, "shallowEqual"], [245, 18, 177, 19], [245, 20, 197, 9, "shallowEqual"], [246, 4, 197, 21], [247, 4, 197, 21, "styleUpdater"], [247, 16, 197, 21], [247, 17, 197, 21, "__workletHash"], [247, 30, 197, 21], [248, 4, 197, 21, "styleUpdater"], [248, 16, 197, 21], [248, 17, 197, 21, "__initData"], [248, 27, 197, 21], [248, 30, 197, 21, "_worklet_9037738663389_init_data"], [248, 62, 197, 21], [249, 4, 197, 21, "styleUpdater"], [249, 16, 197, 21], [249, 17, 197, 21, "__stackDetails"], [249, 31, 197, 21], [249, 34, 197, 21, "_e"], [249, 36, 197, 21], [250, 4, 197, 21], [250, 11, 197, 21, "styleUpdater"], [250, 23, 197, 21], [251, 2, 197, 21], [251, 3, 113, 0], [252, 2, 113, 0], [252, 8, 113, 0, "_worklet_10872013740208_init_data"], [252, 41, 113, 0], [253, 4, 113, 0, "code"], [253, 8, 113, 0], [254, 4, 113, 0, "location"], [254, 12, 113, 0], [255, 4, 113, 0, "sourceMap"], [255, 13, 113, 0], [256, 4, 113, 0, "version"], [256, 11, 113, 0], [257, 2, 113, 0], [258, 2, 113, 0], [258, 8, 113, 0, "jestStyleUpdater"], [258, 24, 113, 0], [258, 27, 203, 0], [259, 4, 203, 0], [259, 10, 203, 0, "_e"], [259, 12, 203, 0], [259, 20, 203, 0, "global"], [259, 26, 203, 0], [259, 27, 203, 0, "Error"], [259, 32, 203, 0], [260, 4, 203, 0], [260, 10, 203, 0, "jestStyleUpdater"], [260, 26, 203, 0], [260, 38, 203, 0, "jestStyleUpdater"], [260, 39, 203, 26, "viewDescriptors"], [260, 54, 203, 41], [260, 56, 203, 43, "updater"], [260, 63, 203, 50], [260, 65, 203, 52, "state"], [260, 70, 203, 57], [260, 72, 203, 59, "animationsActive"], [260, 88, 203, 75], [260, 90, 203, 77, "animatedValues"], [260, 104, 203, 91], [260, 106, 203, 93, "adapters"], [260, 114, 203, 101], [260, 116, 203, 103], [261, 6, 206, 2], [261, 12, 206, 8, "animations"], [261, 22, 206, 18], [261, 25, 206, 21, "state"], [261, 30, 206, 26], [261, 31, 206, 27, "animations"], [261, 41, 206, 37], [261, 45, 206, 41], [261, 46, 206, 42], [261, 47, 206, 43], [262, 6, 207, 2], [262, 12, 207, 8, "newValues"], [262, 21, 207, 17], [262, 24, 207, 20, "updater"], [262, 31, 207, 27], [262, 32, 207, 28], [262, 33, 207, 29], [262, 37, 207, 33], [262, 38, 207, 34], [262, 39, 207, 35], [263, 6, 208, 2], [263, 12, 208, 8, "oldValues"], [263, 21, 208, 17], [263, 24, 208, 20, "state"], [263, 29, 208, 25], [263, 30, 208, 26, "last"], [263, 34, 208, 30], [265, 6, 210, 2], [266, 6, 211, 2], [266, 10, 211, 6, "hasAnimations"], [266, 23, 211, 19], [266, 26, 211, 22], [266, 31, 211, 27], [267, 6, 212, 2], [267, 10, 212, 6, "frameTimestamp"], [267, 24, 212, 20], [268, 6, 213, 2, "Object"], [268, 12, 213, 8], [268, 13, 213, 9, "keys"], [268, 17, 213, 13], [268, 18, 213, 14, "animations"], [268, 28, 213, 24], [268, 29, 213, 25], [268, 30, 213, 26, "for<PERSON>ach"], [268, 37, 213, 33], [268, 38, 213, 34, "key"], [268, 41, 213, 37], [268, 45, 213, 41], [269, 8, 214, 4], [269, 14, 214, 10, "value"], [269, 19, 214, 15], [269, 22, 214, 18, "newValues"], [269, 31, 214, 27], [269, 32, 214, 28, "key"], [269, 35, 214, 31], [269, 36, 214, 32], [270, 8, 215, 4], [270, 12, 215, 8], [270, 13, 215, 9], [270, 17, 215, 9, "isAnimated"], [270, 34, 215, 19], [270, 36, 215, 20, "value"], [270, 41, 215, 25], [270, 42, 215, 26], [270, 44, 215, 28], [271, 10, 216, 6], [271, 17, 216, 13, "animations"], [271, 27, 216, 23], [271, 28, 216, 24, "key"], [271, 31, 216, 27], [271, 32, 216, 28], [272, 8, 217, 4], [273, 6, 218, 2], [273, 7, 218, 3], [273, 8, 218, 4], [274, 6, 219, 2, "Object"], [274, 12, 219, 8], [274, 13, 219, 9, "keys"], [274, 17, 219, 13], [274, 18, 219, 14, "newValues"], [274, 27, 219, 23], [274, 28, 219, 24], [274, 29, 219, 25, "for<PERSON>ach"], [274, 36, 219, 32], [274, 37, 219, 33, "key"], [274, 40, 219, 36], [274, 44, 219, 40], [275, 8, 220, 4], [275, 14, 220, 10, "value"], [275, 19, 220, 15], [275, 22, 220, 18, "newValues"], [275, 31, 220, 27], [275, 32, 220, 28, "key"], [275, 35, 220, 31], [275, 36, 220, 32], [276, 8, 221, 4], [276, 12, 221, 8], [276, 16, 221, 8, "isAnimated"], [276, 33, 221, 18], [276, 35, 221, 19, "value"], [276, 40, 221, 24], [276, 41, 221, 25], [276, 43, 221, 27], [277, 10, 222, 6, "frameTimestamp"], [277, 24, 222, 20], [277, 27, 222, 23, "global"], [277, 33, 222, 29], [277, 34, 222, 30, "__frameTimestamp"], [277, 50, 222, 46], [277, 54, 222, 50, "global"], [277, 60, 222, 56], [277, 61, 222, 57, "_getAnimationTimestamp"], [277, 83, 222, 79], [277, 84, 222, 80], [277, 85, 222, 81], [278, 10, 223, 6, "prepareAnimation"], [278, 26, 223, 22], [278, 27, 223, 23, "frameTimestamp"], [278, 41, 223, 37], [278, 43, 223, 39, "value"], [278, 48, 223, 44], [278, 50, 223, 46, "animations"], [278, 60, 223, 56], [278, 61, 223, 57, "key"], [278, 64, 223, 60], [278, 65, 223, 61], [278, 67, 223, 63, "oldValues"], [278, 76, 223, 72], [278, 77, 223, 73, "key"], [278, 80, 223, 76], [278, 81, 223, 77], [278, 82, 223, 78], [279, 10, 224, 6, "animations"], [279, 20, 224, 16], [279, 21, 224, 17, "key"], [279, 24, 224, 20], [279, 25, 224, 21], [279, 28, 224, 24, "value"], [279, 33, 224, 29], [280, 10, 225, 6, "hasAnimations"], [280, 23, 225, 19], [280, 26, 225, 22], [280, 30, 225, 26], [281, 8, 226, 4], [282, 6, 227, 2], [282, 7, 227, 3], [282, 8, 227, 4], [283, 6, 228, 2], [283, 15, 228, 11, "frame"], [283, 20, 228, 16, "frame"], [283, 21, 228, 17, "timestamp"], [283, 30, 228, 26], [283, 32, 228, 28], [284, 8, 229, 4], [285, 8, 230, 4], [285, 14, 230, 10], [286, 10, 231, 6, "animations"], [286, 20, 231, 16], [287, 10, 232, 6, "last"], [287, 14, 232, 10], [288, 10, 233, 6, "isAnimationCancelled"], [289, 8, 234, 4], [289, 9, 234, 5], [289, 12, 234, 8, "state"], [289, 17, 234, 13], [290, 8, 235, 4], [290, 12, 235, 8, "isAnimationCancelled"], [290, 32, 235, 28], [290, 34, 235, 30], [291, 10, 236, 6, "state"], [291, 15, 236, 11], [291, 16, 236, 12, "isAnimationRunning"], [291, 34, 236, 30], [291, 37, 236, 33], [291, 42, 236, 38], [292, 10, 237, 6], [293, 8, 238, 4], [294, 8, 239, 4], [294, 14, 239, 10, "updates"], [294, 21, 239, 17], [294, 24, 239, 20], [294, 25, 239, 21], [294, 26, 239, 22], [295, 8, 240, 4], [295, 12, 240, 8, "allFinished"], [295, 23, 240, 19], [295, 26, 240, 22], [295, 30, 240, 26], [296, 8, 241, 4, "Object"], [296, 14, 241, 10], [296, 15, 241, 11, "keys"], [296, 19, 241, 15], [296, 20, 241, 16, "animations"], [296, 30, 241, 26], [296, 31, 241, 27], [296, 32, 241, 28, "for<PERSON>ach"], [296, 39, 241, 35], [296, 40, 241, 36, "propName"], [296, 48, 241, 44], [296, 52, 241, 48], [297, 10, 242, 6], [297, 16, 242, 12, "finished"], [297, 24, 242, 20], [297, 27, 242, 23, "runAnimations"], [297, 40, 242, 36], [297, 41, 242, 37, "animations"], [297, 51, 242, 47], [297, 52, 242, 48, "propName"], [297, 60, 242, 56], [297, 61, 242, 57], [297, 63, 242, 59, "timestamp"], [297, 72, 242, 68], [297, 74, 242, 70, "propName"], [297, 82, 242, 78], [297, 84, 242, 80, "updates"], [297, 91, 242, 87], [297, 93, 242, 89, "animationsActive"], [297, 109, 242, 105], [297, 110, 242, 106], [298, 10, 243, 6], [298, 14, 243, 10, "finished"], [298, 22, 243, 18], [298, 24, 243, 20], [299, 12, 244, 8, "last"], [299, 16, 244, 12], [299, 17, 244, 13, "propName"], [299, 25, 244, 21], [299, 26, 244, 22], [299, 29, 244, 25, "updates"], [299, 36, 244, 32], [299, 37, 244, 33, "propName"], [299, 45, 244, 41], [299, 46, 244, 42], [300, 12, 245, 8], [300, 19, 245, 15, "animations"], [300, 29, 245, 25], [300, 30, 245, 26, "propName"], [300, 38, 245, 34], [300, 39, 245, 35], [301, 10, 246, 6], [301, 11, 246, 7], [301, 17, 246, 13], [302, 12, 247, 8, "allFinished"], [302, 23, 247, 19], [302, 26, 247, 22], [302, 31, 247, 27], [303, 10, 248, 6], [304, 8, 249, 4], [304, 9, 249, 5], [304, 10, 249, 6], [305, 8, 250, 4], [305, 12, 250, 8, "Object"], [305, 18, 250, 14], [305, 19, 250, 15, "keys"], [305, 23, 250, 19], [305, 24, 250, 20, "updates"], [305, 31, 250, 27], [305, 32, 250, 28], [305, 33, 250, 29, "length"], [305, 39, 250, 35], [305, 41, 250, 37], [306, 10, 251, 6], [306, 14, 251, 6, "updatePropsJestWrapper"], [306, 44, 251, 28], [306, 46, 251, 29, "viewDescriptors"], [306, 61, 251, 44], [306, 63, 251, 46, "updates"], [306, 70, 251, 53], [306, 72, 251, 55, "animatedValues"], [306, 86, 251, 69], [306, 88, 251, 71, "adapters"], [306, 96, 251, 79], [306, 97, 251, 80], [307, 8, 252, 4], [308, 8, 253, 4], [308, 12, 253, 8], [308, 13, 253, 9, "allFinished"], [308, 24, 253, 20], [308, 26, 253, 22], [309, 10, 254, 6, "requestAnimationFrame"], [309, 31, 254, 27], [309, 32, 254, 28, "frame"], [309, 37, 254, 33], [309, 38, 254, 34], [310, 8, 255, 4], [310, 9, 255, 5], [310, 15, 255, 11], [311, 10, 256, 6, "state"], [311, 15, 256, 11], [311, 16, 256, 12, "isAnimationRunning"], [311, 34, 256, 30], [311, 37, 256, 33], [311, 42, 256, 38], [312, 8, 257, 4], [313, 6, 258, 2], [314, 6, 259, 2], [314, 10, 259, 6, "hasAnimations"], [314, 23, 259, 19], [314, 25, 259, 21], [315, 8, 260, 4, "state"], [315, 13, 260, 9], [315, 14, 260, 10, "animations"], [315, 24, 260, 20], [315, 27, 260, 23, "animations"], [315, 37, 260, 33], [316, 8, 261, 4], [316, 12, 261, 8], [316, 13, 261, 9, "state"], [316, 18, 261, 14], [316, 19, 261, 15, "isAnimationRunning"], [316, 37, 261, 33], [316, 39, 261, 35], [317, 10, 262, 6, "state"], [317, 15, 262, 11], [317, 16, 262, 12, "isAnimationCancelled"], [317, 36, 262, 32], [317, 39, 262, 35], [317, 44, 262, 40], [318, 10, 263, 6, "state"], [318, 15, 263, 11], [318, 16, 263, 12, "isAnimationRunning"], [318, 34, 263, 30], [318, 37, 263, 33], [318, 41, 263, 37], [319, 10, 264, 6, "frame"], [319, 15, 264, 11], [319, 16, 264, 12, "frameTimestamp"], [319, 30, 264, 26], [319, 31, 264, 27], [320, 8, 265, 4], [321, 6, 266, 2], [321, 7, 266, 3], [321, 13, 266, 9], [322, 8, 267, 4, "state"], [322, 13, 267, 9], [322, 14, 267, 10, "isAnimationCancelled"], [322, 34, 267, 30], [322, 37, 267, 33], [322, 41, 267, 37], [323, 8, 268, 4, "state"], [323, 13, 268, 9], [323, 14, 268, 10, "animations"], [323, 24, 268, 20], [323, 27, 268, 23], [323, 29, 268, 25], [324, 6, 269, 2], [326, 6, 271, 2], [327, 6, 272, 2, "state"], [327, 11, 272, 7], [327, 12, 272, 8, "last"], [327, 16, 272, 12], [327, 19, 272, 15, "newValues"], [327, 28, 272, 24], [328, 6, 273, 2], [328, 10, 273, 6], [328, 11, 273, 7], [328, 15, 273, 7, "shallowEqual"], [328, 34, 273, 19], [328, 36, 273, 20, "oldValues"], [328, 45, 273, 29], [328, 47, 273, 31, "newValues"], [328, 56, 273, 40], [328, 57, 273, 41], [328, 59, 273, 43], [329, 8, 274, 4], [329, 12, 274, 4, "updatePropsJestWrapper"], [329, 42, 274, 26], [329, 44, 274, 27, "viewDescriptors"], [329, 59, 274, 42], [329, 61, 274, 44, "newValues"], [329, 70, 274, 53], [329, 72, 274, 55, "animatedValues"], [329, 86, 274, 69], [329, 88, 274, 71, "adapters"], [329, 96, 274, 79], [329, 97, 274, 80], [330, 6, 275, 2], [331, 4, 276, 0], [331, 5, 276, 1], [332, 4, 276, 1, "jestStyleUpdater"], [332, 20, 276, 1], [332, 21, 276, 1, "__closure"], [332, 30, 276, 1], [333, 6, 276, 1, "isAnimated"], [333, 16, 276, 1], [333, 18, 215, 9, "isAnimated"], [333, 35, 215, 19], [334, 6, 215, 19, "prepareAnimation"], [334, 22, 215, 19], [335, 6, 215, 19, "runAnimations"], [335, 19, 215, 19], [336, 6, 215, 19, "updatePropsJestWrapper"], [336, 28, 215, 19], [336, 30, 251, 6, "updatePropsJestWrapper"], [336, 60, 251, 28], [337, 6, 251, 28, "shallowEqual"], [337, 18, 251, 28], [337, 20, 273, 7, "shallowEqual"], [338, 4, 273, 19], [339, 4, 273, 19, "jestStyleUpdater"], [339, 20, 273, 19], [339, 21, 273, 19, "__workletHash"], [339, 34, 273, 19], [340, 4, 273, 19, "jestStyleUpdater"], [340, 20, 273, 19], [340, 21, 273, 19, "__initData"], [340, 31, 273, 19], [340, 34, 273, 19, "_worklet_10872013740208_init_data"], [340, 67, 273, 19], [341, 4, 273, 19, "jestStyleUpdater"], [341, 20, 273, 19], [341, 21, 273, 19, "__stackDetails"], [341, 35, 273, 19], [341, 38, 273, 19, "_e"], [341, 40, 273, 19], [342, 4, 273, 19], [342, 11, 273, 19, "jestStyleUpdater"], [342, 27, 273, 19], [343, 2, 273, 19], [343, 3, 203, 0], [343, 7, 278, 0], [344, 2, 279, 0], [344, 11, 279, 9, "checkSharedValueUsage"], [344, 32, 279, 30, "checkSharedValueUsage"], [344, 33, 279, 31, "prop"], [344, 37, 279, 35], [344, 39, 279, 37, "current<PERSON><PERSON>"], [344, 49, 279, 47], [344, 51, 279, 49], [345, 4, 280, 2], [345, 8, 280, 6, "Array"], [345, 13, 280, 11], [345, 14, 280, 12, "isArray"], [345, 21, 280, 19], [345, 22, 280, 20, "prop"], [345, 26, 280, 24], [345, 27, 280, 25], [345, 29, 280, 27], [346, 6, 281, 4], [347, 6, 282, 4], [347, 11, 282, 9], [347, 17, 282, 15, "element"], [347, 24, 282, 22], [347, 28, 282, 26, "prop"], [347, 32, 282, 30], [347, 34, 282, 32], [348, 8, 283, 6, "checkSharedValueUsage"], [348, 29, 283, 27], [348, 30, 283, 28, "element"], [348, 37, 283, 35], [348, 39, 283, 37, "current<PERSON><PERSON>"], [348, 49, 283, 47], [348, 50, 283, 48], [349, 6, 284, 4], [350, 4, 285, 2], [350, 5, 285, 3], [350, 11, 285, 9], [350, 15, 285, 13], [350, 22, 285, 20, "prop"], [350, 26, 285, 24], [350, 31, 285, 29], [350, 39, 285, 37], [350, 43, 285, 41, "prop"], [350, 47, 285, 45], [350, 52, 285, 50], [350, 56, 285, 54], [350, 60, 285, 58, "prop"], [350, 64, 285, 62], [350, 65, 285, 63, "value"], [350, 70, 285, 68], [350, 75, 285, 73, "undefined"], [350, 84, 285, 82], [350, 86, 285, 84], [351, 6, 286, 4], [352, 6, 287, 4], [352, 11, 287, 9], [352, 17, 287, 15, "key"], [352, 20, 287, 18], [352, 24, 287, 22, "Object"], [352, 30, 287, 28], [352, 31, 287, 29, "keys"], [352, 35, 287, 33], [352, 36, 287, 34, "prop"], [352, 40, 287, 38], [352, 41, 287, 39], [352, 43, 287, 41], [353, 8, 288, 6, "checkSharedValueUsage"], [353, 29, 288, 27], [353, 30, 288, 28, "prop"], [353, 34, 288, 32], [353, 35, 288, 33, "key"], [353, 38, 288, 36], [353, 39, 288, 37], [353, 41, 288, 39, "key"], [353, 44, 288, 42], [353, 45, 288, 43], [354, 6, 289, 4], [355, 4, 290, 2], [355, 5, 290, 3], [355, 11, 290, 9], [355, 15, 290, 13, "current<PERSON><PERSON>"], [355, 25, 290, 23], [355, 30, 290, 28, "undefined"], [355, 39, 290, 37], [355, 43, 290, 41], [355, 50, 290, 48, "prop"], [355, 54, 290, 52], [355, 59, 290, 57], [355, 67, 290, 65], [355, 71, 290, 69, "prop"], [355, 75, 290, 73], [355, 80, 290, 78], [355, 84, 290, 82], [355, 88, 290, 86, "prop"], [355, 92, 290, 90], [355, 93, 290, 91, "value"], [355, 98, 290, 96], [355, 103, 290, 101, "undefined"], [355, 112, 290, 110], [355, 114, 290, 112], [356, 6, 291, 4], [357, 6, 292, 4], [357, 12, 292, 10], [357, 16, 292, 14, "ReanimatedError"], [357, 39, 292, 29], [357, 40, 292, 30], [357, 69, 292, 59, "current<PERSON><PERSON>"], [357, 79, 292, 69], [357, 120, 292, 110], [357, 121, 292, 111], [358, 4, 293, 2], [359, 2, 294, 0], [361, 2, 296, 0], [362, 0, 297, 0], [363, 0, 298, 0], [364, 0, 299, 0], [365, 0, 300, 0], [366, 0, 301, 0], [367, 0, 302, 0], [368, 0, 303, 0], [369, 0, 304, 0], [370, 0, 305, 0], [371, 0, 306, 0], [372, 0, 307, 0], [373, 2, 308, 0], [374, 2, 309, 0], [375, 2, 309, 0], [375, 8, 309, 0, "_worklet_15993059429066_init_data"], [375, 41, 309, 0], [376, 4, 309, 0, "code"], [376, 8, 309, 0], [377, 4, 309, 0, "location"], [377, 12, 309, 0], [378, 4, 309, 0, "sourceMap"], [378, 13, 309, 0], [379, 4, 309, 0, "version"], [379, 11, 309, 0], [380, 2, 309, 0], [381, 2, 309, 0], [381, 8, 309, 0, "_worklet_17205083532123_init_data"], [381, 41, 309, 0], [382, 4, 309, 0, "code"], [382, 8, 309, 0], [383, 4, 309, 0, "location"], [383, 12, 309, 0], [384, 4, 309, 0, "sourceMap"], [384, 13, 309, 0], [385, 4, 309, 0, "version"], [385, 11, 309, 0], [386, 2, 309, 0], [387, 2, 309, 0], [387, 8, 309, 0, "_worklet_5297059117402_init_data"], [387, 40, 309, 0], [388, 4, 309, 0, "code"], [388, 8, 309, 0], [389, 4, 309, 0, "location"], [389, 12, 309, 0], [390, 4, 309, 0, "sourceMap"], [390, 13, 309, 0], [391, 4, 309, 0, "version"], [391, 11, 309, 0], [392, 2, 309, 0], [393, 2, 311, 7], [393, 11, 311, 16, "useAnimatedStyle"], [393, 27, 311, 32, "useAnimatedStyle"], [393, 28, 311, 33, "updater"], [393, 35, 311, 40], [393, 37, 311, 42, "dependencies"], [393, 49, 311, 54], [393, 51, 311, 56, "adapters"], [393, 59, 311, 64], [393, 61, 311, 66, "isAnimatedProps"], [393, 76, 311, 81], [393, 79, 311, 84], [393, 84, 311, 89], [393, 86, 311, 91], [394, 4, 312, 2], [394, 10, 312, 8, "animatedUpdaterData"], [394, 29, 312, 27], [394, 32, 312, 30], [394, 36, 312, 30, "useRef"], [394, 49, 312, 36], [394, 51, 312, 37], [394, 55, 312, 41], [394, 56, 312, 42], [395, 4, 313, 2], [395, 8, 313, 6, "inputs"], [395, 14, 313, 12], [395, 17, 313, 15, "Object"], [395, 23, 313, 21], [395, 24, 313, 22, "values"], [395, 30, 313, 28], [395, 31, 313, 29, "updater"], [395, 38, 313, 36], [395, 39, 313, 37, "__closure"], [395, 48, 313, 46], [395, 52, 313, 50], [395, 53, 313, 51], [395, 54, 313, 52], [395, 55, 313, 53], [396, 4, 314, 2], [396, 8, 314, 6, "SHOULD_BE_USE_WEB"], [396, 25, 314, 23], [396, 27, 314, 25], [397, 6, 315, 4], [397, 10, 315, 8], [397, 11, 315, 9, "inputs"], [397, 17, 315, 15], [397, 18, 315, 16, "length"], [397, 24, 315, 22], [397, 28, 315, 26, "dependencies"], [397, 40, 315, 38], [397, 42, 315, 40, "length"], [397, 48, 315, 46], [397, 50, 315, 48], [398, 8, 316, 6], [399, 8, 317, 6, "inputs"], [399, 14, 317, 12], [399, 17, 317, 15, "dependencies"], [399, 29, 317, 27], [400, 6, 318, 4], [401, 6, 319, 4], [401, 10, 319, 8, "__DEV__"], [401, 17, 319, 15], [401, 21, 319, 19], [401, 22, 319, 20, "inputs"], [401, 28, 319, 26], [401, 29, 319, 27, "length"], [401, 35, 319, 33], [401, 39, 319, 37], [401, 40, 319, 38, "dependencies"], [401, 52, 319, 50], [401, 56, 319, 54], [401, 57, 319, 55], [401, 61, 319, 55, "isWorkletFunction"], [401, 91, 319, 72], [401, 93, 319, 73, "updater"], [401, 100, 319, 80], [401, 101, 319, 81], [401, 103, 319, 83], [402, 8, 320, 6], [402, 14, 320, 12], [402, 18, 320, 16, "ReanimatedError"], [402, 41, 320, 31], [402, 42, 320, 32], [403, 0, 321, 0], [403, 133, 321, 133], [403, 134, 321, 134], [404, 6, 322, 4], [405, 4, 323, 2], [406, 4, 324, 2], [406, 10, 324, 8, "adaptersArray"], [406, 23, 324, 21], [406, 26, 324, 24, "adapters"], [406, 34, 324, 32], [406, 37, 324, 35, "Array"], [406, 42, 324, 40], [406, 43, 324, 41, "isArray"], [406, 50, 324, 48], [406, 51, 324, 49, "adapters"], [406, 59, 324, 57], [406, 60, 324, 58], [406, 63, 324, 61, "adapters"], [406, 71, 324, 69], [406, 74, 324, 72], [406, 75, 324, 73, "adapters"], [406, 83, 324, 81], [406, 84, 324, 82], [406, 87, 324, 85], [406, 89, 324, 87], [407, 4, 325, 2], [407, 10, 325, 8, "adaptersHash"], [407, 22, 325, 20], [407, 25, 325, 23, "adapters"], [407, 33, 325, 31], [407, 36, 325, 34], [407, 40, 325, 34, "buildWorkletsHash"], [407, 64, 325, 51], [407, 66, 325, 52, "adaptersArray"], [407, 79, 325, 65], [407, 80, 325, 66], [407, 83, 325, 69], [407, 87, 325, 73], [408, 4, 326, 2], [408, 10, 326, 8, "areAnimationsActive"], [408, 29, 326, 27], [408, 32, 326, 30], [408, 36, 326, 30, "useSharedValue"], [408, 66, 326, 44], [408, 68, 326, 45], [408, 72, 326, 49], [408, 73, 326, 50], [409, 4, 327, 2], [409, 10, 327, 8, "jestAnimatedValues"], [409, 28, 327, 26], [409, 31, 327, 29], [409, 35, 327, 29, "useRef"], [409, 48, 327, 35], [409, 50, 327, 36], [409, 51, 327, 37], [409, 52, 327, 38], [409, 53, 327, 39], [411, 4, 329, 2], [412, 4, 330, 2], [412, 8, 330, 6], [412, 9, 330, 7, "dependencies"], [412, 21, 330, 19], [412, 23, 330, 21], [413, 6, 331, 4, "dependencies"], [413, 18, 331, 16], [413, 21, 331, 19], [413, 22, 331, 20], [413, 25, 331, 23, "inputs"], [413, 31, 331, 29], [413, 33, 331, 31, "updater"], [413, 40, 331, 38], [413, 41, 331, 39, "__workletHash"], [413, 54, 331, 52], [413, 55, 331, 53], [414, 4, 332, 2], [414, 5, 332, 3], [414, 11, 332, 9], [415, 6, 333, 4, "dependencies"], [415, 18, 333, 16], [415, 19, 333, 17, "push"], [415, 23, 333, 21], [415, 24, 333, 22, "updater"], [415, 31, 333, 29], [415, 32, 333, 30, "__workletHash"], [415, 45, 333, 43], [415, 46, 333, 44], [416, 4, 334, 2], [417, 4, 335, 2, "adaptersHash"], [417, 16, 335, 14], [417, 20, 335, 18, "dependencies"], [417, 32, 335, 30], [417, 33, 335, 31, "push"], [417, 37, 335, 35], [417, 38, 335, 36, "adaptersHash"], [417, 50, 335, 48], [417, 51, 335, 49], [418, 4, 336, 2], [418, 8, 336, 6], [418, 9, 336, 7, "animatedUpdaterData"], [418, 28, 336, 26], [418, 29, 336, 27, "current"], [418, 36, 336, 34], [418, 38, 336, 36], [419, 6, 337, 4], [419, 12, 337, 10, "initialStyle"], [419, 24, 337, 22], [419, 27, 337, 25], [419, 31, 337, 25, "initialUpdaterRun"], [419, 55, 337, 42], [419, 57, 337, 43, "updater"], [419, 64, 337, 50], [419, 65, 337, 51], [420, 6, 338, 4], [420, 10, 338, 8, "__DEV__"], [420, 17, 338, 15], [420, 19, 338, 17], [421, 8, 339, 6], [421, 12, 339, 6, "validateAnimatedStyles"], [421, 41, 339, 28], [421, 43, 339, 29, "initialStyle"], [421, 55, 339, 41], [421, 56, 339, 42], [422, 6, 340, 4], [423, 6, 341, 4, "animatedUpdaterData"], [423, 25, 341, 23], [423, 26, 341, 24, "current"], [423, 33, 341, 31], [423, 36, 341, 34], [424, 8, 342, 6, "initial"], [424, 15, 342, 13], [424, 17, 342, 15], [425, 10, 343, 8, "value"], [425, 15, 343, 13], [425, 17, 343, 15, "initialStyle"], [425, 29, 343, 27], [426, 10, 344, 8, "updater"], [427, 8, 345, 6], [427, 9, 345, 7], [428, 8, 346, 6, "remoteState"], [428, 19, 346, 17], [428, 21, 346, 19], [428, 25, 346, 19, "makeShareable"], [428, 44, 346, 32], [428, 46, 346, 33], [429, 10, 347, 8, "last"], [429, 14, 347, 12], [429, 16, 347, 14, "initialStyle"], [429, 28, 347, 26], [430, 10, 348, 8, "animations"], [430, 20, 348, 18], [430, 22, 348, 20], [430, 23, 348, 21], [430, 24, 348, 22], [431, 10, 349, 8, "isAnimationCancelled"], [431, 30, 349, 28], [431, 32, 349, 30], [431, 37, 349, 35], [432, 10, 350, 8, "isAnimationRunning"], [432, 28, 350, 26], [432, 30, 350, 28], [433, 8, 351, 6], [433, 9, 351, 7], [433, 10, 351, 8], [434, 8, 352, 6, "viewDescriptors"], [434, 23, 352, 21], [434, 25, 352, 23], [434, 29, 352, 23, "makeViewDescriptorsSet"], [434, 71, 352, 45], [434, 73, 352, 46], [435, 6, 353, 4], [435, 7, 353, 5], [436, 4, 354, 2], [437, 4, 355, 2], [437, 10, 355, 8], [438, 6, 356, 4, "initial"], [438, 13, 356, 11], [439, 6, 357, 4, "remoteState"], [439, 17, 357, 15], [440, 6, 358, 4, "viewDescriptors"], [441, 4, 359, 2], [441, 5, 359, 3], [441, 8, 359, 6, "animatedUpdaterData"], [441, 27, 359, 25], [441, 28, 359, 26, "current"], [441, 35, 359, 33], [442, 4, 360, 2], [442, 10, 360, 8, "shareableViewDescriptors"], [442, 34, 360, 32], [442, 37, 360, 35, "viewDescriptors"], [442, 52, 360, 50], [442, 53, 360, 51, "shareableViewDescriptors"], [442, 77, 360, 75], [443, 4, 361, 2, "dependencies"], [443, 16, 361, 14], [443, 17, 361, 15, "push"], [443, 21, 361, 19], [443, 22, 361, 20, "shareableViewDescriptors"], [443, 46, 361, 44], [443, 47, 361, 45], [444, 4, 362, 2], [444, 8, 362, 2, "useEffect"], [444, 24, 362, 11], [444, 26, 362, 12], [444, 32, 362, 18], [445, 6, 363, 4], [445, 10, 363, 8, "fun"], [445, 13, 363, 11], [446, 6, 364, 4], [446, 10, 364, 8, "updaterFn"], [446, 19, 364, 17], [446, 22, 364, 20, "updater"], [446, 29, 364, 27], [447, 6, 365, 4], [447, 10, 365, 8, "adapters"], [447, 18, 365, 16], [447, 20, 365, 18], [448, 8, 366, 6, "updaterFn"], [448, 17, 366, 15], [448, 20, 366, 18], [449, 10, 366, 18], [449, 16, 366, 18, "_e"], [449, 18, 366, 18], [449, 26, 366, 18, "global"], [449, 32, 366, 18], [449, 33, 366, 18, "Error"], [449, 38, 366, 18], [450, 10, 366, 18], [450, 16, 366, 18, "reactNativeReanimated_useAnimatedStyleJs5"], [450, 57, 366, 18], [450, 69, 366, 18, "reactNativeReanimated_useAnimatedStyleJs5"], [450, 70, 366, 18], [450, 72, 366, 24], [451, 12, 369, 8], [451, 18, 369, 14, "newValues"], [451, 27, 369, 23], [451, 30, 369, 26, "updater"], [451, 37, 369, 33], [451, 38, 369, 34], [451, 39, 369, 35], [452, 12, 370, 8, "adaptersArray"], [452, 25, 370, 21], [452, 26, 370, 22, "for<PERSON>ach"], [452, 33, 370, 29], [452, 34, 370, 30, "adapter"], [452, 41, 370, 37], [452, 45, 370, 41], [453, 14, 371, 10, "adapter"], [453, 21, 371, 17], [453, 22, 371, 18, "newValues"], [453, 31, 371, 27], [453, 32, 371, 28], [454, 12, 372, 8], [454, 13, 372, 9], [454, 14, 372, 10], [455, 12, 373, 8], [455, 19, 373, 15, "newValues"], [455, 28, 373, 24], [456, 10, 374, 6], [456, 11, 374, 7], [457, 10, 374, 7, "reactNativeReanimated_useAnimatedStyleJs5"], [457, 51, 374, 7], [457, 52, 374, 7, "__closure"], [457, 61, 374, 7], [458, 12, 374, 7, "updater"], [458, 19, 374, 7], [459, 12, 374, 7, "adaptersArray"], [460, 10, 374, 7], [461, 10, 374, 7, "reactNativeReanimated_useAnimatedStyleJs5"], [461, 51, 374, 7], [461, 52, 374, 7, "__workletHash"], [461, 65, 374, 7], [462, 10, 374, 7, "reactNativeReanimated_useAnimatedStyleJs5"], [462, 51, 374, 7], [462, 52, 374, 7, "__initData"], [462, 62, 374, 7], [462, 65, 374, 7, "_worklet_15993059429066_init_data"], [462, 98, 374, 7], [463, 10, 374, 7, "reactNativeReanimated_useAnimatedStyleJs5"], [463, 51, 374, 7], [463, 52, 374, 7, "__stackDetails"], [463, 66, 374, 7], [463, 69, 374, 7, "_e"], [463, 71, 374, 7], [464, 10, 374, 7], [464, 17, 374, 7, "reactNativeReanimated_useAnimatedStyleJs5"], [464, 58, 374, 7], [465, 8, 374, 7], [465, 9, 366, 18], [465, 11, 374, 7], [466, 6, 375, 4], [467, 6, 376, 4], [467, 10, 376, 8], [467, 14, 376, 8, "isJest"], [467, 37, 376, 14], [467, 39, 376, 15], [467, 40, 376, 16], [467, 42, 376, 18], [468, 8, 377, 6, "fun"], [468, 11, 377, 9], [468, 14, 377, 12], [469, 10, 377, 12], [469, 16, 377, 12, "_e"], [469, 18, 377, 12], [469, 26, 377, 12, "global"], [469, 32, 377, 12], [469, 33, 377, 12, "Error"], [469, 38, 377, 12], [470, 10, 377, 12], [470, 16, 377, 12, "reactNativeReanimated_useAnimatedStyleJs6"], [470, 57, 377, 12], [470, 69, 377, 12, "reactNativeReanimated_useAnimatedStyleJs6"], [470, 70, 377, 12], [470, 72, 377, 18], [471, 12, 380, 8, "jestStyleUpdater"], [471, 28, 380, 24], [471, 29, 380, 25, "shareableViewDescriptors"], [471, 53, 380, 49], [471, 55, 380, 51, "updater"], [471, 62, 380, 58], [471, 64, 380, 60, "remoteState"], [471, 75, 380, 71], [471, 77, 380, 73, "areAnimationsActive"], [471, 96, 380, 92], [471, 98, 380, 94, "jestAnimatedValues"], [471, 116, 380, 112], [471, 118, 380, 114, "adaptersArray"], [471, 131, 380, 127], [471, 132, 380, 128], [472, 10, 381, 6], [472, 11, 381, 7], [473, 10, 381, 7, "reactNativeReanimated_useAnimatedStyleJs6"], [473, 51, 381, 7], [473, 52, 381, 7, "__closure"], [473, 61, 381, 7], [474, 12, 381, 7, "jestStyleUpdater"], [474, 28, 381, 7], [475, 12, 381, 7, "shareableViewDescriptors"], [475, 36, 381, 7], [476, 12, 381, 7, "updater"], [476, 19, 381, 7], [477, 12, 381, 7, "remoteState"], [477, 23, 381, 7], [478, 12, 381, 7, "areAnimationsActive"], [478, 31, 381, 7], [479, 12, 381, 7, "jestAnimatedValues"], [479, 30, 381, 7], [480, 12, 381, 7, "adaptersArray"], [481, 10, 381, 7], [482, 10, 381, 7, "reactNativeReanimated_useAnimatedStyleJs6"], [482, 51, 381, 7], [482, 52, 381, 7, "__workletHash"], [482, 65, 381, 7], [483, 10, 381, 7, "reactNativeReanimated_useAnimatedStyleJs6"], [483, 51, 381, 7], [483, 52, 381, 7, "__initData"], [483, 62, 381, 7], [483, 65, 381, 7, "_worklet_17205083532123_init_data"], [483, 98, 381, 7], [484, 10, 381, 7, "reactNativeReanimated_useAnimatedStyleJs6"], [484, 51, 381, 7], [484, 52, 381, 7, "__stackDetails"], [484, 66, 381, 7], [484, 69, 381, 7, "_e"], [484, 71, 381, 7], [485, 10, 381, 7], [485, 17, 381, 7, "reactNativeReanimated_useAnimatedStyleJs6"], [485, 58, 381, 7], [486, 8, 381, 7], [486, 9, 377, 12], [486, 11, 381, 7], [487, 6, 382, 4], [487, 7, 382, 5], [487, 13, 382, 11], [488, 8, 383, 6, "fun"], [488, 11, 383, 9], [488, 14, 383, 12], [489, 10, 383, 12], [489, 16, 383, 12, "_e"], [489, 18, 383, 12], [489, 26, 383, 12, "global"], [489, 32, 383, 12], [489, 33, 383, 12, "Error"], [489, 38, 383, 12], [490, 10, 383, 12], [490, 16, 383, 12, "reactNativeReanimated_useAnimatedStyleJs7"], [490, 57, 383, 12], [490, 69, 383, 12, "reactNativeReanimated_useAnimatedStyleJs7"], [490, 70, 383, 12], [490, 72, 383, 18], [491, 12, 386, 8, "styleUpdater"], [491, 24, 386, 20], [491, 25, 386, 21, "shareableViewDescriptors"], [491, 49, 386, 45], [491, 51, 386, 47, "updaterFn"], [491, 60, 386, 56], [491, 62, 386, 58, "remoteState"], [491, 73, 386, 69], [491, 75, 386, 71, "areAnimationsActive"], [491, 94, 386, 90], [491, 96, 386, 92, "isAnimatedProps"], [491, 111, 386, 107], [491, 112, 386, 108], [492, 10, 387, 6], [492, 11, 387, 7], [493, 10, 387, 7, "reactNativeReanimated_useAnimatedStyleJs7"], [493, 51, 387, 7], [493, 52, 387, 7, "__closure"], [493, 61, 387, 7], [494, 12, 387, 7, "styleUpdater"], [494, 24, 387, 7], [495, 12, 387, 7, "shareableViewDescriptors"], [495, 36, 387, 7], [496, 12, 387, 7, "updaterFn"], [496, 21, 387, 7], [497, 12, 387, 7, "remoteState"], [497, 23, 387, 7], [498, 12, 387, 7, "areAnimationsActive"], [498, 31, 387, 7], [499, 12, 387, 7, "isAnimatedProps"], [500, 10, 387, 7], [501, 10, 387, 7, "reactNativeReanimated_useAnimatedStyleJs7"], [501, 51, 387, 7], [501, 52, 387, 7, "__workletHash"], [501, 65, 387, 7], [502, 10, 387, 7, "reactNativeReanimated_useAnimatedStyleJs7"], [502, 51, 387, 7], [502, 52, 387, 7, "__initData"], [502, 62, 387, 7], [502, 65, 387, 7, "_worklet_5297059117402_init_data"], [502, 97, 387, 7], [503, 10, 387, 7, "reactNativeReanimated_useAnimatedStyleJs7"], [503, 51, 387, 7], [503, 52, 387, 7, "__stackDetails"], [503, 66, 387, 7], [503, 69, 387, 7, "_e"], [503, 71, 387, 7], [504, 10, 387, 7], [504, 17, 387, 7, "reactNativeReanimated_useAnimatedStyleJs7"], [504, 58, 387, 7], [505, 8, 387, 7], [505, 9, 383, 12], [505, 11, 387, 7], [506, 6, 388, 4], [507, 6, 389, 4], [507, 12, 389, 10, "mapperId"], [507, 20, 389, 18], [507, 23, 389, 21], [507, 27, 389, 21, "startMapper"], [507, 44, 389, 32], [507, 46, 389, 33, "fun"], [507, 49, 389, 36], [507, 51, 389, 38, "inputs"], [507, 57, 389, 44], [507, 58, 389, 45], [508, 6, 390, 4], [508, 13, 390, 11], [508, 19, 390, 17], [509, 8, 391, 6], [509, 12, 391, 6, "stopMapper"], [509, 28, 391, 16], [509, 30, 391, 17, "mapperId"], [509, 38, 391, 25], [509, 39, 391, 26], [510, 6, 392, 4], [510, 7, 392, 5], [511, 6, 393, 4], [512, 4, 394, 2], [512, 5, 394, 3], [512, 7, 394, 5, "dependencies"], [512, 19, 394, 17], [512, 20, 394, 18], [513, 4, 395, 2], [513, 8, 395, 2, "useEffect"], [513, 24, 395, 11], [513, 26, 395, 12], [513, 32, 395, 18], [514, 6, 396, 4, "areAnimationsActive"], [514, 25, 396, 23], [514, 26, 396, 24, "value"], [514, 31, 396, 29], [514, 34, 396, 32], [514, 38, 396, 36], [515, 6, 397, 4], [515, 13, 397, 11], [515, 19, 397, 17], [516, 8, 398, 6, "areAnimationsActive"], [516, 27, 398, 25], [516, 28, 398, 26, "value"], [516, 33, 398, 31], [516, 36, 398, 34], [516, 41, 398, 39], [517, 6, 399, 4], [517, 7, 399, 5], [518, 4, 400, 2], [518, 5, 400, 3], [518, 7, 400, 5], [518, 8, 400, 6, "areAnimationsActive"], [518, 27, 400, 25], [518, 28, 400, 26], [518, 29, 400, 27], [519, 4, 401, 2, "checkSharedValueUsage"], [519, 25, 401, 23], [519, 26, 401, 24, "initial"], [519, 33, 401, 31], [519, 34, 401, 32, "value"], [519, 39, 401, 37], [519, 40, 401, 38], [520, 4, 402, 2], [520, 10, 402, 8, "animatedStyleHandle"], [520, 29, 402, 27], [520, 32, 402, 30], [520, 36, 402, 30, "useRef"], [520, 49, 402, 36], [520, 51, 402, 37], [520, 55, 402, 41], [520, 56, 402, 42], [521, 4, 403, 2], [521, 8, 403, 6], [521, 9, 403, 7, "animatedStyleHandle"], [521, 28, 403, 26], [521, 29, 403, 27, "current"], [521, 36, 403, 34], [521, 38, 403, 36], [522, 6, 404, 4, "animatedStyleHandle"], [522, 25, 404, 23], [522, 26, 404, 24, "current"], [522, 33, 404, 31], [522, 36, 404, 34], [522, 40, 404, 34, "isJest"], [522, 63, 404, 40], [522, 65, 404, 41], [522, 66, 404, 42], [522, 69, 404, 45], [523, 8, 405, 6, "viewDescriptors"], [523, 23, 405, 21], [524, 8, 406, 6, "initial"], [524, 15, 406, 13], [525, 8, 407, 6, "jestAnimatedValues"], [526, 6, 408, 4], [526, 7, 408, 5], [526, 10, 408, 8], [527, 8, 409, 6, "viewDescriptors"], [527, 23, 409, 21], [528, 8, 410, 6, "initial"], [529, 6, 411, 4], [529, 7, 411, 5], [530, 4, 412, 2], [531, 4, 413, 2], [531, 11, 413, 9, "animatedStyleHandle"], [531, 30, 413, 28], [531, 31, 413, 29, "current"], [531, 38, 413, 36], [532, 2, 414, 0], [533, 0, 414, 1], [533, 3]], "functionMap": {"names": ["<global>", "prepareAnimation", "animatedProp.forEach$argument_0", "animation.callStart", "Object.keys.forEach$argument_0", "runAnimations", "animation.forEach$argument_0", "styleUpdater", "frame", "updates.propName.forEach$argument_0", "jestStyleUpdater", "checkSharedValueUsage", "useAnimatedStyle", "useEffect$argument_0", "updaterFn", "adaptersArray.forEach$argument_0", "fun", "<anonymous>"], "mappings": "AAA;ACc;yBCI;KDE;0BEyB;KFE;sCGK,4HH;CDE;AKC;sBCU;KDI;mCDgC;KCI;CLM;AOC;kBC2B;sCCuB;aDI;KDiB;CPkB;AUC;kCNU;GMK;iCNC;GMQ;EFC;oCJa;KIQ;GES;CVkB;AWG;CXe;OYiB;YCmD;kBCI;8BCI;SDE;ODE;YGG;OHI;YGE;OHI;WIG;KJE;GDE;YCC;WIE;KJE;GDC;CZc"}}, "type": "js/module"}]}