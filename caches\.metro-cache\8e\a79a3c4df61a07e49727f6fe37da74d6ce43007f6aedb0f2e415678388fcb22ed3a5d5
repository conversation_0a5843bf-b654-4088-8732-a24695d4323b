{"dependencies": [{"name": "../../Directions", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 66, "index": 277}}], "key": "05BxNcpXaYAeyUvpG1n5gignxMc=", "exportNames": ["*"]}}, {"name": "../constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 278}, "end": {"line": 4, "column": 62, "index": 340}}], "key": "eTOOXVNPpMK2U8dOAmBWjbEJ4yE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _Directions = require(_dependencyMap[0], \"../../Directions\");\n  var _constants = require(_dependencyMap[1], \"../constants\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class Vector {\n    constructor(x, y) {\n      _defineProperty(this, \"x\", void 0);\n      _defineProperty(this, \"y\", void 0);\n      _defineProperty(this, \"unitX\", void 0);\n      _defineProperty(this, \"unitY\", void 0);\n      _defineProperty(this, \"_magnitude\", void 0);\n      this.x = x;\n      this.y = y;\n      this._magnitude = Math.hypot(this.x, this.y);\n      const isMagnitudeSufficient = this._magnitude > _constants.MINIMAL_RECOGNIZABLE_MAGNITUDE;\n      this.unitX = isMagnitudeSufficient ? this.x / this._magnitude : 0;\n      this.unitY = isMagnitudeSufficient ? this.y / this._magnitude : 0;\n    }\n    static fromDirection(direction) {\n      var _DirectionToVectorMap;\n      return (_DirectionToVectorMap = DirectionToVectorMappings.get(direction)) !== null && _DirectionToVectorMap !== void 0 ? _DirectionToVectorMap : new Vector(0, 0);\n    }\n    static fromVelocity(tracker, pointerId) {\n      const velocity = tracker.getVelocity(pointerId);\n      return new Vector(velocity.x, velocity.y);\n    }\n    get magnitude() {\n      return this._magnitude;\n    }\n    computeSimilarity(vector) {\n      return this.unitX * vector.unitX + this.unitY * vector.unitY;\n    }\n    isSimilar(vector, threshold) {\n      return this.computeSimilarity(vector) > threshold;\n    }\n  }\n  exports.default = Vector;\n  const DirectionToVectorMappings = new Map([[_Directions.Directions.LEFT, new Vector(-1, 0)], [_Directions.Directions.RIGHT, new Vector(1, 0)], [_Directions.Directions.UP, new Vector(0, -1)], [_Directions.Directions.DOWN, new Vector(0, 1)], [_Directions.DiagonalDirections.UP_RIGHT, new Vector(1, -1)], [_Directions.DiagonalDirections.DOWN_RIGHT, new Vector(1, 1)], [_Directions.DiagonalDirections.UP_LEFT, new Vector(-1, -1)], [_Directions.DiagonalDirections.DOWN_LEFT, new Vector(-1, 1)]]);\n});", "lineCount": 55, "map": [[6, 2, 3, 0], [6, 6, 3, 0, "_Directions"], [6, 17, 3, 0], [6, 20, 3, 0, "require"], [6, 27, 3, 0], [6, 28, 3, 0, "_dependencyMap"], [6, 42, 3, 0], [7, 2, 4, 0], [7, 6, 4, 0, "_constants"], [7, 16, 4, 0], [7, 19, 4, 0, "require"], [7, 26, 4, 0], [7, 27, 4, 0, "_dependencyMap"], [7, 41, 4, 0], [8, 2, 1, 0], [8, 11, 1, 9, "_defineProperty"], [8, 26, 1, 24, "_defineProperty"], [8, 27, 1, 25, "obj"], [8, 30, 1, 28], [8, 32, 1, 30, "key"], [8, 35, 1, 33], [8, 37, 1, 35, "value"], [8, 42, 1, 40], [8, 44, 1, 42], [9, 4, 1, 44], [9, 8, 1, 48, "key"], [9, 11, 1, 51], [9, 15, 1, 55, "obj"], [9, 18, 1, 58], [9, 20, 1, 60], [10, 6, 1, 62, "Object"], [10, 12, 1, 68], [10, 13, 1, 69, "defineProperty"], [10, 27, 1, 83], [10, 28, 1, 84, "obj"], [10, 31, 1, 87], [10, 33, 1, 89, "key"], [10, 36, 1, 92], [10, 38, 1, 94], [11, 8, 1, 96, "value"], [11, 13, 1, 101], [11, 15, 1, 103, "value"], [11, 20, 1, 108], [12, 8, 1, 110, "enumerable"], [12, 18, 1, 120], [12, 20, 1, 122], [12, 24, 1, 126], [13, 8, 1, 128, "configurable"], [13, 20, 1, 140], [13, 22, 1, 142], [13, 26, 1, 146], [14, 8, 1, 148, "writable"], [14, 16, 1, 156], [14, 18, 1, 158], [15, 6, 1, 163], [15, 7, 1, 164], [15, 8, 1, 165], [16, 4, 1, 167], [16, 5, 1, 168], [16, 11, 1, 174], [17, 6, 1, 176, "obj"], [17, 9, 1, 179], [17, 10, 1, 180, "key"], [17, 13, 1, 183], [17, 14, 1, 184], [17, 17, 1, 187, "value"], [17, 22, 1, 192], [18, 4, 1, 194], [19, 4, 1, 196], [19, 11, 1, 203, "obj"], [19, 14, 1, 206], [20, 2, 1, 208], [21, 2, 5, 15], [21, 8, 5, 21, "Vector"], [21, 14, 5, 27], [21, 15, 5, 28], [22, 4, 6, 2, "constructor"], [22, 15, 6, 13, "constructor"], [22, 16, 6, 14, "x"], [22, 17, 6, 15], [22, 19, 6, 17, "y"], [22, 20, 6, 18], [22, 22, 6, 20], [23, 6, 7, 4, "_defineProperty"], [23, 21, 7, 19], [23, 22, 7, 20], [23, 26, 7, 24], [23, 28, 7, 26], [23, 31, 7, 29], [23, 33, 7, 31], [23, 38, 7, 36], [23, 39, 7, 37], [23, 40, 7, 38], [24, 6, 9, 4, "_defineProperty"], [24, 21, 9, 19], [24, 22, 9, 20], [24, 26, 9, 24], [24, 28, 9, 26], [24, 31, 9, 29], [24, 33, 9, 31], [24, 38, 9, 36], [24, 39, 9, 37], [24, 40, 9, 38], [25, 6, 11, 4, "_defineProperty"], [25, 21, 11, 19], [25, 22, 11, 20], [25, 26, 11, 24], [25, 28, 11, 26], [25, 35, 11, 33], [25, 37, 11, 35], [25, 42, 11, 40], [25, 43, 11, 41], [25, 44, 11, 42], [26, 6, 13, 4, "_defineProperty"], [26, 21, 13, 19], [26, 22, 13, 20], [26, 26, 13, 24], [26, 28, 13, 26], [26, 35, 13, 33], [26, 37, 13, 35], [26, 42, 13, 40], [26, 43, 13, 41], [26, 44, 13, 42], [27, 6, 15, 4, "_defineProperty"], [27, 21, 15, 19], [27, 22, 15, 20], [27, 26, 15, 24], [27, 28, 15, 26], [27, 40, 15, 38], [27, 42, 15, 40], [27, 47, 15, 45], [27, 48, 15, 46], [27, 49, 15, 47], [28, 6, 17, 4], [28, 10, 17, 8], [28, 11, 17, 9, "x"], [28, 12, 17, 10], [28, 15, 17, 13, "x"], [28, 16, 17, 14], [29, 6, 18, 4], [29, 10, 18, 8], [29, 11, 18, 9, "y"], [29, 12, 18, 10], [29, 15, 18, 13, "y"], [29, 16, 18, 14], [30, 6, 19, 4], [30, 10, 19, 8], [30, 11, 19, 9, "_magnitude"], [30, 21, 19, 19], [30, 24, 19, 22, "Math"], [30, 28, 19, 26], [30, 29, 19, 27, "hypot"], [30, 34, 19, 32], [30, 35, 19, 33], [30, 39, 19, 37], [30, 40, 19, 38, "x"], [30, 41, 19, 39], [30, 43, 19, 41], [30, 47, 19, 45], [30, 48, 19, 46, "y"], [30, 49, 19, 47], [30, 50, 19, 48], [31, 6, 20, 4], [31, 12, 20, 10, "isMagnitudeSufficient"], [31, 33, 20, 31], [31, 36, 20, 34], [31, 40, 20, 38], [31, 41, 20, 39, "_magnitude"], [31, 51, 20, 49], [31, 54, 20, 52, "MINIMAL_RECOGNIZABLE_MAGNITUDE"], [31, 95, 20, 82], [32, 6, 21, 4], [32, 10, 21, 8], [32, 11, 21, 9, "unitX"], [32, 16, 21, 14], [32, 19, 21, 17, "isMagnitudeSufficient"], [32, 40, 21, 38], [32, 43, 21, 41], [32, 47, 21, 45], [32, 48, 21, 46, "x"], [32, 49, 21, 47], [32, 52, 21, 50], [32, 56, 21, 54], [32, 57, 21, 55, "_magnitude"], [32, 67, 21, 65], [32, 70, 21, 68], [32, 71, 21, 69], [33, 6, 22, 4], [33, 10, 22, 8], [33, 11, 22, 9, "unitY"], [33, 16, 22, 14], [33, 19, 22, 17, "isMagnitudeSufficient"], [33, 40, 22, 38], [33, 43, 22, 41], [33, 47, 22, 45], [33, 48, 22, 46, "y"], [33, 49, 22, 47], [33, 52, 22, 50], [33, 56, 22, 54], [33, 57, 22, 55, "_magnitude"], [33, 67, 22, 65], [33, 70, 22, 68], [33, 71, 22, 69], [34, 4, 23, 2], [35, 4, 25, 2], [35, 11, 25, 9, "fromDirection"], [35, 24, 25, 22, "fromDirection"], [35, 25, 25, 23, "direction"], [35, 34, 25, 32], [35, 36, 25, 34], [36, 6, 26, 4], [36, 10, 26, 8, "_DirectionToVectorMap"], [36, 31, 26, 29], [37, 6, 28, 4], [37, 13, 28, 11], [37, 14, 28, 12, "_DirectionToVectorMap"], [37, 35, 28, 33], [37, 38, 28, 36, "DirectionToVectorMappings"], [37, 63, 28, 61], [37, 64, 28, 62, "get"], [37, 67, 28, 65], [37, 68, 28, 66, "direction"], [37, 77, 28, 75], [37, 78, 28, 76], [37, 84, 28, 82], [37, 88, 28, 86], [37, 92, 28, 90, "_DirectionToVectorMap"], [37, 113, 28, 111], [37, 118, 28, 116], [37, 123, 28, 121], [37, 124, 28, 122], [37, 127, 28, 125, "_DirectionToVectorMap"], [37, 148, 28, 146], [37, 151, 28, 149], [37, 155, 28, 153, "Vector"], [37, 161, 28, 159], [37, 162, 28, 160], [37, 163, 28, 161], [37, 165, 28, 163], [37, 166, 28, 164], [37, 167, 28, 165], [38, 4, 29, 2], [39, 4, 31, 2], [39, 11, 31, 9, "fromVelocity"], [39, 23, 31, 21, "fromVelocity"], [39, 24, 31, 22, "tracker"], [39, 31, 31, 29], [39, 33, 31, 31, "pointerId"], [39, 42, 31, 40], [39, 44, 31, 42], [40, 6, 32, 4], [40, 12, 32, 10, "velocity"], [40, 20, 32, 18], [40, 23, 32, 21, "tracker"], [40, 30, 32, 28], [40, 31, 32, 29, "getVelocity"], [40, 42, 32, 40], [40, 43, 32, 41, "pointerId"], [40, 52, 32, 50], [40, 53, 32, 51], [41, 6, 33, 4], [41, 13, 33, 11], [41, 17, 33, 15, "Vector"], [41, 23, 33, 21], [41, 24, 33, 22, "velocity"], [41, 32, 33, 30], [41, 33, 33, 31, "x"], [41, 34, 33, 32], [41, 36, 33, 34, "velocity"], [41, 44, 33, 42], [41, 45, 33, 43, "y"], [41, 46, 33, 44], [41, 47, 33, 45], [42, 4, 34, 2], [43, 4, 36, 2], [43, 8, 36, 6, "magnitude"], [43, 17, 36, 15, "magnitude"], [43, 18, 36, 15], [43, 20, 36, 18], [44, 6, 37, 4], [44, 13, 37, 11], [44, 17, 37, 15], [44, 18, 37, 16, "_magnitude"], [44, 28, 37, 26], [45, 4, 38, 2], [46, 4, 40, 2, "computeSimilarity"], [46, 21, 40, 19, "computeSimilarity"], [46, 22, 40, 20, "vector"], [46, 28, 40, 26], [46, 30, 40, 28], [47, 6, 41, 4], [47, 13, 41, 11], [47, 17, 41, 15], [47, 18, 41, 16, "unitX"], [47, 23, 41, 21], [47, 26, 41, 24, "vector"], [47, 32, 41, 30], [47, 33, 41, 31, "unitX"], [47, 38, 41, 36], [47, 41, 41, 39], [47, 45, 41, 43], [47, 46, 41, 44, "unitY"], [47, 51, 41, 49], [47, 54, 41, 52, "vector"], [47, 60, 41, 58], [47, 61, 41, 59, "unitY"], [47, 66, 41, 64], [48, 4, 42, 2], [49, 4, 44, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [49, 13, 44, 11, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [49, 14, 44, 12, "vector"], [49, 20, 44, 18], [49, 22, 44, 20, "threshold"], [49, 31, 44, 29], [49, 33, 44, 31], [50, 6, 45, 4], [50, 13, 45, 11], [50, 17, 45, 15], [50, 18, 45, 16, "computeSimilarity"], [50, 35, 45, 33], [50, 36, 45, 34, "vector"], [50, 42, 45, 40], [50, 43, 45, 41], [50, 46, 45, 44, "threshold"], [50, 55, 45, 53], [51, 4, 46, 2], [52, 2, 48, 0], [53, 2, 48, 1, "exports"], [53, 9, 48, 1], [53, 10, 48, 1, "default"], [53, 17, 48, 1], [53, 20, 48, 1, "Vector"], [53, 26, 48, 1], [54, 2, 49, 0], [54, 8, 49, 6, "DirectionToVectorMappings"], [54, 33, 49, 31], [54, 36, 49, 34], [54, 40, 49, 38, "Map"], [54, 43, 49, 41], [54, 44, 49, 42], [54, 45, 49, 43], [54, 46, 49, 44, "Directions"], [54, 68, 49, 54], [54, 69, 49, 55, "LEFT"], [54, 73, 49, 59], [54, 75, 49, 61], [54, 79, 49, 65, "Vector"], [54, 85, 49, 71], [54, 86, 49, 72], [54, 87, 49, 73], [54, 88, 49, 74], [54, 90, 49, 76], [54, 91, 49, 77], [54, 92, 49, 78], [54, 93, 49, 79], [54, 95, 49, 81], [54, 96, 49, 82, "Directions"], [54, 118, 49, 92], [54, 119, 49, 93, "RIGHT"], [54, 124, 49, 98], [54, 126, 49, 100], [54, 130, 49, 104, "Vector"], [54, 136, 49, 110], [54, 137, 49, 111], [54, 138, 49, 112], [54, 140, 49, 114], [54, 141, 49, 115], [54, 142, 49, 116], [54, 143, 49, 117], [54, 145, 49, 119], [54, 146, 49, 120, "Directions"], [54, 168, 49, 130], [54, 169, 49, 131, "UP"], [54, 171, 49, 133], [54, 173, 49, 135], [54, 177, 49, 139, "Vector"], [54, 183, 49, 145], [54, 184, 49, 146], [54, 185, 49, 147], [54, 187, 49, 149], [54, 188, 49, 150], [54, 189, 49, 151], [54, 190, 49, 152], [54, 191, 49, 153], [54, 193, 49, 155], [54, 194, 49, 156, "Directions"], [54, 216, 49, 166], [54, 217, 49, 167, "DOWN"], [54, 221, 49, 171], [54, 223, 49, 173], [54, 227, 49, 177, "Vector"], [54, 233, 49, 183], [54, 234, 49, 184], [54, 235, 49, 185], [54, 237, 49, 187], [54, 238, 49, 188], [54, 239, 49, 189], [54, 240, 49, 190], [54, 242, 49, 192], [54, 243, 49, 193, "DiagonalDirections"], [54, 273, 49, 211], [54, 274, 49, 212, "UP_RIGHT"], [54, 282, 49, 220], [54, 284, 49, 222], [54, 288, 49, 226, "Vector"], [54, 294, 49, 232], [54, 295, 49, 233], [54, 296, 49, 234], [54, 298, 49, 236], [54, 299, 49, 237], [54, 300, 49, 238], [54, 301, 49, 239], [54, 302, 49, 240], [54, 304, 49, 242], [54, 305, 49, 243, "DiagonalDirections"], [54, 335, 49, 261], [54, 336, 49, 262, "DOWN_RIGHT"], [54, 346, 49, 272], [54, 348, 49, 274], [54, 352, 49, 278, "Vector"], [54, 358, 49, 284], [54, 359, 49, 285], [54, 360, 49, 286], [54, 362, 49, 288], [54, 363, 49, 289], [54, 364, 49, 290], [54, 365, 49, 291], [54, 367, 49, 293], [54, 368, 49, 294, "DiagonalDirections"], [54, 398, 49, 312], [54, 399, 49, 313, "UP_LEFT"], [54, 406, 49, 320], [54, 408, 49, 322], [54, 412, 49, 326, "Vector"], [54, 418, 49, 332], [54, 419, 49, 333], [54, 420, 49, 334], [54, 421, 49, 335], [54, 423, 49, 337], [54, 424, 49, 338], [54, 425, 49, 339], [54, 426, 49, 340], [54, 427, 49, 341], [54, 429, 49, 343], [54, 430, 49, 344, "DiagonalDirections"], [54, 460, 49, 362], [54, 461, 49, 363, "DOWN_LEFT"], [54, 470, 49, 372], [54, 472, 49, 374], [54, 476, 49, 378, "Vector"], [54, 482, 49, 384], [54, 483, 49, 385], [54, 484, 49, 386], [54, 485, 49, 387], [54, 487, 49, 389], [54, 488, 49, 390], [54, 489, 49, 391], [54, 490, 49, 392], [54, 491, 49, 393], [54, 492, 49, 394], [55, 0, 49, 395], [55, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "Vector", "constructor", "fromDirection", "fromVelocity", "get__magnitude", "computeSimilarity", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA,iNC;eCI;ECC;GDiB;EEE;GFI;EGE;GHG;EIE;GJE;EKE;GLE;EME;GNE;CDE"}}, "type": "js/module"}]}