{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectSpread2", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 65, "index": 65}}], "key": "SfRhzMj3Ex6qA89WTFEUm9Lj49A=", "exportNames": ["*"]}}, {"name": "fbjs/lib/invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 301}, "end": {"line": 12, "column": 43, "index": 344}}], "key": "bGUa+dDG2WEhPiIlobT3urS95UE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectSpread2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectSpread2\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[2], \"fbjs/lib/invariant\"));\n  /**\n   * Copyright (c) <PERSON>.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  class TaskQueue {\n    constructor(_ref) {\n      var onMoreTasks = _ref.onMoreTasks;\n      this._onMoreTasks = onMoreTasks;\n      this._queueStack = [{\n        tasks: [],\n        popable: true\n      }];\n    }\n    enqueue(task) {\n      this._getCurrentQueue().push(task);\n    }\n    enqueueTasks(tasks) {\n      tasks.forEach(task => this.enqueue(task));\n    }\n    cancelTasks(tasksToCancel) {\n      this._queueStack = this._queueStack.map(queue => (0, _objectSpread2.default)((0, _objectSpread2.default)({}, queue), {}, {\n        tasks: queue.tasks.filter(task => tasksToCancel.indexOf(task) === -1)\n      })).filter((queue, idx) => queue.tasks.length > 0 || idx === 0);\n    }\n    hasTasksToProcess() {\n      return this._getCurrentQueue().length > 0;\n    }\n\n    /**\n     * Executes the next task in the queue.\n     */\n    processNext() {\n      var queue = this._getCurrentQueue();\n      if (queue.length) {\n        var task = queue.shift();\n        try {\n          if (typeof task === 'object' && task.gen) {\n            this._genPromise(task);\n          } else if (typeof task === 'object' && task.run) {\n            task.run();\n          } else {\n            (0, _invariant.default)(typeof task === 'function', 'Expected Function, SimpleTask, or PromiseTask, but got:\\n' + JSON.stringify(task, null, 2));\n            task();\n          }\n        } catch (e) {\n          e.message = 'TaskQueue: Error with task ' + (task.name || '') + ': ' + e.message;\n          throw e;\n        }\n      }\n    }\n    _getCurrentQueue() {\n      var stackIdx = this._queueStack.length - 1;\n      var queue = this._queueStack[stackIdx];\n      if (queue.popable && queue.tasks.length === 0 && stackIdx > 0) {\n        this._queueStack.pop();\n        return this._getCurrentQueue();\n      } else {\n        return queue.tasks;\n      }\n    }\n    _genPromise(task) {\n      var length = this._queueStack.push({\n        tasks: [],\n        popable: false\n      });\n      var stackIdx = length - 1;\n      var stackItem = this._queueStack[stackIdx];\n      task.gen().then(() => {\n        stackItem.popable = true;\n        this.hasTasksToProcess() && this._onMoreTasks();\n      }).catch(ex => {\n        setTimeout(() => {\n          ex.message = \"TaskQueue: Error resolving Promise in task \" + task.name + \": \" + ex.message;\n          throw ex;\n        }, 0);\n      });\n    }\n  }\n  var _default = exports.default = TaskQueue;\n});", "lineCount": 94, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_objectSpread2"], [7, 20, 1, 0], [7, 23, 1, 0, "_interopRequireDefault"], [7, 45, 1, 0], [7, 46, 1, 0, "require"], [7, 53, 1, 0], [7, 54, 1, 0, "_dependencyMap"], [7, 68, 1, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_invariant"], [8, 16, 12, 0], [8, 19, 12, 0, "_interopRequireDefault"], [8, 41, 12, 0], [8, 42, 12, 0, "require"], [8, 49, 12, 0], [8, 50, 12, 0, "_dependencyMap"], [8, 64, 12, 0], [9, 2, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [14, 0, 7, 0], [15, 0, 8, 0], [16, 0, 9, 0], [17, 0, 10, 0], [19, 2, 13, 0], [19, 8, 13, 6, "TaskQueue"], [19, 17, 13, 15], [19, 18, 13, 16], [20, 4, 14, 2, "constructor"], [20, 15, 14, 13, "constructor"], [20, 16, 14, 14, "_ref"], [20, 20, 14, 18], [20, 22, 14, 20], [21, 6, 15, 4], [21, 10, 15, 8, "onMoreTasks"], [21, 21, 15, 19], [21, 24, 15, 22, "_ref"], [21, 28, 15, 26], [21, 29, 15, 27, "onMoreTasks"], [21, 40, 15, 38], [22, 6, 16, 4], [22, 10, 16, 8], [22, 11, 16, 9, "_onMoreTasks"], [22, 23, 16, 21], [22, 26, 16, 24, "onMoreTasks"], [22, 37, 16, 35], [23, 6, 17, 4], [23, 10, 17, 8], [23, 11, 17, 9, "_queueStack"], [23, 22, 17, 20], [23, 25, 17, 23], [23, 26, 17, 24], [24, 8, 18, 6, "tasks"], [24, 13, 18, 11], [24, 15, 18, 13], [24, 17, 18, 15], [25, 8, 19, 6, "popable"], [25, 15, 19, 13], [25, 17, 19, 15], [26, 6, 20, 4], [26, 7, 20, 5], [26, 8, 20, 6], [27, 4, 21, 2], [28, 4, 22, 2, "enqueue"], [28, 11, 22, 9, "enqueue"], [28, 12, 22, 10, "task"], [28, 16, 22, 14], [28, 18, 22, 16], [29, 6, 23, 4], [29, 10, 23, 8], [29, 11, 23, 9, "_getCurrentQueue"], [29, 27, 23, 25], [29, 28, 23, 26], [29, 29, 23, 27], [29, 30, 23, 28, "push"], [29, 34, 23, 32], [29, 35, 23, 33, "task"], [29, 39, 23, 37], [29, 40, 23, 38], [30, 4, 24, 2], [31, 4, 25, 2, "enqueueTasks"], [31, 16, 25, 14, "enqueueTasks"], [31, 17, 25, 15, "tasks"], [31, 22, 25, 20], [31, 24, 25, 22], [32, 6, 26, 4, "tasks"], [32, 11, 26, 9], [32, 12, 26, 10, "for<PERSON>ach"], [32, 19, 26, 17], [32, 20, 26, 18, "task"], [32, 24, 26, 22], [32, 28, 26, 26], [32, 32, 26, 30], [32, 33, 26, 31, "enqueue"], [32, 40, 26, 38], [32, 41, 26, 39, "task"], [32, 45, 26, 43], [32, 46, 26, 44], [32, 47, 26, 45], [33, 4, 27, 2], [34, 4, 28, 2, "cancelTasks"], [34, 15, 28, 13, "cancelTasks"], [34, 16, 28, 14, "tasksToCancel"], [34, 29, 28, 27], [34, 31, 28, 29], [35, 6, 29, 4], [35, 10, 29, 8], [35, 11, 29, 9, "_queueStack"], [35, 22, 29, 20], [35, 25, 29, 23], [35, 29, 29, 27], [35, 30, 29, 28, "_queueStack"], [35, 41, 29, 39], [35, 42, 29, 40, "map"], [35, 45, 29, 43], [35, 46, 29, 44, "queue"], [35, 51, 29, 49], [35, 55, 29, 53], [35, 59, 29, 53, "_objectSpread"], [35, 81, 29, 66], [35, 83, 29, 67], [35, 87, 29, 67, "_objectSpread"], [35, 109, 29, 80], [35, 111, 29, 81], [35, 112, 29, 82], [35, 113, 29, 83], [35, 115, 29, 85, "queue"], [35, 120, 29, 90], [35, 121, 29, 91], [35, 123, 29, 93], [35, 124, 29, 94], [35, 125, 29, 95], [35, 127, 29, 97], [36, 8, 30, 6, "tasks"], [36, 13, 30, 11], [36, 15, 30, 13, "queue"], [36, 20, 30, 18], [36, 21, 30, 19, "tasks"], [36, 26, 30, 24], [36, 27, 30, 25, "filter"], [36, 33, 30, 31], [36, 34, 30, 32, "task"], [36, 38, 30, 36], [36, 42, 30, 40, "tasksToCancel"], [36, 55, 30, 53], [36, 56, 30, 54, "indexOf"], [36, 63, 30, 61], [36, 64, 30, 62, "task"], [36, 68, 30, 66], [36, 69, 30, 67], [36, 74, 30, 72], [36, 75, 30, 73], [36, 76, 30, 74], [37, 6, 31, 4], [37, 7, 31, 5], [37, 8, 31, 6], [37, 9, 31, 7], [37, 10, 31, 8, "filter"], [37, 16, 31, 14], [37, 17, 31, 15], [37, 18, 31, 16, "queue"], [37, 23, 31, 21], [37, 25, 31, 23, "idx"], [37, 28, 31, 26], [37, 33, 31, 31, "queue"], [37, 38, 31, 36], [37, 39, 31, 37, "tasks"], [37, 44, 31, 42], [37, 45, 31, 43, "length"], [37, 51, 31, 49], [37, 54, 31, 52], [37, 55, 31, 53], [37, 59, 31, 57, "idx"], [37, 62, 31, 60], [37, 67, 31, 65], [37, 68, 31, 66], [37, 69, 31, 67], [38, 4, 32, 2], [39, 4, 33, 2, "hasTasksToProcess"], [39, 21, 33, 19, "hasTasksToProcess"], [39, 22, 33, 19], [39, 24, 33, 22], [40, 6, 34, 4], [40, 13, 34, 11], [40, 17, 34, 15], [40, 18, 34, 16, "_getCurrentQueue"], [40, 34, 34, 32], [40, 35, 34, 33], [40, 36, 34, 34], [40, 37, 34, 35, "length"], [40, 43, 34, 41], [40, 46, 34, 44], [40, 47, 34, 45], [41, 4, 35, 2], [43, 4, 37, 2], [44, 0, 38, 0], [45, 0, 39, 0], [46, 4, 40, 2, "processNext"], [46, 15, 40, 13, "processNext"], [46, 16, 40, 13], [46, 18, 40, 16], [47, 6, 41, 4], [47, 10, 41, 8, "queue"], [47, 15, 41, 13], [47, 18, 41, 16], [47, 22, 41, 20], [47, 23, 41, 21, "_getCurrentQueue"], [47, 39, 41, 37], [47, 40, 41, 38], [47, 41, 41, 39], [48, 6, 42, 4], [48, 10, 42, 8, "queue"], [48, 15, 42, 13], [48, 16, 42, 14, "length"], [48, 22, 42, 20], [48, 24, 42, 22], [49, 8, 43, 6], [49, 12, 43, 10, "task"], [49, 16, 43, 14], [49, 19, 43, 17, "queue"], [49, 24, 43, 22], [49, 25, 43, 23, "shift"], [49, 30, 43, 28], [49, 31, 43, 29], [49, 32, 43, 30], [50, 8, 44, 6], [50, 12, 44, 10], [51, 10, 45, 8], [51, 14, 45, 12], [51, 21, 45, 19, "task"], [51, 25, 45, 23], [51, 30, 45, 28], [51, 38, 45, 36], [51, 42, 45, 40, "task"], [51, 46, 45, 44], [51, 47, 45, 45, "gen"], [51, 50, 45, 48], [51, 52, 45, 50], [52, 12, 46, 10], [52, 16, 46, 14], [52, 17, 46, 15, "_genPromise"], [52, 28, 46, 26], [52, 29, 46, 27, "task"], [52, 33, 46, 31], [52, 34, 46, 32], [53, 10, 47, 8], [53, 11, 47, 9], [53, 17, 47, 15], [53, 21, 47, 19], [53, 28, 47, 26, "task"], [53, 32, 47, 30], [53, 37, 47, 35], [53, 45, 47, 43], [53, 49, 47, 47, "task"], [53, 53, 47, 51], [53, 54, 47, 52, "run"], [53, 57, 47, 55], [53, 59, 47, 57], [54, 12, 48, 10, "task"], [54, 16, 48, 14], [54, 17, 48, 15, "run"], [54, 20, 48, 18], [54, 21, 48, 19], [54, 22, 48, 20], [55, 10, 49, 8], [55, 11, 49, 9], [55, 17, 49, 15], [56, 12, 50, 10], [56, 16, 50, 10, "invariant"], [56, 34, 50, 19], [56, 36, 50, 20], [56, 43, 50, 27, "task"], [56, 47, 50, 31], [56, 52, 50, 36], [56, 62, 50, 46], [56, 64, 50, 48], [56, 123, 50, 107], [56, 126, 50, 110, "JSON"], [56, 130, 50, 114], [56, 131, 50, 115, "stringify"], [56, 140, 50, 124], [56, 141, 50, 125, "task"], [56, 145, 50, 129], [56, 147, 50, 131], [56, 151, 50, 135], [56, 153, 50, 137], [56, 154, 50, 138], [56, 155, 50, 139], [56, 156, 50, 140], [57, 12, 51, 10, "task"], [57, 16, 51, 14], [57, 17, 51, 15], [57, 18, 51, 16], [58, 10, 52, 8], [59, 8, 53, 6], [59, 9, 53, 7], [59, 10, 53, 8], [59, 17, 53, 15, "e"], [59, 18, 53, 16], [59, 20, 53, 18], [60, 10, 54, 8, "e"], [60, 11, 54, 9], [60, 12, 54, 10, "message"], [60, 19, 54, 17], [60, 22, 54, 20], [60, 51, 54, 49], [60, 55, 54, 53, "task"], [60, 59, 54, 57], [60, 60, 54, 58, "name"], [60, 64, 54, 62], [60, 68, 54, 66], [60, 70, 54, 68], [60, 71, 54, 69], [60, 74, 54, 72], [60, 78, 54, 76], [60, 81, 54, 79, "e"], [60, 82, 54, 80], [60, 83, 54, 81, "message"], [60, 90, 54, 88], [61, 10, 55, 8], [61, 16, 55, 14, "e"], [61, 17, 55, 15], [62, 8, 56, 6], [63, 6, 57, 4], [64, 4, 58, 2], [65, 4, 59, 2, "_getCurrentQueue"], [65, 20, 59, 18, "_getCurrentQueue"], [65, 21, 59, 18], [65, 23, 59, 21], [66, 6, 60, 4], [66, 10, 60, 8, "stackIdx"], [66, 18, 60, 16], [66, 21, 60, 19], [66, 25, 60, 23], [66, 26, 60, 24, "_queueStack"], [66, 37, 60, 35], [66, 38, 60, 36, "length"], [66, 44, 60, 42], [66, 47, 60, 45], [66, 48, 60, 46], [67, 6, 61, 4], [67, 10, 61, 8, "queue"], [67, 15, 61, 13], [67, 18, 61, 16], [67, 22, 61, 20], [67, 23, 61, 21, "_queueStack"], [67, 34, 61, 32], [67, 35, 61, 33, "stackIdx"], [67, 43, 61, 41], [67, 44, 61, 42], [68, 6, 62, 4], [68, 10, 62, 8, "queue"], [68, 15, 62, 13], [68, 16, 62, 14, "popable"], [68, 23, 62, 21], [68, 27, 62, 25, "queue"], [68, 32, 62, 30], [68, 33, 62, 31, "tasks"], [68, 38, 62, 36], [68, 39, 62, 37, "length"], [68, 45, 62, 43], [68, 50, 62, 48], [68, 51, 62, 49], [68, 55, 62, 53, "stackIdx"], [68, 63, 62, 61], [68, 66, 62, 64], [68, 67, 62, 65], [68, 69, 62, 67], [69, 8, 63, 6], [69, 12, 63, 10], [69, 13, 63, 11, "_queueStack"], [69, 24, 63, 22], [69, 25, 63, 23, "pop"], [69, 28, 63, 26], [69, 29, 63, 27], [69, 30, 63, 28], [70, 8, 64, 6], [70, 15, 64, 13], [70, 19, 64, 17], [70, 20, 64, 18, "_getCurrentQueue"], [70, 36, 64, 34], [70, 37, 64, 35], [70, 38, 64, 36], [71, 6, 65, 4], [71, 7, 65, 5], [71, 13, 65, 11], [72, 8, 66, 6], [72, 15, 66, 13, "queue"], [72, 20, 66, 18], [72, 21, 66, 19, "tasks"], [72, 26, 66, 24], [73, 6, 67, 4], [74, 4, 68, 2], [75, 4, 69, 2, "_genPromise"], [75, 15, 69, 13, "_genPromise"], [75, 16, 69, 14, "task"], [75, 20, 69, 18], [75, 22, 69, 20], [76, 6, 70, 4], [76, 10, 70, 8, "length"], [76, 16, 70, 14], [76, 19, 70, 17], [76, 23, 70, 21], [76, 24, 70, 22, "_queueStack"], [76, 35, 70, 33], [76, 36, 70, 34, "push"], [76, 40, 70, 38], [76, 41, 70, 39], [77, 8, 71, 6, "tasks"], [77, 13, 71, 11], [77, 15, 71, 13], [77, 17, 71, 15], [78, 8, 72, 6, "popable"], [78, 15, 72, 13], [78, 17, 72, 15], [79, 6, 73, 4], [79, 7, 73, 5], [79, 8, 73, 6], [80, 6, 74, 4], [80, 10, 74, 8, "stackIdx"], [80, 18, 74, 16], [80, 21, 74, 19, "length"], [80, 27, 74, 25], [80, 30, 74, 28], [80, 31, 74, 29], [81, 6, 75, 4], [81, 10, 75, 8, "stackItem"], [81, 19, 75, 17], [81, 22, 75, 20], [81, 26, 75, 24], [81, 27, 75, 25, "_queueStack"], [81, 38, 75, 36], [81, 39, 75, 37, "stackIdx"], [81, 47, 75, 45], [81, 48, 75, 46], [82, 6, 76, 4, "task"], [82, 10, 76, 8], [82, 11, 76, 9, "gen"], [82, 14, 76, 12], [82, 15, 76, 13], [82, 16, 76, 14], [82, 17, 76, 15, "then"], [82, 21, 76, 19], [82, 22, 76, 20], [82, 28, 76, 26], [83, 8, 77, 6, "stackItem"], [83, 17, 77, 15], [83, 18, 77, 16, "popable"], [83, 25, 77, 23], [83, 28, 77, 26], [83, 32, 77, 30], [84, 8, 78, 6], [84, 12, 78, 10], [84, 13, 78, 11, "hasTasksToProcess"], [84, 30, 78, 28], [84, 31, 78, 29], [84, 32, 78, 30], [84, 36, 78, 34], [84, 40, 78, 38], [84, 41, 78, 39, "_onMoreTasks"], [84, 53, 78, 51], [84, 54, 78, 52], [84, 55, 78, 53], [85, 6, 79, 4], [85, 7, 79, 5], [85, 8, 79, 6], [85, 9, 79, 7, "catch"], [85, 14, 79, 12], [85, 15, 79, 13, "ex"], [85, 17, 79, 15], [85, 21, 79, 19], [86, 8, 80, 6, "setTimeout"], [86, 18, 80, 16], [86, 19, 80, 17], [86, 25, 80, 23], [87, 10, 81, 8, "ex"], [87, 12, 81, 10], [87, 13, 81, 11, "message"], [87, 20, 81, 18], [87, 23, 81, 21], [87, 68, 81, 66], [87, 71, 81, 69, "task"], [87, 75, 81, 73], [87, 76, 81, 74, "name"], [87, 80, 81, 78], [87, 83, 81, 81], [87, 87, 81, 85], [87, 90, 81, 88, "ex"], [87, 92, 81, 90], [87, 93, 81, 91, "message"], [87, 100, 81, 98], [88, 10, 82, 8], [88, 16, 82, 14, "ex"], [88, 18, 82, 16], [89, 8, 83, 6], [89, 9, 83, 7], [89, 11, 83, 9], [89, 12, 83, 10], [89, 13, 83, 11], [90, 6, 84, 4], [90, 7, 84, 5], [90, 8, 84, 6], [91, 4, 85, 2], [92, 2, 86, 0], [93, 2, 86, 1], [93, 6, 86, 1, "_default"], [93, 14, 86, 1], [93, 17, 86, 1, "exports"], [93, 24, 86, 1], [93, 25, 86, 1, "default"], [93, 32, 86, 1], [93, 35, 87, 15, "TaskQueue"], [93, 44, 87, 24], [94, 0, 87, 24], [94, 3]], "functionMap": {"names": ["<global>", "TaskQueue", "constructor", "enqueue", "enqueueTasks", "tasks.forEach$argument_0", "cancelTasks", "_queueStack.map$argument_0", "queue.tasks.filter$argument_0", "_queueStack.map.filter$argument_0", "hasTasksToProcess", "processNext", "_getCurrentQueue", "_genPromise", "task.gen.then$argument_0", "task.gen.then._catch$argument_0", "setTimeout$argument_0"], "mappings": "AAA;ACY;ECC;GDO;EEC;GFE;EGC;kBCC,0BD;GHC;EKC;4CCC;gCCC,0CD;MDC,SG,mDH;GLC;ESC;GTE;EUK;GVkB;EWC;GXS;EYC;oBCO;KDG,QE;iBCC;ODG;KFC;GZC;CDC"}}, "type": "js/module"}]}