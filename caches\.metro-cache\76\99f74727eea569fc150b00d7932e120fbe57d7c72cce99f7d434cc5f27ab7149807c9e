{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@react-navigation/elements", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 105, "index": 120}}], "key": "LmqW7jh+SpCzQZMkzh+Awcuawt0=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 121}, "end": {"line": 4, "column": 137, "index": 258}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 259}, "end": {"line": 5, "column": 26, "index": 285}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 286}, "end": {"line": 6, "column": 68, "index": 354}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../utils/BottomTabBarHeightCallbackContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 355}, "end": {"line": 7, "column": 98, "index": 453}}], "key": "ugELg4I63IAZwlqh6l59tEtqTlI=", "exportNames": ["*"]}}, {"name": "../utils/useIsKeyboardShown.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 454}, "end": {"line": 8, "column": 68, "index": 522}}], "key": "mhLM44FQn4R6yOS65twI8hSRhu8=", "exportNames": ["*"]}}, {"name": "./BottomTabItem.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 523}, "end": {"line": 9, "column": 51, "index": 574}}], "key": "ocsmW2cewFboIoyqEEjH8emGUpI=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 575}, "end": {"line": 10, "column": 63, "index": 638}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.BottomTabBar = BottomTabBar;\n  exports.getTabBarHeight = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _elements = require(_dependencyMap[2], \"@react-navigation/elements\");\n  var _native = require(_dependencyMap[3], \"@react-navigation/native\");\n  var _react = _interopRequireDefault(require(_dependencyMap[4], \"react\"));\n  var _reactNative = require(_dependencyMap[5], \"react-native\");\n  var _BottomTabBarHeightCallbackContext = require(_dependencyMap[6], \"../utils/BottomTabBarHeightCallbackContext.js\");\n  var _useIsKeyboardShown = require(_dependencyMap[7], \"../utils/useIsKeyboardShown.js\");\n  var _BottomTabItem = require(_dependencyMap[8], \"./BottomTabItem.js\");\n  var _jsxRuntime = require(_dependencyMap[9], \"react/jsx-runtime\");\n  var TABBAR_HEIGHT_UIKIT = 49;\n  var TABBAR_HEIGHT_UIKIT_COMPACT = 32;\n  var SPACING_UIKIT = 15;\n  var SPACING_MATERIAL = 12;\n  var DEFAULT_MAX_TAB_ITEM_WIDTH = 125;\n  var useNativeDriver = _reactNative.Platform.OS !== 'web';\n  var shouldUseHorizontalLabels = _ref => {\n    var state = _ref.state,\n      descriptors = _ref.descriptors,\n      dimensions = _ref.dimensions;\n    var tabBarLabelPosition = descriptors[state.routes[state.index].key].options.tabBarLabelPosition;\n    if (tabBarLabelPosition) {\n      switch (tabBarLabelPosition) {\n        case 'beside-icon':\n          return true;\n        case 'below-icon':\n          return false;\n      }\n    }\n    if (dimensions.width >= 768) {\n      // Screen size matches a tablet\n      var maxTabWidth = state.routes.reduce((acc, route) => {\n        var tabBarItemStyle = descriptors[route.key].options.tabBarItemStyle;\n        var flattenedStyle = _reactNative.StyleSheet.flatten(tabBarItemStyle);\n        if (flattenedStyle) {\n          if (typeof flattenedStyle.width === 'number') {\n            return acc + flattenedStyle.width;\n          } else if (typeof flattenedStyle.maxWidth === 'number') {\n            return acc + flattenedStyle.maxWidth;\n          }\n        }\n        return acc + DEFAULT_MAX_TAB_ITEM_WIDTH;\n      }, 0);\n      return maxTabWidth <= dimensions.width;\n    } else {\n      return dimensions.width > dimensions.height;\n    }\n  };\n  var isCompact = _ref2 => {\n    var state = _ref2.state,\n      descriptors = _ref2.descriptors,\n      dimensions = _ref2.dimensions;\n    var _descriptors$state$ro = descriptors[state.routes[state.index].key].options,\n      tabBarPosition = _descriptors$state$ro.tabBarPosition,\n      tabBarVariant = _descriptors$state$ro.tabBarVariant;\n    if (tabBarPosition === 'left' || tabBarPosition === 'right' || tabBarVariant === 'material') {\n      return false;\n    }\n    var isLandscape = dimensions.width > dimensions.height;\n    var horizontalLabels = shouldUseHorizontalLabels({\n      state,\n      descriptors,\n      dimensions\n    });\n    if (_reactNative.Platform.OS === 'ios' && !_reactNative.Platform.isPad && isLandscape && horizontalLabels) {\n      return true;\n    }\n    return false;\n  };\n  var getTabBarHeight = _ref3 => {\n    var state = _ref3.state,\n      descriptors = _ref3.descriptors,\n      dimensions = _ref3.dimensions,\n      insets = _ref3.insets,\n      style = _ref3.style;\n    var tabBarPosition = descriptors[state.routes[state.index].key].options.tabBarPosition;\n    var flattenedStyle = _reactNative.StyleSheet.flatten(style);\n    var customHeight = flattenedStyle && 'height' in flattenedStyle ? flattenedStyle.height : undefined;\n    if (typeof customHeight === 'number') {\n      return customHeight;\n    }\n    var inset = insets[tabBarPosition === 'top' ? 'top' : 'bottom'];\n    if (isCompact({\n      state,\n      descriptors,\n      dimensions\n    })) {\n      return TABBAR_HEIGHT_UIKIT_COMPACT + inset;\n    }\n    return TABBAR_HEIGHT_UIKIT + inset;\n  };\n  exports.getTabBarHeight = getTabBarHeight;\n  function BottomTabBar(_ref4) {\n    var state = _ref4.state,\n      navigation = _ref4.navigation,\n      descriptors = _ref4.descriptors,\n      insets = _ref4.insets,\n      style = _ref4.style;\n    var _useTheme = (0, _native.useTheme)(),\n      colors = _useTheme.colors;\n    var _useLocale = (0, _native.useLocale)(),\n      direction = _useLocale.direction;\n    var _useLinkBuilder = (0, _native.useLinkBuilder)(),\n      buildHref = _useLinkBuilder.buildHref;\n    var focusedRoute = state.routes[state.index];\n    var focusedDescriptor = descriptors[focusedRoute.key];\n    var focusedOptions = focusedDescriptor.options;\n    var _focusedOptions$tabBa = focusedOptions.tabBarPosition,\n      tabBarPosition = _focusedOptions$tabBa === void 0 ? 'bottom' : _focusedOptions$tabBa,\n      tabBarShowLabel = focusedOptions.tabBarShowLabel,\n      tabBarLabelPosition = focusedOptions.tabBarLabelPosition,\n      _focusedOptions$tabBa2 = focusedOptions.tabBarHideOnKeyboard,\n      tabBarHideOnKeyboard = _focusedOptions$tabBa2 === void 0 ? false : _focusedOptions$tabBa2,\n      tabBarVisibilityAnimationConfig = focusedOptions.tabBarVisibilityAnimationConfig,\n      _focusedOptions$tabBa3 = focusedOptions.tabBarVariant,\n      tabBarVariant = _focusedOptions$tabBa3 === void 0 ? 'uikit' : _focusedOptions$tabBa3,\n      tabBarStyle = focusedOptions.tabBarStyle,\n      tabBarBackground = focusedOptions.tabBarBackground,\n      tabBarActiveTintColor = focusedOptions.tabBarActiveTintColor,\n      tabBarInactiveTintColor = focusedOptions.tabBarInactiveTintColor,\n      tabBarActiveBackgroundColor = focusedOptions.tabBarActiveBackgroundColor,\n      tabBarInactiveBackgroundColor = focusedOptions.tabBarInactiveBackgroundColor;\n    if (tabBarVariant === 'material' && tabBarPosition !== 'left' && tabBarPosition !== 'right') {\n      throw new Error(\"The 'material' variant for tab bar is only supported when 'tabBarPosition' is set to 'left' or 'right'.\");\n    }\n    if (tabBarLabelPosition === 'below-icon' && tabBarVariant === 'uikit' && (tabBarPosition === 'left' || tabBarPosition === 'right')) {\n      throw new Error(\"The 'below-icon' label position for tab bar is only supported when 'tabBarPosition' is set to 'top' or 'bottom' when using the 'uikit' variant.\");\n    }\n    var isKeyboardShown = (0, _useIsKeyboardShown.useIsKeyboardShown)();\n    var onHeightChange = _react.default.useContext(_BottomTabBarHeightCallbackContext.BottomTabBarHeightCallbackContext);\n    var shouldShowTabBar = !(tabBarHideOnKeyboard && isKeyboardShown);\n    var visibilityAnimationConfigRef = _react.default.useRef(tabBarVisibilityAnimationConfig);\n    _react.default.useEffect(() => {\n      visibilityAnimationConfigRef.current = tabBarVisibilityAnimationConfig;\n    });\n    var _React$useState = _react.default.useState(!shouldShowTabBar),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      isTabBarHidden = _React$useState2[0],\n      setIsTabBarHidden = _React$useState2[1];\n    var _React$useState3 = _react.default.useState(() => new _reactNative.Animated.Value(shouldShowTabBar ? 1 : 0)),\n      _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 1),\n      visible = _React$useState4[0];\n    _react.default.useEffect(() => {\n      var visibilityAnimationConfig = visibilityAnimationConfigRef.current;\n      if (shouldShowTabBar) {\n        var animation = visibilityAnimationConfig?.show?.animation === 'spring' ? _reactNative.Animated.spring : _reactNative.Animated.timing;\n        animation(visible, {\n          toValue: 1,\n          useNativeDriver,\n          duration: 250,\n          ...visibilityAnimationConfig?.show?.config\n        }).start(_ref5 => {\n          var finished = _ref5.finished;\n          if (finished) {\n            setIsTabBarHidden(false);\n          }\n        });\n      } else {\n        // eslint-disable-next-line @eslint-react/hooks-extra/no-direct-set-state-in-use-effect\n        setIsTabBarHidden(true);\n        var _animation = visibilityAnimationConfig?.hide?.animation === 'spring' ? _reactNative.Animated.spring : _reactNative.Animated.timing;\n        _animation(visible, {\n          toValue: 0,\n          useNativeDriver,\n          duration: 200,\n          ...visibilityAnimationConfig?.hide?.config\n        }).start();\n      }\n      return () => visible.stopAnimation();\n    }, [visible, shouldShowTabBar]);\n    var _React$useState5 = _react.default.useState({\n        height: 0\n      }),\n      _React$useState6 = (0, _slicedToArray2.default)(_React$useState5, 2),\n      layout = _React$useState6[0],\n      setLayout = _React$useState6[1];\n    var handleLayout = e => {\n      var height = e.nativeEvent.layout.height;\n      onHeightChange?.(height);\n      setLayout(layout => {\n        if (height === layout.height) {\n          return layout;\n        } else {\n          return {\n            height\n          };\n        }\n      });\n    };\n    var routes = state.routes;\n    var tabBarHeight = (0, _elements.useFrameSize)(dimensions => getTabBarHeight({\n      state,\n      descriptors,\n      insets,\n      dimensions,\n      style: [tabBarStyle, style]\n    }));\n    var hasHorizontalLabels = (0, _elements.useFrameSize)(dimensions => shouldUseHorizontalLabels({\n      state,\n      descriptors,\n      dimensions\n    }));\n    var compact = (0, _elements.useFrameSize)(dimensions => isCompact({\n      state,\n      descriptors,\n      dimensions\n    }));\n    var sidebar = tabBarPosition === 'left' || tabBarPosition === 'right';\n    var spacing = tabBarVariant === 'material' ? SPACING_MATERIAL : SPACING_UIKIT;\n    var minSidebarWidth = (0, _elements.useFrameSize)(size => sidebar && hasHorizontalLabels ? (0, _elements.getDefaultSidebarWidth)(size) : 0);\n    var tabBarBackgroundElement = tabBarBackground?.();\n    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.Animated.View, {\n      style: [tabBarPosition === 'left' ? styles.start : tabBarPosition === 'right' ? styles.end : styles.bottom, (_reactNative.Platform.OS === 'web' ? tabBarPosition === 'right' : direction === 'rtl' && tabBarPosition === 'left' || direction !== 'rtl' && tabBarPosition === 'right') ? {\n        borderLeftWidth: _reactNative.StyleSheet.hairlineWidth\n      } : (_reactNative.Platform.OS === 'web' ? tabBarPosition === 'left' : direction === 'rtl' && tabBarPosition === 'right' || direction !== 'rtl' && tabBarPosition === 'left') ? {\n        borderRightWidth: _reactNative.StyleSheet.hairlineWidth\n      } : tabBarPosition === 'top' ? {\n        borderBottomWidth: _reactNative.StyleSheet.hairlineWidth\n      } : {\n        borderTopWidth: _reactNative.StyleSheet.hairlineWidth\n      }, {\n        backgroundColor: tabBarBackgroundElement != null ? 'transparent' : colors.card,\n        borderColor: colors.border\n      }, sidebar ? {\n        paddingTop: (hasHorizontalLabels ? spacing : spacing / 2) + insets.top,\n        paddingBottom: (hasHorizontalLabels ? spacing : spacing / 2) + insets.bottom,\n        paddingStart: spacing + (tabBarPosition === 'left' ? insets.left : 0),\n        paddingEnd: spacing + (tabBarPosition === 'right' ? insets.right : 0),\n        minWidth: minSidebarWidth\n      } : [{\n        transform: [{\n          translateY: visible.interpolate({\n            inputRange: [0, 1],\n            outputRange: [layout.height + insets[tabBarPosition === 'top' ? 'top' : 'bottom'] + _reactNative.StyleSheet.hairlineWidth, 0]\n          })\n        }],\n        // Absolutely position the tab bar so that the content is below it\n        // This is needed to avoid gap at bottom when the tab bar is hidden\n        position: isTabBarHidden ? 'absolute' : undefined\n      }, {\n        height: tabBarHeight,\n        paddingBottom: tabBarPosition === 'bottom' ? insets.bottom : 0,\n        paddingTop: tabBarPosition === 'top' ? insets.top : 0,\n        paddingHorizontal: Math.max(insets.left, insets.right)\n      }], tabBarStyle],\n      pointerEvents: isTabBarHidden ? 'none' : 'auto',\n      onLayout: sidebar ? undefined : handleLayout,\n      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n        pointerEvents: \"none\",\n        style: _reactNative.StyleSheet.absoluteFill,\n        children: tabBarBackgroundElement\n      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n        role: \"tablist\",\n        style: sidebar ? styles.sideContent : styles.bottomContent,\n        children: routes.map((route, index) => {\n          var focused = index === state.index;\n          var options = descriptors[route.key].options;\n          var onPress = () => {\n            var event = navigation.emit({\n              type: 'tabPress',\n              target: route.key,\n              canPreventDefault: true\n            });\n            if (!focused && !event.defaultPrevented) {\n              navigation.dispatch({\n                ..._native.CommonActions.navigate(route),\n                target: state.key\n              });\n            }\n          };\n          var onLongPress = () => {\n            navigation.emit({\n              type: 'tabLongPress',\n              target: route.key\n            });\n          };\n          var label = typeof options.tabBarLabel === 'function' ? options.tabBarLabel : (0, _elements.getLabel)({\n            label: options.tabBarLabel,\n            title: options.title\n          }, route.name);\n          var accessibilityLabel = options.tabBarAccessibilityLabel !== undefined ? options.tabBarAccessibilityLabel : typeof label === 'string' && _reactNative.Platform.OS === 'ios' ? `${label}, tab, ${index + 1} of ${routes.length}` : undefined;\n          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_native.NavigationContext.Provider, {\n            value: descriptors[route.key].navigation,\n            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_native.NavigationRouteContext.Provider, {\n              value: route,\n              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_BottomTabItem.BottomTabItem, {\n                href: buildHref(route.name, route.params),\n                route: route,\n                descriptor: descriptors[route.key],\n                focused: focused,\n                horizontal: hasHorizontalLabels,\n                compact: compact,\n                sidebar: sidebar,\n                variant: tabBarVariant,\n                onPress: onPress,\n                onLongPress: onLongPress,\n                accessibilityLabel: accessibilityLabel,\n                testID: options.tabBarButtonTestID,\n                allowFontScaling: options.tabBarAllowFontScaling,\n                activeTintColor: tabBarActiveTintColor,\n                inactiveTintColor: tabBarInactiveTintColor,\n                activeBackgroundColor: tabBarActiveBackgroundColor,\n                inactiveBackgroundColor: tabBarInactiveBackgroundColor,\n                button: options.tabBarButton,\n                icon: options.tabBarIcon ?? (_ref6 => {\n                  var color = _ref6.color,\n                    size = _ref6.size;\n                  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.MissingIcon, {\n                    color: color,\n                    size: size\n                  });\n                }),\n                badge: options.tabBarBadge,\n                badgeStyle: options.tabBarBadgeStyle,\n                label: label,\n                showLabel: tabBarShowLabel,\n                labelStyle: options.tabBarLabelStyle,\n                iconStyle: options.tabBarIconStyle,\n                style: [sidebar ? {\n                  marginVertical: hasHorizontalLabels ? tabBarVariant === 'material' ? 0 : 1 : spacing / 2\n                } : styles.bottomItem, options.tabBarItemStyle]\n              })\n            })\n          }, route.key);\n        })\n      })]\n    });\n  }\n  var styles = _reactNative.StyleSheet.create({\n    start: {\n      top: 0,\n      bottom: 0,\n      start: 0\n    },\n    end: {\n      top: 0,\n      bottom: 0,\n      end: 0\n    },\n    bottom: {\n      start: 0,\n      end: 0,\n      bottom: 0,\n      elevation: 8\n    },\n    bottomContent: {\n      flex: 1,\n      flexDirection: 'row'\n    },\n    sideContent: {\n      flex: 1,\n      flexDirection: 'column'\n    },\n    bottomItem: {\n      flex: 1\n    }\n  });\n});", "lineCount": 366, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "BottomTabBar"], [8, 22, 1, 13], [8, 25, 1, 13, "BottomTabBar"], [8, 37, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "getTabBarHeight"], [9, 25, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_slicedToArray2"], [10, 21, 1, 13], [10, 24, 1, 13, "_interopRequireDefault"], [10, 46, 1, 13], [10, 47, 1, 13, "require"], [10, 54, 1, 13], [10, 55, 1, 13, "_dependencyMap"], [10, 69, 1, 13], [11, 2, 3, 0], [11, 6, 3, 0, "_elements"], [11, 15, 3, 0], [11, 18, 3, 0, "require"], [11, 25, 3, 0], [11, 26, 3, 0, "_dependencyMap"], [11, 40, 3, 0], [12, 2, 4, 0], [12, 6, 4, 0, "_native"], [12, 13, 4, 0], [12, 16, 4, 0, "require"], [12, 23, 4, 0], [12, 24, 4, 0, "_dependencyMap"], [12, 38, 4, 0], [13, 2, 5, 0], [13, 6, 5, 0, "_react"], [13, 12, 5, 0], [13, 15, 5, 0, "_interopRequireDefault"], [13, 37, 5, 0], [13, 38, 5, 0, "require"], [13, 45, 5, 0], [13, 46, 5, 0, "_dependencyMap"], [13, 60, 5, 0], [14, 2, 6, 0], [14, 6, 6, 0, "_reactNative"], [14, 18, 6, 0], [14, 21, 6, 0, "require"], [14, 28, 6, 0], [14, 29, 6, 0, "_dependencyMap"], [14, 43, 6, 0], [15, 2, 7, 0], [15, 6, 7, 0, "_BottomTabBarHeightCallbackContext"], [15, 40, 7, 0], [15, 43, 7, 0, "require"], [15, 50, 7, 0], [15, 51, 7, 0, "_dependencyMap"], [15, 65, 7, 0], [16, 2, 8, 0], [16, 6, 8, 0, "_useIsKeyboardShown"], [16, 25, 8, 0], [16, 28, 8, 0, "require"], [16, 35, 8, 0], [16, 36, 8, 0, "_dependencyMap"], [16, 50, 8, 0], [17, 2, 9, 0], [17, 6, 9, 0, "_BottomTabItem"], [17, 20, 9, 0], [17, 23, 9, 0, "require"], [17, 30, 9, 0], [17, 31, 9, 0, "_dependencyMap"], [17, 45, 9, 0], [18, 2, 10, 0], [18, 6, 10, 0, "_jsxRuntime"], [18, 17, 10, 0], [18, 20, 10, 0, "require"], [18, 27, 10, 0], [18, 28, 10, 0, "_dependencyMap"], [18, 42, 10, 0], [19, 2, 11, 0], [19, 6, 11, 6, "TABBAR_HEIGHT_UIKIT"], [19, 25, 11, 25], [19, 28, 11, 28], [19, 30, 11, 30], [20, 2, 12, 0], [20, 6, 12, 6, "TABBAR_HEIGHT_UIKIT_COMPACT"], [20, 33, 12, 33], [20, 36, 12, 36], [20, 38, 12, 38], [21, 2, 13, 0], [21, 6, 13, 6, "SPACING_UIKIT"], [21, 19, 13, 19], [21, 22, 13, 22], [21, 24, 13, 24], [22, 2, 14, 0], [22, 6, 14, 6, "SPACING_MATERIAL"], [22, 22, 14, 22], [22, 25, 14, 25], [22, 27, 14, 27], [23, 2, 15, 0], [23, 6, 15, 6, "DEFAULT_MAX_TAB_ITEM_WIDTH"], [23, 32, 15, 32], [23, 35, 15, 35], [23, 38, 15, 38], [24, 2, 16, 0], [24, 6, 16, 6, "useNativeDriver"], [24, 21, 16, 21], [24, 24, 16, 24, "Platform"], [24, 45, 16, 32], [24, 46, 16, 33, "OS"], [24, 48, 16, 35], [24, 53, 16, 40], [24, 58, 16, 45], [25, 2, 17, 0], [25, 6, 17, 6, "shouldUseHorizontalLabels"], [25, 31, 17, 31], [25, 34, 17, 34, "_ref"], [25, 38, 17, 34], [25, 42, 21, 6], [26, 4, 21, 6], [26, 8, 18, 2, "state"], [26, 13, 18, 7], [26, 16, 18, 7, "_ref"], [26, 20, 18, 7], [26, 21, 18, 2, "state"], [26, 26, 18, 7], [27, 6, 19, 2, "descriptors"], [27, 17, 19, 13], [27, 20, 19, 13, "_ref"], [27, 24, 19, 13], [27, 25, 19, 2, "descriptors"], [27, 36, 19, 13], [28, 6, 20, 2, "dimensions"], [28, 16, 20, 12], [28, 19, 20, 12, "_ref"], [28, 23, 20, 12], [28, 24, 20, 2, "dimensions"], [28, 34, 20, 12], [29, 4, 22, 2], [29, 8, 23, 4, "tabBarLabelPosition"], [29, 27, 23, 23], [29, 30, 24, 6, "descriptors"], [29, 41, 24, 17], [29, 42, 24, 18, "state"], [29, 47, 24, 23], [29, 48, 24, 24, "routes"], [29, 54, 24, 30], [29, 55, 24, 31, "state"], [29, 60, 24, 36], [29, 61, 24, 37, "index"], [29, 66, 24, 42], [29, 67, 24, 43], [29, 68, 24, 44, "key"], [29, 71, 24, 47], [29, 72, 24, 48], [29, 73, 24, 49, "options"], [29, 80, 24, 56], [29, 81, 23, 4, "tabBarLabelPosition"], [29, 100, 23, 23], [30, 4, 25, 2], [30, 8, 25, 6, "tabBarLabelPosition"], [30, 27, 25, 25], [30, 29, 25, 27], [31, 6, 26, 4], [31, 14, 26, 12, "tabBarLabelPosition"], [31, 33, 26, 31], [32, 8, 27, 6], [32, 13, 27, 11], [32, 26, 27, 24], [33, 10, 28, 8], [33, 17, 28, 15], [33, 21, 28, 19], [34, 8, 29, 6], [34, 13, 29, 11], [34, 25, 29, 23], [35, 10, 30, 8], [35, 17, 30, 15], [35, 22, 30, 20], [36, 6, 31, 4], [37, 4, 32, 2], [38, 4, 33, 2], [38, 8, 33, 6, "dimensions"], [38, 18, 33, 16], [38, 19, 33, 17, "width"], [38, 24, 33, 22], [38, 28, 33, 26], [38, 31, 33, 29], [38, 33, 33, 31], [39, 6, 34, 4], [40, 6, 35, 4], [40, 10, 35, 10, "max<PERSON>ab<PERSON><PERSON><PERSON>"], [40, 21, 35, 21], [40, 24, 35, 24, "state"], [40, 29, 35, 29], [40, 30, 35, 30, "routes"], [40, 36, 35, 36], [40, 37, 35, 37, "reduce"], [40, 43, 35, 43], [40, 44, 35, 44], [40, 45, 35, 45, "acc"], [40, 48, 35, 48], [40, 50, 35, 50, "route"], [40, 55, 35, 55], [40, 60, 35, 60], [41, 8, 36, 6], [41, 12, 37, 8, "tabBarItemStyle"], [41, 27, 37, 23], [41, 30, 38, 10, "descriptors"], [41, 41, 38, 21], [41, 42, 38, 22, "route"], [41, 47, 38, 27], [41, 48, 38, 28, "key"], [41, 51, 38, 31], [41, 52, 38, 32], [41, 53, 38, 33, "options"], [41, 60, 38, 40], [41, 61, 37, 8, "tabBarItemStyle"], [41, 76, 37, 23], [42, 8, 39, 6], [42, 12, 39, 12, "flattenedStyle"], [42, 26, 39, 26], [42, 29, 39, 29, "StyleSheet"], [42, 52, 39, 39], [42, 53, 39, 40, "flatten"], [42, 60, 39, 47], [42, 61, 39, 48, "tabBarItemStyle"], [42, 76, 39, 63], [42, 77, 39, 64], [43, 8, 40, 6], [43, 12, 40, 10, "flattenedStyle"], [43, 26, 40, 24], [43, 28, 40, 26], [44, 10, 41, 8], [44, 14, 41, 12], [44, 21, 41, 19, "flattenedStyle"], [44, 35, 41, 33], [44, 36, 41, 34, "width"], [44, 41, 41, 39], [44, 46, 41, 44], [44, 54, 41, 52], [44, 56, 41, 54], [45, 12, 42, 10], [45, 19, 42, 17, "acc"], [45, 22, 42, 20], [45, 25, 42, 23, "flattenedStyle"], [45, 39, 42, 37], [45, 40, 42, 38, "width"], [45, 45, 42, 43], [46, 10, 43, 8], [46, 11, 43, 9], [46, 17, 43, 15], [46, 21, 43, 19], [46, 28, 43, 26, "flattenedStyle"], [46, 42, 43, 40], [46, 43, 43, 41, "max<PERSON><PERSON><PERSON>"], [46, 51, 43, 49], [46, 56, 43, 54], [46, 64, 43, 62], [46, 66, 43, 64], [47, 12, 44, 10], [47, 19, 44, 17, "acc"], [47, 22, 44, 20], [47, 25, 44, 23, "flattenedStyle"], [47, 39, 44, 37], [47, 40, 44, 38, "max<PERSON><PERSON><PERSON>"], [47, 48, 44, 46], [48, 10, 45, 8], [49, 8, 46, 6], [50, 8, 47, 6], [50, 15, 47, 13, "acc"], [50, 18, 47, 16], [50, 21, 47, 19, "DEFAULT_MAX_TAB_ITEM_WIDTH"], [50, 47, 47, 45], [51, 6, 48, 4], [51, 7, 48, 5], [51, 9, 48, 7], [51, 10, 48, 8], [51, 11, 48, 9], [52, 6, 49, 4], [52, 13, 49, 11, "max<PERSON>ab<PERSON><PERSON><PERSON>"], [52, 24, 49, 22], [52, 28, 49, 26, "dimensions"], [52, 38, 49, 36], [52, 39, 49, 37, "width"], [52, 44, 49, 42], [53, 4, 50, 2], [53, 5, 50, 3], [53, 11, 50, 9], [54, 6, 51, 4], [54, 13, 51, 11, "dimensions"], [54, 23, 51, 21], [54, 24, 51, 22, "width"], [54, 29, 51, 27], [54, 32, 51, 30, "dimensions"], [54, 42, 51, 40], [54, 43, 51, 41, "height"], [54, 49, 51, 47], [55, 4, 52, 2], [56, 2, 53, 0], [56, 3, 53, 1], [57, 2, 54, 0], [57, 6, 54, 6, "isCompact"], [57, 15, 54, 15], [57, 18, 54, 18, "_ref2"], [57, 23, 54, 18], [57, 27, 58, 6], [58, 4, 58, 6], [58, 8, 55, 2, "state"], [58, 13, 55, 7], [58, 16, 55, 7, "_ref2"], [58, 21, 55, 7], [58, 22, 55, 2, "state"], [58, 27, 55, 7], [59, 6, 56, 2, "descriptors"], [59, 17, 56, 13], [59, 20, 56, 13, "_ref2"], [59, 25, 56, 13], [59, 26, 56, 2, "descriptors"], [59, 37, 56, 13], [60, 6, 57, 2, "dimensions"], [60, 16, 57, 12], [60, 19, 57, 12, "_ref2"], [60, 24, 57, 12], [60, 25, 57, 2, "dimensions"], [60, 35, 57, 12], [61, 4, 59, 2], [61, 8, 59, 2, "_descriptors$state$ro"], [61, 29, 59, 2], [61, 32, 62, 6, "descriptors"], [61, 43, 62, 17], [61, 44, 62, 18, "state"], [61, 49, 62, 23], [61, 50, 62, 24, "routes"], [61, 56, 62, 30], [61, 57, 62, 31, "state"], [61, 62, 62, 36], [61, 63, 62, 37, "index"], [61, 68, 62, 42], [61, 69, 62, 43], [61, 70, 62, 44, "key"], [61, 73, 62, 47], [61, 74, 62, 48], [61, 75, 62, 49, "options"], [61, 82, 62, 56], [62, 6, 60, 4, "tabBarPosition"], [62, 20, 60, 18], [62, 23, 60, 18, "_descriptors$state$ro"], [62, 44, 60, 18], [62, 45, 60, 4, "tabBarPosition"], [62, 59, 60, 18], [63, 6, 61, 4, "tabBarVariant"], [63, 19, 61, 17], [63, 22, 61, 17, "_descriptors$state$ro"], [63, 43, 61, 17], [63, 44, 61, 4, "tabBarVariant"], [63, 57, 61, 17], [64, 4, 63, 2], [64, 8, 63, 6, "tabBarPosition"], [64, 22, 63, 20], [64, 27, 63, 25], [64, 33, 63, 31], [64, 37, 63, 35, "tabBarPosition"], [64, 51, 63, 49], [64, 56, 63, 54], [64, 63, 63, 61], [64, 67, 63, 65, "tabBarVariant"], [64, 80, 63, 78], [64, 85, 63, 83], [64, 95, 63, 93], [64, 97, 63, 95], [65, 6, 64, 4], [65, 13, 64, 11], [65, 18, 64, 16], [66, 4, 65, 2], [67, 4, 66, 2], [67, 8, 66, 8, "isLandscape"], [67, 19, 66, 19], [67, 22, 66, 22, "dimensions"], [67, 32, 66, 32], [67, 33, 66, 33, "width"], [67, 38, 66, 38], [67, 41, 66, 41, "dimensions"], [67, 51, 66, 51], [67, 52, 66, 52, "height"], [67, 58, 66, 58], [68, 4, 67, 2], [68, 8, 67, 8, "horizontalLabels"], [68, 24, 67, 24], [68, 27, 67, 27, "shouldUseHorizontalLabels"], [68, 52, 67, 52], [68, 53, 67, 53], [69, 6, 68, 4, "state"], [69, 11, 68, 9], [70, 6, 69, 4, "descriptors"], [70, 17, 69, 15], [71, 6, 70, 4, "dimensions"], [72, 4, 71, 2], [72, 5, 71, 3], [72, 6, 71, 4], [73, 4, 72, 2], [73, 8, 72, 6, "Platform"], [73, 29, 72, 14], [73, 30, 72, 15, "OS"], [73, 32, 72, 17], [73, 37, 72, 22], [73, 42, 72, 27], [73, 46, 72, 31], [73, 47, 72, 32, "Platform"], [73, 68, 72, 40], [73, 69, 72, 41, "isPad"], [73, 74, 72, 46], [73, 78, 72, 50, "isLandscape"], [73, 89, 72, 61], [73, 93, 72, 65, "horizontalLabels"], [73, 109, 72, 81], [73, 111, 72, 83], [74, 6, 73, 4], [74, 13, 73, 11], [74, 17, 73, 15], [75, 4, 74, 2], [76, 4, 75, 2], [76, 11, 75, 9], [76, 16, 75, 14], [77, 2, 76, 0], [77, 3, 76, 1], [78, 2, 77, 7], [78, 6, 77, 13, "getTabBarHeight"], [78, 21, 77, 28], [78, 24, 77, 31, "_ref3"], [78, 29, 77, 31], [78, 33, 83, 6], [79, 4, 83, 6], [79, 8, 78, 2, "state"], [79, 13, 78, 7], [79, 16, 78, 7, "_ref3"], [79, 21, 78, 7], [79, 22, 78, 2, "state"], [79, 27, 78, 7], [80, 6, 79, 2, "descriptors"], [80, 17, 79, 13], [80, 20, 79, 13, "_ref3"], [80, 25, 79, 13], [80, 26, 79, 2, "descriptors"], [80, 37, 79, 13], [81, 6, 80, 2, "dimensions"], [81, 16, 80, 12], [81, 19, 80, 12, "_ref3"], [81, 24, 80, 12], [81, 25, 80, 2, "dimensions"], [81, 35, 80, 12], [82, 6, 81, 2, "insets"], [82, 12, 81, 8], [82, 15, 81, 8, "_ref3"], [82, 20, 81, 8], [82, 21, 81, 2, "insets"], [82, 27, 81, 8], [83, 6, 82, 2, "style"], [83, 11, 82, 7], [83, 14, 82, 7, "_ref3"], [83, 19, 82, 7], [83, 20, 82, 2, "style"], [83, 25, 82, 7], [84, 4, 84, 2], [84, 8, 85, 4, "tabBarPosition"], [84, 22, 85, 18], [84, 25, 86, 6, "descriptors"], [84, 36, 86, 17], [84, 37, 86, 18, "state"], [84, 42, 86, 23], [84, 43, 86, 24, "routes"], [84, 49, 86, 30], [84, 50, 86, 31, "state"], [84, 55, 86, 36], [84, 56, 86, 37, "index"], [84, 61, 86, 42], [84, 62, 86, 43], [84, 63, 86, 44, "key"], [84, 66, 86, 47], [84, 67, 86, 48], [84, 68, 86, 49, "options"], [84, 75, 86, 56], [84, 76, 85, 4, "tabBarPosition"], [84, 90, 85, 18], [85, 4, 87, 2], [85, 8, 87, 8, "flattenedStyle"], [85, 22, 87, 22], [85, 25, 87, 25, "StyleSheet"], [85, 48, 87, 35], [85, 49, 87, 36, "flatten"], [85, 56, 87, 43], [85, 57, 87, 44, "style"], [85, 62, 87, 49], [85, 63, 87, 50], [86, 4, 88, 2], [86, 8, 88, 8, "customHeight"], [86, 20, 88, 20], [86, 23, 88, 23, "flattenedStyle"], [86, 37, 88, 37], [86, 41, 88, 41], [86, 49, 88, 49], [86, 53, 88, 53, "flattenedStyle"], [86, 67, 88, 67], [86, 70, 88, 70, "flattenedStyle"], [86, 84, 88, 84], [86, 85, 88, 85, "height"], [86, 91, 88, 91], [86, 94, 88, 94, "undefined"], [86, 103, 88, 103], [87, 4, 89, 2], [87, 8, 89, 6], [87, 15, 89, 13, "customHeight"], [87, 27, 89, 25], [87, 32, 89, 30], [87, 40, 89, 38], [87, 42, 89, 40], [88, 6, 90, 4], [88, 13, 90, 11, "customHeight"], [88, 25, 90, 23], [89, 4, 91, 2], [90, 4, 92, 2], [90, 8, 92, 8, "inset"], [90, 13, 92, 13], [90, 16, 92, 16, "insets"], [90, 22, 92, 22], [90, 23, 92, 23, "tabBarPosition"], [90, 37, 92, 37], [90, 42, 92, 42], [90, 47, 92, 47], [90, 50, 92, 50], [90, 55, 92, 55], [90, 58, 92, 58], [90, 66, 92, 66], [90, 67, 92, 67], [91, 4, 93, 2], [91, 8, 93, 6, "isCompact"], [91, 17, 93, 15], [91, 18, 93, 16], [92, 6, 94, 4, "state"], [92, 11, 94, 9], [93, 6, 95, 4, "descriptors"], [93, 17, 95, 15], [94, 6, 96, 4, "dimensions"], [95, 4, 97, 2], [95, 5, 97, 3], [95, 6, 97, 4], [95, 8, 97, 6], [96, 6, 98, 4], [96, 13, 98, 11, "TABBAR_HEIGHT_UIKIT_COMPACT"], [96, 40, 98, 38], [96, 43, 98, 41, "inset"], [96, 48, 98, 46], [97, 4, 99, 2], [98, 4, 100, 2], [98, 11, 100, 9, "TABBAR_HEIGHT_UIKIT"], [98, 30, 100, 28], [98, 33, 100, 31, "inset"], [98, 38, 100, 36], [99, 2, 101, 0], [99, 3, 101, 1], [100, 2, 101, 2, "exports"], [100, 9, 101, 2], [100, 10, 101, 2, "getTabBarHeight"], [100, 25, 101, 2], [100, 28, 101, 2, "getTabBarHeight"], [100, 43, 101, 2], [101, 2, 102, 7], [101, 11, 102, 16, "BottomTabBar"], [101, 23, 102, 28, "BottomTabBar"], [101, 24, 102, 28, "_ref4"], [101, 29, 102, 28], [101, 31, 108, 3], [102, 4, 108, 3], [102, 8, 103, 2, "state"], [102, 13, 103, 7], [102, 16, 103, 7, "_ref4"], [102, 21, 103, 7], [102, 22, 103, 2, "state"], [102, 27, 103, 7], [103, 6, 104, 2, "navigation"], [103, 16, 104, 12], [103, 19, 104, 12, "_ref4"], [103, 24, 104, 12], [103, 25, 104, 2, "navigation"], [103, 35, 104, 12], [104, 6, 105, 2, "descriptors"], [104, 17, 105, 13], [104, 20, 105, 13, "_ref4"], [104, 25, 105, 13], [104, 26, 105, 2, "descriptors"], [104, 37, 105, 13], [105, 6, 106, 2, "insets"], [105, 12, 106, 8], [105, 15, 106, 8, "_ref4"], [105, 20, 106, 8], [105, 21, 106, 2, "insets"], [105, 27, 106, 8], [106, 6, 107, 2, "style"], [106, 11, 107, 7], [106, 14, 107, 7, "_ref4"], [106, 19, 107, 7], [106, 20, 107, 2, "style"], [106, 25, 107, 7], [107, 4, 109, 2], [107, 8, 109, 2, "_useTheme"], [107, 17, 109, 2], [107, 20, 111, 6], [107, 24, 111, 6, "useTheme"], [107, 40, 111, 14], [107, 42, 111, 15], [107, 43, 111, 16], [108, 6, 110, 4, "colors"], [108, 12, 110, 10], [108, 15, 110, 10, "_useTheme"], [108, 24, 110, 10], [108, 25, 110, 4, "colors"], [108, 31, 110, 10], [109, 4, 112, 2], [109, 8, 112, 2, "_useLocale"], [109, 18, 112, 2], [109, 21, 114, 6], [109, 25, 114, 6, "useLocale"], [109, 42, 114, 15], [109, 44, 114, 16], [109, 45, 114, 17], [110, 6, 113, 4, "direction"], [110, 15, 113, 13], [110, 18, 113, 13, "_useLocale"], [110, 28, 113, 13], [110, 29, 113, 4, "direction"], [110, 38, 113, 13], [111, 4, 115, 2], [111, 8, 115, 2, "_useLinkBuilder"], [111, 23, 115, 2], [111, 26, 117, 6], [111, 30, 117, 6, "useLinkBuilder"], [111, 52, 117, 20], [111, 54, 117, 21], [111, 55, 117, 22], [112, 6, 116, 4, "buildHref"], [112, 15, 116, 13], [112, 18, 116, 13, "_useLinkBuilder"], [112, 33, 116, 13], [112, 34, 116, 4, "buildHref"], [112, 43, 116, 13], [113, 4, 118, 2], [113, 8, 118, 8, "focusedRoute"], [113, 20, 118, 20], [113, 23, 118, 23, "state"], [113, 28, 118, 28], [113, 29, 118, 29, "routes"], [113, 35, 118, 35], [113, 36, 118, 36, "state"], [113, 41, 118, 41], [113, 42, 118, 42, "index"], [113, 47, 118, 47], [113, 48, 118, 48], [114, 4, 119, 2], [114, 8, 119, 8, "focusedDescriptor"], [114, 25, 119, 25], [114, 28, 119, 28, "descriptors"], [114, 39, 119, 39], [114, 40, 119, 40, "focusedRoute"], [114, 52, 119, 52], [114, 53, 119, 53, "key"], [114, 56, 119, 56], [114, 57, 119, 57], [115, 4, 120, 2], [115, 8, 120, 8, "focusedOptions"], [115, 22, 120, 22], [115, 25, 120, 25, "focusedDescriptor"], [115, 42, 120, 42], [115, 43, 120, 43, "options"], [115, 50, 120, 50], [116, 4, 121, 2], [116, 8, 121, 2, "_focusedOptions$tabBa"], [116, 29, 121, 2], [116, 32, 134, 6, "focusedOptions"], [116, 46, 134, 20], [116, 47, 122, 4, "tabBarPosition"], [116, 61, 122, 18], [117, 6, 122, 4, "tabBarPosition"], [117, 20, 122, 18], [117, 23, 122, 18, "_focusedOptions$tabBa"], [117, 44, 122, 18], [117, 58, 122, 21], [117, 66, 122, 29], [117, 69, 122, 29, "_focusedOptions$tabBa"], [117, 90, 122, 29], [118, 6, 123, 4, "tabBarShowLabel"], [118, 21, 123, 19], [118, 24, 134, 6, "focusedOptions"], [118, 38, 134, 20], [118, 39, 123, 4, "tabBarShowLabel"], [118, 54, 123, 19], [119, 6, 124, 4, "tabBarLabelPosition"], [119, 25, 124, 23], [119, 28, 134, 6, "focusedOptions"], [119, 42, 134, 20], [119, 43, 124, 4, "tabBarLabelPosition"], [119, 62, 124, 23], [120, 6, 124, 23, "_focusedOptions$tabBa2"], [120, 28, 124, 23], [120, 31, 134, 6, "focusedOptions"], [120, 45, 134, 20], [120, 46, 125, 4, "tabBarHideOnKeyboard"], [120, 66, 125, 24], [121, 6, 125, 4, "tabBarHideOnKeyboard"], [121, 26, 125, 24], [121, 29, 125, 24, "_focusedOptions$tabBa2"], [121, 51, 125, 24], [121, 65, 125, 27], [121, 70, 125, 32], [121, 73, 125, 32, "_focusedOptions$tabBa2"], [121, 95, 125, 32], [122, 6, 126, 4, "tabBarVisibilityAnimationConfig"], [122, 37, 126, 35], [122, 40, 134, 6, "focusedOptions"], [122, 54, 134, 20], [122, 55, 126, 4, "tabBarVisibilityAnimationConfig"], [122, 86, 126, 35], [123, 6, 126, 35, "_focusedOptions$tabBa3"], [123, 28, 126, 35], [123, 31, 134, 6, "focusedOptions"], [123, 45, 134, 20], [123, 46, 127, 4, "tabBarVariant"], [123, 59, 127, 17], [124, 6, 127, 4, "tabBarVariant"], [124, 19, 127, 17], [124, 22, 127, 17, "_focusedOptions$tabBa3"], [124, 44, 127, 17], [124, 58, 127, 20], [124, 65, 127, 27], [124, 68, 127, 27, "_focusedOptions$tabBa3"], [124, 90, 127, 27], [125, 6, 128, 4, "tabBarStyle"], [125, 17, 128, 15], [125, 20, 134, 6, "focusedOptions"], [125, 34, 134, 20], [125, 35, 128, 4, "tabBarStyle"], [125, 46, 128, 15], [126, 6, 129, 4, "tabBarBackground"], [126, 22, 129, 20], [126, 25, 134, 6, "focusedOptions"], [126, 39, 134, 20], [126, 40, 129, 4, "tabBarBackground"], [126, 56, 129, 20], [127, 6, 130, 4, "tabBarActiveTintColor"], [127, 27, 130, 25], [127, 30, 134, 6, "focusedOptions"], [127, 44, 134, 20], [127, 45, 130, 4, "tabBarActiveTintColor"], [127, 66, 130, 25], [128, 6, 131, 4, "tabBarInactiveTintColor"], [128, 29, 131, 27], [128, 32, 134, 6, "focusedOptions"], [128, 46, 134, 20], [128, 47, 131, 4, "tabBarInactiveTintColor"], [128, 70, 131, 27], [129, 6, 132, 4, "tabBarActiveBackgroundColor"], [129, 33, 132, 31], [129, 36, 134, 6, "focusedOptions"], [129, 50, 134, 20], [129, 51, 132, 4, "tabBarActiveBackgroundColor"], [129, 78, 132, 31], [130, 6, 133, 4, "tabBarInactiveBackgroundColor"], [130, 35, 133, 33], [130, 38, 134, 6, "focusedOptions"], [130, 52, 134, 20], [130, 53, 133, 4, "tabBarInactiveBackgroundColor"], [130, 82, 133, 33], [131, 4, 135, 2], [131, 8, 135, 6, "tabBarVariant"], [131, 21, 135, 19], [131, 26, 135, 24], [131, 36, 135, 34], [131, 40, 135, 38, "tabBarPosition"], [131, 54, 135, 52], [131, 59, 135, 57], [131, 65, 135, 63], [131, 69, 135, 67, "tabBarPosition"], [131, 83, 135, 81], [131, 88, 135, 86], [131, 95, 135, 93], [131, 97, 135, 95], [132, 6, 136, 4], [132, 12, 136, 10], [132, 16, 136, 14, "Error"], [132, 21, 136, 19], [132, 22, 136, 20], [132, 127, 136, 125], [132, 128, 136, 126], [133, 4, 137, 2], [134, 4, 138, 2], [134, 8, 138, 6, "tabBarLabelPosition"], [134, 27, 138, 25], [134, 32, 138, 30], [134, 44, 138, 42], [134, 48, 138, 46, "tabBarVariant"], [134, 61, 138, 59], [134, 66, 138, 64], [134, 73, 138, 71], [134, 78, 138, 76, "tabBarPosition"], [134, 92, 138, 90], [134, 97, 138, 95], [134, 103, 138, 101], [134, 107, 138, 105, "tabBarPosition"], [134, 121, 138, 119], [134, 126, 138, 124], [134, 133, 138, 131], [134, 134, 138, 132], [134, 136, 138, 134], [135, 6, 139, 4], [135, 12, 139, 10], [135, 16, 139, 14, "Error"], [135, 21, 139, 19], [135, 22, 139, 20], [135, 167, 139, 165], [135, 168, 139, 166], [136, 4, 140, 2], [137, 4, 141, 2], [137, 8, 141, 8, "isKeyboardShown"], [137, 23, 141, 23], [137, 26, 141, 26], [137, 30, 141, 26, "useIsKeyboardShown"], [137, 68, 141, 44], [137, 70, 141, 45], [137, 71, 141, 46], [138, 4, 142, 2], [138, 8, 142, 8, "onHeightChange"], [138, 22, 142, 22], [138, 25, 142, 25, "React"], [138, 39, 142, 30], [138, 40, 142, 31, "useContext"], [138, 50, 142, 41], [138, 51, 142, 42, "BottomTabBarHeightCallbackContext"], [138, 119, 142, 75], [138, 120, 142, 76], [139, 4, 143, 2], [139, 8, 143, 8, "shouldShowTabBar"], [139, 24, 143, 24], [139, 27, 143, 27], [139, 29, 143, 29, "tabBarHideOnKeyboard"], [139, 49, 143, 49], [139, 53, 143, 53, "isKeyboardShown"], [139, 68, 143, 68], [139, 69, 143, 69], [140, 4, 144, 2], [140, 8, 144, 8, "visibilityAnimationConfigRef"], [140, 36, 144, 36], [140, 39, 144, 39, "React"], [140, 53, 144, 44], [140, 54, 144, 45, "useRef"], [140, 60, 144, 51], [140, 61, 144, 52, "tabBarVisibilityAnimationConfig"], [140, 92, 144, 83], [140, 93, 144, 84], [141, 4, 145, 2, "React"], [141, 18, 145, 7], [141, 19, 145, 8, "useEffect"], [141, 28, 145, 17], [141, 29, 145, 18], [141, 35, 145, 24], [142, 6, 146, 4, "visibilityAnimationConfigRef"], [142, 34, 146, 32], [142, 35, 146, 33, "current"], [142, 42, 146, 40], [142, 45, 146, 43, "tabBarVisibilityAnimationConfig"], [142, 76, 146, 74], [143, 4, 147, 2], [143, 5, 147, 3], [143, 6, 147, 4], [144, 4, 148, 2], [144, 8, 148, 2, "_React$useState"], [144, 23, 148, 2], [144, 26, 148, 46, "React"], [144, 40, 148, 51], [144, 41, 148, 52, "useState"], [144, 49, 148, 60], [144, 50, 148, 61], [144, 51, 148, 62, "shouldShowTabBar"], [144, 67, 148, 78], [144, 68, 148, 79], [145, 6, 148, 79, "_React$useState2"], [145, 22, 148, 79], [145, 29, 148, 79, "_slicedToArray2"], [145, 44, 148, 79], [145, 45, 148, 79, "default"], [145, 52, 148, 79], [145, 54, 148, 79, "_React$useState"], [145, 69, 148, 79], [146, 6, 148, 9, "isTabBarHidden"], [146, 20, 148, 23], [146, 23, 148, 23, "_React$useState2"], [146, 39, 148, 23], [147, 6, 148, 25, "setIsTabBarHidden"], [147, 23, 148, 42], [147, 26, 148, 42, "_React$useState2"], [147, 42, 148, 42], [148, 4, 149, 2], [148, 8, 149, 2, "_React$useState3"], [148, 24, 149, 2], [148, 27, 149, 20, "React"], [148, 41, 149, 25], [148, 42, 149, 26, "useState"], [148, 50, 149, 34], [148, 51, 149, 35], [148, 57, 149, 41], [148, 61, 149, 45, "Animated"], [148, 82, 149, 53], [148, 83, 149, 54, "Value"], [148, 88, 149, 59], [148, 89, 149, 60, "shouldShowTabBar"], [148, 105, 149, 76], [148, 108, 149, 79], [148, 109, 149, 80], [148, 112, 149, 83], [148, 113, 149, 84], [148, 114, 149, 85], [148, 115, 149, 86], [149, 6, 149, 86, "_React$useState4"], [149, 22, 149, 86], [149, 29, 149, 86, "_slicedToArray2"], [149, 44, 149, 86], [149, 45, 149, 86, "default"], [149, 52, 149, 86], [149, 54, 149, 86, "_React$useState3"], [149, 70, 149, 86], [150, 6, 149, 9, "visible"], [150, 13, 149, 16], [150, 16, 149, 16, "_React$useState4"], [150, 32, 149, 16], [151, 4, 150, 2, "React"], [151, 18, 150, 7], [151, 19, 150, 8, "useEffect"], [151, 28, 150, 17], [151, 29, 150, 18], [151, 35, 150, 24], [152, 6, 151, 4], [152, 10, 151, 10, "visibilityAnimationConfig"], [152, 35, 151, 35], [152, 38, 151, 38, "visibilityAnimationConfigRef"], [152, 66, 151, 66], [152, 67, 151, 67, "current"], [152, 74, 151, 74], [153, 6, 152, 4], [153, 10, 152, 8, "shouldShowTabBar"], [153, 26, 152, 24], [153, 28, 152, 26], [154, 8, 153, 6], [154, 12, 153, 12, "animation"], [154, 21, 153, 21], [154, 24, 153, 24, "visibilityAnimationConfig"], [154, 49, 153, 49], [154, 51, 153, 51, "show"], [154, 55, 153, 55], [154, 57, 153, 57, "animation"], [154, 66, 153, 66], [154, 71, 153, 71], [154, 79, 153, 79], [154, 82, 153, 82, "Animated"], [154, 103, 153, 90], [154, 104, 153, 91, "spring"], [154, 110, 153, 97], [154, 113, 153, 100, "Animated"], [154, 134, 153, 108], [154, 135, 153, 109, "timing"], [154, 141, 153, 115], [155, 8, 154, 6, "animation"], [155, 17, 154, 15], [155, 18, 154, 16, "visible"], [155, 25, 154, 23], [155, 27, 154, 25], [156, 10, 155, 8, "toValue"], [156, 17, 155, 15], [156, 19, 155, 17], [156, 20, 155, 18], [157, 10, 156, 8, "useNativeDriver"], [157, 25, 156, 23], [158, 10, 157, 8, "duration"], [158, 18, 157, 16], [158, 20, 157, 18], [158, 23, 157, 21], [159, 10, 158, 8], [159, 13, 158, 11, "visibilityAnimationConfig"], [159, 38, 158, 36], [159, 40, 158, 38, "show"], [159, 44, 158, 42], [159, 46, 158, 44, "config"], [160, 8, 159, 6], [160, 9, 159, 7], [160, 10, 159, 8], [160, 11, 159, 9, "start"], [160, 16, 159, 14], [160, 17, 159, 15, "_ref5"], [160, 22, 159, 15], [160, 26, 161, 12], [161, 10, 161, 12], [161, 14, 160, 8, "finished"], [161, 22, 160, 16], [161, 25, 160, 16, "_ref5"], [161, 30, 160, 16], [161, 31, 160, 8, "finished"], [161, 39, 160, 16], [162, 10, 162, 8], [162, 14, 162, 12, "finished"], [162, 22, 162, 20], [162, 24, 162, 22], [163, 12, 163, 10, "setIsTabBarHidden"], [163, 29, 163, 27], [163, 30, 163, 28], [163, 35, 163, 33], [163, 36, 163, 34], [164, 10, 164, 8], [165, 8, 165, 6], [165, 9, 165, 7], [165, 10, 165, 8], [166, 6, 166, 4], [166, 7, 166, 5], [166, 13, 166, 11], [167, 8, 167, 6], [168, 8, 168, 6, "setIsTabBarHidden"], [168, 25, 168, 23], [168, 26, 168, 24], [168, 30, 168, 28], [168, 31, 168, 29], [169, 8, 169, 6], [169, 12, 169, 12, "animation"], [169, 22, 169, 21], [169, 25, 169, 24, "visibilityAnimationConfig"], [169, 50, 169, 49], [169, 52, 169, 51, "hide"], [169, 56, 169, 55], [169, 58, 169, 57, "animation"], [169, 67, 169, 66], [169, 72, 169, 71], [169, 80, 169, 79], [169, 83, 169, 82, "Animated"], [169, 104, 169, 90], [169, 105, 169, 91, "spring"], [169, 111, 169, 97], [169, 114, 169, 100, "Animated"], [169, 135, 169, 108], [169, 136, 169, 109, "timing"], [169, 142, 169, 115], [170, 8, 170, 6, "animation"], [170, 18, 170, 15], [170, 19, 170, 16, "visible"], [170, 26, 170, 23], [170, 28, 170, 25], [171, 10, 171, 8, "toValue"], [171, 17, 171, 15], [171, 19, 171, 17], [171, 20, 171, 18], [172, 10, 172, 8, "useNativeDriver"], [172, 25, 172, 23], [173, 10, 173, 8, "duration"], [173, 18, 173, 16], [173, 20, 173, 18], [173, 23, 173, 21], [174, 10, 174, 8], [174, 13, 174, 11, "visibilityAnimationConfig"], [174, 38, 174, 36], [174, 40, 174, 38, "hide"], [174, 44, 174, 42], [174, 46, 174, 44, "config"], [175, 8, 175, 6], [175, 9, 175, 7], [175, 10, 175, 8], [175, 11, 175, 9, "start"], [175, 16, 175, 14], [175, 17, 175, 15], [175, 18, 175, 16], [176, 6, 176, 4], [177, 6, 177, 4], [177, 13, 177, 11], [177, 19, 177, 17, "visible"], [177, 26, 177, 24], [177, 27, 177, 25, "stopAnimation"], [177, 40, 177, 38], [177, 41, 177, 39], [177, 42, 177, 40], [178, 4, 178, 2], [178, 5, 178, 3], [178, 7, 178, 5], [178, 8, 178, 6, "visible"], [178, 15, 178, 13], [178, 17, 178, 15, "shouldShowTabBar"], [178, 33, 178, 31], [178, 34, 178, 32], [178, 35, 178, 33], [179, 4, 179, 2], [179, 8, 179, 2, "_React$useState5"], [179, 24, 179, 2], [179, 27, 179, 30, "React"], [179, 41, 179, 35], [179, 42, 179, 36, "useState"], [179, 50, 179, 44], [179, 51, 179, 45], [180, 8, 180, 4, "height"], [180, 14, 180, 10], [180, 16, 180, 12], [181, 6, 181, 2], [181, 7, 181, 3], [181, 8, 181, 4], [182, 6, 181, 4, "_React$useState6"], [182, 22, 181, 4], [182, 29, 181, 4, "_slicedToArray2"], [182, 44, 181, 4], [182, 45, 181, 4, "default"], [182, 52, 181, 4], [182, 54, 181, 4, "_React$useState5"], [182, 70, 181, 4], [183, 6, 179, 9, "layout"], [183, 12, 179, 15], [183, 15, 179, 15, "_React$useState6"], [183, 31, 179, 15], [184, 6, 179, 17, "setLayout"], [184, 15, 179, 26], [184, 18, 179, 26, "_React$useState6"], [184, 34, 179, 26], [185, 4, 182, 2], [185, 8, 182, 8, "handleLayout"], [185, 20, 182, 20], [185, 23, 182, 23, "e"], [185, 24, 182, 24], [185, 28, 182, 28], [186, 6, 183, 4], [186, 10, 184, 6, "height"], [186, 16, 184, 12], [186, 19, 185, 8, "e"], [186, 20, 185, 9], [186, 21, 185, 10, "nativeEvent"], [186, 32, 185, 21], [186, 33, 185, 22, "layout"], [186, 39, 185, 28], [186, 40, 184, 6, "height"], [186, 46, 184, 12], [187, 6, 186, 4, "onHeightChange"], [187, 20, 186, 18], [187, 23, 186, 21, "height"], [187, 29, 186, 27], [187, 30, 186, 28], [188, 6, 187, 4, "setLayout"], [188, 15, 187, 13], [188, 16, 187, 14, "layout"], [188, 22, 187, 20], [188, 26, 187, 24], [189, 8, 188, 6], [189, 12, 188, 10, "height"], [189, 18, 188, 16], [189, 23, 188, 21, "layout"], [189, 29, 188, 27], [189, 30, 188, 28, "height"], [189, 36, 188, 34], [189, 38, 188, 36], [190, 10, 189, 8], [190, 17, 189, 15, "layout"], [190, 23, 189, 21], [191, 8, 190, 6], [191, 9, 190, 7], [191, 15, 190, 13], [192, 10, 191, 8], [192, 17, 191, 15], [193, 12, 192, 10, "height"], [194, 10, 193, 8], [194, 11, 193, 9], [195, 8, 194, 6], [196, 6, 195, 4], [196, 7, 195, 5], [196, 8, 195, 6], [197, 4, 196, 2], [197, 5, 196, 3], [198, 4, 197, 2], [198, 8, 198, 4, "routes"], [198, 14, 198, 10], [198, 17, 199, 6, "state"], [198, 22, 199, 11], [198, 23, 198, 4, "routes"], [198, 29, 198, 10], [199, 4, 200, 2], [199, 8, 200, 8, "tabBarHeight"], [199, 20, 200, 20], [199, 23, 200, 23], [199, 27, 200, 23, "useFrameSize"], [199, 49, 200, 35], [199, 51, 200, 36, "dimensions"], [199, 61, 200, 46], [199, 65, 200, 50, "getTabBarHeight"], [199, 80, 200, 65], [199, 81, 200, 66], [200, 6, 201, 4, "state"], [200, 11, 201, 9], [201, 6, 202, 4, "descriptors"], [201, 17, 202, 15], [202, 6, 203, 4, "insets"], [202, 12, 203, 10], [203, 6, 204, 4, "dimensions"], [203, 16, 204, 14], [204, 6, 205, 4, "style"], [204, 11, 205, 9], [204, 13, 205, 11], [204, 14, 205, 12, "tabBarStyle"], [204, 25, 205, 23], [204, 27, 205, 25, "style"], [204, 32, 205, 30], [205, 4, 206, 2], [205, 5, 206, 3], [205, 6, 206, 4], [205, 7, 206, 5], [206, 4, 207, 2], [206, 8, 207, 8, "hasHorizontalLabels"], [206, 27, 207, 27], [206, 30, 207, 30], [206, 34, 207, 30, "useFrameSize"], [206, 56, 207, 42], [206, 58, 207, 43, "dimensions"], [206, 68, 207, 53], [206, 72, 207, 57, "shouldUseHorizontalLabels"], [206, 97, 207, 82], [206, 98, 207, 83], [207, 6, 208, 4, "state"], [207, 11, 208, 9], [208, 6, 209, 4, "descriptors"], [208, 17, 209, 15], [209, 6, 210, 4, "dimensions"], [210, 4, 211, 2], [210, 5, 211, 3], [210, 6, 211, 4], [210, 7, 211, 5], [211, 4, 212, 2], [211, 8, 212, 8, "compact"], [211, 15, 212, 15], [211, 18, 212, 18], [211, 22, 212, 18, "useFrameSize"], [211, 44, 212, 30], [211, 46, 212, 31, "dimensions"], [211, 56, 212, 41], [211, 60, 212, 45, "isCompact"], [211, 69, 212, 54], [211, 70, 212, 55], [212, 6, 213, 4, "state"], [212, 11, 213, 9], [213, 6, 214, 4, "descriptors"], [213, 17, 214, 15], [214, 6, 215, 4, "dimensions"], [215, 4, 216, 2], [215, 5, 216, 3], [215, 6, 216, 4], [215, 7, 216, 5], [216, 4, 217, 2], [216, 8, 217, 8, "sidebar"], [216, 15, 217, 15], [216, 18, 217, 18, "tabBarPosition"], [216, 32, 217, 32], [216, 37, 217, 37], [216, 43, 217, 43], [216, 47, 217, 47, "tabBarPosition"], [216, 61, 217, 61], [216, 66, 217, 66], [216, 73, 217, 73], [217, 4, 218, 2], [217, 8, 218, 8, "spacing"], [217, 15, 218, 15], [217, 18, 218, 18, "tabBarVariant"], [217, 31, 218, 31], [217, 36, 218, 36], [217, 46, 218, 46], [217, 49, 218, 49, "SPACING_MATERIAL"], [217, 65, 218, 65], [217, 68, 218, 68, "SPACING_UIKIT"], [217, 81, 218, 81], [218, 4, 219, 2], [218, 8, 219, 8, "minSidebarWidth"], [218, 23, 219, 23], [218, 26, 219, 26], [218, 30, 219, 26, "useFrameSize"], [218, 52, 219, 38], [218, 54, 219, 39, "size"], [218, 58, 219, 43], [218, 62, 219, 47, "sidebar"], [218, 69, 219, 54], [218, 73, 219, 58, "hasHorizontalLabels"], [218, 92, 219, 77], [218, 95, 219, 80], [218, 99, 219, 80, "getDefaultSidebarWidth"], [218, 131, 219, 102], [218, 133, 219, 103, "size"], [218, 137, 219, 107], [218, 138, 219, 108], [218, 141, 219, 111], [218, 142, 219, 112], [218, 143, 219, 113], [219, 4, 220, 2], [219, 8, 220, 8, "tabBarBackgroundElement"], [219, 31, 220, 31], [219, 34, 220, 34, "tabBarBackground"], [219, 50, 220, 50], [219, 53, 220, 53], [219, 54, 220, 54], [220, 4, 221, 2], [220, 11, 221, 9], [220, 24, 221, 22], [220, 28, 221, 22, "_jsxs"], [220, 44, 221, 27], [220, 46, 221, 28, "Animated"], [220, 67, 221, 36], [220, 68, 221, 37, "View"], [220, 72, 221, 41], [220, 74, 221, 43], [221, 6, 222, 4, "style"], [221, 11, 222, 9], [221, 13, 222, 11], [221, 14, 222, 12, "tabBarPosition"], [221, 28, 222, 26], [221, 33, 222, 31], [221, 39, 222, 37], [221, 42, 222, 40, "styles"], [221, 48, 222, 46], [221, 49, 222, 47, "start"], [221, 54, 222, 52], [221, 57, 222, 55, "tabBarPosition"], [221, 71, 222, 69], [221, 76, 222, 74], [221, 83, 222, 81], [221, 86, 222, 84, "styles"], [221, 92, 222, 90], [221, 93, 222, 91, "end"], [221, 96, 222, 94], [221, 99, 222, 97, "styles"], [221, 105, 222, 103], [221, 106, 222, 104, "bottom"], [221, 112, 222, 110], [221, 114, 222, 112], [221, 115, 222, 113, "Platform"], [221, 136, 222, 121], [221, 137, 222, 122, "OS"], [221, 139, 222, 124], [221, 144, 222, 129], [221, 149, 222, 134], [221, 152, 222, 137, "tabBarPosition"], [221, 166, 222, 151], [221, 171, 222, 156], [221, 178, 222, 163], [221, 181, 222, 166, "direction"], [221, 190, 222, 175], [221, 195, 222, 180], [221, 200, 222, 185], [221, 204, 222, 189, "tabBarPosition"], [221, 218, 222, 203], [221, 223, 222, 208], [221, 229, 222, 214], [221, 233, 222, 218, "direction"], [221, 242, 222, 227], [221, 247, 222, 232], [221, 252, 222, 237], [221, 256, 222, 241, "tabBarPosition"], [221, 270, 222, 255], [221, 275, 222, 260], [221, 282, 222, 267], [221, 286, 222, 271], [222, 8, 223, 6, "borderLeftWidth"], [222, 23, 223, 21], [222, 25, 223, 23, "StyleSheet"], [222, 48, 223, 33], [222, 49, 223, 34, "hairlineWidth"], [223, 6, 224, 4], [223, 7, 224, 5], [223, 10, 224, 8], [223, 11, 224, 9, "Platform"], [223, 32, 224, 17], [223, 33, 224, 18, "OS"], [223, 35, 224, 20], [223, 40, 224, 25], [223, 45, 224, 30], [223, 48, 224, 33, "tabBarPosition"], [223, 62, 224, 47], [223, 67, 224, 52], [223, 73, 224, 58], [223, 76, 224, 61, "direction"], [223, 85, 224, 70], [223, 90, 224, 75], [223, 95, 224, 80], [223, 99, 224, 84, "tabBarPosition"], [223, 113, 224, 98], [223, 118, 224, 103], [223, 125, 224, 110], [223, 129, 224, 114, "direction"], [223, 138, 224, 123], [223, 143, 224, 128], [223, 148, 224, 133], [223, 152, 224, 137, "tabBarPosition"], [223, 166, 224, 151], [223, 171, 224, 156], [223, 177, 224, 162], [223, 181, 224, 166], [224, 8, 225, 6, "borderRightWidth"], [224, 24, 225, 22], [224, 26, 225, 24, "StyleSheet"], [224, 49, 225, 34], [224, 50, 225, 35, "hairlineWidth"], [225, 6, 226, 4], [225, 7, 226, 5], [225, 10, 226, 8, "tabBarPosition"], [225, 24, 226, 22], [225, 29, 226, 27], [225, 34, 226, 32], [225, 37, 226, 35], [226, 8, 227, 6, "borderBottomWidth"], [226, 25, 227, 23], [226, 27, 227, 25, "StyleSheet"], [226, 50, 227, 35], [226, 51, 227, 36, "hairlineWidth"], [227, 6, 228, 4], [227, 7, 228, 5], [227, 10, 228, 8], [228, 8, 229, 6, "borderTopWidth"], [228, 22, 229, 20], [228, 24, 229, 22, "StyleSheet"], [228, 47, 229, 32], [228, 48, 229, 33, "hairlineWidth"], [229, 6, 230, 4], [229, 7, 230, 5], [229, 9, 230, 7], [230, 8, 231, 6, "backgroundColor"], [230, 23, 231, 21], [230, 25, 231, 23, "tabBarBackgroundElement"], [230, 48, 231, 46], [230, 52, 231, 50], [230, 56, 231, 54], [230, 59, 231, 57], [230, 72, 231, 70], [230, 75, 231, 73, "colors"], [230, 81, 231, 79], [230, 82, 231, 80, "card"], [230, 86, 231, 84], [231, 8, 232, 6, "borderColor"], [231, 19, 232, 17], [231, 21, 232, 19, "colors"], [231, 27, 232, 25], [231, 28, 232, 26, "border"], [232, 6, 233, 4], [232, 7, 233, 5], [232, 9, 233, 7, "sidebar"], [232, 16, 233, 14], [232, 19, 233, 17], [233, 8, 234, 6, "paddingTop"], [233, 18, 234, 16], [233, 20, 234, 18], [233, 21, 234, 19, "hasHorizontalLabels"], [233, 40, 234, 38], [233, 43, 234, 41, "spacing"], [233, 50, 234, 48], [233, 53, 234, 51, "spacing"], [233, 60, 234, 58], [233, 63, 234, 61], [233, 64, 234, 62], [233, 68, 234, 66, "insets"], [233, 74, 234, 72], [233, 75, 234, 73, "top"], [233, 78, 234, 76], [234, 8, 235, 6, "paddingBottom"], [234, 21, 235, 19], [234, 23, 235, 21], [234, 24, 235, 22, "hasHorizontalLabels"], [234, 43, 235, 41], [234, 46, 235, 44, "spacing"], [234, 53, 235, 51], [234, 56, 235, 54, "spacing"], [234, 63, 235, 61], [234, 66, 235, 64], [234, 67, 235, 65], [234, 71, 235, 69, "insets"], [234, 77, 235, 75], [234, 78, 235, 76, "bottom"], [234, 84, 235, 82], [235, 8, 236, 6, "paddingStart"], [235, 20, 236, 18], [235, 22, 236, 20, "spacing"], [235, 29, 236, 27], [235, 33, 236, 31, "tabBarPosition"], [235, 47, 236, 45], [235, 52, 236, 50], [235, 58, 236, 56], [235, 61, 236, 59, "insets"], [235, 67, 236, 65], [235, 68, 236, 66, "left"], [235, 72, 236, 70], [235, 75, 236, 73], [235, 76, 236, 74], [235, 77, 236, 75], [236, 8, 237, 6, "paddingEnd"], [236, 18, 237, 16], [236, 20, 237, 18, "spacing"], [236, 27, 237, 25], [236, 31, 237, 29, "tabBarPosition"], [236, 45, 237, 43], [236, 50, 237, 48], [236, 57, 237, 55], [236, 60, 237, 58, "insets"], [236, 66, 237, 64], [236, 67, 237, 65, "right"], [236, 72, 237, 70], [236, 75, 237, 73], [236, 76, 237, 74], [236, 77, 237, 75], [237, 8, 238, 6, "min<PERSON><PERSON><PERSON>"], [237, 16, 238, 14], [237, 18, 238, 16, "minSidebarWidth"], [238, 6, 239, 4], [238, 7, 239, 5], [238, 10, 239, 8], [238, 11, 239, 9], [239, 8, 240, 6, "transform"], [239, 17, 240, 15], [239, 19, 240, 17], [239, 20, 240, 18], [240, 10, 241, 8, "translateY"], [240, 20, 241, 18], [240, 22, 241, 20, "visible"], [240, 29, 241, 27], [240, 30, 241, 28, "interpolate"], [240, 41, 241, 39], [240, 42, 241, 40], [241, 12, 242, 10, "inputRange"], [241, 22, 242, 20], [241, 24, 242, 22], [241, 25, 242, 23], [241, 26, 242, 24], [241, 28, 242, 26], [241, 29, 242, 27], [241, 30, 242, 28], [242, 12, 243, 10, "outputRange"], [242, 23, 243, 21], [242, 25, 243, 23], [242, 26, 243, 24, "layout"], [242, 32, 243, 30], [242, 33, 243, 31, "height"], [242, 39, 243, 37], [242, 42, 243, 40, "insets"], [242, 48, 243, 46], [242, 49, 243, 47, "tabBarPosition"], [242, 63, 243, 61], [242, 68, 243, 66], [242, 73, 243, 71], [242, 76, 243, 74], [242, 81, 243, 79], [242, 84, 243, 82], [242, 92, 243, 90], [242, 93, 243, 91], [242, 96, 243, 94, "StyleSheet"], [242, 119, 243, 104], [242, 120, 243, 105, "hairlineWidth"], [242, 133, 243, 118], [242, 135, 243, 120], [242, 136, 243, 121], [243, 10, 244, 8], [243, 11, 244, 9], [244, 8, 245, 6], [244, 9, 245, 7], [244, 10, 245, 8], [245, 8, 246, 6], [246, 8, 247, 6], [247, 8, 248, 6, "position"], [247, 16, 248, 14], [247, 18, 248, 16, "isTabBarHidden"], [247, 32, 248, 30], [247, 35, 248, 33], [247, 45, 248, 43], [247, 48, 248, 46, "undefined"], [248, 6, 249, 4], [248, 7, 249, 5], [248, 9, 249, 7], [249, 8, 250, 6, "height"], [249, 14, 250, 12], [249, 16, 250, 14, "tabBarHeight"], [249, 28, 250, 26], [250, 8, 251, 6, "paddingBottom"], [250, 21, 251, 19], [250, 23, 251, 21, "tabBarPosition"], [250, 37, 251, 35], [250, 42, 251, 40], [250, 50, 251, 48], [250, 53, 251, 51, "insets"], [250, 59, 251, 57], [250, 60, 251, 58, "bottom"], [250, 66, 251, 64], [250, 69, 251, 67], [250, 70, 251, 68], [251, 8, 252, 6, "paddingTop"], [251, 18, 252, 16], [251, 20, 252, 18, "tabBarPosition"], [251, 34, 252, 32], [251, 39, 252, 37], [251, 44, 252, 42], [251, 47, 252, 45, "insets"], [251, 53, 252, 51], [251, 54, 252, 52, "top"], [251, 57, 252, 55], [251, 60, 252, 58], [251, 61, 252, 59], [252, 8, 253, 6, "paddingHorizontal"], [252, 25, 253, 23], [252, 27, 253, 25, "Math"], [252, 31, 253, 29], [252, 32, 253, 30, "max"], [252, 35, 253, 33], [252, 36, 253, 34, "insets"], [252, 42, 253, 40], [252, 43, 253, 41, "left"], [252, 47, 253, 45], [252, 49, 253, 47, "insets"], [252, 55, 253, 53], [252, 56, 253, 54, "right"], [252, 61, 253, 59], [253, 6, 254, 4], [253, 7, 254, 5], [253, 8, 254, 6], [253, 10, 254, 8, "tabBarStyle"], [253, 21, 254, 19], [253, 22, 254, 20], [254, 6, 255, 4, "pointerEvents"], [254, 19, 255, 17], [254, 21, 255, 19, "isTabBarHidden"], [254, 35, 255, 33], [254, 38, 255, 36], [254, 44, 255, 42], [254, 47, 255, 45], [254, 53, 255, 51], [255, 6, 256, 4, "onLayout"], [255, 14, 256, 12], [255, 16, 256, 14, "sidebar"], [255, 23, 256, 21], [255, 26, 256, 24, "undefined"], [255, 35, 256, 33], [255, 38, 256, 36, "handleLayout"], [255, 50, 256, 48], [256, 6, 257, 4, "children"], [256, 14, 257, 12], [256, 16, 257, 14], [256, 17, 257, 15], [256, 30, 257, 28], [256, 34, 257, 28, "_jsx"], [256, 49, 257, 32], [256, 51, 257, 33, "View"], [256, 68, 257, 37], [256, 70, 257, 39], [257, 8, 258, 6, "pointerEvents"], [257, 21, 258, 19], [257, 23, 258, 21], [257, 29, 258, 27], [258, 8, 259, 6, "style"], [258, 13, 259, 11], [258, 15, 259, 13, "StyleSheet"], [258, 38, 259, 23], [258, 39, 259, 24, "absoluteFill"], [258, 51, 259, 36], [259, 8, 260, 6, "children"], [259, 16, 260, 14], [259, 18, 260, 16, "tabBarBackgroundElement"], [260, 6, 261, 4], [260, 7, 261, 5], [260, 8, 261, 6], [260, 10, 261, 8], [260, 23, 261, 21], [260, 27, 261, 21, "_jsx"], [260, 42, 261, 25], [260, 44, 261, 26, "View"], [260, 61, 261, 30], [260, 63, 261, 32], [261, 8, 262, 6, "role"], [261, 12, 262, 10], [261, 14, 262, 12], [261, 23, 262, 21], [262, 8, 263, 6, "style"], [262, 13, 263, 11], [262, 15, 263, 13, "sidebar"], [262, 22, 263, 20], [262, 25, 263, 23, "styles"], [262, 31, 263, 29], [262, 32, 263, 30, "sideContent"], [262, 43, 263, 41], [262, 46, 263, 44, "styles"], [262, 52, 263, 50], [262, 53, 263, 51, "bottomContent"], [262, 66, 263, 64], [263, 8, 264, 6, "children"], [263, 16, 264, 14], [263, 18, 264, 16, "routes"], [263, 24, 264, 22], [263, 25, 264, 23, "map"], [263, 28, 264, 26], [263, 29, 264, 27], [263, 30, 264, 28, "route"], [263, 35, 264, 33], [263, 37, 264, 35, "index"], [263, 42, 264, 40], [263, 47, 264, 45], [264, 10, 265, 8], [264, 14, 265, 14, "focused"], [264, 21, 265, 21], [264, 24, 265, 24, "index"], [264, 29, 265, 29], [264, 34, 265, 34, "state"], [264, 39, 265, 39], [264, 40, 265, 40, "index"], [264, 45, 265, 45], [265, 10, 266, 8], [265, 14, 267, 10, "options"], [265, 21, 267, 17], [265, 24, 268, 12, "descriptors"], [265, 35, 268, 23], [265, 36, 268, 24, "route"], [265, 41, 268, 29], [265, 42, 268, 30, "key"], [265, 45, 268, 33], [265, 46, 268, 34], [265, 47, 267, 10, "options"], [265, 54, 267, 17], [266, 10, 269, 8], [266, 14, 269, 14, "onPress"], [266, 21, 269, 21], [266, 24, 269, 24, "onPress"], [266, 25, 269, 24], [266, 30, 269, 30], [267, 12, 270, 10], [267, 16, 270, 16, "event"], [267, 21, 270, 21], [267, 24, 270, 24, "navigation"], [267, 34, 270, 34], [267, 35, 270, 35, "emit"], [267, 39, 270, 39], [267, 40, 270, 40], [268, 14, 271, 12, "type"], [268, 18, 271, 16], [268, 20, 271, 18], [268, 30, 271, 28], [269, 14, 272, 12, "target"], [269, 20, 272, 18], [269, 22, 272, 20, "route"], [269, 27, 272, 25], [269, 28, 272, 26, "key"], [269, 31, 272, 29], [270, 14, 273, 12, "canPreventDefault"], [270, 31, 273, 29], [270, 33, 273, 31], [271, 12, 274, 10], [271, 13, 274, 11], [271, 14, 274, 12], [272, 12, 275, 10], [272, 16, 275, 14], [272, 17, 275, 15, "focused"], [272, 24, 275, 22], [272, 28, 275, 26], [272, 29, 275, 27, "event"], [272, 34, 275, 32], [272, 35, 275, 33, "defaultPrevented"], [272, 51, 275, 49], [272, 53, 275, 51], [273, 14, 276, 12, "navigation"], [273, 24, 276, 22], [273, 25, 276, 23, "dispatch"], [273, 33, 276, 31], [273, 34, 276, 32], [274, 16, 277, 14], [274, 19, 277, 17, "CommonActions"], [274, 40, 277, 30], [274, 41, 277, 31, "navigate"], [274, 49, 277, 39], [274, 50, 277, 40, "route"], [274, 55, 277, 45], [274, 56, 277, 46], [275, 16, 278, 14, "target"], [275, 22, 278, 20], [275, 24, 278, 22, "state"], [275, 29, 278, 27], [275, 30, 278, 28, "key"], [276, 14, 279, 12], [276, 15, 279, 13], [276, 16, 279, 14], [277, 12, 280, 10], [278, 10, 281, 8], [278, 11, 281, 9], [279, 10, 282, 8], [279, 14, 282, 14, "onLongPress"], [279, 25, 282, 25], [279, 28, 282, 28, "onLongPress"], [279, 29, 282, 28], [279, 34, 282, 34], [280, 12, 283, 10, "navigation"], [280, 22, 283, 20], [280, 23, 283, 21, "emit"], [280, 27, 283, 25], [280, 28, 283, 26], [281, 14, 284, 12, "type"], [281, 18, 284, 16], [281, 20, 284, 18], [281, 34, 284, 32], [282, 14, 285, 12, "target"], [282, 20, 285, 18], [282, 22, 285, 20, "route"], [282, 27, 285, 25], [282, 28, 285, 26, "key"], [283, 12, 286, 10], [283, 13, 286, 11], [283, 14, 286, 12], [284, 10, 287, 8], [284, 11, 287, 9], [285, 10, 288, 8], [285, 14, 288, 14, "label"], [285, 19, 288, 19], [285, 22, 288, 22], [285, 29, 288, 29, "options"], [285, 36, 288, 36], [285, 37, 288, 37, "tabBarLabel"], [285, 48, 288, 48], [285, 53, 288, 53], [285, 63, 288, 63], [285, 66, 288, 66, "options"], [285, 73, 288, 73], [285, 74, 288, 74, "tabBarLabel"], [285, 85, 288, 85], [285, 88, 288, 88], [285, 92, 288, 88, "get<PERSON><PERSON><PERSON>"], [285, 110, 288, 96], [285, 112, 288, 97], [286, 12, 289, 10, "label"], [286, 17, 289, 15], [286, 19, 289, 17, "options"], [286, 26, 289, 24], [286, 27, 289, 25, "tabBarLabel"], [286, 38, 289, 36], [287, 12, 290, 10, "title"], [287, 17, 290, 15], [287, 19, 290, 17, "options"], [287, 26, 290, 24], [287, 27, 290, 25, "title"], [288, 10, 291, 8], [288, 11, 291, 9], [288, 13, 291, 11, "route"], [288, 18, 291, 16], [288, 19, 291, 17, "name"], [288, 23, 291, 21], [288, 24, 291, 22], [289, 10, 292, 8], [289, 14, 292, 14, "accessibilityLabel"], [289, 32, 292, 32], [289, 35, 292, 35, "options"], [289, 42, 292, 42], [289, 43, 292, 43, "tabBarAccessibilityLabel"], [289, 67, 292, 67], [289, 72, 292, 72, "undefined"], [289, 81, 292, 81], [289, 84, 292, 84, "options"], [289, 91, 292, 91], [289, 92, 292, 92, "tabBarAccessibilityLabel"], [289, 116, 292, 116], [289, 119, 292, 119], [289, 126, 292, 126, "label"], [289, 131, 292, 131], [289, 136, 292, 136], [289, 144, 292, 144], [289, 148, 292, 148, "Platform"], [289, 169, 292, 156], [289, 170, 292, 157, "OS"], [289, 172, 292, 159], [289, 177, 292, 164], [289, 182, 292, 169], [289, 185, 292, 172], [289, 188, 292, 175, "label"], [289, 193, 292, 180], [289, 203, 292, 190, "index"], [289, 208, 292, 195], [289, 211, 292, 198], [289, 212, 292, 199], [289, 219, 292, 206, "routes"], [289, 225, 292, 212], [289, 226, 292, 213, "length"], [289, 232, 292, 219], [289, 234, 292, 221], [289, 237, 292, 224, "undefined"], [289, 246, 292, 233], [290, 10, 293, 8], [290, 17, 293, 15], [290, 30, 293, 28], [290, 34, 293, 28, "_jsx"], [290, 49, 293, 32], [290, 51, 293, 33, "NavigationContext"], [290, 76, 293, 50], [290, 77, 293, 51, "Provider"], [290, 85, 293, 59], [290, 87, 293, 61], [291, 12, 294, 10, "value"], [291, 17, 294, 15], [291, 19, 294, 17, "descriptors"], [291, 30, 294, 28], [291, 31, 294, 29, "route"], [291, 36, 294, 34], [291, 37, 294, 35, "key"], [291, 40, 294, 38], [291, 41, 294, 39], [291, 42, 294, 40, "navigation"], [291, 52, 294, 50], [292, 12, 295, 10, "children"], [292, 20, 295, 18], [292, 22, 295, 20], [292, 35, 295, 33], [292, 39, 295, 33, "_jsx"], [292, 54, 295, 37], [292, 56, 295, 38, "NavigationRouteContext"], [292, 86, 295, 60], [292, 87, 295, 61, "Provider"], [292, 95, 295, 69], [292, 97, 295, 71], [293, 14, 296, 12, "value"], [293, 19, 296, 17], [293, 21, 296, 19, "route"], [293, 26, 296, 24], [294, 14, 297, 12, "children"], [294, 22, 297, 20], [294, 24, 297, 22], [294, 37, 297, 35], [294, 41, 297, 35, "_jsx"], [294, 56, 297, 39], [294, 58, 297, 40, "BottomTabItem"], [294, 86, 297, 53], [294, 88, 297, 55], [295, 16, 298, 14, "href"], [295, 20, 298, 18], [295, 22, 298, 20, "buildHref"], [295, 31, 298, 29], [295, 32, 298, 30, "route"], [295, 37, 298, 35], [295, 38, 298, 36, "name"], [295, 42, 298, 40], [295, 44, 298, 42, "route"], [295, 49, 298, 47], [295, 50, 298, 48, "params"], [295, 56, 298, 54], [295, 57, 298, 55], [296, 16, 299, 14, "route"], [296, 21, 299, 19], [296, 23, 299, 21, "route"], [296, 28, 299, 26], [297, 16, 300, 14, "descriptor"], [297, 26, 300, 24], [297, 28, 300, 26, "descriptors"], [297, 39, 300, 37], [297, 40, 300, 38, "route"], [297, 45, 300, 43], [297, 46, 300, 44, "key"], [297, 49, 300, 47], [297, 50, 300, 48], [298, 16, 301, 14, "focused"], [298, 23, 301, 21], [298, 25, 301, 23, "focused"], [298, 32, 301, 30], [299, 16, 302, 14, "horizontal"], [299, 26, 302, 24], [299, 28, 302, 26, "hasHorizontalLabels"], [299, 47, 302, 45], [300, 16, 303, 14, "compact"], [300, 23, 303, 21], [300, 25, 303, 23, "compact"], [300, 32, 303, 30], [301, 16, 304, 14, "sidebar"], [301, 23, 304, 21], [301, 25, 304, 23, "sidebar"], [301, 32, 304, 30], [302, 16, 305, 14, "variant"], [302, 23, 305, 21], [302, 25, 305, 23, "tabBarVariant"], [302, 38, 305, 36], [303, 16, 306, 14, "onPress"], [303, 23, 306, 21], [303, 25, 306, 23, "onPress"], [303, 32, 306, 30], [304, 16, 307, 14, "onLongPress"], [304, 27, 307, 25], [304, 29, 307, 27, "onLongPress"], [304, 40, 307, 38], [305, 16, 308, 14, "accessibilityLabel"], [305, 34, 308, 32], [305, 36, 308, 34, "accessibilityLabel"], [305, 54, 308, 52], [306, 16, 309, 14, "testID"], [306, 22, 309, 20], [306, 24, 309, 22, "options"], [306, 31, 309, 29], [306, 32, 309, 30, "tabBarButtonTestID"], [306, 50, 309, 48], [307, 16, 310, 14, "allowFontScaling"], [307, 32, 310, 30], [307, 34, 310, 32, "options"], [307, 41, 310, 39], [307, 42, 310, 40, "tabBarAllowFontScaling"], [307, 64, 310, 62], [308, 16, 311, 14, "activeTintColor"], [308, 31, 311, 29], [308, 33, 311, 31, "tabBarActiveTintColor"], [308, 54, 311, 52], [309, 16, 312, 14, "inactiveTintColor"], [309, 33, 312, 31], [309, 35, 312, 33, "tabBarInactiveTintColor"], [309, 58, 312, 56], [310, 16, 313, 14, "activeBackgroundColor"], [310, 37, 313, 35], [310, 39, 313, 37, "tabBarActiveBackgroundColor"], [310, 66, 313, 64], [311, 16, 314, 14, "inactiveBackgroundColor"], [311, 39, 314, 37], [311, 41, 314, 39, "tabBarInactiveBackgroundColor"], [311, 70, 314, 68], [312, 16, 315, 14, "button"], [312, 22, 315, 20], [312, 24, 315, 22, "options"], [312, 31, 315, 29], [312, 32, 315, 30, "tabBarButton"], [312, 44, 315, 42], [313, 16, 316, 14, "icon"], [313, 20, 316, 18], [313, 22, 316, 20, "options"], [313, 29, 316, 27], [313, 30, 316, 28, "tabBarIcon"], [313, 40, 316, 38], [313, 45, 316, 43, "_ref6"], [313, 50, 316, 43], [314, 18, 316, 43], [314, 22, 317, 16, "color"], [314, 27, 317, 21], [314, 30, 317, 21, "_ref6"], [314, 35, 317, 21], [314, 36, 317, 16, "color"], [314, 41, 317, 21], [315, 20, 318, 16, "size"], [315, 24, 318, 20], [315, 27, 318, 20, "_ref6"], [315, 32, 318, 20], [315, 33, 318, 16, "size"], [315, 37, 318, 20], [316, 18, 318, 20], [316, 25, 319, 20], [316, 38, 319, 33], [316, 42, 319, 33, "_jsx"], [316, 57, 319, 37], [316, 59, 319, 38, "MissingIcon"], [316, 80, 319, 49], [316, 82, 319, 51], [317, 20, 320, 16, "color"], [317, 25, 320, 21], [317, 27, 320, 23, "color"], [317, 32, 320, 28], [318, 20, 321, 16, "size"], [318, 24, 321, 20], [318, 26, 321, 22, "size"], [319, 18, 322, 14], [319, 19, 322, 15], [319, 20, 322, 16], [320, 16, 322, 16], [320, 18, 322, 17], [321, 16, 323, 14, "badge"], [321, 21, 323, 19], [321, 23, 323, 21, "options"], [321, 30, 323, 28], [321, 31, 323, 29, "tabBarBadge"], [321, 42, 323, 40], [322, 16, 324, 14, "badgeStyle"], [322, 26, 324, 24], [322, 28, 324, 26, "options"], [322, 35, 324, 33], [322, 36, 324, 34, "tabBarBadgeStyle"], [322, 52, 324, 50], [323, 16, 325, 14, "label"], [323, 21, 325, 19], [323, 23, 325, 21, "label"], [323, 28, 325, 26], [324, 16, 326, 14, "showLabel"], [324, 25, 326, 23], [324, 27, 326, 25, "tabBarShowLabel"], [324, 42, 326, 40], [325, 16, 327, 14, "labelStyle"], [325, 26, 327, 24], [325, 28, 327, 26, "options"], [325, 35, 327, 33], [325, 36, 327, 34, "tabBarLabelStyle"], [325, 52, 327, 50], [326, 16, 328, 14, "iconStyle"], [326, 25, 328, 23], [326, 27, 328, 25, "options"], [326, 34, 328, 32], [326, 35, 328, 33, "tabBarIconStyle"], [326, 50, 328, 48], [327, 16, 329, 14, "style"], [327, 21, 329, 19], [327, 23, 329, 21], [327, 24, 329, 22, "sidebar"], [327, 31, 329, 29], [327, 34, 329, 32], [328, 18, 330, 16, "marginVertical"], [328, 32, 330, 30], [328, 34, 330, 32, "hasHorizontalLabels"], [328, 53, 330, 51], [328, 56, 330, 54, "tabBarVariant"], [328, 69, 330, 67], [328, 74, 330, 72], [328, 84, 330, 82], [328, 87, 330, 85], [328, 88, 330, 86], [328, 91, 330, 89], [328, 92, 330, 90], [328, 95, 330, 93, "spacing"], [328, 102, 330, 100], [328, 105, 330, 103], [329, 16, 331, 14], [329, 17, 331, 15], [329, 20, 331, 18, "styles"], [329, 26, 331, 24], [329, 27, 331, 25, "bottomItem"], [329, 37, 331, 35], [329, 39, 331, 37, "options"], [329, 46, 331, 44], [329, 47, 331, 45, "tabBarItemStyle"], [329, 62, 331, 60], [330, 14, 332, 12], [330, 15, 332, 13], [331, 12, 333, 10], [331, 13, 333, 11], [332, 10, 334, 8], [332, 11, 334, 9], [332, 13, 334, 11, "route"], [332, 18, 334, 16], [332, 19, 334, 17, "key"], [332, 22, 334, 20], [332, 23, 334, 21], [333, 8, 335, 6], [333, 9, 335, 7], [334, 6, 336, 4], [334, 7, 336, 5], [334, 8, 336, 6], [335, 4, 337, 2], [335, 5, 337, 3], [335, 6, 337, 4], [336, 2, 338, 0], [337, 2, 339, 0], [337, 6, 339, 6, "styles"], [337, 12, 339, 12], [337, 15, 339, 15, "StyleSheet"], [337, 38, 339, 25], [337, 39, 339, 26, "create"], [337, 45, 339, 32], [337, 46, 339, 33], [338, 4, 340, 2, "start"], [338, 9, 340, 7], [338, 11, 340, 9], [339, 6, 341, 4, "top"], [339, 9, 341, 7], [339, 11, 341, 9], [339, 12, 341, 10], [340, 6, 342, 4, "bottom"], [340, 12, 342, 10], [340, 14, 342, 12], [340, 15, 342, 13], [341, 6, 343, 4, "start"], [341, 11, 343, 9], [341, 13, 343, 11], [342, 4, 344, 2], [342, 5, 344, 3], [343, 4, 345, 2, "end"], [343, 7, 345, 5], [343, 9, 345, 7], [344, 6, 346, 4, "top"], [344, 9, 346, 7], [344, 11, 346, 9], [344, 12, 346, 10], [345, 6, 347, 4, "bottom"], [345, 12, 347, 10], [345, 14, 347, 12], [345, 15, 347, 13], [346, 6, 348, 4, "end"], [346, 9, 348, 7], [346, 11, 348, 9], [347, 4, 349, 2], [347, 5, 349, 3], [348, 4, 350, 2, "bottom"], [348, 10, 350, 8], [348, 12, 350, 10], [349, 6, 351, 4, "start"], [349, 11, 351, 9], [349, 13, 351, 11], [349, 14, 351, 12], [350, 6, 352, 4, "end"], [350, 9, 352, 7], [350, 11, 352, 9], [350, 12, 352, 10], [351, 6, 353, 4, "bottom"], [351, 12, 353, 10], [351, 14, 353, 12], [351, 15, 353, 13], [352, 6, 354, 4, "elevation"], [352, 15, 354, 13], [352, 17, 354, 15], [353, 4, 355, 2], [353, 5, 355, 3], [354, 4, 356, 2, "bottomContent"], [354, 17, 356, 15], [354, 19, 356, 17], [355, 6, 357, 4, "flex"], [355, 10, 357, 8], [355, 12, 357, 10], [355, 13, 357, 11], [356, 6, 358, 4, "flexDirection"], [356, 19, 358, 17], [356, 21, 358, 19], [357, 4, 359, 2], [357, 5, 359, 3], [358, 4, 360, 2, "sideContent"], [358, 15, 360, 13], [358, 17, 360, 15], [359, 6, 361, 4, "flex"], [359, 10, 361, 8], [359, 12, 361, 10], [359, 13, 361, 11], [360, 6, 362, 4, "flexDirection"], [360, 19, 362, 17], [360, 21, 362, 19], [361, 4, 363, 2], [361, 5, 363, 3], [362, 4, 364, 2, "bottomItem"], [362, 14, 364, 12], [362, 16, 364, 14], [363, 6, 365, 4, "flex"], [363, 10, 365, 8], [363, 12, 365, 10], [364, 4, 366, 2], [365, 2, 367, 0], [365, 3, 367, 1], [365, 4, 367, 2], [366, 0, 367, 3], [366, 3]], "functionMap": {"names": ["<global>", "shouldUseHorizontalLabels", "state.routes.reduce$argument_0", "isCompact", "getTabBarHeight", "BottomTabBar", "React.useEffect$argument_0", "React.useState$argument_0", "animation.start$argument_0", "<anonymous>", "handleLayout", "setLayout$argument_0", "useFrameSize$argument_0", "routes.map$argument_0", "onPress", "onLongPress"], "mappings": "AAA;kCCgB;4CCkB;KDa;CDK;kBGC;CHsB;+BIC;CJwB;OKC;kBC2C;GDE;mCEE,kDF;kBCC;eES;OFM;WGY,6BH;GDC;uBKI;cCK;KDQ;GLC;oCOI;IPM;2COC;IPI;+BOC;IPI;uCOG,yEP;2BQ6C;wBCK;SDY;4BEC;SFK;2CJ6B;gBIM;ORa;CLG"}}, "type": "js/module"}]}