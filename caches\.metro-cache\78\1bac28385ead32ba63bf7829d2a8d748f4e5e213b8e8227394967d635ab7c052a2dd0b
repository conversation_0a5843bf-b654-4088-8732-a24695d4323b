{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = warnForStyleProps;\n  function warnForStyleProps(props, validAttributes) {\n    if (__DEV__) {\n      for (var key in validAttributes.style) {\n        if (!(validAttributes[key] || props[key] === undefined)) {\n          console.error('You are setting the style `{ %s' + ': ... }` as a prop. You ' + 'should nest it in a style object. ' + 'E.g. `{ style: { %s' + ': ... } }`', key, key);\n        }\n      }\n    }\n  }\n});", "lineCount": 15, "map": [[6, 2, 13, 15], [6, 11, 13, 24, "warnForStyleProps"], [6, 28, 13, 41, "warnForStyleProps"], [6, 29, 14, 2, "props"], [6, 34, 14, 14], [6, 36, 15, 2, "validAttributes"], [6, 51, 15, 41], [6, 53, 16, 8], [7, 4, 17, 2], [7, 8, 17, 6, "__DEV__"], [7, 15, 17, 13], [7, 17, 17, 15], [8, 6, 18, 4], [8, 11, 18, 9], [8, 15, 18, 15, "key"], [8, 18, 18, 18], [8, 22, 18, 22, "validAttributes"], [8, 37, 18, 37], [8, 38, 18, 38, "style"], [8, 43, 18, 43], [8, 45, 18, 45], [9, 8, 20, 6], [9, 12, 20, 10], [9, 14, 20, 12, "validAttributes"], [9, 29, 20, 27], [9, 30, 20, 28, "key"], [9, 33, 20, 31], [9, 34, 20, 32], [9, 38, 20, 36, "props"], [9, 43, 20, 41], [9, 44, 20, 42, "key"], [9, 47, 20, 45], [9, 48, 20, 46], [9, 53, 20, 51, "undefined"], [9, 62, 20, 60], [9, 63, 20, 61], [9, 65, 20, 63], [10, 10, 21, 8, "console"], [10, 17, 21, 15], [10, 18, 21, 16, "error"], [10, 23, 21, 21], [10, 24, 22, 10], [10, 57, 22, 43], [10, 60, 23, 12], [10, 86, 23, 38], [10, 89, 24, 12], [10, 125, 24, 48], [10, 128, 25, 12], [10, 149, 25, 33], [10, 152, 26, 12], [10, 164, 26, 24], [10, 166, 27, 10, "key"], [10, 169, 27, 13], [10, 171, 28, 10, "key"], [10, 174, 29, 8], [10, 175, 29, 9], [11, 8, 30, 6], [12, 6, 31, 4], [13, 4, 32, 2], [14, 2, 33, 0], [15, 0, 33, 1], [15, 3]], "functionMap": {"names": ["<global>", "warnForStyleProps"], "mappings": "AAA;eCY"}}, "type": "js/module"}]}