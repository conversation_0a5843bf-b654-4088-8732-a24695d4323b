{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationIndependentTreeContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 89, "index": 136}}], "key": "R+yxAMdry72RcVivDQ5Mcw6/NAU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useNavigationIndependentTree = useNavigationIndependentTree;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _NavigationIndependentTreeContext = require(_dependencyMap[1], \"./NavigationIndependentTreeContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function useNavigationIndependentTree() {\n    return React.useContext(_NavigationIndependentTreeContext.NavigationIndependentTreeContext);\n  }\n});", "lineCount": 14, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useNavigationIndependentTree"], [7, 38, 1, 13], [7, 41, 1, 13, "useNavigationIndependentTree"], [7, 69, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_NavigationIndependentTreeContext"], [9, 39, 4, 0], [9, 42, 4, 0, "require"], [9, 49, 4, 0], [9, 50, 4, 0, "_dependencyMap"], [9, 64, 4, 0], [10, 2, 4, 89], [10, 11, 4, 89, "_interopRequireWildcard"], [10, 35, 4, 89, "e"], [10, 36, 4, 89], [10, 38, 4, 89, "t"], [10, 39, 4, 89], [10, 68, 4, 89, "WeakMap"], [10, 75, 4, 89], [10, 81, 4, 89, "r"], [10, 82, 4, 89], [10, 89, 4, 89, "WeakMap"], [10, 96, 4, 89], [10, 100, 4, 89, "n"], [10, 101, 4, 89], [10, 108, 4, 89, "WeakMap"], [10, 115, 4, 89], [10, 127, 4, 89, "_interopRequireWildcard"], [10, 150, 4, 89], [10, 162, 4, 89, "_interopRequireWildcard"], [10, 163, 4, 89, "e"], [10, 164, 4, 89], [10, 166, 4, 89, "t"], [10, 167, 4, 89], [10, 176, 4, 89, "t"], [10, 177, 4, 89], [10, 181, 4, 89, "e"], [10, 182, 4, 89], [10, 186, 4, 89, "e"], [10, 187, 4, 89], [10, 188, 4, 89, "__esModule"], [10, 198, 4, 89], [10, 207, 4, 89, "e"], [10, 208, 4, 89], [10, 214, 4, 89, "o"], [10, 215, 4, 89], [10, 217, 4, 89, "i"], [10, 218, 4, 89], [10, 220, 4, 89, "f"], [10, 221, 4, 89], [10, 226, 4, 89, "__proto__"], [10, 235, 4, 89], [10, 243, 4, 89, "default"], [10, 250, 4, 89], [10, 252, 4, 89, "e"], [10, 253, 4, 89], [10, 270, 4, 89, "e"], [10, 271, 4, 89], [10, 294, 4, 89, "e"], [10, 295, 4, 89], [10, 320, 4, 89, "e"], [10, 321, 4, 89], [10, 330, 4, 89, "f"], [10, 331, 4, 89], [10, 337, 4, 89, "o"], [10, 338, 4, 89], [10, 341, 4, 89, "t"], [10, 342, 4, 89], [10, 345, 4, 89, "n"], [10, 346, 4, 89], [10, 349, 4, 89, "r"], [10, 350, 4, 89], [10, 358, 4, 89, "o"], [10, 359, 4, 89], [10, 360, 4, 89, "has"], [10, 363, 4, 89], [10, 364, 4, 89, "e"], [10, 365, 4, 89], [10, 375, 4, 89, "o"], [10, 376, 4, 89], [10, 377, 4, 89, "get"], [10, 380, 4, 89], [10, 381, 4, 89, "e"], [10, 382, 4, 89], [10, 385, 4, 89, "o"], [10, 386, 4, 89], [10, 387, 4, 89, "set"], [10, 390, 4, 89], [10, 391, 4, 89, "e"], [10, 392, 4, 89], [10, 394, 4, 89, "f"], [10, 395, 4, 89], [10, 409, 4, 89, "_t"], [10, 411, 4, 89], [10, 415, 4, 89, "e"], [10, 416, 4, 89], [10, 432, 4, 89, "_t"], [10, 434, 4, 89], [10, 441, 4, 89, "hasOwnProperty"], [10, 455, 4, 89], [10, 456, 4, 89, "call"], [10, 460, 4, 89], [10, 461, 4, 89, "e"], [10, 462, 4, 89], [10, 464, 4, 89, "_t"], [10, 466, 4, 89], [10, 473, 4, 89, "i"], [10, 474, 4, 89], [10, 478, 4, 89, "o"], [10, 479, 4, 89], [10, 482, 4, 89, "Object"], [10, 488, 4, 89], [10, 489, 4, 89, "defineProperty"], [10, 503, 4, 89], [10, 508, 4, 89, "Object"], [10, 514, 4, 89], [10, 515, 4, 89, "getOwnPropertyDescriptor"], [10, 539, 4, 89], [10, 540, 4, 89, "e"], [10, 541, 4, 89], [10, 543, 4, 89, "_t"], [10, 545, 4, 89], [10, 552, 4, 89, "i"], [10, 553, 4, 89], [10, 554, 4, 89, "get"], [10, 557, 4, 89], [10, 561, 4, 89, "i"], [10, 562, 4, 89], [10, 563, 4, 89, "set"], [10, 566, 4, 89], [10, 570, 4, 89, "o"], [10, 571, 4, 89], [10, 572, 4, 89, "f"], [10, 573, 4, 89], [10, 575, 4, 89, "_t"], [10, 577, 4, 89], [10, 579, 4, 89, "i"], [10, 580, 4, 89], [10, 584, 4, 89, "f"], [10, 585, 4, 89], [10, 586, 4, 89, "_t"], [10, 588, 4, 89], [10, 592, 4, 89, "e"], [10, 593, 4, 89], [10, 594, 4, 89, "_t"], [10, 596, 4, 89], [10, 607, 4, 89, "f"], [10, 608, 4, 89], [10, 613, 4, 89, "e"], [10, 614, 4, 89], [10, 616, 4, 89, "t"], [10, 617, 4, 89], [11, 2, 5, 7], [11, 11, 5, 16, "useNavigationIndependentTree"], [11, 39, 5, 44, "useNavigationIndependentTree"], [11, 40, 5, 44], [11, 42, 5, 47], [12, 4, 6, 2], [12, 11, 6, 9, "React"], [12, 16, 6, 14], [12, 17, 6, 15, "useContext"], [12, 27, 6, 25], [12, 28, 6, 26, "NavigationIndependentTreeContext"], [12, 94, 6, 58], [12, 95, 6, 59], [13, 2, 7, 0], [14, 0, 7, 1], [14, 3]], "functionMap": {"names": ["<global>", "useNavigationIndependentTree"], "mappings": "AAA;OCI;CDE"}}, "type": "js/module"}]}