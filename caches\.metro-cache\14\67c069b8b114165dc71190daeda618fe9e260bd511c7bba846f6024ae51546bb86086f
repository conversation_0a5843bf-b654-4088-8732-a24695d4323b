{"dependencies": [{"name": "./getPrototypeOf.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 1, "column": 21, "index": 21}, "end": {"line": 1, "column": 51, "index": 51}}], "key": "vHkEouWADzjCoPQf+U9aET1cyIA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var getPrototypeOf = require(_dependencyMap[0], \"./getPrototypeOf.js\");\n  function _superPropBase(t, o) {\n    for (; !{}.hasOwnProperty.call(t, o) && null !== (t = getPrototypeOf(t)););\n    return t;\n  }\n  module.exports = _superPropBase, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 8, "map": [[2, 2, 1, 0], [2, 6, 1, 4, "getPrototypeOf"], [2, 20, 1, 18], [2, 23, 1, 21, "require"], [2, 30, 1, 28], [2, 31, 1, 28, "_dependencyMap"], [2, 45, 1, 28], [2, 71, 1, 50], [2, 72, 1, 51], [3, 2, 2, 0], [3, 11, 2, 9, "_superPropBase"], [3, 25, 2, 23, "_superPropBase"], [3, 26, 2, 24, "t"], [3, 27, 2, 25], [3, 29, 2, 27, "o"], [3, 30, 2, 28], [3, 32, 2, 30], [4, 4, 3, 2], [4, 11, 3, 9], [4, 12, 3, 10], [4, 13, 3, 11], [4, 14, 3, 12], [4, 15, 3, 13, "hasOwnProperty"], [4, 29, 3, 27], [4, 30, 3, 28, "call"], [4, 34, 3, 32], [4, 35, 3, 33, "t"], [4, 36, 3, 34], [4, 38, 3, 36, "o"], [4, 39, 3, 37], [4, 40, 3, 38], [4, 44, 3, 42], [4, 48, 3, 46], [4, 54, 3, 52, "t"], [4, 55, 3, 53], [4, 58, 3, 56, "getPrototypeOf"], [4, 72, 3, 70], [4, 73, 3, 71, "t"], [4, 74, 3, 72], [4, 75, 3, 73], [4, 76, 3, 74], [4, 78, 3, 76], [5, 4, 4, 2], [5, 11, 4, 9, "t"], [5, 12, 4, 10], [6, 2, 5, 0], [7, 2, 6, 0, "module"], [7, 8, 6, 6], [7, 9, 6, 7, "exports"], [7, 16, 6, 14], [7, 19, 6, 17, "_superPropBase"], [7, 33, 6, 31], [7, 35, 6, 33, "module"], [7, 41, 6, 39], [7, 42, 6, 40, "exports"], [7, 49, 6, 47], [7, 50, 6, 48, "__esModule"], [7, 60, 6, 58], [7, 63, 6, 61], [7, 67, 6, 65], [7, 69, 6, 67, "module"], [7, 75, 6, 73], [7, 76, 6, 74, "exports"], [7, 83, 6, 81], [7, 84, 6, 82], [7, 93, 6, 91], [7, 94, 6, 92], [7, 97, 6, 95, "module"], [7, 103, 6, 101], [7, 104, 6, 102, "exports"], [7, 111, 6, 109], [8, 0, 6, 110], [8, 3]], "functionMap": {"names": ["<global>", "_superPropBase"], "mappings": "AAA;ACC;CDG"}}, "type": "js/module"}]}