{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 34, "index": 49}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../commonTypes.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 50}, "end": {"line": 4, "column": 49, "index": 99}}], "key": "9j6OaBzi0V5srVAX3iTMRrWOBnc=", "exportNames": ["*"]}}, {"name": "../logger/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 100}, "end": {"line": 5, "column": 44, "index": 144}}], "key": "pBiviTeVoxyQBwxnAV5OZFjetV0=", "exportNames": ["*"]}}, {"name": "../ReducedMotion.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 145}, "end": {"line": 6, "column": 91, "index": 236}}], "key": "JONtVVBa2Bvh65ik/Vv2/Ov7kNs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ReducedMotionConfig = ReducedMotionConfig;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _commonTypes = require(_dependencyMap[1], \"../commonTypes.js\");\n  var _index = require(_dependencyMap[2], \"../logger/index.js\");\n  var _ReducedMotion = require(_dependencyMap[3], \"../ReducedMotion.js\");\n  /**\n   * A component that lets you overwrite default reduce motion behavior globally\n   * in your application.\n   *\n   * @param mode - Determines default reduce motion behavior globally in your\n   *   application. Configured with {@link ReduceMotion} enum.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/components/ReducedMotionConfig\n   */\n  function ReducedMotionConfig({\n    mode\n  }) {\n    (0, _react.useEffect)(() => {\n      if (!__DEV__) {\n        return;\n      }\n      _index.logger.warn(`Reduced motion setting is overwritten with mode '${mode}'.`);\n    }, []);\n    (0, _react.useEffect)(() => {\n      const wasEnabled = _ReducedMotion.ReducedMotionManager.jsValue;\n      switch (mode) {\n        case _commonTypes.ReduceMotion.System:\n          _ReducedMotion.ReducedMotionManager.setEnabled((0, _ReducedMotion.isReducedMotionEnabledInSystem)());\n          break;\n        case _commonTypes.ReduceMotion.Always:\n          _ReducedMotion.ReducedMotionManager.setEnabled(true);\n          break;\n        case _commonTypes.ReduceMotion.Never:\n          _ReducedMotion.ReducedMotionManager.setEnabled(false);\n          break;\n      }\n      return () => {\n        _ReducedMotion.ReducedMotionManager.setEnabled(wasEnabled);\n      };\n    }, [mode]);\n    return null;\n  }\n});", "lineCount": 48, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "ReducedMotionConfig"], [7, 29, 1, 13], [7, 32, 1, 13, "ReducedMotionConfig"], [7, 51, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_react"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_commonTypes"], [9, 18, 4, 0], [9, 21, 4, 0, "require"], [9, 28, 4, 0], [9, 29, 4, 0, "_dependencyMap"], [9, 43, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_index"], [10, 12, 5, 0], [10, 15, 5, 0, "require"], [10, 22, 5, 0], [10, 23, 5, 0, "_dependencyMap"], [10, 37, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_ReducedMotion"], [11, 20, 6, 0], [11, 23, 6, 0, "require"], [11, 30, 6, 0], [11, 31, 6, 0, "_dependencyMap"], [11, 45, 6, 0], [12, 2, 8, 0], [13, 0, 9, 0], [14, 0, 10, 0], [15, 0, 11, 0], [16, 0, 12, 0], [17, 0, 13, 0], [18, 0, 14, 0], [19, 0, 15, 0], [20, 2, 16, 7], [20, 11, 16, 16, "ReducedMotionConfig"], [20, 30, 16, 35, "ReducedMotionConfig"], [20, 31, 16, 36], [21, 4, 17, 2, "mode"], [22, 2, 18, 0], [22, 3, 18, 1], [22, 5, 18, 3], [23, 4, 19, 2], [23, 8, 19, 2, "useEffect"], [23, 24, 19, 11], [23, 26, 19, 12], [23, 32, 19, 18], [24, 6, 20, 4], [24, 10, 20, 8], [24, 11, 20, 9, "__DEV__"], [24, 18, 20, 16], [24, 20, 20, 18], [25, 8, 21, 6], [26, 6, 22, 4], [27, 6, 23, 4, "logger"], [27, 19, 23, 10], [27, 20, 23, 11, "warn"], [27, 24, 23, 15], [27, 25, 23, 16], [27, 77, 23, 68, "mode"], [27, 81, 23, 72], [27, 85, 23, 76], [27, 86, 23, 77], [28, 4, 24, 2], [28, 5, 24, 3], [28, 7, 24, 5], [28, 9, 24, 7], [28, 10, 24, 8], [29, 4, 25, 2], [29, 8, 25, 2, "useEffect"], [29, 24, 25, 11], [29, 26, 25, 12], [29, 32, 25, 18], [30, 6, 26, 4], [30, 12, 26, 10, "wasEnabled"], [30, 22, 26, 20], [30, 25, 26, 23, "ReducedMotionManager"], [30, 60, 26, 43], [30, 61, 26, 44, "jsValue"], [30, 68, 26, 51], [31, 6, 27, 4], [31, 14, 27, 12, "mode"], [31, 18, 27, 16], [32, 8, 28, 6], [32, 13, 28, 11, "ReduceMotion"], [32, 38, 28, 23], [32, 39, 28, 24, "System"], [32, 45, 28, 30], [33, 10, 29, 8, "ReducedMotionManager"], [33, 45, 29, 28], [33, 46, 29, 29, "setEnabled"], [33, 56, 29, 39], [33, 57, 29, 40], [33, 61, 29, 40, "isReducedMotionEnabledInSystem"], [33, 106, 29, 70], [33, 108, 29, 71], [33, 109, 29, 72], [33, 110, 29, 73], [34, 10, 30, 8], [35, 8, 31, 6], [35, 13, 31, 11, "ReduceMotion"], [35, 38, 31, 23], [35, 39, 31, 24, "Always"], [35, 45, 31, 30], [36, 10, 32, 8, "ReducedMotionManager"], [36, 45, 32, 28], [36, 46, 32, 29, "setEnabled"], [36, 56, 32, 39], [36, 57, 32, 40], [36, 61, 32, 44], [36, 62, 32, 45], [37, 10, 33, 8], [38, 8, 34, 6], [38, 13, 34, 11, "ReduceMotion"], [38, 38, 34, 23], [38, 39, 34, 24, "Never"], [38, 44, 34, 29], [39, 10, 35, 8, "ReducedMotionManager"], [39, 45, 35, 28], [39, 46, 35, 29, "setEnabled"], [39, 56, 35, 39], [39, 57, 35, 40], [39, 62, 35, 45], [39, 63, 35, 46], [40, 10, 36, 8], [41, 6, 37, 4], [42, 6, 38, 4], [42, 13, 38, 11], [42, 19, 38, 17], [43, 8, 39, 6, "ReducedMotionManager"], [43, 43, 39, 26], [43, 44, 39, 27, "setEnabled"], [43, 54, 39, 37], [43, 55, 39, 38, "wasEnabled"], [43, 65, 39, 48], [43, 66, 39, 49], [44, 6, 40, 4], [44, 7, 40, 5], [45, 4, 41, 2], [45, 5, 41, 3], [45, 7, 41, 5], [45, 8, 41, 6, "mode"], [45, 12, 41, 10], [45, 13, 41, 11], [45, 14, 41, 12], [46, 4, 42, 2], [46, 11, 42, 9], [46, 15, 42, 13], [47, 2, 43, 0], [48, 0, 43, 1], [48, 3]], "functionMap": {"names": ["<global>", "ReducedMotionConfig", "useEffect$argument_0", "<anonymous>"], "mappings": "AAA;OCe;YCG;GDK;YCC;WCa;KDE;GDC;CDE"}}, "type": "js/module"}]}