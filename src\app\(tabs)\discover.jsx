import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  Pressable,
  StyleSheet,
  RefreshControl,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Image } from 'expo-image';
import { 
  Heart, 
  Share, 
  Bookmark,
  TrendingUp,
  Users,
  Sparkles,
  Filter,
  Search
} from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../../utils/theme';
import { useFonts, Inter_400Regular, Inter_500Medium, Inter_600SemiBold } from '@expo-google-fonts/inter';

// Sample discover content
const trendingOutfits = [
  {
    id: '1',
    user: '<PERSON>',
    userAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b72bb37e?w=100',
    image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',
    caption: 'Perfect summer vibes ☀️',
    likes: 234,
    isLiked: false,
    isSaved: false,
    tags: ['summer', 'casual', 'yellow'],
  },
  {
    id: '2',
    user: '<PERSON>',
    userAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',
    image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',
    caption: 'Business casual done right 💼',
    likes: 189,
    isLiked: true,
    isSaved: false,
    tags: ['business', 'formal', 'white'],
  },
  {
    id: '3',
    user: 'Maya Patel',
    userAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',
    image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=400',
    caption: 'Cozy autumn layers 🍂',
    likes: 156,
    isLiked: false,
    isSaved: true,
    tags: ['autumn', 'layers', 'blue'],
  },
];

const aiRecommendations = [
  {
    id: '1',
    title: 'Minimalist Chic',
    description: 'Clean lines and neutral tones for effortless elegance',
    image: 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5e?w=400',
    confidence: 92,
  },
  {
    id: '2',
    title: 'Retro Revival',
    description: 'Vintage-inspired pieces with a modern twist',
    image: 'https://images.unsplash.com/photo-1551488831-00ddcb6c6bd3?w=400',
    confidence: 87,
  },
  {
    id: '3',
    title: 'Street Style Edge',
    description: 'Urban fashion with bold statements',
    image: 'https://images.unsplash.com/photo-1556905055-8f358a7a47b2?w=400',
    confidence: 95,
  },
];

const categories = [
  { id: 'trending', name: 'Trending', icon: TrendingUp },
  { id: 'recommended', name: 'For You', icon: Sparkles },
  { id: 'following', name: 'Following', icon: Users },
];

export default function DiscoverScreen() {
  const insets = useSafeAreaInsets();
  const { colors, isDark } = useTheme();
  const [selectedCategory, setSelectedCategory] = useState('trending');
  const [refreshing, setRefreshing] = useState(false);
  const [outfits, setOutfits] = useState(trendingOutfits);

  const [fontsLoaded] = useFonts({
    Inter_400Regular,
    Inter_500Medium,
    Inter_600SemiBold,
  });

  if (!fontsLoaded) {
    return null;
  }

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate loading new content
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const toggleLike = (outfitId) => {
    setOutfits(prev => prev.map(outfit => 
      outfit.id === outfitId 
        ? { 
            ...outfit, 
            isLiked: !outfit.isLiked,
            likes: outfit.isLiked ? outfit.likes - 1 : outfit.likes + 1
          }
        : outfit
    ));
  };

  const toggleSave = (outfitId) => {
    setOutfits(prev => prev.map(outfit => 
      outfit.id === outfitId 
        ? { ...outfit, isSaved: !outfit.isSaved }
        : outfit
    ));
  };

  const renderCategoryButton = (category) => {
    const isSelected = selectedCategory === category.id;
    const IconComponent = category.icon;
    
    return (
      <Pressable
        key={category.id}
        style={[
          styles.categoryButton,
          { backgroundColor: colors.surface },
          isSelected && { backgroundColor: colors.primary }
        ]}
        onPress={() => setSelectedCategory(category.id)}
      >
        <IconComponent 
          size={16} 
          color={isSelected ? colors.buttonPrimaryText : colors.textSecondary} 
        />
        <Text style={[
          styles.categoryText,
          { color: colors.textSecondary },
          isSelected && { color: colors.buttonPrimaryText }
        ]}>
          {category.name}
        </Text>
      </Pressable>
    );
  };

  const renderOutfitCard = (outfit) => (
    <View key={outfit.id} style={[styles.outfitCard, { backgroundColor: colors.cardBackground }]}>
      {/* User Header */}
      <View style={styles.outfitHeader}>
        <View style={styles.userInfo}>
          <Image
            source={{ uri: outfit.userAvatar }}
            style={styles.userAvatar}
            contentFit="cover"
            transition={200}
          />
          <Text style={[styles.userName, { color: colors.text }]}>
            {outfit.user}
          </Text>
        </View>
        <Pressable>
          <Bookmark 
            size={20} 
            color={outfit.isSaved ? colors.primary : colors.textSecondary}
            fill={outfit.isSaved ? colors.primary : 'none'}
          />
        </Pressable>
      </View>

      {/* Outfit Image */}
      <Image
        source={{ uri: outfit.image }}
        style={styles.outfitImage}
        contentFit="cover"
        transition={200}
      />

      {/* Actions */}
      <View style={styles.outfitActions}>
        <View style={styles.leftActions}>
          <Pressable 
            style={styles.actionButton}
            onPress={() => toggleLike(outfit.id)}
          >
            <Heart 
              size={20} 
              color={outfit.isLiked ? colors.error : colors.textSecondary}
              fill={outfit.isLiked ? colors.error : 'none'}
            />
            <Text style={[styles.actionText, { color: colors.textSecondary }]}>
              {outfit.likes}
            </Text>
          </Pressable>
          
          <Pressable style={styles.actionButton}>
            <Share size={20} color={colors.textSecondary} />
            <Text style={[styles.actionText, { color: colors.textSecondary }]}>
              Share
            </Text>
          </Pressable>
        </View>
        
        <Pressable 
          style={[styles.tryOnButton, { backgroundColor: colors.primary }]}
        >
          <Text style={[styles.tryOnText, { color: colors.buttonPrimaryText }]}>
            Try On
          </Text>
        </Pressable>
      </View>

      {/* Caption */}
      <View style={styles.outfitCaption}>
        <Text style={[styles.captionText, { color: colors.text }]}>
          {outfit.caption}
        </Text>
        <View style={styles.tags}>
          {outfit.tags.map((tag, index) => (
            <Text key={index} style={[styles.tag, { color: colors.primary }]}>
              #{tag}
            </Text>
          ))}
        </View>
      </View>
    </View>
  );

  const renderAIRecommendation = (recommendation) => (
    <Pressable
      key={recommendation.id}
      style={[styles.recommendationCard, { backgroundColor: colors.cardBackground }]}
    >
      <Image
        source={{ uri: recommendation.image }}
        style={styles.recommendationImage}
        contentFit="cover"
        transition={200}
      />
      <View style={styles.recommendationInfo}>
        <View style={styles.recommendationHeader}>
          <Text style={[styles.recommendationTitle, { color: colors.text }]}>
            {recommendation.title}
          </Text>
          <View style={[styles.confidenceBadge, { backgroundColor: colors.success }]}>
            <Text style={[styles.confidenceText, { color: '#FFFFFF' }]}>
              {recommendation.confidence}%
            </Text>
          </View>
        </View>
        <Text style={[styles.recommendationDescription, { color: colors.textSecondary }]}>
          {recommendation.description}
        </Text>
        <Pressable style={[styles.exploreButton, { borderColor: colors.primary }]}>
          <Sparkles size={16} color={colors.primary} />
          <Text style={[styles.exploreText, { color: colors.primary }]}>
            Explore Style
          </Text>
        </Pressable>
      </View>
    </Pressable>
  );

  const renderTrendingContent = () => (
    <View style={styles.contentSection}>
      {outfits.map(renderOutfitCard)}
    </View>
  );

  const renderRecommendedContent = () => (
    <View style={styles.contentSection}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        AI Style Recommendations
      </Text>
      <Text style={[styles.sectionSubtitle, { color: colors.textSecondary }]}>
        Personalized styles based on your preferences
      </Text>
      {aiRecommendations.map(renderAIRecommendation)}
    </View>
  );

  const renderFollowingContent = () => (
    <View style={styles.contentSection}>
      <View style={styles.emptyState}>
        <View style={[styles.emptyIconContainer, { backgroundColor: colors.emptyStateBackground }]}>
          <Users size={48} color={colors.textTertiary} strokeWidth={1} />
        </View>
        <Text style={[styles.emptyTitle, { color: colors.text }]}>
          Follow some stylists
        </Text>
        <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
          Connect with fashion influencers and stylists to see their latest outfits
        </Text>
        <Pressable style={[styles.findPeopleButton, { backgroundColor: colors.primary }]}>
          <Text style={[styles.findPeopleText, { color: colors.buttonPrimaryText }]}>
            Find People to Follow
          </Text>
        </Pressable>
      </View>
    </View>
  );

  const getContentForCategory = () => {
    switch (selectedCategory) {
      case 'trending':
        return renderTrendingContent();
      case 'recommended':
        return renderRecommendedContent();
      case 'following':
        return renderFollowingContent();
      default:
        return renderTrendingContent();
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      
      {/* Header */}
      <View style={[
        styles.header,
        { 
          paddingTop: insets.top + 16,
          backgroundColor: colors.background,
          borderBottomColor: colors.border
        }
      ]}>
        <View style={styles.headerTop}>
          <Text style={[styles.title, { color: colors.text }]}>
            Discover
          </Text>
          <View style={styles.headerActions}>
            <Pressable style={[styles.iconButton, { backgroundColor: colors.surface }]}>
              <Search size={20} color={colors.text} />
            </Pressable>
            <Pressable style={[styles.iconButton, { backgroundColor: colors.surface }]}>
              <Filter size={20} color={colors.text} />
            </Pressable>
          </View>
        </View>

        {/* Categories */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.categoriesScroll}
          contentContainerStyle={styles.categoriesContainer}
        >
          {categories.map(renderCategoryButton)}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        contentContainerStyle={[styles.contentContainer, { paddingBottom: insets.bottom + 20 }]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary}
          />
        }
      >
        {getContentForCategory()}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter_600SemiBold',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  categoriesScroll: {
    marginBottom: 8,
  },
  categoriesContainer: {
    paddingRight: 20,
    gap: 8,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  categoryText: {
    fontSize: 14,
    fontFamily: 'Inter_500Medium',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  contentSection: {
    gap: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter_600SemiBold',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter_400Regular',
    marginBottom: 16,
  },
  outfitCard: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  outfitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  userAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  userName: {
    fontSize: 14,
    fontFamily: 'Inter_500Medium',
  },
  outfitImage: {
    width: '100%',
    height: 300,
  },
  outfitActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingTop: 12,
  },
  leftActions: {
    flexDirection: 'row',
    gap: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  actionText: {
    fontSize: 12,
    fontFamily: 'Inter_500Medium',
  },
  tryOnButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  tryOnText: {
    fontSize: 12,
    fontFamily: 'Inter_600SemiBold',
  },
  outfitCaption: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  captionText: {
    fontSize: 14,
    fontFamily: 'Inter_400Regular',
    lineHeight: 18,
    marginBottom: 8,
  },
  tags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    fontSize: 12,
    fontFamily: 'Inter_500Medium',
  },
  recommendationCard: {
    flexDirection: 'row',
    borderRadius: 16,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  recommendationImage: {
    width: 120,
    height: 160,
  },
  recommendationInfo: {
    flex: 1,
    padding: 16,
    justifyContent: 'space-between',
  },
  recommendationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  recommendationTitle: {
    fontSize: 16,
    fontFamily: 'Inter_600SemiBold',
    flex: 1,
    marginRight: 8,
  },
  confidenceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  confidenceText: {
    fontSize: 10,
    fontFamily: 'Inter_600SemiBold',
  },
  recommendationDescription: {
    fontSize: 13,
    fontFamily: 'Inter_400Regular',
    lineHeight: 18,
    marginBottom: 16,
  },
  exploreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    gap: 4,
  },
  exploreText: {
    fontSize: 12,
    fontFamily: 'Inter_500Medium',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 80,
    paddingHorizontal: 40,
  },
  emptyIconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 20,
    fontFamily: 'Inter_600SemiBold',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    fontFamily: 'Inter_400Regular',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 32,
  },
  findPeopleButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
  },
  findPeopleText: {
    fontSize: 14,
    fontFamily: 'Inter_500Medium',
  },
});