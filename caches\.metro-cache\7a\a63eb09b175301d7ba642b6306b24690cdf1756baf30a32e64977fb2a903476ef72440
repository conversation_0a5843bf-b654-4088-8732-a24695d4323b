{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 52, "index": 67}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Animated", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dNPzxVfn0yBoRxvhD+vE+lN7k4Q=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 131}, "end": {"line": 5, "column": 48, "index": 179}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HeaderTitle = HeaderTitle;\n  var _native = require(_dependencyMap[1], \"@react-navigation/native\");\n  var _Animated = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Animated\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/StyleSheet\"));\n  var _jsxRuntime = require(_dependencyMap[5], \"react/jsx-runtime\");\n  function HeaderTitle({\n    tintColor,\n    style,\n    ...rest\n  }) {\n    const {\n      colors,\n      fonts\n    } = (0, _native.useTheme)();\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_Animated.default.Text, {\n      role: \"heading\",\n      \"aria-level\": \"1\",\n      numberOfLines: 1,\n      ...rest,\n      style: [{\n        color: tintColor === undefined ? colors.text : tintColor\n      }, _Platform.default.select({\n        ios: fonts.bold,\n        default: fonts.medium\n      }), styles.title, style]\n    });\n  }\n  const styles = _StyleSheet.default.create({\n    title: _Platform.default.select({\n      ios: {\n        fontSize: 17\n      },\n      android: {\n        fontSize: 20\n      },\n      default: {\n        fontSize: 18\n      }\n    })\n  });\n});", "lineCount": 49, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 21, 1, 13], [8, 24, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 35, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_native"], [9, 13, 3, 0], [9, 16, 3, 0, "require"], [9, 23, 3, 0], [9, 24, 3, 0, "_dependencyMap"], [9, 38, 3, 0], [10, 2, 3, 52], [10, 6, 3, 52, "_Animated"], [10, 15, 3, 52], [10, 18, 3, 52, "_interopRequireDefault"], [10, 40, 3, 52], [10, 41, 3, 52, "require"], [10, 48, 3, 52], [10, 49, 3, 52, "_dependencyMap"], [10, 63, 3, 52], [11, 2, 3, 52], [11, 6, 3, 52, "_Platform"], [11, 15, 3, 52], [11, 18, 3, 52, "_interopRequireDefault"], [11, 40, 3, 52], [11, 41, 3, 52, "require"], [11, 48, 3, 52], [11, 49, 3, 52, "_dependencyMap"], [11, 63, 3, 52], [12, 2, 3, 52], [12, 6, 3, 52, "_StyleSheet"], [12, 17, 3, 52], [12, 20, 3, 52, "_interopRequireDefault"], [12, 42, 3, 52], [12, 43, 3, 52, "require"], [12, 50, 3, 52], [12, 51, 3, 52, "_dependencyMap"], [12, 65, 3, 52], [13, 2, 5, 0], [13, 6, 5, 0, "_jsxRuntime"], [13, 17, 5, 0], [13, 20, 5, 0, "require"], [13, 27, 5, 0], [13, 28, 5, 0, "_dependencyMap"], [13, 42, 5, 0], [14, 2, 6, 7], [14, 11, 6, 16, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [14, 22, 6, 27, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [14, 23, 6, 28], [15, 4, 7, 2, "tintColor"], [15, 13, 7, 11], [16, 4, 8, 2, "style"], [16, 9, 8, 7], [17, 4, 9, 2], [17, 7, 9, 5, "rest"], [18, 2, 10, 0], [18, 3, 10, 1], [18, 5, 10, 3], [19, 4, 11, 2], [19, 10, 11, 8], [20, 6, 12, 4, "colors"], [20, 12, 12, 10], [21, 6, 13, 4, "fonts"], [22, 4, 14, 2], [22, 5, 14, 3], [22, 8, 14, 6], [22, 12, 14, 6, "useTheme"], [22, 28, 14, 14], [22, 30, 14, 15], [22, 31, 14, 16], [23, 4, 15, 2], [23, 11, 15, 9], [23, 24, 15, 22], [23, 28, 15, 22, "_jsx"], [23, 43, 15, 26], [23, 45, 15, 27, "Animated"], [23, 62, 15, 35], [23, 63, 15, 36, "Text"], [23, 67, 15, 40], [23, 69, 15, 42], [24, 6, 16, 4, "role"], [24, 10, 16, 8], [24, 12, 16, 10], [24, 21, 16, 19], [25, 6, 17, 4], [25, 18, 17, 16], [25, 20, 17, 18], [25, 23, 17, 21], [26, 6, 18, 4, "numberOfLines"], [26, 19, 18, 17], [26, 21, 18, 19], [26, 22, 18, 20], [27, 6, 19, 4], [27, 9, 19, 7, "rest"], [27, 13, 19, 11], [28, 6, 20, 4, "style"], [28, 11, 20, 9], [28, 13, 20, 11], [28, 14, 20, 12], [29, 8, 21, 6, "color"], [29, 13, 21, 11], [29, 15, 21, 13, "tintColor"], [29, 24, 21, 22], [29, 29, 21, 27, "undefined"], [29, 38, 21, 36], [29, 41, 21, 39, "colors"], [29, 47, 21, 45], [29, 48, 21, 46, "text"], [29, 52, 21, 50], [29, 55, 21, 53, "tintColor"], [30, 6, 22, 4], [30, 7, 22, 5], [30, 9, 22, 7, "Platform"], [30, 26, 22, 15], [30, 27, 22, 16, "select"], [30, 33, 22, 22], [30, 34, 22, 23], [31, 8, 23, 6, "ios"], [31, 11, 23, 9], [31, 13, 23, 11, "fonts"], [31, 18, 23, 16], [31, 19, 23, 17, "bold"], [31, 23, 23, 21], [32, 8, 24, 6, "default"], [32, 15, 24, 13], [32, 17, 24, 15, "fonts"], [32, 22, 24, 20], [32, 23, 24, 21, "medium"], [33, 6, 25, 4], [33, 7, 25, 5], [33, 8, 25, 6], [33, 10, 25, 8, "styles"], [33, 16, 25, 14], [33, 17, 25, 15, "title"], [33, 22, 25, 20], [33, 24, 25, 22, "style"], [33, 29, 25, 27], [34, 4, 26, 2], [34, 5, 26, 3], [34, 6, 26, 4], [35, 2, 27, 0], [36, 2, 28, 0], [36, 8, 28, 6, "styles"], [36, 14, 28, 12], [36, 17, 28, 15, "StyleSheet"], [36, 36, 28, 25], [36, 37, 28, 26, "create"], [36, 43, 28, 32], [36, 44, 28, 33], [37, 4, 29, 2, "title"], [37, 9, 29, 7], [37, 11, 29, 9, "Platform"], [37, 28, 29, 17], [37, 29, 29, 18, "select"], [37, 35, 29, 24], [37, 36, 29, 25], [38, 6, 30, 4, "ios"], [38, 9, 30, 7], [38, 11, 30, 9], [39, 8, 31, 6, "fontSize"], [39, 16, 31, 14], [39, 18, 31, 16], [40, 6, 32, 4], [40, 7, 32, 5], [41, 6, 33, 4, "android"], [41, 13, 33, 11], [41, 15, 33, 13], [42, 8, 34, 6, "fontSize"], [42, 16, 34, 14], [42, 18, 34, 16], [43, 6, 35, 4], [43, 7, 35, 5], [44, 6, 36, 4, "default"], [44, 13, 36, 11], [44, 15, 36, 13], [45, 8, 37, 6, "fontSize"], [45, 16, 37, 14], [45, 18, 37, 16], [46, 6, 38, 4], [47, 4, 39, 2], [47, 5, 39, 3], [48, 2, 40, 0], [48, 3, 40, 1], [48, 4, 40, 2], [49, 0, 40, 3], [49, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;OCK;CDqB"}}, "type": "js/module"}]}