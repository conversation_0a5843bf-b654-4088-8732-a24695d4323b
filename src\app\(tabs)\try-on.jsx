import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  Pressable,
  StyleSheet,
  Dimensions,
  Alert,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { Image } from "expo-image";
import {
  Camera,
  Image as ImageIcon,
  User,
  Sparkles,
  RotateCcw,
  Download,
  Share,
  Heart,
  X,
  Check,
} from "lucide-react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTheme } from "../../utils/theme";
import {
  useFonts,
  Inter_400Regular,
  Inter_500Medium,
  Inter_600SemiBold,
} from "@expo-google-fonts/inter";
import * as ImagePicker from "expo-image-picker";
import { useUpload } from "../../utils/useUpload";

const { width: screenWidth, height: screenHeight } = Dimensions.get("window");

// Sample clothing items from closet - will be replaced with real data
const sampleClosetItems = [];

export default function TryOnScreen() {
  const insets = useSafeAreaInsets();
  const { colors, isDark } = useTheme();
  const [userPhoto, setUserPhoto] = useState(null);
  const [selectedClothes, setSelectedClothes] = useState([]);
  const [tryOnResult, setTryOnResult] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [upload, { loading: uploadLoading }] = useUpload();
  const [closetItems, setClosetItems] = useState([]);

  const [fontsLoaded] = useFonts({
    Inter_400Regular,
    Inter_500Medium,
    Inter_600SemiBold,
  });

  useEffect(() => {
    fetchClosetItems();
  }, []);

  const fetchClosetItems = async () => {
    try {
      const response = await fetch("/api/clothing-items");
      const result = await response.json();

      if (result.success) {
        setClosetItems(result.data);
      }
    } catch (error) {
      console.error("Error fetching closet items:", error);
    }
  };

  if (!fontsLoaded) {
    return null;
  }

  const takePhoto = async () => {
    const permissionResult = await ImagePicker.requestCameraPermissionsAsync();

    if (permissionResult.granted === false) {
      Alert.alert(
        "Permission Required",
        "Camera permission is needed to take photos",
      );
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [3, 4],
      quality: 0.8,
    });

    if (!result.canceled) {
      const uploadResult = await upload({
        reactNativeAsset: {
          uri: result.assets[0].uri,
          name: "user-photo.jpg",
          mimeType: "image/jpeg",
        },
      });

      if (uploadResult.url) {
        setUserPhoto(uploadResult.url);
      }
    }
  };

  const pickFromGallery = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (permissionResult.granted === false) {
      Alert.alert(
        "Permission Required",
        "Gallery permission is needed to select photos",
      );
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [3, 4],
      quality: 0.8,
    });

    if (!result.canceled) {
      const uploadResult = await upload({
        reactNativeAsset: {
          uri: result.assets[0].uri,
          name: "user-photo.jpg",
          mimeType: "image/jpeg",
        },
      });

      if (uploadResult.url) {
        setUserPhoto(uploadResult.url);
      }
    }
  };

  const toggleClothingItem = (item) => {
    setSelectedClothes((prev) => {
      const isSelected = prev.find((c) => c.id === item.id);
      if (isSelected) {
        return prev.filter((c) => c.id !== item.id);
      } else {
        return [...prev, item];
      }
    });
  };

  const generateTryOn = async () => {
    if (!userPhoto || selectedClothes.length === 0) {
      Alert.alert(
        "Missing Requirements",
        "Please upload your photo and select at least one clothing item",
      );
      return;
    }

    setIsGenerating(true);

    try {
      const clothingItemIds = selectedClothes.map((item) => item.id);

      const response = await fetch("/api/try-on", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userPhotoUrl: userPhoto,
          clothingItemIds: clothingItemIds,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setTryOnResult({
          image: result.data.result_image_url || userPhoto,
          prompt: result.data.ai_prompt,
          clothes: selectedClothes,
        });
      } else {
        throw new Error(result.error || "Try-on generation failed");
      }
    } catch (error) {
      console.error("Try-on generation failed:", error);
      Alert.alert("Error", "Failed to generate try-on. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const saveOutfit = () => {
    // TODO: Save outfit to database
    Alert.alert(
      "Outfit Saved!",
      "Your outfit has been saved to your virtual closet",
    );
  };

  const shareResult = () => {
    // TODO: Implement sharing
    Alert.alert("Share", "Sharing functionality coming soon!");
  };

  const resetTryOn = () => {
    setUserPhoto(null);
    setSelectedClothes([]);
    setTryOnResult(null);
  };

  const renderUserPhotoSection = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Your Photo
      </Text>

      {userPhoto ? (
        <View style={styles.userPhotoContainer}>
          <Image
            source={{ uri: userPhoto }}
            style={[styles.userPhoto, { backgroundColor: colors.surface }]}
            contentFit="cover"
            transition={200}
          />
          <Pressable
            style={[
              styles.removePhotoButton,
              { backgroundColor: colors.error },
            ]}
            onPress={() => setUserPhoto(null)}
          >
            <X size={16} color="#FFFFFF" />
          </Pressable>
        </View>
      ) : (
        <View style={styles.photoActions}>
          <Pressable
            style={[styles.photoButton, { backgroundColor: colors.primary }]}
            onPress={takePhoto}
            disabled={uploadLoading}
          >
            <Camera size={24} color={colors.buttonPrimaryText} />
            <Text
              style={[
                styles.photoButtonText,
                { color: colors.buttonPrimaryText },
              ]}
            >
              Take Photo
            </Text>
          </Pressable>

          <Pressable
            style={[
              styles.photoButton,
              {
                backgroundColor: colors.surface,
                borderWidth: 1,
                borderColor: colors.border,
              },
            ]}
            onPress={pickFromGallery}
            disabled={uploadLoading}
          >
            <ImageIcon size={24} color={colors.text} />
            <Text style={[styles.photoButtonText, { color: colors.text }]}>
              Choose from Gallery
            </Text>
          </Pressable>
        </View>
      )}
    </View>
  );

  const renderClothingSelection = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Select Clothes
      </Text>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.clothingScrollContainer}
      >
        {closetItems.map((item) => {
          const isSelected = selectedClothes.find((c) => c.id === item.id);

          return (
            <Pressable
              key={item.id}
              style={[
                styles.clothingCard,
                { backgroundColor: colors.cardBackground },
                isSelected && { borderColor: colors.primary, borderWidth: 2 },
              ]}
              onPress={() => toggleClothingItem(item)}
            >
              <Image
                source={{ uri: item.image_url }}
                style={styles.clothingCardImage}
                contentFit="cover"
                transition={200}
              />
              <Text
                style={[styles.clothingCardText, { color: colors.text }]}
                numberOfLines={2}
              >
                {item.name}
              </Text>

              {isSelected && (
                <View
                  style={[
                    styles.selectedBadge,
                    { backgroundColor: colors.primary },
                  ]}
                >
                  <Check size={12} color={colors.buttonPrimaryText} />
                </View>
              )}
            </Pressable>
          );
        })}
      </ScrollView>
    </View>
  );

  const renderTryOnResult = () => {
    if (!tryOnResult) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Try-On Result
        </Text>

        <View style={styles.resultContainer}>
          <Image
            source={{ uri: tryOnResult.image }}
            style={[styles.resultImage, { backgroundColor: colors.surface }]}
            contentFit="cover"
            transition={200}
          />

          <View style={styles.resultActions}>
            <Pressable
              style={[
                styles.resultActionButton,
                { backgroundColor: colors.surface },
              ]}
              onPress={saveOutfit}
            >
              <Heart size={20} color={colors.text} />
              <Text style={[styles.resultActionText, { color: colors.text }]}>
                Save
              </Text>
            </Pressable>

            <Pressable
              style={[
                styles.resultActionButton,
                { backgroundColor: colors.surface },
              ]}
              onPress={shareResult}
            >
              <Share size={20} color={colors.text} />
              <Text style={[styles.resultActionText, { color: colors.text }]}>
                Share
              </Text>
            </Pressable>

            <Pressable
              style={[
                styles.resultActionButton,
                { backgroundColor: colors.surface },
              ]}
              onPress={() => setTryOnResult(null)}
            >
              <RotateCcw size={20} color={colors.text} />
              <Text style={[styles.resultActionText, { color: colors.text }]}>
                Try Again
              </Text>
            </Pressable>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style={isDark ? "light" : "dark"} />

      {/* Header */}
      <View
        style={[
          styles.header,
          {
            paddingTop: insets.top + 16,
            backgroundColor: colors.background,
            borderBottomColor: colors.border,
          },
        ]}
      >
        <Text style={[styles.title, { color: colors.text }]}>
          Virtual Try-On
        </Text>
        <Pressable
          style={[styles.resetButton, { backgroundColor: colors.surface }]}
          onPress={resetTryOn}
        >
          <RotateCcw size={20} color={colors.text} />
        </Pressable>
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={[
          styles.contentContainer,
          { paddingBottom: insets.bottom + 120 },
        ]}
        showsVerticalScrollIndicator={false}
      >
        {renderUserPhotoSection()}
        {renderClothingSelection()}
        {renderTryOnResult()}
      </ScrollView>

      {/* Generate Button */}
      {userPhoto && selectedClothes.length > 0 && !tryOnResult && (
        <View
          style={[
            styles.generateButtonContainer,
            {
              backgroundColor: colors.background,
              paddingBottom: insets.bottom + 20,
            },
          ]}
        >
          <Pressable
            style={[
              styles.generateButton,
              { backgroundColor: colors.primary },
              isGenerating && { opacity: 0.7 },
            ]}
            onPress={generateTryOn}
            disabled={isGenerating}
          >
            <Sparkles size={20} color={colors.buttonPrimaryText} />
            <Text
              style={[
                styles.generateButtonText,
                { color: colors.buttonPrimaryText },
              ]}
            >
              {isGenerating ? "Generating..." : "Generate Try-On"}
            </Text>
          </Pressable>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 28,
    fontFamily: "Inter_600SemiBold",
  },
  resetButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: "Inter_600SemiBold",
    marginBottom: 16,
  },
  userPhotoContainer: {
    position: "relative",
    alignSelf: "center",
  },
  userPhoto: {
    width: 200,
    height: 260,
    borderRadius: 16,
  },
  removePhotoButton: {
    position: "absolute",
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  photoActions: {
    gap: 12,
  },
  photoButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 12,
  },
  photoButtonText: {
    fontSize: 16,
    fontFamily: "Inter_500Medium",
  },
  clothingScrollContainer: {
    paddingRight: 20,
    gap: 12,
  },
  clothingCard: {
    width: 120,
    borderRadius: 12,
    overflow: "hidden",
    position: "relative",
  },
  clothingCardImage: {
    width: 120,
    height: 140,
  },
  clothingCardText: {
    fontSize: 12,
    fontFamily: "Inter_500Medium",
    padding: 8,
    lineHeight: 16,
  },
  selectedBadge: {
    position: "absolute",
    top: 8,
    right: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
  },
  resultContainer: {
    alignItems: "center",
  },
  resultImage: {
    width: 240,
    height: 320,
    borderRadius: 16,
    marginBottom: 20,
  },
  resultActions: {
    flexDirection: "row",
    gap: 12,
  },
  resultActionButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 20,
    gap: 6,
  },
  resultActionText: {
    fontSize: 14,
    fontFamily: "Inter_500Medium",
  },
  generateButtonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingTop: 16,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  generateButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  generateButtonText: {
    fontSize: 16,
    fontFamily: "Inter_600SemiBold",
  },
});
