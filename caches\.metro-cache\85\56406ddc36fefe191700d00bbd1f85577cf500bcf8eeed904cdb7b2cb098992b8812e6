{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 37, "column": 27, "index": 1508}, "end": {"line": 37, "column": 43, "index": 1524}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var __createBinding = this && this.__createBinding || (Object.create ? function (o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = {\n        enumerable: true,\n        get: function () {\n          return m[k];\n        }\n      };\n    }\n    Object.defineProperty(o, k2, desc);\n  } : function (o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n  });\n  var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function (o, v) {\n    Object.defineProperty(o, \"default\", {\n      enumerable: true,\n      value: v\n    });\n  } : function (o, v) {\n    o[\"default\"] = v;\n  });\n  var __importStar = this && this.__importStar || function () {\n    var ownKeys = function (o) {\n      ownKeys = Object.getOwnPropertyNames || function (o) {\n        var ar = [];\n        for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n        return ar;\n      };\n      return ownKeys(o);\n    };\n    return function (mod) {\n      if (mod && mod.__esModule) return mod;\n      var result = {};\n      if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n      __setModuleDefault(result, mod);\n      return result;\n    };\n  }();\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useDocumentTitle = useDocumentTitle;\n  const React = __importStar(require(_dependencyMap[0], \"react\"));\n  // import type { DocumentTitleOptions } from './types';\n  /**\n   * Set the document title for the active screen\n   */\n  function useDocumentTitle(ref, {\n    enabled = true,\n    formatter = (options, route) => options?.title ?? route?.name\n  } = {}) {\n    React.useEffect(() => {\n      if (!enabled) {\n        return;\n      }\n      const navigation = ref.current;\n      if (navigation) {\n        const title = formatter(navigation.getCurrentOptions(), navigation.getCurrentRoute());\n        document.title = title;\n      }\n      return navigation?.addListener('options', e => {\n        const title = formatter(e.data.options, navigation?.getCurrentRoute());\n        document.title = title;\n      });\n    });\n  }\n});", "lineCount": 73, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [4, 6, 2, 4, "__createBinding"], [4, 21, 2, 19], [4, 24, 2, 23], [4, 28, 2, 27], [4, 32, 2, 31], [4, 36, 2, 35], [4, 37, 2, 36, "__createBinding"], [4, 52, 2, 51], [4, 57, 2, 57, "Object"], [4, 63, 2, 63], [4, 64, 2, 64, "create"], [4, 70, 2, 70], [4, 73, 2, 74], [4, 83, 2, 83, "o"], [4, 84, 2, 84], [4, 86, 2, 86, "m"], [4, 87, 2, 87], [4, 89, 2, 89, "k"], [4, 90, 2, 90], [4, 92, 2, 92, "k2"], [4, 94, 2, 94], [4, 96, 2, 96], [5, 4, 3, 4], [5, 8, 3, 8, "k2"], [5, 10, 3, 10], [5, 15, 3, 15, "undefined"], [5, 24, 3, 24], [5, 26, 3, 26, "k2"], [5, 28, 3, 28], [5, 31, 3, 31, "k"], [5, 32, 3, 32], [6, 4, 4, 4], [6, 8, 4, 8, "desc"], [6, 12, 4, 12], [6, 15, 4, 15, "Object"], [6, 21, 4, 21], [6, 22, 4, 22, "getOwnPropertyDescriptor"], [6, 46, 4, 46], [6, 47, 4, 47, "m"], [6, 48, 4, 48], [6, 50, 4, 50, "k"], [6, 51, 4, 51], [6, 52, 4, 52], [7, 4, 5, 4], [7, 8, 5, 8], [7, 9, 5, 9, "desc"], [7, 13, 5, 13], [7, 18, 5, 18], [7, 23, 5, 23], [7, 27, 5, 27, "desc"], [7, 31, 5, 31], [7, 34, 5, 34], [7, 35, 5, 35, "m"], [7, 36, 5, 36], [7, 37, 5, 37, "__esModule"], [7, 47, 5, 47], [7, 50, 5, 50, "desc"], [7, 54, 5, 54], [7, 55, 5, 55, "writable"], [7, 63, 5, 63], [7, 67, 5, 67, "desc"], [7, 71, 5, 71], [7, 72, 5, 72, "configurable"], [7, 84, 5, 84], [7, 85, 5, 85], [7, 87, 5, 87], [8, 6, 6, 6, "desc"], [8, 10, 6, 10], [8, 13, 6, 13], [9, 8, 6, 15, "enumerable"], [9, 18, 6, 25], [9, 20, 6, 27], [9, 24, 6, 31], [10, 8, 6, 33, "get"], [10, 11, 6, 36], [10, 13, 6, 38], [10, 22, 6, 38, "get"], [10, 23, 6, 38], [10, 25, 6, 49], [11, 10, 6, 51], [11, 17, 6, 58, "m"], [11, 18, 6, 59], [11, 19, 6, 60, "k"], [11, 20, 6, 61], [11, 21, 6, 62], [12, 8, 6, 64], [13, 6, 6, 66], [13, 7, 6, 67], [14, 4, 7, 4], [15, 4, 8, 4, "Object"], [15, 10, 8, 10], [15, 11, 8, 11, "defineProperty"], [15, 25, 8, 25], [15, 26, 8, 26, "o"], [15, 27, 8, 27], [15, 29, 8, 29, "k2"], [15, 31, 8, 31], [15, 33, 8, 33, "desc"], [15, 37, 8, 37], [15, 38, 8, 38], [16, 2, 9, 0], [16, 3, 9, 1], [16, 6, 9, 6], [16, 16, 9, 15, "o"], [16, 17, 9, 16], [16, 19, 9, 18, "m"], [16, 20, 9, 19], [16, 22, 9, 21, "k"], [16, 23, 9, 22], [16, 25, 9, 24, "k2"], [16, 27, 9, 26], [16, 29, 9, 28], [17, 4, 10, 4], [17, 8, 10, 8, "k2"], [17, 10, 10, 10], [17, 15, 10, 15, "undefined"], [17, 24, 10, 24], [17, 26, 10, 26, "k2"], [17, 28, 10, 28], [17, 31, 10, 31, "k"], [17, 32, 10, 32], [18, 4, 11, 4, "o"], [18, 5, 11, 5], [18, 6, 11, 6, "k2"], [18, 8, 11, 8], [18, 9, 11, 9], [18, 12, 11, 12, "m"], [18, 13, 11, 13], [18, 14, 11, 14, "k"], [18, 15, 11, 15], [18, 16, 11, 16], [19, 2, 12, 0], [19, 3, 12, 2], [19, 4, 12, 3], [20, 2, 13, 0], [20, 6, 13, 4, "__setModuleDefault"], [20, 24, 13, 22], [20, 27, 13, 26], [20, 31, 13, 30], [20, 35, 13, 34], [20, 39, 13, 38], [20, 40, 13, 39, "__setModuleDefault"], [20, 58, 13, 57], [20, 63, 13, 63, "Object"], [20, 69, 13, 69], [20, 70, 13, 70, "create"], [20, 76, 13, 76], [20, 79, 13, 80], [20, 89, 13, 89, "o"], [20, 90, 13, 90], [20, 92, 13, 92, "v"], [20, 93, 13, 93], [20, 95, 13, 95], [21, 4, 14, 4, "Object"], [21, 10, 14, 10], [21, 11, 14, 11, "defineProperty"], [21, 25, 14, 25], [21, 26, 14, 26, "o"], [21, 27, 14, 27], [21, 29, 14, 29], [21, 38, 14, 38], [21, 40, 14, 40], [22, 6, 14, 42, "enumerable"], [22, 16, 14, 52], [22, 18, 14, 54], [22, 22, 14, 58], [23, 6, 14, 60, "value"], [23, 11, 14, 65], [23, 13, 14, 67, "v"], [24, 4, 14, 69], [24, 5, 14, 70], [24, 6, 14, 71], [25, 2, 15, 0], [25, 3, 15, 1], [25, 6, 15, 5], [25, 16, 15, 14, "o"], [25, 17, 15, 15], [25, 19, 15, 17, "v"], [25, 20, 15, 18], [25, 22, 15, 20], [26, 4, 16, 4, "o"], [26, 5, 16, 5], [26, 6, 16, 6], [26, 15, 16, 15], [26, 16, 16, 16], [26, 19, 16, 19, "v"], [26, 20, 16, 20], [27, 2, 17, 0], [27, 3, 17, 1], [27, 4, 17, 2], [28, 2, 18, 0], [28, 6, 18, 4, "__importStar"], [28, 18, 18, 16], [28, 21, 18, 20], [28, 25, 18, 24], [28, 29, 18, 28], [28, 33, 18, 32], [28, 34, 18, 33, "__importStar"], [28, 46, 18, 45], [28, 50, 18, 51], [28, 62, 18, 63], [29, 4, 19, 4], [29, 8, 19, 8, "ownKeys"], [29, 15, 19, 15], [29, 18, 19, 18], [29, 27, 19, 18, "ownKeys"], [29, 28, 19, 27, "o"], [29, 29, 19, 28], [29, 31, 19, 30], [30, 6, 20, 8, "ownKeys"], [30, 13, 20, 15], [30, 16, 20, 18, "Object"], [30, 22, 20, 24], [30, 23, 20, 25, "getOwnPropertyNames"], [30, 42, 20, 44], [30, 46, 20, 48], [30, 56, 20, 58, "o"], [30, 57, 20, 59], [30, 59, 20, 61], [31, 8, 21, 12], [31, 12, 21, 16, "ar"], [31, 14, 21, 18], [31, 17, 21, 21], [31, 19, 21, 23], [32, 8, 22, 12], [32, 13, 22, 17], [32, 17, 22, 21, "k"], [32, 18, 22, 22], [32, 22, 22, 26, "o"], [32, 23, 22, 27], [32, 25, 22, 29], [32, 29, 22, 33, "Object"], [32, 35, 22, 39], [32, 36, 22, 40, "prototype"], [32, 45, 22, 49], [32, 46, 22, 50, "hasOwnProperty"], [32, 60, 22, 64], [32, 61, 22, 65, "call"], [32, 65, 22, 69], [32, 66, 22, 70, "o"], [32, 67, 22, 71], [32, 69, 22, 73, "k"], [32, 70, 22, 74], [32, 71, 22, 75], [32, 73, 22, 77, "ar"], [32, 75, 22, 79], [32, 76, 22, 80, "ar"], [32, 78, 22, 82], [32, 79, 22, 83, "length"], [32, 85, 22, 89], [32, 86, 22, 90], [32, 89, 22, 93, "k"], [32, 90, 22, 94], [33, 8, 23, 12], [33, 15, 23, 19, "ar"], [33, 17, 23, 21], [34, 6, 24, 8], [34, 7, 24, 9], [35, 6, 25, 8], [35, 13, 25, 15, "ownKeys"], [35, 20, 25, 22], [35, 21, 25, 23, "o"], [35, 22, 25, 24], [35, 23, 25, 25], [36, 4, 26, 4], [36, 5, 26, 5], [37, 4, 27, 4], [37, 11, 27, 11], [37, 21, 27, 21, "mod"], [37, 24, 27, 24], [37, 26, 27, 26], [38, 6, 28, 8], [38, 10, 28, 12, "mod"], [38, 13, 28, 15], [38, 17, 28, 19, "mod"], [38, 20, 28, 22], [38, 21, 28, 23, "__esModule"], [38, 31, 28, 33], [38, 33, 28, 35], [38, 40, 28, 42, "mod"], [38, 43, 28, 45], [39, 6, 29, 8], [39, 10, 29, 12, "result"], [39, 16, 29, 18], [39, 19, 29, 21], [39, 20, 29, 22], [39, 21, 29, 23], [40, 6, 30, 8], [40, 10, 30, 12, "mod"], [40, 13, 30, 15], [40, 17, 30, 19], [40, 21, 30, 23], [40, 23, 30, 25], [40, 28, 30, 30], [40, 32, 30, 34, "k"], [40, 33, 30, 35], [40, 36, 30, 38, "ownKeys"], [40, 43, 30, 45], [40, 44, 30, 46, "mod"], [40, 47, 30, 49], [40, 48, 30, 50], [40, 50, 30, 52, "i"], [40, 51, 30, 53], [40, 54, 30, 56], [40, 55, 30, 57], [40, 57, 30, 59, "i"], [40, 58, 30, 60], [40, 61, 30, 63, "k"], [40, 62, 30, 64], [40, 63, 30, 65, "length"], [40, 69, 30, 71], [40, 71, 30, 73, "i"], [40, 72, 30, 74], [40, 74, 30, 76], [40, 76, 30, 78], [40, 80, 30, 82, "k"], [40, 81, 30, 83], [40, 82, 30, 84, "i"], [40, 83, 30, 85], [40, 84, 30, 86], [40, 89, 30, 91], [40, 98, 30, 100], [40, 100, 30, 102, "__createBinding"], [40, 115, 30, 117], [40, 116, 30, 118, "result"], [40, 122, 30, 124], [40, 124, 30, 126, "mod"], [40, 127, 30, 129], [40, 129, 30, 131, "k"], [40, 130, 30, 132], [40, 131, 30, 133, "i"], [40, 132, 30, 134], [40, 133, 30, 135], [40, 134, 30, 136], [41, 6, 31, 8, "__setModuleDefault"], [41, 24, 31, 26], [41, 25, 31, 27, "result"], [41, 31, 31, 33], [41, 33, 31, 35, "mod"], [41, 36, 31, 38], [41, 37, 31, 39], [42, 6, 32, 8], [42, 13, 32, 15, "result"], [42, 19, 32, 21], [43, 4, 33, 4], [43, 5, 33, 5], [44, 2, 34, 0], [44, 3, 34, 1], [44, 4, 34, 3], [44, 5, 34, 4], [45, 2, 35, 0, "Object"], [45, 8, 35, 6], [45, 9, 35, 7, "defineProperty"], [45, 23, 35, 21], [45, 24, 35, 22, "exports"], [45, 31, 35, 29], [45, 33, 35, 31], [45, 45, 35, 43], [45, 47, 35, 45], [46, 4, 35, 47, "value"], [46, 9, 35, 52], [46, 11, 35, 54], [47, 2, 35, 59], [47, 3, 35, 60], [47, 4, 35, 61], [48, 2, 36, 0, "exports"], [48, 9, 36, 7], [48, 10, 36, 8, "useDocumentTitle"], [48, 26, 36, 24], [48, 29, 36, 27, "useDocumentTitle"], [48, 45, 36, 43], [49, 2, 37, 0], [49, 8, 37, 6, "React"], [49, 13, 37, 11], [49, 16, 37, 14, "__importStar"], [49, 28, 37, 26], [49, 29, 37, 27, "require"], [49, 36, 37, 34], [49, 37, 37, 34, "_dependencyMap"], [49, 51, 37, 34], [49, 63, 37, 42], [49, 64, 37, 43], [49, 65, 37, 44], [50, 2, 38, 0], [51, 2, 39, 0], [52, 0, 40, 0], [53, 0, 41, 0], [54, 2, 42, 0], [54, 11, 42, 9, "useDocumentTitle"], [54, 27, 42, 25, "useDocumentTitle"], [54, 28, 42, 26, "ref"], [54, 31, 42, 29], [54, 33, 42, 31], [55, 4, 42, 33, "enabled"], [55, 11, 42, 40], [55, 14, 42, 43], [55, 18, 42, 47], [56, 4, 42, 49, "formatter"], [56, 13, 42, 58], [56, 16, 42, 61, "formatter"], [56, 17, 42, 62, "options"], [56, 24, 42, 69], [56, 26, 42, 71, "route"], [56, 31, 42, 76], [56, 36, 42, 81, "options"], [56, 43, 42, 88], [56, 45, 42, 90, "title"], [56, 50, 42, 95], [56, 54, 42, 99, "route"], [56, 59, 42, 104], [56, 61, 42, 106, "name"], [57, 2, 42, 112], [57, 3, 42, 113], [57, 6, 42, 116], [57, 7, 42, 117], [57, 8, 42, 118], [57, 10, 42, 120], [58, 4, 43, 4, "React"], [58, 9, 43, 9], [58, 10, 43, 10, "useEffect"], [58, 19, 43, 19], [58, 20, 43, 20], [58, 26, 43, 26], [59, 6, 44, 8], [59, 10, 44, 12], [59, 11, 44, 13, "enabled"], [59, 18, 44, 20], [59, 20, 44, 22], [60, 8, 45, 12], [61, 6, 46, 8], [62, 6, 47, 8], [62, 12, 47, 14, "navigation"], [62, 22, 47, 24], [62, 25, 47, 27, "ref"], [62, 28, 47, 30], [62, 29, 47, 31, "current"], [62, 36, 47, 38], [63, 6, 48, 8], [63, 10, 48, 12, "navigation"], [63, 20, 48, 22], [63, 22, 48, 24], [64, 8, 49, 12], [64, 14, 49, 18, "title"], [64, 19, 49, 23], [64, 22, 49, 26, "formatter"], [64, 31, 49, 35], [64, 32, 49, 36, "navigation"], [64, 42, 49, 46], [64, 43, 49, 47, "getCurrentOptions"], [64, 60, 49, 64], [64, 61, 49, 65], [64, 62, 49, 66], [64, 64, 49, 68, "navigation"], [64, 74, 49, 78], [64, 75, 49, 79, "getCurrentRoute"], [64, 90, 49, 94], [64, 91, 49, 95], [64, 92, 49, 96], [64, 93, 49, 97], [65, 8, 50, 12, "document"], [65, 16, 50, 20], [65, 17, 50, 21, "title"], [65, 22, 50, 26], [65, 25, 50, 29, "title"], [65, 30, 50, 34], [66, 6, 51, 8], [67, 6, 52, 8], [67, 13, 52, 15, "navigation"], [67, 23, 52, 25], [67, 25, 52, 27, "addListener"], [67, 36, 52, 38], [67, 37, 52, 39], [67, 46, 52, 48], [67, 48, 52, 51, "e"], [67, 49, 52, 52], [67, 53, 52, 57], [68, 8, 53, 12], [68, 14, 53, 18, "title"], [68, 19, 53, 23], [68, 22, 53, 26, "formatter"], [68, 31, 53, 35], [68, 32, 53, 36, "e"], [68, 33, 53, 37], [68, 34, 53, 38, "data"], [68, 38, 53, 42], [68, 39, 53, 43, "options"], [68, 46, 53, 50], [68, 48, 53, 52, "navigation"], [68, 58, 53, 62], [68, 60, 53, 64, "getCurrentRoute"], [68, 75, 53, 79], [68, 76, 53, 80], [68, 77, 53, 81], [68, 78, 53, 82], [69, 8, 54, 12, "document"], [69, 16, 54, 20], [69, 17, 54, 21, "title"], [69, 22, 54, 26], [69, 25, 54, 29, "title"], [69, 30, 54, 34], [70, 6, 55, 8], [70, 7, 55, 9], [70, 8, 55, 10], [71, 4, 56, 4], [71, 5, 56, 5], [71, 6, 56, 6], [72, 2, 57, 0], [73, 0, 57, 1], [73, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "desc.get", "ownKeys", "useDocumentTitle", "React.useEffect$argument_0", "navigation.addListener$argument_1"], "mappings": "AAA;0ECC;sCCI,2BD;CDG,KC;CDG;gFCC;CDE,IC;CDE;mDCC;kBEC;gDFC;SEI;KFE;CDQ;AIQ,6DH,iDG;oBCC;kDCS;SDG;KDC;CJC"}}, "type": "js/module"}]}