{"dependencies": [{"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 56, "index": 56}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getExpoGoProjectConfig = getExpoGoProjectConfig;\n  exports.isRunningInExpoGo = isRunningInExpoGo;\n  var _expoModulesCore = require(_dependencyMap[0], \"expo-modules-core\");\n  // ExpoGo module is available only when the app is run in Expo Go,\n  // otherwise we use `null` instead of throwing an error.\n  var NativeExpoGoModule = (() => {\n    try {\n      return (0, _expoModulesCore.requireNativeModule)('ExpoGo');\n    } catch {\n      return null;\n    }\n  })();\n\n  /**\n   * Returns a boolean value whether the app is running in Expo Go.\n   */\n  function isRunningInExpoGo() {\n    return NativeExpoGoModule != null;\n  }\n\n  /**\n   * @hidden\n   * Returns an Expo Go project config from the manifest or `null` if the app is not running in Expo Go.\n   */\n  function getExpoGoProjectConfig() {\n    return NativeExpoGoModule?.projectConfig ?? null;\n  }\n});", "lineCount": 32, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_expoModulesCore"], [7, 22, 1, 0], [7, 25, 1, 0, "require"], [7, 32, 1, 0], [7, 33, 1, 0, "_dependencyMap"], [7, 47, 1, 0], [8, 2, 30, 0], [9, 2, 31, 0], [10, 2, 32, 0], [10, 6, 32, 6, "NativeExpoGoModule"], [10, 24, 32, 24], [10, 27, 32, 27], [10, 28, 32, 28], [10, 34, 32, 55], [11, 4, 33, 2], [11, 8, 33, 6], [12, 6, 34, 4], [12, 13, 34, 11], [12, 17, 34, 11, "requireNativeModule"], [12, 53, 34, 30], [12, 55, 34, 31], [12, 63, 34, 39], [12, 64, 34, 40], [13, 4, 35, 2], [13, 5, 35, 3], [13, 6, 35, 4], [13, 12, 35, 10], [14, 6, 36, 4], [14, 13, 36, 11], [14, 17, 36, 15], [15, 4, 37, 2], [16, 2, 38, 0], [16, 3, 38, 1], [16, 5, 38, 3], [16, 6, 38, 4], [18, 2, 40, 0], [19, 0, 41, 0], [20, 0, 42, 0], [21, 2, 43, 7], [21, 11, 43, 16, "isRunningInExpoGo"], [21, 28, 43, 33, "isRunningInExpoGo"], [21, 29, 43, 33], [21, 31, 43, 45], [22, 4, 44, 2], [22, 11, 44, 9, "NativeExpoGoModule"], [22, 29, 44, 27], [22, 33, 44, 31], [22, 37, 44, 35], [23, 2, 45, 0], [25, 2, 47, 0], [26, 0, 48, 0], [27, 0, 49, 0], [28, 0, 50, 0], [29, 2, 51, 7], [29, 11, 51, 16, "getExpoGoProjectConfig"], [29, 33, 51, 38, "getExpoGoProjectConfig"], [29, 34, 51, 38], [29, 36, 51, 69], [30, 4, 52, 2], [30, 11, 52, 9, "NativeExpoGoModule"], [30, 29, 52, 27], [30, 31, 52, 29, "projectConfig"], [30, 44, 52, 42], [30, 48, 52, 46], [30, 52, 52, 50], [31, 2, 53, 0], [32, 0, 53, 1], [32, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "isRunningInExpoGo", "getExpoGoProjectConfig"], "mappings": "AAA;4BC+B;CDM;OEK;CFE;OGM;CHE"}}, "type": "js/module"}]}