{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-css-interop", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "GojJv+4gs9E3DINH6CdbjjB/YmY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 319}, "end": {"line": 3, "column": 31, "index": 350}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/FlatList", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "DmaJKCBnIi5ZEPZkQdrt7FKBDEo=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Switch", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "azIZPyCppTdhkoWVIy+kAtKhyQg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TextInput", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "DmXc1F5dPYWntVgqRwh73w0VngA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "../handlers/createNativeWrapper", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 486}, "end": {"line": 5, "column": 66, "index": 552}}], "key": "uy698ALVvfnGA/D1vVARxm+VSzo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.TextInput = exports.Switch = exports.ScrollView = exports.RefreshControl = exports.FlatList = exports.DrawerLayoutAndroid = void 0;\n  var _ReactNativeCSSInterop = _interopRequireWildcard(require(_dependencyMap[1], \"react-native-css-interop\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _FlatList = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/FlatList\"));\n  var _Switch = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Switch\"));\n  var _TextInput = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/TextInput\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/ScrollView\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/View\"));\n  var _createNativeWrapper = _interopRequireDefault(require(_dependencyMap[8], \"../handlers/createNativeWrapper\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function _extends() {\n    _extends = Object.assign || function (target) {\n      for (var i = 1; i < arguments.length; i++) {\n        var source = arguments[i];\n        for (var key in source) {\n          if (Object.prototype.hasOwnProperty.call(source, key)) {\n            target[key] = source[key];\n          }\n        }\n      }\n      return target;\n    };\n    return _extends.apply(this, arguments);\n  }\n  const ScrollView = exports.ScrollView = (0, _createNativeWrapper.default)(_ScrollView.default, {\n    disallowInterruption: false\n  });\n  const Switch = exports.Switch = (0, _createNativeWrapper.default)(_Switch.default, {\n    shouldCancelWhenOutside: false,\n    shouldActivateOnStart: true,\n    disallowInterruption: true\n  });\n  const TextInput = exports.TextInput = (0, _createNativeWrapper.default)(_TextInput.default);\n  const DrawerLayoutAndroid = () => {\n    console.warn('DrawerLayoutAndroid is not supported on web!');\n    return /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_View.default, null);\n  }; // RefreshControl is implemented as a functional component, rendering a View\n  // NativeViewGestureHandler needs to set a ref on its child, which cannot be done\n  // on functional components\n  exports.DrawerLayoutAndroid = DrawerLayoutAndroid;\n  const RefreshControl = exports.RefreshControl = (0, _createNativeWrapper.default)(_View.default);\n  const FlatList = exports.FlatList = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_FlatList.default, _extends({\n    ref: ref\n  }, props, {\n    renderScrollComponent: scrollProps => /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(ScrollView, scrollProps)\n  })));\n});", "lineCount": 52, "map": [[8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 3, 31], [9, 6, 3, 31, "_FlatList"], [9, 15, 3, 31], [9, 18, 3, 31, "_interopRequireDefault"], [9, 40, 3, 31], [9, 41, 3, 31, "require"], [9, 48, 3, 31], [9, 49, 3, 31, "_dependencyMap"], [9, 63, 3, 31], [10, 2, 3, 31], [10, 6, 3, 31, "_Switch"], [10, 13, 3, 31], [10, 16, 3, 31, "_interopRequireDefault"], [10, 38, 3, 31], [10, 39, 3, 31, "require"], [10, 46, 3, 31], [10, 47, 3, 31, "_dependencyMap"], [10, 61, 3, 31], [11, 2, 3, 31], [11, 6, 3, 31, "_TextInput"], [11, 16, 3, 31], [11, 19, 3, 31, "_interopRequireDefault"], [11, 41, 3, 31], [11, 42, 3, 31, "require"], [11, 49, 3, 31], [11, 50, 3, 31, "_dependencyMap"], [11, 64, 3, 31], [12, 2, 3, 31], [12, 6, 3, 31, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [12, 17, 3, 31], [12, 20, 3, 31, "_interopRequireDefault"], [12, 42, 3, 31], [12, 43, 3, 31, "require"], [12, 50, 3, 31], [12, 51, 3, 31, "_dependencyMap"], [12, 65, 3, 31], [13, 2, 3, 31], [13, 6, 3, 31, "_View"], [13, 11, 3, 31], [13, 14, 3, 31, "_interopRequireDefault"], [13, 36, 3, 31], [13, 37, 3, 31, "require"], [13, 44, 3, 31], [13, 45, 3, 31, "_dependencyMap"], [13, 59, 3, 31], [14, 2, 5, 0], [14, 6, 5, 0, "_createNativeWrapper"], [14, 26, 5, 0], [14, 29, 5, 0, "_interopRequireDefault"], [14, 51, 5, 0], [14, 52, 5, 0, "require"], [14, 59, 5, 0], [14, 60, 5, 0, "_dependencyMap"], [14, 74, 5, 0], [15, 2, 5, 66], [15, 11, 5, 66, "_interopRequireWildcard"], [15, 35, 5, 66, "e"], [15, 36, 5, 66], [15, 38, 5, 66, "t"], [15, 39, 5, 66], [15, 68, 5, 66, "WeakMap"], [15, 75, 5, 66], [15, 81, 5, 66, "r"], [15, 82, 5, 66], [15, 89, 5, 66, "WeakMap"], [15, 96, 5, 66], [15, 100, 5, 66, "n"], [15, 101, 5, 66], [15, 108, 5, 66, "WeakMap"], [15, 115, 5, 66], [15, 127, 5, 66, "_interopRequireWildcard"], [15, 150, 5, 66], [15, 162, 5, 66, "_interopRequireWildcard"], [15, 163, 5, 66, "e"], [15, 164, 5, 66], [15, 166, 5, 66, "t"], [15, 167, 5, 66], [15, 176, 5, 66, "t"], [15, 177, 5, 66], [15, 181, 5, 66, "e"], [15, 182, 5, 66], [15, 186, 5, 66, "e"], [15, 187, 5, 66], [15, 188, 5, 66, "__esModule"], [15, 198, 5, 66], [15, 207, 5, 66, "e"], [15, 208, 5, 66], [15, 214, 5, 66, "o"], [15, 215, 5, 66], [15, 217, 5, 66, "i"], [15, 218, 5, 66], [15, 220, 5, 66, "f"], [15, 221, 5, 66], [15, 226, 5, 66, "__proto__"], [15, 235, 5, 66], [15, 243, 5, 66, "default"], [15, 250, 5, 66], [15, 252, 5, 66, "e"], [15, 253, 5, 66], [15, 270, 5, 66, "e"], [15, 271, 5, 66], [15, 294, 5, 66, "e"], [15, 295, 5, 66], [15, 320, 5, 66, "e"], [15, 321, 5, 66], [15, 330, 5, 66, "f"], [15, 331, 5, 66], [15, 337, 5, 66, "o"], [15, 338, 5, 66], [15, 341, 5, 66, "t"], [15, 342, 5, 66], [15, 345, 5, 66, "n"], [15, 346, 5, 66], [15, 349, 5, 66, "r"], [15, 350, 5, 66], [15, 358, 5, 66, "o"], [15, 359, 5, 66], [15, 360, 5, 66, "has"], [15, 363, 5, 66], [15, 364, 5, 66, "e"], [15, 365, 5, 66], [15, 375, 5, 66, "o"], [15, 376, 5, 66], [15, 377, 5, 66, "get"], [15, 380, 5, 66], [15, 381, 5, 66, "e"], [15, 382, 5, 66], [15, 385, 5, 66, "o"], [15, 386, 5, 66], [15, 387, 5, 66, "set"], [15, 390, 5, 66], [15, 391, 5, 66, "e"], [15, 392, 5, 66], [15, 394, 5, 66, "f"], [15, 395, 5, 66], [15, 411, 5, 66, "t"], [15, 412, 5, 66], [15, 416, 5, 66, "e"], [15, 417, 5, 66], [15, 433, 5, 66, "t"], [15, 434, 5, 66], [15, 441, 5, 66, "hasOwnProperty"], [15, 455, 5, 66], [15, 456, 5, 66, "call"], [15, 460, 5, 66], [15, 461, 5, 66, "e"], [15, 462, 5, 66], [15, 464, 5, 66, "t"], [15, 465, 5, 66], [15, 472, 5, 66, "i"], [15, 473, 5, 66], [15, 477, 5, 66, "o"], [15, 478, 5, 66], [15, 481, 5, 66, "Object"], [15, 487, 5, 66], [15, 488, 5, 66, "defineProperty"], [15, 502, 5, 66], [15, 507, 5, 66, "Object"], [15, 513, 5, 66], [15, 514, 5, 66, "getOwnPropertyDescriptor"], [15, 538, 5, 66], [15, 539, 5, 66, "e"], [15, 540, 5, 66], [15, 542, 5, 66, "t"], [15, 543, 5, 66], [15, 550, 5, 66, "i"], [15, 551, 5, 66], [15, 552, 5, 66, "get"], [15, 555, 5, 66], [15, 559, 5, 66, "i"], [15, 560, 5, 66], [15, 561, 5, 66, "set"], [15, 564, 5, 66], [15, 568, 5, 66, "o"], [15, 569, 5, 66], [15, 570, 5, 66, "f"], [15, 571, 5, 66], [15, 573, 5, 66, "t"], [15, 574, 5, 66], [15, 576, 5, 66, "i"], [15, 577, 5, 66], [15, 581, 5, 66, "f"], [15, 582, 5, 66], [15, 583, 5, 66, "t"], [15, 584, 5, 66], [15, 588, 5, 66, "e"], [15, 589, 5, 66], [15, 590, 5, 66, "t"], [15, 591, 5, 66], [15, 602, 5, 66, "f"], [15, 603, 5, 66], [15, 608, 5, 66, "e"], [15, 609, 5, 66], [15, 611, 5, 66, "t"], [15, 612, 5, 66], [16, 2, 1, 0], [16, 11, 1, 9, "_extends"], [16, 19, 1, 17, "_extends"], [16, 20, 1, 17], [16, 22, 1, 20], [17, 4, 1, 22, "_extends"], [17, 12, 1, 30], [17, 15, 1, 33, "Object"], [17, 21, 1, 39], [17, 22, 1, 40, "assign"], [17, 28, 1, 46], [17, 32, 1, 50], [17, 42, 1, 60, "target"], [17, 48, 1, 66], [17, 50, 1, 68], [18, 6, 1, 70], [18, 11, 1, 75], [18, 15, 1, 79, "i"], [18, 16, 1, 80], [18, 19, 1, 83], [18, 20, 1, 84], [18, 22, 1, 86, "i"], [18, 23, 1, 87], [18, 26, 1, 90, "arguments"], [18, 35, 1, 99], [18, 36, 1, 100, "length"], [18, 42, 1, 106], [18, 44, 1, 108, "i"], [18, 45, 1, 109], [18, 47, 1, 111], [18, 49, 1, 113], [19, 8, 1, 115], [19, 12, 1, 119, "source"], [19, 18, 1, 125], [19, 21, 1, 128, "arguments"], [19, 30, 1, 137], [19, 31, 1, 138, "i"], [19, 32, 1, 139], [19, 33, 1, 140], [20, 8, 1, 142], [20, 13, 1, 147], [20, 17, 1, 151, "key"], [20, 20, 1, 154], [20, 24, 1, 158, "source"], [20, 30, 1, 164], [20, 32, 1, 166], [21, 10, 1, 168], [21, 14, 1, 172, "Object"], [21, 20, 1, 178], [21, 21, 1, 179, "prototype"], [21, 30, 1, 188], [21, 31, 1, 189, "hasOwnProperty"], [21, 45, 1, 203], [21, 46, 1, 204, "call"], [21, 50, 1, 208], [21, 51, 1, 209, "source"], [21, 57, 1, 215], [21, 59, 1, 217, "key"], [21, 62, 1, 220], [21, 63, 1, 221], [21, 65, 1, 223], [22, 12, 1, 225, "target"], [22, 18, 1, 231], [22, 19, 1, 232, "key"], [22, 22, 1, 235], [22, 23, 1, 236], [22, 26, 1, 239, "source"], [22, 32, 1, 245], [22, 33, 1, 246, "key"], [22, 36, 1, 249], [22, 37, 1, 250], [23, 10, 1, 252], [24, 8, 1, 254], [25, 6, 1, 256], [26, 6, 1, 258], [26, 13, 1, 265, "target"], [26, 19, 1, 271], [27, 4, 1, 273], [27, 5, 1, 274], [28, 4, 1, 276], [28, 11, 1, 283, "_extends"], [28, 19, 1, 291], [28, 20, 1, 292, "apply"], [28, 25, 1, 297], [28, 26, 1, 298], [28, 30, 1, 302], [28, 32, 1, 304, "arguments"], [28, 41, 1, 313], [28, 42, 1, 314], [29, 2, 1, 316], [30, 2, 6, 7], [30, 8, 6, 13, "ScrollView"], [30, 18, 6, 23], [30, 21, 6, 23, "exports"], [30, 28, 6, 23], [30, 29, 6, 23, "ScrollView"], [30, 39, 6, 23], [30, 42, 6, 26], [30, 46, 6, 26, "createNativeWrapper"], [30, 74, 6, 45], [30, 76, 6, 46, "RNScrollView"], [30, 95, 6, 58], [30, 97, 6, 60], [31, 4, 7, 2, "disallowInterruption"], [31, 24, 7, 22], [31, 26, 7, 24], [32, 2, 8, 0], [32, 3, 8, 1], [32, 4, 8, 2], [33, 2, 9, 7], [33, 8, 9, 13, "Switch"], [33, 14, 9, 19], [33, 17, 9, 19, "exports"], [33, 24, 9, 19], [33, 25, 9, 19, "Switch"], [33, 31, 9, 19], [33, 34, 9, 22], [33, 38, 9, 22, "createNativeWrapper"], [33, 66, 9, 41], [33, 68, 9, 42, "RNSwitch"], [33, 83, 9, 50], [33, 85, 9, 52], [34, 4, 10, 2, "shouldCancelWhenOutside"], [34, 27, 10, 25], [34, 29, 10, 27], [34, 34, 10, 32], [35, 4, 11, 2, "shouldActivateOnStart"], [35, 25, 11, 23], [35, 27, 11, 25], [35, 31, 11, 29], [36, 4, 12, 2, "disallowInterruption"], [36, 24, 12, 22], [36, 26, 12, 24], [37, 2, 13, 0], [37, 3, 13, 1], [37, 4, 13, 2], [38, 2, 14, 7], [38, 8, 14, 13, "TextInput"], [38, 17, 14, 22], [38, 20, 14, 22, "exports"], [38, 27, 14, 22], [38, 28, 14, 22, "TextInput"], [38, 37, 14, 22], [38, 40, 14, 25], [38, 44, 14, 25, "createNativeWrapper"], [38, 72, 14, 44], [38, 74, 14, 45, "RNTextInput"], [38, 92, 14, 56], [38, 93, 14, 57], [39, 2, 15, 7], [39, 8, 15, 13, "DrawerLayoutAndroid"], [39, 27, 15, 32], [39, 30, 15, 35, "DrawerLayoutAndroid"], [39, 31, 15, 35], [39, 36, 15, 41], [40, 4, 16, 2, "console"], [40, 11, 16, 9], [40, 12, 16, 10, "warn"], [40, 16, 16, 14], [40, 17, 16, 15], [40, 63, 16, 61], [40, 64, 16, 62], [41, 4, 17, 2], [41, 11, 17, 9], [41, 24, 17, 22, "_ReactNativeCSSInterop"], [41, 46, 17, 22], [41, 47, 17, 22, "createInteropElement"], [41, 67, 17, 22], [41, 68, 17, 42, "View"], [41, 81, 17, 46], [41, 83, 17, 48], [41, 87, 17, 52], [41, 88, 17, 53], [42, 2, 18, 0], [42, 3, 18, 1], [42, 4, 18, 2], [42, 5, 18, 3], [43, 2, 19, 0], [44, 2, 20, 0], [45, 2, 20, 0, "exports"], [45, 9, 20, 0], [45, 10, 20, 0, "DrawerLayoutAndroid"], [45, 29, 20, 0], [45, 32, 20, 0, "DrawerLayoutAndroid"], [45, 51, 20, 0], [46, 2, 22, 7], [46, 8, 22, 13, "RefreshControl"], [46, 22, 22, 27], [46, 25, 22, 27, "exports"], [46, 32, 22, 27], [46, 33, 22, 27, "RefreshControl"], [46, 47, 22, 27], [46, 50, 22, 30], [46, 54, 22, 30, "createNativeWrapper"], [46, 82, 22, 49], [46, 84, 22, 50, "View"], [46, 97, 22, 54], [46, 98, 22, 55], [47, 2, 23, 7], [47, 8, 23, 13, "FlatList"], [47, 16, 23, 21], [47, 19, 23, 21, "exports"], [47, 26, 23, 21], [47, 27, 23, 21, "FlatList"], [47, 35, 23, 21], [47, 38, 23, 24], [47, 51, 23, 37, "React"], [47, 56, 23, 42], [47, 57, 23, 43, "forwardRef"], [47, 67, 23, 53], [47, 68, 23, 54], [47, 69, 23, 55, "props"], [47, 74, 23, 60], [47, 76, 23, 62, "ref"], [47, 79, 23, 65], [47, 84, 23, 70], [47, 97, 23, 83, "_ReactNativeCSSInterop"], [47, 119, 23, 83], [47, 120, 23, 83, "createInteropElement"], [47, 140, 23, 83], [47, 141, 23, 103, "RNFlatList"], [47, 158, 23, 113], [47, 160, 23, 115, "_extends"], [47, 168, 23, 123], [47, 169, 23, 124], [48, 4, 24, 2, "ref"], [48, 7, 24, 5], [48, 9, 24, 7, "ref"], [49, 2, 25, 0], [49, 3, 25, 1], [49, 5, 25, 3, "props"], [49, 10, 25, 8], [49, 12, 25, 10], [50, 4, 26, 2, "renderScrollComponent"], [50, 25, 26, 23], [50, 27, 26, 25, "scrollProps"], [50, 38, 26, 36], [50, 42, 26, 40], [50, 55, 26, 53, "_ReactNativeCSSInterop"], [50, 77, 26, 53], [50, 78, 26, 53, "createInteropElement"], [50, 98, 26, 53], [50, 99, 26, 73, "ScrollView"], [50, 109, 26, 83], [50, 111, 26, 85, "scrollProps"], [50, 122, 26, 96], [51, 2, 27, 0], [51, 3, 27, 1], [51, 4, 27, 2], [51, 5, 27, 3], [51, 6, 27, 4], [52, 0, 27, 5], [52, 3]], "functionMap": {"names": ["_extends", "<anonymous>", "<global>", "DrawerLayoutAndroid", "React.forwardRef$argument_0", "_extends$argument_2.renderScrollComponent"], "mappings": "AAA,kDC,gOD,2CE;mCCc;CDG;sDEK;yBCG,wED;GFC"}}, "type": "js/module"}]}