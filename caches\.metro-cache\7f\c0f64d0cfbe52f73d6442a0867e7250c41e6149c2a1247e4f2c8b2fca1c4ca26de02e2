{"dependencies": [{"name": "../PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 55, "index": 70}}], "key": "iJ0YgfbcPgrclB5t1J5j2jedwxA=", "exportNames": ["*"]}}, {"name": "./useAnimatedStyle.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 71}, "end": {"line": 4, "column": 57, "index": 128}}], "key": "yHan7VuoBXv02ZWGFJiF2AL1XQw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedProps = void 0;\n  var _PlatformChecker = require(_dependencyMap[0], \"../PlatformChecker.js\");\n  var _useAnimatedStyle = require(_dependencyMap[1], \"./useAnimatedStyle.js\");\n  // TODO: we should make sure that when useAP is used we are not assigning styles\n\n  function useAnimatedPropsJS(updater, deps, adapters) {\n    return (0, _useAnimatedStyle.useAnimatedStyle)(updater, deps, adapters, true);\n  }\n  const useAnimatedPropsNative = _useAnimatedStyle.useAnimatedStyle;\n\n  /**\n   * Lets you create an animated props object which can be animated using shared\n   * values.\n   *\n   * @param updater - A function returning an object with properties you want to\n   *   animate.\n   * @param dependencies - An optional array of dependencies. Only relevant when\n   *   using Reanimated without the Babel plugin on the Web.\n   * @param adapters - An optional function or array of functions allowing to\n   *   adopt prop naming between J<PERSON> and the native side.\n   * @returns An animated props object which has to be passed to `animatedProps`\n   *   property of an Animated component that you want to animate.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedProps\n   */\n  const useAnimatedProps = exports.useAnimatedProps = (0, _PlatformChecker.shouldBeUseWeb)() ? useAnimatedPropsJS : useAnimatedPropsNative;\n});", "lineCount": 32, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedProps"], [7, 26, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_PlatformChecker"], [8, 22, 3, 0], [8, 25, 3, 0, "require"], [8, 32, 3, 0], [8, 33, 3, 0, "_dependencyMap"], [8, 47, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_useAnimatedStyle"], [9, 23, 4, 0], [9, 26, 4, 0, "require"], [9, 33, 4, 0], [9, 34, 4, 0, "_dependencyMap"], [9, 48, 4, 0], [10, 2, 6, 0], [12, 2, 8, 0], [12, 11, 8, 9, "useAnimatedPropsJS"], [12, 29, 8, 27, "useAnimatedPropsJS"], [12, 30, 8, 28, "updater"], [12, 37, 8, 35], [12, 39, 8, 37, "deps"], [12, 43, 8, 41], [12, 45, 8, 43, "adapters"], [12, 53, 8, 51], [12, 55, 8, 53], [13, 4, 9, 2], [13, 11, 9, 9], [13, 15, 9, 9, "useAnimatedStyle"], [13, 49, 9, 25], [13, 51, 9, 26, "updater"], [13, 58, 9, 33], [13, 60, 9, 35, "deps"], [13, 64, 9, 39], [13, 66, 9, 41, "adapters"], [13, 74, 9, 49], [13, 76, 9, 51], [13, 80, 9, 55], [13, 81, 9, 56], [14, 2, 10, 0], [15, 2, 11, 0], [15, 8, 11, 6, "useAnimatedPropsNative"], [15, 30, 11, 28], [15, 33, 11, 31, "useAnimatedStyle"], [15, 67, 11, 47], [17, 2, 13, 0], [18, 0, 14, 0], [19, 0, 15, 0], [20, 0, 16, 0], [21, 0, 17, 0], [22, 0, 18, 0], [23, 0, 19, 0], [24, 0, 20, 0], [25, 0, 21, 0], [26, 0, 22, 0], [27, 0, 23, 0], [28, 0, 24, 0], [29, 0, 25, 0], [30, 0, 26, 0], [31, 2, 27, 7], [31, 8, 27, 13, "useAnimatedProps"], [31, 24, 27, 29], [31, 27, 27, 29, "exports"], [31, 34, 27, 29], [31, 35, 27, 29, "useAnimatedProps"], [31, 51, 27, 29], [31, 54, 27, 32], [31, 58, 27, 32, "shouldBeUseWeb"], [31, 89, 27, 46], [31, 91, 27, 47], [31, 92, 27, 48], [31, 95, 27, 51, "useAnimatedPropsJS"], [31, 113, 27, 69], [31, 116, 27, 72, "useAnimatedPropsNative"], [31, 138, 27, 94], [32, 0, 27, 95], [32, 3]], "functionMap": {"names": ["<global>", "useAnimatedPropsJS"], "mappings": "AAA;ACO;CDE"}}, "type": "js/module"}]}