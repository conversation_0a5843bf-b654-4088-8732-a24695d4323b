{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 26, "index": 237}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 238}, "end": {"line": 4, "column": 38, "index": 276}}], "key": "mL7nJyZhzUYx+zMcIt1cBzVuRps=", "exportNames": ["*"]}}, {"name": "../PlatformConstants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 277}, "end": {"line": 5, "column": 53, "index": 330}}], "key": "Q46Gl6Q6xVYszCJBCyudBafKJCc=", "exportNames": ["*"]}}, {"name": "./createHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 331}, "end": {"line": 6, "column": 44, "index": 375}}], "key": "j9sUgJL2drnBoAedJuo4/l2ILqw=", "exportNames": ["*"]}}, {"name": "./gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 376}, "end": {"line": 7, "column": 65, "index": 441}}], "key": "M3YJtGPnWOlAL/cGsCkMRGpSLhc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.forceTouchHandlerName = exports.forceTouchGestureHandlerProps = exports.ForceTouchGestureHandler = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _utils = require(_dependencyMap[2], \"../utils\");\n  var _PlatformConstants = _interopRequireDefault(require(_dependencyMap[3], \"../PlatformConstants\"));\n  var _createHandler = _interopRequireDefault(require(_dependencyMap[4], \"./createHandler\"));\n  var _gestureHandlerCommon = require(_dependencyMap[5], \"./gestureHandlerCommon\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  const forceTouchGestureHandlerProps = exports.forceTouchGestureHandlerProps = ['minForce', 'maxForce', 'feedbackOnActivation']; // implicit `children` prop has been removed in @types/react^18.0.0\n\n  class ForceTouchFallback extends _react.default.Component {\n    componentDidMount() {\n      console.warn((0, _utils.tagMessage)('ForceTouchGestureHandler is not available on this platform. Please use ForceTouchGestureHandler.forceTouchAvailable to conditionally render other components that would provide a fallback behavior specific to your usecase'));\n    }\n    render() {\n      return this.props.children;\n    }\n  }\n  _defineProperty(ForceTouchFallback, \"forceTouchAvailable\", false);\n  const forceTouchHandlerName = exports.forceTouchHandlerName = 'ForceTouchGestureHandler';\n  /**\n   * @deprecated ForceTouchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.ForceTouch()` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\n\n  const ForceTouchGestureHandler = exports.ForceTouchGestureHandler = _PlatformConstants.default !== null && _PlatformConstants.default !== void 0 && _PlatformConstants.default.forceTouchAvailable ? (0, _createHandler.default)({\n    name: forceTouchHandlerName,\n    allowedProps: [..._gestureHandlerCommon.baseGestureHandlerProps, ...forceTouchGestureHandlerProps],\n    config: {}\n  }) : ForceTouchFallback;\n  ForceTouchGestureHandler.forceTouchAvailable = (_PlatformConstants.default === null || _PlatformConstants.default === void 0 ? void 0 : _PlatformConstants.default.forceTouchAvailable) || false;\n});", "lineCount": 48, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_react"], [7, 12, 3, 0], [7, 15, 3, 0, "_interopRequireDefault"], [7, 37, 3, 0], [7, 38, 3, 0, "require"], [7, 45, 3, 0], [7, 46, 3, 0, "_dependencyMap"], [7, 60, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_utils"], [8, 12, 4, 0], [8, 15, 4, 0, "require"], [8, 22, 4, 0], [8, 23, 4, 0, "_dependencyMap"], [8, 37, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_PlatformConstants"], [9, 24, 5, 0], [9, 27, 5, 0, "_interopRequireDefault"], [9, 49, 5, 0], [9, 50, 5, 0, "require"], [9, 57, 5, 0], [9, 58, 5, 0, "_dependencyMap"], [9, 72, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_createHandler"], [10, 20, 6, 0], [10, 23, 6, 0, "_interopRequireDefault"], [10, 45, 6, 0], [10, 46, 6, 0, "require"], [10, 53, 6, 0], [10, 54, 6, 0, "_dependencyMap"], [10, 68, 6, 0], [11, 2, 7, 0], [11, 6, 7, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 27, 7, 0], [11, 30, 7, 0, "require"], [11, 37, 7, 0], [11, 38, 7, 0, "_dependencyMap"], [11, 52, 7, 0], [12, 2, 1, 0], [12, 11, 1, 9, "_defineProperty"], [12, 26, 1, 24, "_defineProperty"], [12, 27, 1, 25, "obj"], [12, 30, 1, 28], [12, 32, 1, 30, "key"], [12, 35, 1, 33], [12, 37, 1, 35, "value"], [12, 42, 1, 40], [12, 44, 1, 42], [13, 4, 1, 44], [13, 8, 1, 48, "key"], [13, 11, 1, 51], [13, 15, 1, 55, "obj"], [13, 18, 1, 58], [13, 20, 1, 60], [14, 6, 1, 62, "Object"], [14, 12, 1, 68], [14, 13, 1, 69, "defineProperty"], [14, 27, 1, 83], [14, 28, 1, 84, "obj"], [14, 31, 1, 87], [14, 33, 1, 89, "key"], [14, 36, 1, 92], [14, 38, 1, 94], [15, 8, 1, 96, "value"], [15, 13, 1, 101], [15, 15, 1, 103, "value"], [15, 20, 1, 108], [16, 8, 1, 110, "enumerable"], [16, 18, 1, 120], [16, 20, 1, 122], [16, 24, 1, 126], [17, 8, 1, 128, "configurable"], [17, 20, 1, 140], [17, 22, 1, 142], [17, 26, 1, 146], [18, 8, 1, 148, "writable"], [18, 16, 1, 156], [18, 18, 1, 158], [19, 6, 1, 163], [19, 7, 1, 164], [19, 8, 1, 165], [20, 4, 1, 167], [20, 5, 1, 168], [20, 11, 1, 174], [21, 6, 1, 176, "obj"], [21, 9, 1, 179], [21, 10, 1, 180, "key"], [21, 13, 1, 183], [21, 14, 1, 184], [21, 17, 1, 187, "value"], [21, 22, 1, 192], [22, 4, 1, 194], [23, 4, 1, 196], [23, 11, 1, 203, "obj"], [23, 14, 1, 206], [24, 2, 1, 208], [25, 2, 8, 7], [25, 8, 8, 13, "forceTouchGestureHandlerProps"], [25, 37, 8, 42], [25, 40, 8, 42, "exports"], [25, 47, 8, 42], [25, 48, 8, 42, "forceTouchGestureHandlerProps"], [25, 77, 8, 42], [25, 80, 8, 45], [25, 81, 8, 46], [25, 91, 8, 56], [25, 93, 8, 58], [25, 103, 8, 68], [25, 105, 8, 70], [25, 127, 8, 92], [25, 128, 8, 93], [25, 129, 8, 94], [25, 130, 8, 95], [27, 2, 10, 0], [27, 8, 10, 6, "ForceTouchFallback"], [27, 26, 10, 24], [27, 35, 10, 33, "React"], [27, 49, 10, 38], [27, 50, 10, 39, "Component"], [27, 59, 10, 48], [27, 60, 10, 49], [28, 4, 11, 2, "componentDidMount"], [28, 21, 11, 19, "componentDidMount"], [28, 22, 11, 19], [28, 24, 11, 22], [29, 6, 12, 4, "console"], [29, 13, 12, 11], [29, 14, 12, 12, "warn"], [29, 18, 12, 16], [29, 19, 12, 17], [29, 23, 12, 17, "tagMessage"], [29, 40, 12, 27], [29, 42, 12, 28], [29, 264, 12, 250], [29, 265, 12, 251], [29, 266, 12, 252], [30, 4, 13, 2], [31, 4, 15, 2, "render"], [31, 10, 15, 8, "render"], [31, 11, 15, 8], [31, 13, 15, 11], [32, 6, 16, 4], [32, 13, 16, 11], [32, 17, 16, 15], [32, 18, 16, 16, "props"], [32, 23, 16, 21], [32, 24, 16, 22, "children"], [32, 32, 16, 30], [33, 4, 17, 2], [34, 2, 19, 0], [35, 2, 21, 0, "_defineProperty"], [35, 17, 21, 15], [35, 18, 21, 16, "ForceTouchFallback"], [35, 36, 21, 34], [35, 38, 21, 36], [35, 59, 21, 57], [35, 61, 21, 59], [35, 66, 21, 64], [35, 67, 21, 65], [36, 2, 23, 7], [36, 8, 23, 13, "forceTouchHandlerName"], [36, 29, 23, 34], [36, 32, 23, 34, "exports"], [36, 39, 23, 34], [36, 40, 23, 34, "forceTouchHandlerName"], [36, 61, 23, 34], [36, 64, 23, 37], [36, 90, 23, 63], [37, 2, 24, 0], [38, 0, 25, 0], [39, 0, 26, 0], [40, 2, 27, 0], [42, 2, 29, 7], [42, 8, 29, 13, "ForceTouchGestureHandler"], [42, 32, 29, 37], [42, 35, 29, 37, "exports"], [42, 42, 29, 37], [42, 43, 29, 37, "ForceTouchGestureHandler"], [42, 67, 29, 37], [42, 70, 29, 40, "PlatformConstants"], [42, 96, 29, 57], [42, 101, 29, 62], [42, 105, 29, 66], [42, 109, 29, 70, "PlatformConstants"], [42, 135, 29, 87], [42, 140, 29, 92], [42, 145, 29, 97], [42, 146, 29, 98], [42, 150, 29, 102, "PlatformConstants"], [42, 176, 29, 119], [42, 177, 29, 120, "forceTouchAvailable"], [42, 196, 29, 139], [42, 199, 29, 142], [42, 203, 29, 142, "createHandler"], [42, 225, 29, 155], [42, 227, 29, 156], [43, 4, 30, 2, "name"], [43, 8, 30, 6], [43, 10, 30, 8, "forceTouchHandlerName"], [43, 31, 30, 29], [44, 4, 31, 2, "allowedProps"], [44, 16, 31, 14], [44, 18, 31, 16], [44, 19, 31, 17], [44, 22, 31, 20, "baseGestureHandlerProps"], [44, 67, 31, 43], [44, 69, 31, 45], [44, 72, 31, 48, "forceTouchGestureHandlerProps"], [44, 101, 31, 77], [44, 102, 31, 78], [45, 4, 32, 2, "config"], [45, 10, 32, 8], [45, 12, 32, 10], [45, 13, 32, 11], [46, 2, 33, 0], [46, 3, 33, 1], [46, 4, 33, 2], [46, 7, 33, 5, "ForceTouchFallback"], [46, 25, 33, 23], [47, 2, 34, 0, "ForceTouchGestureHandler"], [47, 26, 34, 24], [47, 27, 34, 25, "forceTouchAvailable"], [47, 46, 34, 44], [47, 49, 34, 47], [47, 50, 34, 48, "PlatformConstants"], [47, 76, 34, 65], [47, 81, 34, 70], [47, 85, 34, 74], [47, 89, 34, 78, "PlatformConstants"], [47, 115, 34, 95], [47, 120, 34, 100], [47, 125, 34, 105], [47, 126, 34, 106], [47, 129, 34, 109], [47, 134, 34, 114], [47, 135, 34, 115], [47, 138, 34, 118, "PlatformConstants"], [47, 164, 34, 135], [47, 165, 34, 136, "forceTouchAvailable"], [47, 184, 34, 155], [47, 189, 34, 160], [47, 194, 34, 165], [48, 0, 34, 166], [48, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "ForceTouchFallback", "ForceTouchFallback#componentDidMount", "ForceTouchFallback#render"], "mappings": "AAA,iNC;ACS;ECC;GDE;EEE;GFE;CDE"}}, "type": "js/module"}]}