{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./RCTDeviceEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 60}}], "key": "NlRzjeXokeOx1NjjdCtV9kKz8dY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _RCTDeviceEventEmitter = _interopRequireDefault(require(_dependencyMap[1], \"./RCTDeviceEventEmitter\"));\n  var RCTNativeAppEventEmitter = _RCTDeviceEventEmitter.default;\n  var _default = exports.default = RCTNativeAppEventEmitter;\n});", "lineCount": 10, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_RCTDeviceEventEmitter"], [7, 28, 11, 0], [7, 31, 11, 0, "_interopRequireDefault"], [7, 53, 11, 0], [7, 54, 11, 0, "require"], [7, 61, 11, 0], [7, 62, 11, 0, "_dependencyMap"], [7, 76, 11, 0], [8, 2, 17, 0], [8, 6, 17, 6, "RCTNativeAppEventEmitter"], [8, 30, 17, 30], [8, 33, 17, 33, "RCTDeviceEventEmitter"], [8, 63, 17, 54], [9, 2, 17, 55], [9, 6, 17, 55, "_default"], [9, 14, 17, 55], [9, 17, 17, 55, "exports"], [9, 24, 17, 55], [9, 25, 17, 55, "default"], [9, 32, 17, 55], [9, 35, 18, 15, "RCTNativeAppEventEmitter"], [9, 59, 18, 39], [10, 0, 18, 39], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}