import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  Pressable,
  StyleSheet,
  Dimensions,
  RefreshControl,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { Image } from "expo-image";
import {
  Plus,
  Search,
  Filter,
  Grid3X3,
  List,
  Shirt,
  Package,
  ShirtIcon as T<PERSON>hirt,
  Sun,
  Snowflake,
  Leaf,
  Flower,
} from "lucide-react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTheme } from "../../utils/theme";
import {
  useFonts,
  Inter_400Regular,
  Inter_500Medium,
  Inter_600SemiBold,
} from "@expo-google-fonts/inter";

const { width: screenWidth } = Dimensions.get("window");

const categories = [
  { id: "all", name: "All", icon: Package },
  { id: "tops", name: "Tops", icon: TShirt },
  { id: "bottoms", name: "Bottoms", icon: Package },
  { id: "shoes", name: "Shoes", icon: Package },
  { id: "accessories", name: "Accessories", icon: Package },
];

const seasons = [
  { id: "spring", name: "Spring", icon: Flower, color: "#10B981" },
  { id: "summer", name: "Summer", icon: Sun, color: "#F59E0B" },
  { id: "autumn", name: "Autumn", icon: Leaf, color: "#EF4444" },
  { id: "winter", name: "Winter", icon: Snowflake, color: "#3B82F6" },
];

// Sample clothing items - will be replaced with real data
const sampleClothes = [
  {
    id: "1",
    name: "White Cotton Shirt",
    category: "tops",
    season: "spring",
    image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400",
    color: "white",
    brand: "H&M",
  },
  {
    id: "2",
    name: "Blue Jeans",
    category: "bottoms",
    season: "all",
    image: "https://images.unsplash.com/photo-1542272604-787c3835535d?w=400",
    color: "blue",
    brand: "Zara",
  },
  {
    id: "3",
    name: "Summer Dress",
    category: "tops",
    season: "summer",
    image: "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400",
    color: "yellow",
    brand: "Forever 21",
  },
];

export default function ClosetScreen() {
  const insets = useSafeAreaInsets();
  const { colors, isDark } = useTheme();
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedSeason, setSelectedSeason] = useState(null);
  const [viewMode, setViewMode] = useState("grid"); // 'grid' or 'list'
  const [refreshing, setRefreshing] = useState(false);
  const [clothesItems, setClothesItems] = useState([]);
  const [loading, setLoading] = useState(true);

  const [fontsLoaded] = useFonts({
    Inter_400Regular,
    Inter_500Medium,
    Inter_600SemiBold,
  });

  useEffect(() => {
    fetchClothingItems();
  }, [selectedCategory, selectedSeason]);

  const fetchClothingItems = async () => {
    try {
      const params = new URLSearchParams();
      if (selectedCategory !== "all") {
        params.append("category", selectedCategory);
      }
      if (selectedSeason) {
        params.append("season", selectedSeason);
      }

      const response = await fetch(`/api/clothing-items?${params}`);
      const result = await response.json();

      if (result.success) {
        setClothesItems(result.data);
      }
    } catch (error) {
      console.error("Error fetching clothing items:", error);
    } finally {
      setLoading(false);
    }
  };

  if (!fontsLoaded) {
    return null;
  }

  const filteredItems = clothesItems;

  const itemWidth =
    viewMode === "grid" ? (screenWidth - 60) / 2 : screenWidth - 40;

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchClothingItems();
    setRefreshing(false);
  };

  const renderCategoryButton = (category) => {
    const isSelected = selectedCategory === category.id;
    const IconComponent = category.icon;

    return (
      <Pressable
        key={category.id}
        style={[
          styles.categoryButton,
          { backgroundColor: colors.surface },
          isSelected && { backgroundColor: colors.primary },
        ]}
        onPress={() => setSelectedCategory(category.id)}
      >
        <IconComponent
          size={16}
          color={isSelected ? colors.buttonPrimaryText : colors.textSecondary}
        />
        <Text
          style={[
            styles.categoryText,
            { color: colors.textSecondary },
            isSelected && { color: colors.buttonPrimaryText },
          ]}
        >
          {category.name}
        </Text>
      </Pressable>
    );
  };

  const renderSeasonButton = (season) => {
    const isSelected = selectedSeason === season.id;
    const IconComponent = season.icon;

    return (
      <Pressable
        key={season.id}
        style={[
          styles.seasonButton,
          { backgroundColor: colors.surface },
          isSelected && { backgroundColor: season.color },
        ]}
        onPress={() => setSelectedSeason(isSelected ? null : season.id)}
      >
        <IconComponent
          size={14}
          color={isSelected ? "#FFFFFF" : season.color}
        />
        <Text
          style={[
            styles.seasonText,
            { color: colors.textSecondary },
            isSelected && { color: "#FFFFFF" },
          ]}
        >
          {season.name}
        </Text>
      </Pressable>
    );
  };

  const renderClothingItem = (item) => {
    return (
      <Pressable
        key={item.id}
        style={[
          styles.clothingItem,
          {
            width: itemWidth,
            backgroundColor: colors.cardBackground,
          },
          viewMode === "list" && styles.listItem,
        ]}
      >
        <Image
          source={{ uri: item.image }}
          style={[
            styles.clothingImage,
            viewMode === "list" && styles.listImage,
          ]}
          contentFit="cover"
          transition={200}
        />
        <View
          style={[styles.itemInfo, viewMode === "list" && styles.listItemInfo]}
        >
          <Text
            style={[styles.itemName, { color: colors.text }]}
            numberOfLines={viewMode === "grid" ? 2 : 1}
          >
            {item.name}
          </Text>
          <Text style={[styles.itemBrand, { color: colors.textSecondary }]}>
            {item.brand}
          </Text>
          <View style={styles.itemMeta}>
            <View style={[styles.colorDot, { backgroundColor: item.color }]} />
            <Text style={[styles.itemCategory, { color: colors.textTertiary }]}>
              {categories.find((c) => c.id === item.category)?.name}
            </Text>
          </View>
        </View>
      </Pressable>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <View
        style={[
          styles.emptyIconContainer,
          { backgroundColor: colors.emptyStateBackground },
        ]}
      >
        <Shirt size={48} color={colors.textTertiary} strokeWidth={1} />
      </View>
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        Your closet is empty
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        Start building your virtual wardrobe by adding your first clothing item
      </Text>
      <Pressable
        style={[styles.addFirstButton, { backgroundColor: colors.primary }]}
        onPress={() => {
          /* Navigate to add item */
        }}
      >
        <Plus size={20} color={colors.buttonPrimaryText} />
        <Text
          style={[styles.addFirstText, { color: colors.buttonPrimaryText }]}
        >
          Add Your First Item
        </Text>
      </Pressable>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style={isDark ? "light" : "dark"} />

      {/* Header */}
      <View
        style={[
          styles.header,
          {
            paddingTop: insets.top + 16,
            backgroundColor: colors.background,
            borderBottomColor: colors.border,
          },
        ]}
      >
        <View style={styles.headerTop}>
          <Text style={[styles.title, { color: colors.text }]}>My Closet</Text>
          <View style={styles.headerActions}>
            <Pressable
              style={[styles.iconButton, { backgroundColor: colors.surface }]}
            >
              <Search size={20} color={colors.text} />
            </Pressable>
            <Pressable
              style={[styles.iconButton, { backgroundColor: colors.surface }]}
              onPress={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
            >
              {viewMode === "grid" ? (
                <List size={20} color={colors.text} />
              ) : (
                <Grid3X3 size={20} color={colors.text} />
              )}
            </Pressable>
          </View>
        </View>

        {/* Categories */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoriesScroll}
          contentContainerStyle={styles.categoriesContainer}
        >
          {categories.map(renderCategoryButton)}
        </ScrollView>

        {/* Seasons */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.seasonsScroll}
          contentContainerStyle={styles.seasonsContainer}
        >
          {seasons.map(renderSeasonButton)}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        contentContainerStyle={[
          styles.contentContainer,
          viewMode === "list" && styles.listContainer,
        ]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary}
          />
        }
      >
        {filteredItems.length === 0 ? (
          renderEmptyState()
        ) : (
          <View
            style={[styles.itemsGrid, viewMode === "list" && styles.itemsList]}
          >
            {filteredItems.map(renderClothingItem)}
          </View>
        )}
      </ScrollView>

      {/* Add Button */}
      <Pressable
        style={[
          styles.addButton,
          {
            backgroundColor: colors.primary,
            bottom: insets.bottom + 100,
            shadowColor: colors.shadow,
          },
        ]}
        onPress={() => {
          /* Navigate to camera/upload */
        }}
      >
        <Plus size={24} color={colors.buttonPrimaryText} strokeWidth={2} />
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontFamily: "Inter_600SemiBold",
  },
  headerActions: {
    flexDirection: "row",
    gap: 8,
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  categoriesScroll: {
    marginBottom: 16,
  },
  categoriesContainer: {
    paddingRight: 20,
    gap: 8,
  },
  categoryButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  categoryText: {
    fontSize: 14,
    fontFamily: "Inter_500Medium",
  },
  seasonsScroll: {
    marginBottom: 8,
  },
  seasonsContainer: {
    paddingRight: 20,
    gap: 8,
  },
  seasonButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  seasonText: {
    fontSize: 12,
    fontFamily: "Inter_500Medium",
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 120,
  },
  listContainer: {
    paddingBottom: 120,
  },
  itemsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 16,
  },
  itemsList: {
    flexDirection: "column",
    gap: 12,
  },
  clothingItem: {
    borderRadius: 16,
    overflow: "hidden",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  listItem: {
    flexDirection: "row",
    width: "100%",
  },
  clothingImage: {
    width: "100%",
    height: 180,
  },
  listImage: {
    width: 80,
    height: 80,
    borderRadius: 12,
    marginRight: 12,
  },
  itemInfo: {
    padding: 12,
  },
  listItemInfo: {
    flex: 1,
    padding: 0,
    justifyContent: "center",
  },
  itemName: {
    fontSize: 14,
    fontFamily: "Inter_500Medium",
    lineHeight: 18,
    marginBottom: 4,
  },
  itemBrand: {
    fontSize: 12,
    fontFamily: "Inter_400Regular",
    marginBottom: 8,
  },
  itemMeta: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  colorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.1)",
  },
  itemCategory: {
    fontSize: 11,
    fontFamily: "Inter_400Regular",
    textTransform: "capitalize",
  },
  addButton: {
    position: "absolute",
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: "center",
    justifyContent: "center",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  emptyState: {
    alignItems: "center",
    paddingVertical: 80,
    paddingHorizontal: 40,
  },
  emptyIconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 20,
    fontFamily: "Inter_600SemiBold",
    marginBottom: 8,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: 14,
    fontFamily: "Inter_400Regular",
    textAlign: "center",
    lineHeight: 20,
    marginBottom: 32,
  },
  addFirstButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  addFirstText: {
    fontSize: 14,
    fontFamily: "Inter_500Medium",
  },
});
