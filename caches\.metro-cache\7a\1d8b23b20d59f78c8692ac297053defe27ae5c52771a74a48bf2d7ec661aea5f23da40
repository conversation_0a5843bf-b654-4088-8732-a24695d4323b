{"dependencies": [{"name": "./ConfigHelper.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 62, "index": 77}}], "key": "aUdqUxrjDuz/kfUeBuBHUNOtCzA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createAnimatedPropAdapter = createAnimatedPropAdapter;\n  var _ConfigHelper = require(_dependencyMap[0], \"./ConfigHelper.js\");\n  // @ts-expect-error This overload is required by our API.\n\n  function createAnimatedPropAdapter(adapter, nativeProps) {\n    const nativePropsToAdd = {};\n    nativeProps?.forEach(prop => {\n      nativePropsToAdd[prop] = true;\n    });\n    (0, _ConfigHelper.addWhitelistedNativeProps)(nativePropsToAdd);\n    return adapter;\n  }\n});", "lineCount": 19, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "createAnimatedPropAdapter"], [7, 35, 1, 13], [7, 38, 1, 13, "createAnimatedPropAdapter"], [7, 63, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_ConfigHelper"], [8, 19, 3, 0], [8, 22, 3, 0, "require"], [8, 29, 3, 0], [8, 30, 3, 0, "_dependencyMap"], [8, 44, 3, 0], [9, 2, 5, 0], [11, 2, 7, 7], [11, 11, 7, 16, "createAnimatedPropAdapter"], [11, 36, 7, 41, "createAnimatedPropAdapter"], [11, 37, 7, 42, "adapter"], [11, 44, 7, 49], [11, 46, 7, 51, "nativeProps"], [11, 57, 7, 62], [11, 59, 7, 64], [12, 4, 8, 2], [12, 10, 8, 8, "nativePropsToAdd"], [12, 26, 8, 24], [12, 29, 8, 27], [12, 30, 8, 28], [12, 31, 8, 29], [13, 4, 9, 2, "nativeProps"], [13, 15, 9, 13], [13, 17, 9, 15, "for<PERSON>ach"], [13, 24, 9, 22], [13, 25, 9, 23, "prop"], [13, 29, 9, 27], [13, 33, 9, 31], [14, 6, 10, 4, "nativePropsToAdd"], [14, 22, 10, 20], [14, 23, 10, 21, "prop"], [14, 27, 10, 25], [14, 28, 10, 26], [14, 31, 10, 29], [14, 35, 10, 33], [15, 4, 11, 2], [15, 5, 11, 3], [15, 6, 11, 4], [16, 4, 12, 2], [16, 8, 12, 2, "addWhitelistedNativeProps"], [16, 47, 12, 27], [16, 49, 12, 28, "nativePropsToAdd"], [16, 65, 12, 44], [16, 66, 12, 45], [17, 4, 13, 2], [17, 11, 13, 9, "adapter"], [17, 18, 13, 16], [18, 2, 14, 0], [19, 0, 14, 1], [19, 3]], "functionMap": {"names": ["<global>", "createAnimatedPropAdapter", "nativeProps.forEach$argument_0"], "mappings": "AAA;OCM;uBCE;GDE;CDG"}}, "type": "js/module"}]}