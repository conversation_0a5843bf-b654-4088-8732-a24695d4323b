{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/extends", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 54, "index": 54}}], "key": "yLIpKqfSeOZo7yhmpj6jeRbKj/A=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutPropertiesLoose", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 55}, "end": {"line": 2, "column": 96, "index": 151}}], "key": "h/v2q98AsT4QTiU2QmCS7mQfUgY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 416}, "end": {"line": 13, "column": 31, "index": 447}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../Image", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 484}, "end": {"line": 15, "column": 29, "index": 513}}], "key": "r96J5D1jm+D8ZhyS8Od8ckxX8P0=", "exportNames": ["*"]}}, {"name": "../StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 514}, "end": {"line": 16, "column": 39, "index": 553}}], "key": "Pz10tXyA/z/1zTYUTTxDDbnOtjE=", "exportNames": ["*"]}}, {"name": "../View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 554}, "end": {"line": 17, "column": 27, "index": 581}}], "key": "z+h67QhWT4Dd/ILcrpyPJ2FPLGs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _extends2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/extends\"));\n  var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var React = _react;\n  var _Image = _interopRequireDefault(require(_dependencyMap[4], \"../Image\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"../StyleSheet\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[6], \"../View\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  var _excluded = [\"children\", \"style\", \"imageStyle\", \"imageRef\"];\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var emptyObject = {};\n\n  /**\n   * Very simple drop-in replacement for <Image> which supports nesting views.\n   */\n  var ImageBackground = /*#__PURE__*/(0, _react.forwardRef)((props, forwardedRef) => {\n    var children = props.children,\n      _props$style = props.style,\n      style = _props$style === void 0 ? emptyObject : _props$style,\n      imageStyle = props.imageStyle,\n      imageRef = props.imageRef,\n      rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n    var _StyleSheet$flatten = _StyleSheet.default.flatten(style),\n      height = _StyleSheet$flatten.height,\n      width = _StyleSheet$flatten.width;\n    return /*#__PURE__*/React.createElement(_View.default, {\n      ref: forwardedRef,\n      style: style\n    }, /*#__PURE__*/React.createElement(_Image.default, (0, _extends2.default)({}, rest, {\n      ref: imageRef,\n      style: [{\n        // Temporary Workaround:\n        // Current (imperfect yet) implementation of <Image> overwrites width and height styles\n        // (which is not quite correct), and these styles conflict with explicitly set styles\n        // of <ImageBackground> and with our internal layout model here.\n        // So, we have to proxy/reapply these styles explicitly for actual <Image> component.\n        // This workaround should be removed after implementing proper support of\n        // intrinsic content size of the <Image>.\n        width,\n        height,\n        zIndex: -1\n      }, _StyleSheet.default.absoluteFill, imageStyle]\n    })), children);\n  });\n  ImageBackground.displayName = 'ImageBackground';\n  var _default = exports.default = ImageBackground;\n});", "lineCount": 61, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_extends2"], [7, 15, 1, 0], [7, 18, 1, 0, "_interopRequireDefault"], [7, 40, 1, 0], [7, 41, 1, 0, "require"], [7, 48, 1, 0], [7, 49, 1, 0, "_dependencyMap"], [7, 63, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_objectWithoutPropertiesLoose2"], [8, 36, 2, 0], [8, 39, 2, 0, "_interopRequireDefault"], [8, 61, 2, 0], [8, 62, 2, 0, "require"], [8, 69, 2, 0], [8, 70, 2, 0, "_dependencyMap"], [8, 84, 2, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_react"], [9, 12, 13, 0], [9, 15, 13, 0, "_interopRequireWildcard"], [9, 38, 13, 0], [9, 39, 13, 0, "require"], [9, 46, 13, 0], [9, 47, 13, 0, "_dependencyMap"], [9, 61, 13, 0], [10, 2, 13, 31], [10, 6, 13, 31, "React"], [10, 11, 13, 31], [10, 14, 13, 31, "_react"], [10, 20, 13, 31], [11, 2, 15, 0], [11, 6, 15, 0, "_Image"], [11, 12, 15, 0], [11, 15, 15, 0, "_interopRequireDefault"], [11, 37, 15, 0], [11, 38, 15, 0, "require"], [11, 45, 15, 0], [11, 46, 15, 0, "_dependencyMap"], [11, 60, 15, 0], [12, 2, 16, 0], [12, 6, 16, 0, "_StyleSheet"], [12, 17, 16, 0], [12, 20, 16, 0, "_interopRequireDefault"], [12, 42, 16, 0], [12, 43, 16, 0, "require"], [12, 50, 16, 0], [12, 51, 16, 0, "_dependencyMap"], [12, 65, 16, 0], [13, 2, 17, 0], [13, 6, 17, 0, "_View"], [13, 11, 17, 0], [13, 14, 17, 0, "_interopRequireDefault"], [13, 36, 17, 0], [13, 37, 17, 0, "require"], [13, 44, 17, 0], [13, 45, 17, 0, "_dependencyMap"], [13, 59, 17, 0], [14, 2, 17, 27], [14, 11, 17, 27, "_interopRequireWildcard"], [14, 35, 17, 27, "e"], [14, 36, 17, 27], [14, 38, 17, 27, "t"], [14, 39, 17, 27], [14, 68, 17, 27, "WeakMap"], [14, 75, 17, 27], [14, 81, 17, 27, "r"], [14, 82, 17, 27], [14, 89, 17, 27, "WeakMap"], [14, 96, 17, 27], [14, 100, 17, 27, "n"], [14, 101, 17, 27], [14, 108, 17, 27, "WeakMap"], [14, 115, 17, 27], [14, 127, 17, 27, "_interopRequireWildcard"], [14, 150, 17, 27], [14, 162, 17, 27, "_interopRequireWildcard"], [14, 163, 17, 27, "e"], [14, 164, 17, 27], [14, 166, 17, 27, "t"], [14, 167, 17, 27], [14, 176, 17, 27, "t"], [14, 177, 17, 27], [14, 181, 17, 27, "e"], [14, 182, 17, 27], [14, 186, 17, 27, "e"], [14, 187, 17, 27], [14, 188, 17, 27, "__esModule"], [14, 198, 17, 27], [14, 207, 17, 27, "e"], [14, 208, 17, 27], [14, 214, 17, 27, "o"], [14, 215, 17, 27], [14, 217, 17, 27, "i"], [14, 218, 17, 27], [14, 220, 17, 27, "f"], [14, 221, 17, 27], [14, 226, 17, 27, "__proto__"], [14, 235, 17, 27], [14, 243, 17, 27, "default"], [14, 250, 17, 27], [14, 252, 17, 27, "e"], [14, 253, 17, 27], [14, 270, 17, 27, "e"], [14, 271, 17, 27], [14, 294, 17, 27, "e"], [14, 295, 17, 27], [14, 320, 17, 27, "e"], [14, 321, 17, 27], [14, 330, 17, 27, "f"], [14, 331, 17, 27], [14, 337, 17, 27, "o"], [14, 338, 17, 27], [14, 341, 17, 27, "t"], [14, 342, 17, 27], [14, 345, 17, 27, "n"], [14, 346, 17, 27], [14, 349, 17, 27, "r"], [14, 350, 17, 27], [14, 358, 17, 27, "o"], [14, 359, 17, 27], [14, 360, 17, 27, "has"], [14, 363, 17, 27], [14, 364, 17, 27, "e"], [14, 365, 17, 27], [14, 375, 17, 27, "o"], [14, 376, 17, 27], [14, 377, 17, 27, "get"], [14, 380, 17, 27], [14, 381, 17, 27, "e"], [14, 382, 17, 27], [14, 385, 17, 27, "o"], [14, 386, 17, 27], [14, 387, 17, 27, "set"], [14, 390, 17, 27], [14, 391, 17, 27, "e"], [14, 392, 17, 27], [14, 394, 17, 27, "f"], [14, 395, 17, 27], [14, 411, 17, 27, "t"], [14, 412, 17, 27], [14, 416, 17, 27, "e"], [14, 417, 17, 27], [14, 433, 17, 27, "t"], [14, 434, 17, 27], [14, 441, 17, 27, "hasOwnProperty"], [14, 455, 17, 27], [14, 456, 17, 27, "call"], [14, 460, 17, 27], [14, 461, 17, 27, "e"], [14, 462, 17, 27], [14, 464, 17, 27, "t"], [14, 465, 17, 27], [14, 472, 17, 27, "i"], [14, 473, 17, 27], [14, 477, 17, 27, "o"], [14, 478, 17, 27], [14, 481, 17, 27, "Object"], [14, 487, 17, 27], [14, 488, 17, 27, "defineProperty"], [14, 502, 17, 27], [14, 507, 17, 27, "Object"], [14, 513, 17, 27], [14, 514, 17, 27, "getOwnPropertyDescriptor"], [14, 538, 17, 27], [14, 539, 17, 27, "e"], [14, 540, 17, 27], [14, 542, 17, 27, "t"], [14, 543, 17, 27], [14, 550, 17, 27, "i"], [14, 551, 17, 27], [14, 552, 17, 27, "get"], [14, 555, 17, 27], [14, 559, 17, 27, "i"], [14, 560, 17, 27], [14, 561, 17, 27, "set"], [14, 564, 17, 27], [14, 568, 17, 27, "o"], [14, 569, 17, 27], [14, 570, 17, 27, "f"], [14, 571, 17, 27], [14, 573, 17, 27, "t"], [14, 574, 17, 27], [14, 576, 17, 27, "i"], [14, 577, 17, 27], [14, 581, 17, 27, "f"], [14, 582, 17, 27], [14, 583, 17, 27, "t"], [14, 584, 17, 27], [14, 588, 17, 27, "e"], [14, 589, 17, 27], [14, 590, 17, 27, "t"], [14, 591, 17, 27], [14, 602, 17, 27, "f"], [14, 603, 17, 27], [14, 608, 17, 27, "e"], [14, 609, 17, 27], [14, 611, 17, 27, "t"], [14, 612, 17, 27], [15, 2, 3, 0], [15, 6, 3, 4, "_excluded"], [15, 15, 3, 13], [15, 18, 3, 16], [15, 19, 3, 17], [15, 29, 3, 27], [15, 31, 3, 29], [15, 38, 3, 36], [15, 40, 3, 38], [15, 52, 3, 50], [15, 54, 3, 52], [15, 64, 3, 62], [15, 65, 3, 63], [16, 2, 4, 0], [17, 0, 5, 0], [18, 0, 6, 0], [19, 0, 7, 0], [20, 0, 8, 0], [21, 0, 9, 0], [22, 0, 10, 0], [23, 0, 11, 0], [25, 2, 18, 0], [25, 6, 18, 4, "emptyObject"], [25, 17, 18, 15], [25, 20, 18, 18], [25, 21, 18, 19], [25, 22, 18, 20], [27, 2, 20, 0], [28, 0, 21, 0], [29, 0, 22, 0], [30, 2, 23, 0], [30, 6, 23, 4, "ImageBackground"], [30, 21, 23, 19], [30, 24, 23, 22], [30, 37, 23, 35], [30, 41, 23, 35, "forwardRef"], [30, 58, 23, 45], [30, 60, 23, 46], [30, 61, 23, 47, "props"], [30, 66, 23, 52], [30, 68, 23, 54, "forwardedRef"], [30, 80, 23, 66], [30, 85, 23, 71], [31, 4, 24, 2], [31, 8, 24, 6, "children"], [31, 16, 24, 14], [31, 19, 24, 17, "props"], [31, 24, 24, 22], [31, 25, 24, 23, "children"], [31, 33, 24, 31], [32, 6, 25, 4, "_props$style"], [32, 18, 25, 16], [32, 21, 25, 19, "props"], [32, 26, 25, 24], [32, 27, 25, 25, "style"], [32, 32, 25, 30], [33, 6, 26, 4, "style"], [33, 11, 26, 9], [33, 14, 26, 12, "_props$style"], [33, 26, 26, 24], [33, 31, 26, 29], [33, 36, 26, 34], [33, 37, 26, 35], [33, 40, 26, 38, "emptyObject"], [33, 51, 26, 49], [33, 54, 26, 52, "_props$style"], [33, 66, 26, 64], [34, 6, 27, 4, "imageStyle"], [34, 16, 27, 14], [34, 19, 27, 17, "props"], [34, 24, 27, 22], [34, 25, 27, 23, "imageStyle"], [34, 35, 27, 33], [35, 6, 28, 4, "imageRef"], [35, 14, 28, 12], [35, 17, 28, 15, "props"], [35, 22, 28, 20], [35, 23, 28, 21, "imageRef"], [35, 31, 28, 29], [36, 6, 29, 4, "rest"], [36, 10, 29, 8], [36, 13, 29, 11], [36, 17, 29, 11, "_objectWithoutPropertiesLoose"], [36, 55, 29, 40], [36, 57, 29, 41, "props"], [36, 62, 29, 46], [36, 64, 29, 48, "_excluded"], [36, 73, 29, 57], [36, 74, 29, 58], [37, 4, 30, 2], [37, 8, 30, 6, "_StyleSheet$flatten"], [37, 27, 30, 25], [37, 30, 30, 28, "StyleSheet"], [37, 49, 30, 38], [37, 50, 30, 39, "flatten"], [37, 57, 30, 46], [37, 58, 30, 47, "style"], [37, 63, 30, 52], [37, 64, 30, 53], [38, 6, 31, 4, "height"], [38, 12, 31, 10], [38, 15, 31, 13, "_StyleSheet$flatten"], [38, 34, 31, 32], [38, 35, 31, 33, "height"], [38, 41, 31, 39], [39, 6, 32, 4, "width"], [39, 11, 32, 9], [39, 14, 32, 12, "_StyleSheet$flatten"], [39, 33, 32, 31], [39, 34, 32, 32, "width"], [39, 39, 32, 37], [40, 4, 33, 2], [40, 11, 33, 9], [40, 24, 33, 22, "React"], [40, 29, 33, 27], [40, 30, 33, 28, "createElement"], [40, 43, 33, 41], [40, 44, 33, 42, "View"], [40, 57, 33, 46], [40, 59, 33, 48], [41, 6, 34, 4, "ref"], [41, 9, 34, 7], [41, 11, 34, 9, "forwardedRef"], [41, 23, 34, 21], [42, 6, 35, 4, "style"], [42, 11, 35, 9], [42, 13, 35, 11, "style"], [43, 4, 36, 2], [43, 5, 36, 3], [43, 7, 36, 5], [43, 20, 36, 18, "React"], [43, 25, 36, 23], [43, 26, 36, 24, "createElement"], [43, 39, 36, 37], [43, 40, 36, 38, "Image"], [43, 54, 36, 43], [43, 56, 36, 45], [43, 60, 36, 45, "_extends"], [43, 77, 36, 53], [43, 79, 36, 54], [43, 80, 36, 55], [43, 81, 36, 56], [43, 83, 36, 58, "rest"], [43, 87, 36, 62], [43, 89, 36, 64], [44, 6, 37, 4, "ref"], [44, 9, 37, 7], [44, 11, 37, 9, "imageRef"], [44, 19, 37, 17], [45, 6, 38, 4, "style"], [45, 11, 38, 9], [45, 13, 38, 11], [45, 14, 38, 12], [46, 8, 39, 6], [47, 8, 40, 6], [48, 8, 41, 6], [49, 8, 42, 6], [50, 8, 43, 6], [51, 8, 44, 6], [52, 8, 45, 6], [53, 8, 46, 6, "width"], [53, 13, 46, 11], [54, 8, 47, 6, "height"], [54, 14, 47, 12], [55, 8, 48, 6, "zIndex"], [55, 14, 48, 12], [55, 16, 48, 14], [55, 17, 48, 15], [56, 6, 49, 4], [56, 7, 49, 5], [56, 9, 49, 7, "StyleSheet"], [56, 28, 49, 17], [56, 29, 49, 18, "absoluteFill"], [56, 41, 49, 30], [56, 43, 49, 32, "imageStyle"], [56, 53, 49, 42], [57, 4, 50, 2], [57, 5, 50, 3], [57, 6, 50, 4], [57, 7, 50, 5], [57, 9, 50, 7, "children"], [57, 17, 50, 15], [57, 18, 50, 16], [58, 2, 51, 0], [58, 3, 51, 1], [58, 4, 51, 2], [59, 2, 52, 0, "ImageBackground"], [59, 17, 52, 15], [59, 18, 52, 16, "displayName"], [59, 29, 52, 27], [59, 32, 52, 30], [59, 49, 52, 47], [60, 2, 52, 48], [60, 6, 52, 48, "_default"], [60, 14, 52, 48], [60, 17, 52, 48, "exports"], [60, 24, 52, 48], [60, 25, 52, 48, "default"], [60, 32, 52, 48], [60, 35, 53, 15, "ImageBackground"], [60, 50, 53, 30], [61, 0, 53, 30], [61, 3]], "functionMap": {"names": ["<global>", "forwardRef$argument_0"], "mappings": "AAA;8CCsB;CD4B"}}, "type": "js/module"}]}