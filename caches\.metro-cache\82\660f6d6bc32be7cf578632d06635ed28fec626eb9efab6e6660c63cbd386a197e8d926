{"dependencies": [{"name": "/home/<USER>/apps/mobile/.env", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 3, "column": 45, "index": 157}, "end": {"line": 3, "column": 83, "index": 195}}], "key": "pKkGRNRym0LzLhNTPO9VNeN2jc8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  // All of the requested modules are loaded behind enumerable getters.\n  const map = Object.defineProperties({}, {\n    \".env\": {\n      enumerable: true,\n      get() {\n        return require(_dependencyMap[0], \"/home/<USER>/apps/mobile/.env\");\n      }\n    }\n  });\n  function metroContext(request) {\n    return map[request];\n  }\n\n  // Return the keys that can be resolved.\n  metroContext.keys = function metroContextKeys() {\n    return Object.keys(map);\n  };\n\n  // Return the module identifier for a user request.\n  metroContext.resolve = function metroContextResolve(request) {\n    throw new Error('Unimplemented Metro module context functionality');\n  };\n  module.exports = metroContext;\n});", "lineCount": 25, "map": [[2, 2, 1, 0], [3, 2, 2, 0], [3, 8, 2, 6, "map"], [3, 11, 2, 9], [3, 14, 2, 12, "Object"], [3, 20, 2, 18], [3, 21, 2, 19, "defineProperties"], [3, 37, 2, 35], [3, 38, 2, 36], [3, 39, 2, 37], [3, 40, 2, 38], [3, 42, 2, 40], [4, 4, 3, 2], [4, 10, 3, 8], [4, 12, 3, 10], [5, 6, 3, 12, "enumerable"], [5, 16, 3, 22], [5, 18, 3, 24], [5, 22, 3, 28], [6, 6, 3, 30, "get"], [6, 9, 3, 33, "get"], [6, 10, 3, 33], [6, 12, 3, 36], [7, 8, 3, 38], [7, 15, 3, 45, "require"], [7, 22, 3, 52], [7, 23, 3, 52, "_dependencyMap"], [7, 37, 3, 52], [7, 71, 3, 82], [7, 72, 3, 83], [8, 6, 3, 85], [9, 4, 3, 87], [10, 2, 4, 0], [10, 3, 4, 1], [10, 4, 4, 2], [11, 2, 6, 0], [11, 11, 6, 9, "metroContext"], [11, 23, 6, 21, "metroContext"], [11, 24, 6, 22, "request"], [11, 31, 6, 29], [11, 33, 6, 31], [12, 4, 7, 4], [12, 11, 7, 11, "map"], [12, 14, 7, 14], [12, 15, 7, 15, "request"], [12, 22, 7, 22], [12, 23, 7, 23], [13, 2, 8, 0], [15, 2, 10, 0], [16, 2, 11, 0, "metroContext"], [16, 14, 11, 12], [16, 15, 11, 13, "keys"], [16, 19, 11, 17], [16, 22, 11, 20], [16, 31, 11, 29, "metroContextKeys"], [16, 47, 11, 45, "metroContextKeys"], [16, 48, 11, 45], [16, 50, 11, 48], [17, 4, 12, 2], [17, 11, 12, 9, "Object"], [17, 17, 12, 15], [17, 18, 12, 16, "keys"], [17, 22, 12, 20], [17, 23, 12, 21, "map"], [17, 26, 12, 24], [17, 27, 12, 25], [18, 2, 13, 0], [18, 3, 13, 1], [20, 2, 15, 0], [21, 2, 16, 0, "metroContext"], [21, 14, 16, 12], [21, 15, 16, 13, "resolve"], [21, 22, 16, 20], [21, 25, 16, 23], [21, 34, 16, 32, "metroContextResolve"], [21, 53, 16, 51, "metroContextResolve"], [21, 54, 16, 52, "request"], [21, 61, 16, 59], [21, 63, 16, 61], [22, 4, 17, 2], [22, 10, 17, 8], [22, 14, 17, 12, "Error"], [22, 19, 17, 17], [22, 20, 17, 18], [22, 70, 17, 68], [22, 71, 17, 69], [23, 2, 18, 0], [23, 3, 18, 1], [24, 2, 20, 0, "module"], [24, 8, 20, 6], [24, 9, 20, 7, "exports"], [24, 16, 20, 14], [24, 19, 20, 17, "metroContext"], [24, 31, 20, 29], [25, 0, 20, 30], [25, 3]], "functionMap": {"names": ["<global>", "Object.defineProperties$argument_1.env.get", "metroContext", "metroContextKeys", "metroContextResolve"], "mappings": "AAA;8BCE,wDD;AEG;CFE;oBGG;CHE;uBIG;CJE"}}, "type": "js/module"}]}