{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../modules/isWebColor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 181}, "end": {"line": 10, "column": 53, "index": 234}}], "key": "cwmPJIMH9ANQU9vlPIsVE4/Mseo=", "exportNames": ["*"]}}, {"name": "../../../exports/processColor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 235}, "end": {"line": 11, "column": 57, "index": 292}}], "key": "1LfHhihrxnxkyOujWKF/xyAY//g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _isWebColor = _interopRequireDefault(require(_dependencyMap[1], \"../../../modules/isWebColor\"));\n  var _processColor = _interopRequireDefault(require(_dependencyMap[2], \"../../../exports/processColor\"));\n  /**\n   * Copyright (c) Nicolas <PERSON>.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var normalizeColor = function normalizeColor(color, opacity) {\n    if (opacity === void 0) {\n      opacity = 1;\n    }\n    if (color == null) return;\n    if (typeof color === 'string' && (0, _isWebColor.default)(color)) {\n      return color;\n    }\n    var colorInt = (0, _processColor.default)(color);\n    if (colorInt != null) {\n      var r = colorInt >> 16 & 255;\n      var g = colorInt >> 8 & 255;\n      var b = colorInt & 255;\n      var a = (colorInt >> 24 & 255) / 255;\n      var alpha = (a * opacity).toFixed(2);\n      return \"rgba(\" + r + \",\" + g + \",\" + b + \",\" + alpha + \")\";\n    }\n  };\n  var _default = exports.default = normalizeColor;\n});", "lineCount": 37, "map": [[7, 2, 10, 0], [7, 6, 10, 0, "_isWebColor"], [7, 17, 10, 0], [7, 20, 10, 0, "_interopRequireDefault"], [7, 42, 10, 0], [7, 43, 10, 0, "require"], [7, 50, 10, 0], [7, 51, 10, 0, "_dependencyMap"], [7, 65, 10, 0], [8, 2, 11, 0], [8, 6, 11, 0, "_processColor"], [8, 19, 11, 0], [8, 22, 11, 0, "_interopRequireDefault"], [8, 44, 11, 0], [8, 45, 11, 0, "require"], [8, 52, 11, 0], [8, 53, 11, 0, "_dependencyMap"], [8, 67, 11, 0], [9, 2, 1, 0], [10, 0, 2, 0], [11, 0, 3, 0], [12, 0, 4, 0], [13, 0, 5, 0], [14, 0, 6, 0], [15, 0, 7, 0], [16, 0, 8, 0], [18, 2, 12, 0], [18, 6, 12, 4, "normalizeColor"], [18, 20, 12, 18], [18, 23, 12, 21], [18, 32, 12, 30, "normalizeColor"], [18, 46, 12, 44, "normalizeColor"], [18, 47, 12, 45, "color"], [18, 52, 12, 50], [18, 54, 12, 52, "opacity"], [18, 61, 12, 59], [18, 63, 12, 61], [19, 4, 13, 2], [19, 8, 13, 6, "opacity"], [19, 15, 13, 13], [19, 20, 13, 18], [19, 25, 13, 23], [19, 26, 13, 24], [19, 28, 13, 26], [20, 6, 14, 4, "opacity"], [20, 13, 14, 11], [20, 16, 14, 14], [20, 17, 14, 15], [21, 4, 15, 2], [22, 4, 16, 2], [22, 8, 16, 6, "color"], [22, 13, 16, 11], [22, 17, 16, 15], [22, 21, 16, 19], [22, 23, 16, 21], [23, 4, 17, 2], [23, 8, 17, 6], [23, 15, 17, 13, "color"], [23, 20, 17, 18], [23, 25, 17, 23], [23, 33, 17, 31], [23, 37, 17, 35], [23, 41, 17, 35, "isWebColor"], [23, 60, 17, 45], [23, 62, 17, 46, "color"], [23, 67, 17, 51], [23, 68, 17, 52], [23, 70, 17, 54], [24, 6, 18, 4], [24, 13, 18, 11, "color"], [24, 18, 18, 16], [25, 4, 19, 2], [26, 4, 20, 2], [26, 8, 20, 6, "colorInt"], [26, 16, 20, 14], [26, 19, 20, 17], [26, 23, 20, 17, "processColor"], [26, 44, 20, 29], [26, 46, 20, 30, "color"], [26, 51, 20, 35], [26, 52, 20, 36], [27, 4, 21, 2], [27, 8, 21, 6, "colorInt"], [27, 16, 21, 14], [27, 20, 21, 18], [27, 24, 21, 22], [27, 26, 21, 24], [28, 6, 22, 4], [28, 10, 22, 8, "r"], [28, 11, 22, 9], [28, 14, 22, 12, "colorInt"], [28, 22, 22, 20], [28, 26, 22, 24], [28, 28, 22, 26], [28, 31, 22, 29], [28, 34, 22, 32], [29, 6, 23, 4], [29, 10, 23, 8, "g"], [29, 11, 23, 9], [29, 14, 23, 12, "colorInt"], [29, 22, 23, 20], [29, 26, 23, 24], [29, 27, 23, 25], [29, 30, 23, 28], [29, 33, 23, 31], [30, 6, 24, 4], [30, 10, 24, 8, "b"], [30, 11, 24, 9], [30, 14, 24, 12, "colorInt"], [30, 22, 24, 20], [30, 25, 24, 23], [30, 28, 24, 26], [31, 6, 25, 4], [31, 10, 25, 8, "a"], [31, 11, 25, 9], [31, 14, 25, 12], [31, 15, 25, 13, "colorInt"], [31, 23, 25, 21], [31, 27, 25, 25], [31, 29, 25, 27], [31, 32, 25, 30], [31, 35, 25, 33], [31, 39, 25, 37], [31, 42, 25, 40], [32, 6, 26, 4], [32, 10, 26, 8, "alpha"], [32, 15, 26, 13], [32, 18, 26, 16], [32, 19, 26, 17, "a"], [32, 20, 26, 18], [32, 23, 26, 21, "opacity"], [32, 30, 26, 28], [32, 32, 26, 30, "toFixed"], [32, 39, 26, 37], [32, 40, 26, 38], [32, 41, 26, 39], [32, 42, 26, 40], [33, 6, 27, 4], [33, 13, 27, 11], [33, 20, 27, 18], [33, 23, 27, 21, "r"], [33, 24, 27, 22], [33, 27, 27, 25], [33, 30, 27, 28], [33, 33, 27, 31, "g"], [33, 34, 27, 32], [33, 37, 27, 35], [33, 40, 27, 38], [33, 43, 27, 41, "b"], [33, 44, 27, 42], [33, 47, 27, 45], [33, 50, 27, 48], [33, 53, 27, 51, "alpha"], [33, 58, 27, 56], [33, 61, 27, 59], [33, 64, 27, 62], [34, 4, 28, 2], [35, 2, 29, 0], [35, 3, 29, 1], [36, 2, 29, 2], [36, 6, 29, 2, "_default"], [36, 14, 29, 2], [36, 17, 29, 2, "exports"], [36, 24, 29, 2], [36, 25, 29, 2, "default"], [36, 32, 29, 2], [36, 35, 30, 15, "normalizeColor"], [36, 49, 30, 29], [37, 0, 30, 29], [37, 3]], "functionMap": {"names": ["<global>", "normalizeColor"], "mappings": "AAA;qBCW;CDiB"}}, "type": "js/module"}]}