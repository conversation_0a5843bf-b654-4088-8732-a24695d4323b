{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PointMode = void 0;\n  let PointMode = exports.PointMode = /*#__PURE__*/function (PointMode) {\n    PointMode[PointMode[\"Points\"] = 0] = \"Points\";\n    PointMode[PointMode[\"Lines\"] = 1] = \"Lines\";\n    PointMode[PointMode[\"Polygon\"] = 2] = \"Polygon\";\n    return PointMode;\n  }({});\n});", "lineCount": 12, "map": [[6, 2, 1, 7], [6, 6, 1, 11, "PointMode"], [6, 15, 1, 20], [6, 18, 1, 20, "exports"], [6, 25, 1, 20], [6, 26, 1, 20, "PointMode"], [6, 35, 1, 20], [6, 38, 1, 23], [6, 51, 1, 36], [6, 61, 1, 46, "PointMode"], [6, 70, 1, 55], [6, 72, 1, 57], [7, 4, 2, 2, "PointMode"], [7, 13, 2, 11], [7, 14, 2, 12, "PointMode"], [7, 23, 2, 21], [7, 24, 2, 22], [7, 32, 2, 30], [7, 33, 2, 31], [7, 36, 2, 34], [7, 37, 2, 35], [7, 38, 2, 36], [7, 41, 2, 39], [7, 49, 2, 47], [8, 4, 3, 2, "PointMode"], [8, 13, 3, 11], [8, 14, 3, 12, "PointMode"], [8, 23, 3, 21], [8, 24, 3, 22], [8, 31, 3, 29], [8, 32, 3, 30], [8, 35, 3, 33], [8, 36, 3, 34], [8, 37, 3, 35], [8, 40, 3, 38], [8, 47, 3, 45], [9, 4, 4, 2, "PointMode"], [9, 13, 4, 11], [9, 14, 4, 12, "PointMode"], [9, 23, 4, 21], [9, 24, 4, 22], [9, 33, 4, 31], [9, 34, 4, 32], [9, 37, 4, 35], [9, 38, 4, 36], [9, 39, 4, 37], [9, 42, 4, 40], [9, 51, 4, 49], [10, 4, 5, 2], [10, 11, 5, 9, "PointMode"], [10, 20, 5, 18], [11, 2, 6, 0], [11, 3, 6, 1], [11, 4, 6, 2], [11, 5, 6, 3], [11, 6, 6, 4], [11, 7, 6, 5], [12, 0, 6, 6], [12, 3]], "functionMap": {"names": ["<global>", "<anonymous>"], "mappings": "AAA,oCC;CDK"}}, "type": "js/module"}]}