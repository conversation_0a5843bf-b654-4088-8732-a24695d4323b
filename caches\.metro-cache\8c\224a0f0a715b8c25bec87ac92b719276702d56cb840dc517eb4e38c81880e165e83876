{"dependencies": [{"name": "@react-navigation/routers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 58, "index": 73}}], "key": "TumjUqgKkj40CL5/as2VxzLfO54=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 74}, "end": {"line": 4, "column": 31, "index": 105}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 106}, "end": {"line": 5, "column": 59, "index": 165}}], "key": "RM0XoJ1uy5+hqq85ZlLNt6FYuco=", "exportNames": ["*"]}}, {"name": "./types.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 166}, "end": {"line": 6, "column": 47, "index": 213}}], "key": "yJvqu7zVoaSgx/LOxsKU/6eppkQ=", "exportNames": ["*"]}}, {"name": "./UnhandledActionContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 214}, "end": {"line": 7, "column": 69, "index": 283}}], "key": "hbxQFgxZ0nD1dniBnLKjFC5C/nw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useNavigationHelpers = useNavigationHelpers;\n  var _routers = require(_dependencyMap[0], \"@react-navigation/routers\");\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _NavigationContext = require(_dependencyMap[2], \"./NavigationContext.js\");\n  var _types = require(_dependencyMap[3], \"./types.js\");\n  var _UnhandledActionContext = require(_dependencyMap[4], \"./UnhandledActionContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  // This is to make TypeScript compiler happy\n  // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n  _types.PrivateValueStore;\n  /**\n   * Navigation object with helper methods to be used by a navigator.\n   * This object includes methods for common actions as well as methods the parent screen's navigation object.\n   */\n  function useNavigationHelpers({\n    id: navigatorId,\n    onAction,\n    getState,\n    emitter,\n    router,\n    stateRef\n  }) {\n    const onUnhandledAction = React.useContext(_UnhandledActionContext.UnhandledActionContext);\n    const parentNavigationHelpers = React.useContext(_NavigationContext.NavigationContext);\n    return React.useMemo(() => {\n      const dispatch = op => {\n        const action = typeof op === 'function' ? op(getState()) : op;\n        const handled = onAction(action);\n        if (!handled) {\n          onUnhandledAction?.(action);\n        }\n      };\n      const actions = {\n        ...router.actionCreators,\n        ..._routers.CommonActions\n      };\n      const helpers = Object.keys(actions).reduce((acc, name) => {\n        // @ts-expect-error: name is a valid key, but TypeScript is dumb\n        acc[name] = (...args) => dispatch(actions[name](...args));\n        return acc;\n      }, {});\n      const navigationHelpers = {\n        ...parentNavigationHelpers,\n        ...helpers,\n        dispatch,\n        emit: emitter.emit,\n        isFocused: parentNavigationHelpers ? parentNavigationHelpers.isFocused : () => true,\n        canGoBack: () => {\n          const state = getState();\n          return router.getStateForAction(state, _routers.CommonActions.goBack(), {\n            routeNames: state.routeNames,\n            routeParamList: {},\n            routeGetIdList: {}\n          }) !== null || parentNavigationHelpers?.canGoBack() || false;\n        },\n        getId: () => navigatorId,\n        getParent: id => {\n          if (id !== undefined) {\n            let current = navigationHelpers;\n            while (current && id !== current.getId()) {\n              current = current.getParent();\n            }\n            return current;\n          }\n          return parentNavigationHelpers;\n        },\n        getState: () => {\n          // FIXME: Workaround for when the state is read during render\n          // By this time, we haven't committed the new state yet\n          // Without this `useSyncExternalStore` will keep reading the old state\n          // This may result in `useNavigationState` or `useIsFocused` returning wrong values\n          // Apart from `useSyncExternalStore`, `getState` should never be called during render\n          if (stateRef.current != null) {\n            return stateRef.current;\n          }\n          return getState();\n        }\n      };\n      return navigationHelpers;\n    }, [router, parentNavigationHelpers, emitter.emit, getState, onAction, onUnhandledAction, navigatorId, stateRef]);\n  }\n});", "lineCount": 88, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useNavigationHelpers"], [7, 30, 1, 13], [7, 33, 1, 13, "useNavigationHelpers"], [7, 53, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_routers"], [8, 14, 3, 0], [8, 17, 3, 0, "require"], [8, 24, 3, 0], [8, 25, 3, 0, "_dependencyMap"], [8, 39, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "React"], [9, 11, 4, 0], [9, 14, 4, 0, "_interopRequireWildcard"], [9, 37, 4, 0], [9, 38, 4, 0, "require"], [9, 45, 4, 0], [9, 46, 4, 0, "_dependencyMap"], [9, 60, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_NavigationContext"], [10, 24, 5, 0], [10, 27, 5, 0, "require"], [10, 34, 5, 0], [10, 35, 5, 0, "_dependencyMap"], [10, 49, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_types"], [11, 12, 6, 0], [11, 15, 6, 0, "require"], [11, 22, 6, 0], [11, 23, 6, 0, "_dependencyMap"], [11, 37, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_UnhandledActionContext"], [12, 29, 7, 0], [12, 32, 7, 0, "require"], [12, 39, 7, 0], [12, 40, 7, 0, "_dependencyMap"], [12, 54, 7, 0], [13, 2, 7, 69], [13, 11, 7, 69, "_interopRequireWildcard"], [13, 35, 7, 69, "e"], [13, 36, 7, 69], [13, 38, 7, 69, "t"], [13, 39, 7, 69], [13, 68, 7, 69, "WeakMap"], [13, 75, 7, 69], [13, 81, 7, 69, "r"], [13, 82, 7, 69], [13, 89, 7, 69, "WeakMap"], [13, 96, 7, 69], [13, 100, 7, 69, "n"], [13, 101, 7, 69], [13, 108, 7, 69, "WeakMap"], [13, 115, 7, 69], [13, 127, 7, 69, "_interopRequireWildcard"], [13, 150, 7, 69], [13, 162, 7, 69, "_interopRequireWildcard"], [13, 163, 7, 69, "e"], [13, 164, 7, 69], [13, 166, 7, 69, "t"], [13, 167, 7, 69], [13, 176, 7, 69, "t"], [13, 177, 7, 69], [13, 181, 7, 69, "e"], [13, 182, 7, 69], [13, 186, 7, 69, "e"], [13, 187, 7, 69], [13, 188, 7, 69, "__esModule"], [13, 198, 7, 69], [13, 207, 7, 69, "e"], [13, 208, 7, 69], [13, 214, 7, 69, "o"], [13, 215, 7, 69], [13, 217, 7, 69, "i"], [13, 218, 7, 69], [13, 220, 7, 69, "f"], [13, 221, 7, 69], [13, 226, 7, 69, "__proto__"], [13, 235, 7, 69], [13, 243, 7, 69, "default"], [13, 250, 7, 69], [13, 252, 7, 69, "e"], [13, 253, 7, 69], [13, 270, 7, 69, "e"], [13, 271, 7, 69], [13, 294, 7, 69, "e"], [13, 295, 7, 69], [13, 320, 7, 69, "e"], [13, 321, 7, 69], [13, 330, 7, 69, "f"], [13, 331, 7, 69], [13, 337, 7, 69, "o"], [13, 338, 7, 69], [13, 341, 7, 69, "t"], [13, 342, 7, 69], [13, 345, 7, 69, "n"], [13, 346, 7, 69], [13, 349, 7, 69, "r"], [13, 350, 7, 69], [13, 358, 7, 69, "o"], [13, 359, 7, 69], [13, 360, 7, 69, "has"], [13, 363, 7, 69], [13, 364, 7, 69, "e"], [13, 365, 7, 69], [13, 375, 7, 69, "o"], [13, 376, 7, 69], [13, 377, 7, 69, "get"], [13, 380, 7, 69], [13, 381, 7, 69, "e"], [13, 382, 7, 69], [13, 385, 7, 69, "o"], [13, 386, 7, 69], [13, 387, 7, 69, "set"], [13, 390, 7, 69], [13, 391, 7, 69, "e"], [13, 392, 7, 69], [13, 394, 7, 69, "f"], [13, 395, 7, 69], [13, 411, 7, 69, "t"], [13, 412, 7, 69], [13, 416, 7, 69, "e"], [13, 417, 7, 69], [13, 433, 7, 69, "t"], [13, 434, 7, 69], [13, 441, 7, 69, "hasOwnProperty"], [13, 455, 7, 69], [13, 456, 7, 69, "call"], [13, 460, 7, 69], [13, 461, 7, 69, "e"], [13, 462, 7, 69], [13, 464, 7, 69, "t"], [13, 465, 7, 69], [13, 472, 7, 69, "i"], [13, 473, 7, 69], [13, 477, 7, 69, "o"], [13, 478, 7, 69], [13, 481, 7, 69, "Object"], [13, 487, 7, 69], [13, 488, 7, 69, "defineProperty"], [13, 502, 7, 69], [13, 507, 7, 69, "Object"], [13, 513, 7, 69], [13, 514, 7, 69, "getOwnPropertyDescriptor"], [13, 538, 7, 69], [13, 539, 7, 69, "e"], [13, 540, 7, 69], [13, 542, 7, 69, "t"], [13, 543, 7, 69], [13, 550, 7, 69, "i"], [13, 551, 7, 69], [13, 552, 7, 69, "get"], [13, 555, 7, 69], [13, 559, 7, 69, "i"], [13, 560, 7, 69], [13, 561, 7, 69, "set"], [13, 564, 7, 69], [13, 568, 7, 69, "o"], [13, 569, 7, 69], [13, 570, 7, 69, "f"], [13, 571, 7, 69], [13, 573, 7, 69, "t"], [13, 574, 7, 69], [13, 576, 7, 69, "i"], [13, 577, 7, 69], [13, 581, 7, 69, "f"], [13, 582, 7, 69], [13, 583, 7, 69, "t"], [13, 584, 7, 69], [13, 588, 7, 69, "e"], [13, 589, 7, 69], [13, 590, 7, 69, "t"], [13, 591, 7, 69], [13, 602, 7, 69, "f"], [13, 603, 7, 69], [13, 608, 7, 69, "e"], [13, 609, 7, 69], [13, 611, 7, 69, "t"], [13, 612, 7, 69], [14, 2, 8, 0], [15, 2, 9, 0], [16, 2, 10, 0, "PrivateValueStore"], [16, 26, 10, 17], [17, 2, 11, 0], [18, 0, 12, 0], [19, 0, 13, 0], [20, 0, 14, 0], [21, 2, 15, 7], [21, 11, 15, 16, "useNavigationHelpers"], [21, 31, 15, 36, "useNavigationHelpers"], [21, 32, 15, 37], [22, 4, 16, 2, "id"], [22, 6, 16, 4], [22, 8, 16, 6, "navigatorId"], [22, 19, 16, 17], [23, 4, 17, 2, "onAction"], [23, 12, 17, 10], [24, 4, 18, 2, "getState"], [24, 12, 18, 10], [25, 4, 19, 2, "emitter"], [25, 11, 19, 9], [26, 4, 20, 2, "router"], [26, 10, 20, 8], [27, 4, 21, 2, "stateRef"], [28, 2, 22, 0], [28, 3, 22, 1], [28, 5, 22, 3], [29, 4, 23, 2], [29, 10, 23, 8, "onUnhandledAction"], [29, 27, 23, 25], [29, 30, 23, 28, "React"], [29, 35, 23, 33], [29, 36, 23, 34, "useContext"], [29, 46, 23, 44], [29, 47, 23, 45, "UnhandledActionContext"], [29, 93, 23, 67], [29, 94, 23, 68], [30, 4, 24, 2], [30, 10, 24, 8, "parentNavigationHelpers"], [30, 33, 24, 31], [30, 36, 24, 34, "React"], [30, 41, 24, 39], [30, 42, 24, 40, "useContext"], [30, 52, 24, 50], [30, 53, 24, 51, "NavigationContext"], [30, 89, 24, 68], [30, 90, 24, 69], [31, 4, 25, 2], [31, 11, 25, 9, "React"], [31, 16, 25, 14], [31, 17, 25, 15, "useMemo"], [31, 24, 25, 22], [31, 25, 25, 23], [31, 31, 25, 29], [32, 6, 26, 4], [32, 12, 26, 10, "dispatch"], [32, 20, 26, 18], [32, 23, 26, 21, "op"], [32, 25, 26, 23], [32, 29, 26, 27], [33, 8, 27, 6], [33, 14, 27, 12, "action"], [33, 20, 27, 18], [33, 23, 27, 21], [33, 30, 27, 28, "op"], [33, 32, 27, 30], [33, 37, 27, 35], [33, 47, 27, 45], [33, 50, 27, 48, "op"], [33, 52, 27, 50], [33, 53, 27, 51, "getState"], [33, 61, 27, 59], [33, 62, 27, 60], [33, 63, 27, 61], [33, 64, 27, 62], [33, 67, 27, 65, "op"], [33, 69, 27, 67], [34, 8, 28, 6], [34, 14, 28, 12, "handled"], [34, 21, 28, 19], [34, 24, 28, 22, "onAction"], [34, 32, 28, 30], [34, 33, 28, 31, "action"], [34, 39, 28, 37], [34, 40, 28, 38], [35, 8, 29, 6], [35, 12, 29, 10], [35, 13, 29, 11, "handled"], [35, 20, 29, 18], [35, 22, 29, 20], [36, 10, 30, 8, "onUnhandledAction"], [36, 27, 30, 25], [36, 30, 30, 28, "action"], [36, 36, 30, 34], [36, 37, 30, 35], [37, 8, 31, 6], [38, 6, 32, 4], [38, 7, 32, 5], [39, 6, 33, 4], [39, 12, 33, 10, "actions"], [39, 19, 33, 17], [39, 22, 33, 20], [40, 8, 34, 6], [40, 11, 34, 9, "router"], [40, 17, 34, 15], [40, 18, 34, 16, "actionCreators"], [40, 32, 34, 30], [41, 8, 35, 6], [41, 11, 35, 9, "CommonActions"], [42, 6, 36, 4], [42, 7, 36, 5], [43, 6, 37, 4], [43, 12, 37, 10, "helpers"], [43, 19, 37, 17], [43, 22, 37, 20, "Object"], [43, 28, 37, 26], [43, 29, 37, 27, "keys"], [43, 33, 37, 31], [43, 34, 37, 32, "actions"], [43, 41, 37, 39], [43, 42, 37, 40], [43, 43, 37, 41, "reduce"], [43, 49, 37, 47], [43, 50, 37, 48], [43, 51, 37, 49, "acc"], [43, 54, 37, 52], [43, 56, 37, 54, "name"], [43, 60, 37, 58], [43, 65, 37, 63], [44, 8, 38, 6], [45, 8, 39, 6, "acc"], [45, 11, 39, 9], [45, 12, 39, 10, "name"], [45, 16, 39, 14], [45, 17, 39, 15], [45, 20, 39, 18], [45, 21, 39, 19], [45, 24, 39, 22, "args"], [45, 28, 39, 26], [45, 33, 39, 31, "dispatch"], [45, 41, 39, 39], [45, 42, 39, 40, "actions"], [45, 49, 39, 47], [45, 50, 39, 48, "name"], [45, 54, 39, 52], [45, 55, 39, 53], [45, 56, 39, 54], [45, 59, 39, 57, "args"], [45, 63, 39, 61], [45, 64, 39, 62], [45, 65, 39, 63], [46, 8, 40, 6], [46, 15, 40, 13, "acc"], [46, 18, 40, 16], [47, 6, 41, 4], [47, 7, 41, 5], [47, 9, 41, 7], [47, 10, 41, 8], [47, 11, 41, 9], [47, 12, 41, 10], [48, 6, 42, 4], [48, 12, 42, 10, "navigationHelpers"], [48, 29, 42, 27], [48, 32, 42, 30], [49, 8, 43, 6], [49, 11, 43, 9, "parentNavigationHelpers"], [49, 34, 43, 32], [50, 8, 44, 6], [50, 11, 44, 9, "helpers"], [50, 18, 44, 16], [51, 8, 45, 6, "dispatch"], [51, 16, 45, 14], [52, 8, 46, 6, "emit"], [52, 12, 46, 10], [52, 14, 46, 12, "emitter"], [52, 21, 46, 19], [52, 22, 46, 20, "emit"], [52, 26, 46, 24], [53, 8, 47, 6, "isFocused"], [53, 17, 47, 15], [53, 19, 47, 17, "parentNavigationHelpers"], [53, 42, 47, 40], [53, 45, 47, 43, "parentNavigationHelpers"], [53, 68, 47, 66], [53, 69, 47, 67, "isFocused"], [53, 78, 47, 76], [53, 81, 47, 79], [53, 87, 47, 85], [53, 91, 47, 89], [54, 8, 48, 6, "canGoBack"], [54, 17, 48, 15], [54, 19, 48, 17, "canGoBack"], [54, 20, 48, 17], [54, 25, 48, 23], [55, 10, 49, 8], [55, 16, 49, 14, "state"], [55, 21, 49, 19], [55, 24, 49, 22, "getState"], [55, 32, 49, 30], [55, 33, 49, 31], [55, 34, 49, 32], [56, 10, 50, 8], [56, 17, 50, 15, "router"], [56, 23, 50, 21], [56, 24, 50, 22, "getStateForAction"], [56, 41, 50, 39], [56, 42, 50, 40, "state"], [56, 47, 50, 45], [56, 49, 50, 47, "CommonActions"], [56, 71, 50, 60], [56, 72, 50, 61, "goBack"], [56, 78, 50, 67], [56, 79, 50, 68], [56, 80, 50, 69], [56, 82, 50, 71], [57, 12, 51, 10, "routeNames"], [57, 22, 51, 20], [57, 24, 51, 22, "state"], [57, 29, 51, 27], [57, 30, 51, 28, "routeNames"], [57, 40, 51, 38], [58, 12, 52, 10, "routeParamList"], [58, 26, 52, 24], [58, 28, 52, 26], [58, 29, 52, 27], [58, 30, 52, 28], [59, 12, 53, 10, "routeGetIdList"], [59, 26, 53, 24], [59, 28, 53, 26], [59, 29, 53, 27], [60, 10, 54, 8], [60, 11, 54, 9], [60, 12, 54, 10], [60, 17, 54, 15], [60, 21, 54, 19], [60, 25, 54, 23, "parentNavigationHelpers"], [60, 48, 54, 46], [60, 50, 54, 48, "canGoBack"], [60, 59, 54, 57], [60, 60, 54, 58], [60, 61, 54, 59], [60, 65, 54, 63], [60, 70, 54, 68], [61, 8, 55, 6], [61, 9, 55, 7], [62, 8, 56, 6, "getId"], [62, 13, 56, 11], [62, 15, 56, 13, "getId"], [62, 16, 56, 13], [62, 21, 56, 19, "navigatorId"], [62, 32, 56, 30], [63, 8, 57, 6, "getParent"], [63, 17, 57, 15], [63, 19, 57, 17, "id"], [63, 21, 57, 19], [63, 25, 57, 23], [64, 10, 58, 8], [64, 14, 58, 12, "id"], [64, 16, 58, 14], [64, 21, 58, 19, "undefined"], [64, 30, 58, 28], [64, 32, 58, 30], [65, 12, 59, 10], [65, 16, 59, 14, "current"], [65, 23, 59, 21], [65, 26, 59, 24, "navigationHelpers"], [65, 43, 59, 41], [66, 12, 60, 10], [66, 19, 60, 17, "current"], [66, 26, 60, 24], [66, 30, 60, 28, "id"], [66, 32, 60, 30], [66, 37, 60, 35, "current"], [66, 44, 60, 42], [66, 45, 60, 43, "getId"], [66, 50, 60, 48], [66, 51, 60, 49], [66, 52, 60, 50], [66, 54, 60, 52], [67, 14, 61, 12, "current"], [67, 21, 61, 19], [67, 24, 61, 22, "current"], [67, 31, 61, 29], [67, 32, 61, 30, "getParent"], [67, 41, 61, 39], [67, 42, 61, 40], [67, 43, 61, 41], [68, 12, 62, 10], [69, 12, 63, 10], [69, 19, 63, 17, "current"], [69, 26, 63, 24], [70, 10, 64, 8], [71, 10, 65, 8], [71, 17, 65, 15, "parentNavigationHelpers"], [71, 40, 65, 38], [72, 8, 66, 6], [72, 9, 66, 7], [73, 8, 67, 6, "getState"], [73, 16, 67, 14], [73, 18, 67, 16, "getState"], [73, 19, 67, 16], [73, 24, 67, 22], [74, 10, 68, 8], [75, 10, 69, 8], [76, 10, 70, 8], [77, 10, 71, 8], [78, 10, 72, 8], [79, 10, 73, 8], [79, 14, 73, 12, "stateRef"], [79, 22, 73, 20], [79, 23, 73, 21, "current"], [79, 30, 73, 28], [79, 34, 73, 32], [79, 38, 73, 36], [79, 40, 73, 38], [80, 12, 74, 10], [80, 19, 74, 17, "stateRef"], [80, 27, 74, 25], [80, 28, 74, 26, "current"], [80, 35, 74, 33], [81, 10, 75, 8], [82, 10, 76, 8], [82, 17, 76, 15, "getState"], [82, 25, 76, 23], [82, 26, 76, 24], [82, 27, 76, 25], [83, 8, 77, 6], [84, 6, 78, 4], [84, 7, 78, 5], [85, 6, 79, 4], [85, 13, 79, 11, "navigationHelpers"], [85, 30, 79, 28], [86, 4, 80, 2], [86, 5, 80, 3], [86, 7, 80, 5], [86, 8, 80, 6, "router"], [86, 14, 80, 12], [86, 16, 80, 14, "parentNavigationHelpers"], [86, 39, 80, 37], [86, 41, 80, 39, "emitter"], [86, 48, 80, 46], [86, 49, 80, 47, "emit"], [86, 53, 80, 51], [86, 55, 80, 53, "getState"], [86, 63, 80, 61], [86, 65, 80, 63, "onAction"], [86, 73, 80, 71], [86, 75, 80, 73, "onUnhandledAction"], [86, 92, 80, 90], [86, 94, 80, 92, "navigatorId"], [86, 105, 80, 103], [86, 107, 80, 105, "stateRef"], [86, 115, 80, 113], [86, 116, 80, 114], [86, 117, 80, 115], [87, 2, 81, 0], [88, 0, 81, 1], [88, 3]], "functionMap": {"names": ["<global>", "useNavigationHelpers", "React.useMemo$argument_0", "dispatch", "Object.keys.reduce$argument_0", "acc.name", "<anonymous>", "navigationHelpers.canGoBack", "navigationHelpers.getId", "navigationHelpers.getParent", "navigationHelpers.getState"], "mappings": "AAA;OCc;uBCU;qBCC;KDM;gDEK;kBCE,6CD;KFE;+EIM,UJ;iBKC;OLO;aMC,iBN;iBOC;OPS;gBQC;ORU;GDG;CDC"}}, "type": "js/module"}]}