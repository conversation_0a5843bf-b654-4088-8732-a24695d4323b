{"dependencies": [{"name": "../../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 152}, "end": {"line": 10, "column": 46, "index": 198}}], "key": "pPfOdxbh9mtPdO2EBvl67ARfj+c=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.isValidRubberBandConfig = exports.VELOCITY_EPS = exports.SLOPE_FACTOR = void 0;\n  var _PlatformChecker = require(_dependencyMap[0], \"../../PlatformChecker\");\n  var IS_WEB = (0, _PlatformChecker.isWeb)();\n  var VELOCITY_EPS = exports.VELOCITY_EPS = IS_WEB ? 1 / 20 : 1;\n  var SLOPE_FACTOR = exports.SLOPE_FACTOR = 0.1;\n\n  /**\n   * The decay animation configuration.\n   *\n   * @param velocity - Initial velocity of the animation. Defaults to 0.\n   * @param deceleration - The rate at which the velocity decreases over time.\n   *   Defaults to 0.998.\n   * @param clamp - Array of two numbers which restricts animation's range.\n   *   Defaults to [].\n   * @param velocityFactor - Velocity multiplier. Defaults to 1.\n   * @param rubberBandEffect - Makes the animation bounce over the limit specified\n   *   in `clamp`. Defaults to `false`.\n   * @param rubberBandFactor - Strength of the rubber band effect. Defaults to\n   *   0.6.\n   * @param reduceMotion - Determines how the animation responds to the device's\n   *   reduced motion accessibility setting. Default to `ReduceMotion.System` -\n   *   {@link ReduceMotion}.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withDecay#config\n   */\n\n  // If user wants to use rubber band decay animation we have to make sure he has provided clamp\n  var _worklet_2431003556525_init_data = {\n    code: \"function isValidRubberBandConfig_reactNativeReanimated_utilsTs1(config){return!!config.rubberBandEffect&&Array.isArray(config.clamp)&&config.clamp.length===2;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/animation/decay/utils.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"isValidRubberBandConfig_reactNativeReanimated_utilsTs1\\\",\\\"config\\\",\\\"rubberBandEffect\\\",\\\"Array\\\",\\\"isArray\\\",\\\"clamp\\\",\\\"length\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/animation/decay/utils.ts\\\"],\\\"mappings\\\":\\\"AA4EO,SAAAA,sDAE4BA,CAAAC,MAAA,EAEjC,MACE,CAAC,CAACA,MAAM,CAACC,gBAAgB,EACzBC,KAAK,CAACC,OAAO,CAACH,MAAM,CAACI,KAAK,CAAC,EAC3BJ,MAAM,CAACI,KAAK,CAACC,MAAM,GAAK,CAAC,CAE7B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var isValidRubberBandConfig = exports.isValidRubberBandConfig = function () {\n    var _e = [new global.Error(), 1, -27];\n    var isValidRubberBandConfig = function (config) {\n      return !!config.rubberBandEffect && Array.isArray(config.clamp) && config.clamp.length === 2;\n    };\n    isValidRubberBandConfig.__closure = {};\n    isValidRubberBandConfig.__workletHash = 2431003556525;\n    isValidRubberBandConfig.__initData = _worklet_2431003556525_init_data;\n    isValidRubberBandConfig.__stackDetails = _e;\n    return isValidRubberBandConfig;\n  }();\n});", "lineCount": 50, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "isValidRubberBandConfig"], [7, 33, 1, 13], [7, 36, 1, 13, "exports"], [7, 43, 1, 13], [7, 44, 1, 13, "VELOCITY_EPS"], [7, 56, 1, 13], [7, 59, 1, 13, "exports"], [7, 66, 1, 13], [7, 67, 1, 13, "SLOPE_FACTOR"], [7, 79, 1, 13], [8, 2, 10, 0], [8, 6, 10, 0, "_PlatformChecker"], [8, 22, 10, 0], [8, 25, 10, 0, "require"], [8, 32, 10, 0], [8, 33, 10, 0, "_dependencyMap"], [8, 47, 10, 0], [9, 2, 12, 0], [9, 6, 12, 6, "IS_WEB"], [9, 12, 12, 12], [9, 15, 12, 15], [9, 19, 12, 15, "isWeb"], [9, 41, 12, 20], [9, 43, 12, 21], [9, 44, 12, 22], [10, 2, 13, 7], [10, 6, 13, 13, "VELOCITY_EPS"], [10, 18, 13, 25], [10, 21, 13, 25, "exports"], [10, 28, 13, 25], [10, 29, 13, 25, "VELOCITY_EPS"], [10, 41, 13, 25], [10, 44, 13, 28, "IS_WEB"], [10, 50, 13, 34], [10, 53, 13, 37], [10, 54, 13, 38], [10, 57, 13, 41], [10, 59, 13, 43], [10, 62, 13, 46], [10, 63, 13, 47], [11, 2, 14, 7], [11, 6, 14, 13, "SLOPE_FACTOR"], [11, 18, 14, 25], [11, 21, 14, 25, "exports"], [11, 28, 14, 25], [11, 29, 14, 25, "SLOPE_FACTOR"], [11, 41, 14, 25], [11, 44, 14, 28], [11, 47, 14, 31], [13, 2, 31, 0], [14, 0, 32, 0], [15, 0, 33, 0], [16, 0, 34, 0], [17, 0, 35, 0], [18, 0, 36, 0], [19, 0, 37, 0], [20, 0, 38, 0], [21, 0, 39, 0], [22, 0, 40, 0], [23, 0, 41, 0], [24, 0, 42, 0], [25, 0, 43, 0], [26, 0, 44, 0], [27, 0, 45, 0], [28, 0, 46, 0], [29, 0, 47, 0], [30, 0, 48, 0], [32, 2, 71, 0], [33, 2, 71, 0], [33, 6, 71, 0, "_worklet_2431003556525_init_data"], [33, 38, 71, 0], [34, 4, 71, 0, "code"], [34, 8, 71, 0], [35, 4, 71, 0, "location"], [35, 12, 71, 0], [36, 4, 71, 0, "sourceMap"], [36, 13, 71, 0], [37, 4, 71, 0, "version"], [37, 11, 71, 0], [38, 2, 71, 0], [39, 2, 71, 0], [39, 6, 71, 0, "isValidRubberBandConfig"], [39, 29, 71, 0], [39, 32, 71, 0, "exports"], [39, 39, 71, 0], [39, 40, 71, 0, "isValidRubberBandConfig"], [39, 63, 71, 0], [39, 66, 77, 7], [40, 4, 77, 7], [40, 8, 77, 7, "_e"], [40, 10, 77, 7], [40, 18, 77, 7, "global"], [40, 24, 77, 7], [40, 25, 77, 7, "Error"], [40, 30, 77, 7], [41, 4, 77, 7], [41, 8, 77, 7, "isValidRubberBandConfig"], [41, 31, 77, 7], [41, 43, 77, 7, "isValidRubberBandConfig"], [41, 44, 78, 2, "config"], [41, 50, 78, 28], [41, 52, 79, 35], [42, 6, 81, 2], [42, 13, 82, 4], [42, 14, 82, 5], [42, 15, 82, 6, "config"], [42, 21, 82, 12], [42, 22, 82, 13, "rubberBandEffect"], [42, 38, 82, 29], [42, 42, 83, 4, "Array"], [42, 47, 83, 9], [42, 48, 83, 10, "isArray"], [42, 55, 83, 17], [42, 56, 83, 18, "config"], [42, 62, 83, 24], [42, 63, 83, 25, "clamp"], [42, 68, 83, 30], [42, 69, 83, 31], [42, 73, 84, 4, "config"], [42, 79, 84, 10], [42, 80, 84, 11, "clamp"], [42, 85, 84, 16], [42, 86, 84, 17, "length"], [42, 92, 84, 23], [42, 97, 84, 28], [42, 98, 84, 29], [43, 4, 86, 0], [43, 5, 86, 1], [44, 4, 86, 1, "isValidRubberBandConfig"], [44, 27, 86, 1], [44, 28, 86, 1, "__closure"], [44, 37, 86, 1], [45, 4, 86, 1, "isValidRubberBandConfig"], [45, 27, 86, 1], [45, 28, 86, 1, "__workletHash"], [45, 41, 86, 1], [46, 4, 86, 1, "isValidRubberBandConfig"], [46, 27, 86, 1], [46, 28, 86, 1, "__initData"], [46, 38, 86, 1], [46, 41, 86, 1, "_worklet_2431003556525_init_data"], [46, 73, 86, 1], [47, 4, 86, 1, "isValidRubberBandConfig"], [47, 27, 86, 1], [47, 28, 86, 1, "__stackDetails"], [47, 42, 86, 1], [47, 45, 86, 1, "_e"], [47, 47, 86, 1], [48, 4, 86, 1], [48, 11, 86, 1, "isValidRubberBandConfig"], [48, 34, 86, 1], [49, 2, 86, 1], [49, 3, 77, 7], [50, 0, 77, 7], [50, 3]], "functionMap": {"names": ["<global>", "isValidRubberBandConfig"], "mappings": "AAA;OC4E;CDS"}}, "type": "js/module"}]}