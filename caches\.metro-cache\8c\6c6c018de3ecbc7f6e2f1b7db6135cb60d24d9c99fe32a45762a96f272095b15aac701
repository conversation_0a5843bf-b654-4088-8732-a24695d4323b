{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 48}}], "key": "/m0HqCpVZ4yItbJJaw+YeR/qFWU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _Platform = _interopRequireDefault(require(_dependencyMap[1], \"../../Utilities/Platform\"));\n  function processDecelerationRate(decelerationRate) {\n    if (decelerationRate === 'normal') {\n      return _Platform.default.select({\n        ios: 0.998,\n        android: 0.985\n      });\n    } else if (decelerationRate === 'fast') {\n      return _Platform.default.select({\n        ios: 0.99,\n        android: 0.9\n      });\n    }\n    return decelerationRate;\n  }\n  var _default = exports.default = processDecelerationRate;\n});", "lineCount": 23, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_Platform"], [7, 15, 11, 0], [7, 18, 11, 0, "_interopRequireDefault"], [7, 40, 11, 0], [7, 41, 11, 0, "require"], [7, 48, 11, 0], [7, 49, 11, 0, "_dependencyMap"], [7, 63, 11, 0], [8, 2, 13, 0], [8, 11, 13, 9, "processDecelerationRate"], [8, 34, 13, 32, "processDecelerationRate"], [8, 35, 14, 2, "decelerationRate"], [8, 51, 14, 46], [8, 53, 15, 10], [9, 4, 16, 2], [9, 8, 16, 6, "decelerationRate"], [9, 24, 16, 22], [9, 29, 16, 27], [9, 37, 16, 35], [9, 39, 16, 37], [10, 6, 17, 4], [10, 13, 17, 11, "Platform"], [10, 30, 17, 19], [10, 31, 17, 20, "select"], [10, 37, 17, 26], [10, 38, 17, 27], [11, 8, 18, 6, "ios"], [11, 11, 18, 9], [11, 13, 18, 11], [11, 18, 18, 16], [12, 8, 19, 6, "android"], [12, 15, 19, 13], [12, 17, 19, 15], [13, 6, 20, 4], [13, 7, 20, 5], [13, 8, 20, 6], [14, 4, 21, 2], [14, 5, 21, 3], [14, 11, 21, 9], [14, 15, 21, 13, "decelerationRate"], [14, 31, 21, 29], [14, 36, 21, 34], [14, 42, 21, 40], [14, 44, 21, 42], [15, 6, 22, 4], [15, 13, 22, 11, "Platform"], [15, 30, 22, 19], [15, 31, 22, 20, "select"], [15, 37, 22, 26], [15, 38, 22, 27], [16, 8, 23, 6, "ios"], [16, 11, 23, 9], [16, 13, 23, 11], [16, 17, 23, 15], [17, 8, 24, 6, "android"], [17, 15, 24, 13], [17, 17, 24, 15], [18, 6, 25, 4], [18, 7, 25, 5], [18, 8, 25, 6], [19, 4, 26, 2], [20, 4, 27, 2], [20, 11, 27, 9, "decelerationRate"], [20, 27, 27, 25], [21, 2, 28, 0], [22, 2, 28, 1], [22, 6, 28, 1, "_default"], [22, 14, 28, 1], [22, 17, 28, 1, "exports"], [22, 24, 28, 1], [22, 25, 28, 1, "default"], [22, 32, 28, 1], [22, 35, 30, 15, "processDecelerationRate"], [22, 58, 30, 38], [23, 0, 30, 38], [23, 3]], "functionMap": {"names": ["<global>", "processDecelerationRate"], "mappings": "AAA;ACY;CDe"}}, "type": "js/module"}]}