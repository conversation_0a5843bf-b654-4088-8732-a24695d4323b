{"dependencies": [{"name": "../utils/getBundleUrl", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 64}, "end": {"line": 3, "column": 53, "index": 117}}], "key": "FCUzmmLFksayvyWuU4zO7lqriIM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ImportMetaRegistry = void 0;\n  var _getBundleUrl = require(_dependencyMap[0], \"../utils/getBundleUrl\");\n  // Copyright 2015-present 650 Industries. All rights reserved.\n\n  /**\n   * Registry to handle import.meta functionality for React Native environment\n   * Similar to how it works in the web, but adapted for the RN context\n   * https://github.com/wintercg/import-meta-registry\n   */\n  const ImportMetaRegistry = exports.ImportMetaRegistry = {\n    get url() {\n      return (0, _getBundleUrl.getBundleUrl)();\n    }\n  };\n});", "lineCount": 19, "map": [[6, 2, 3, 0], [6, 6, 3, 0, "_getBundleUrl"], [6, 19, 3, 0], [6, 22, 3, 0, "require"], [6, 29, 3, 0], [6, 30, 3, 0, "_dependencyMap"], [6, 44, 3, 0], [7, 2, 1, 0], [9, 2, 5, 0], [10, 0, 6, 0], [11, 0, 7, 0], [12, 0, 8, 0], [13, 0, 9, 0], [14, 2, 10, 7], [14, 8, 10, 13, "ImportMetaRegistry"], [14, 26, 10, 31], [14, 29, 10, 31, "exports"], [14, 36, 10, 31], [14, 37, 10, 31, "ImportMetaRegistry"], [14, 55, 10, 31], [14, 58, 10, 34], [15, 4, 11, 2], [15, 8, 11, 6, "url"], [15, 11, 11, 9, "url"], [15, 12, 11, 9], [15, 14, 11, 12], [16, 6, 12, 4], [16, 13, 12, 11], [16, 17, 12, 11, "getBundleUrl"], [16, 43, 12, 23], [16, 45, 12, 24], [16, 46, 12, 25], [17, 4, 13, 2], [18, 2, 14, 0], [18, 3, 14, 1], [19, 0, 14, 2], [19, 3]], "functionMap": {"names": ["<global>", "get__url"], "mappings": "AAA;ECU;GDE"}}, "type": "js/module"}]}