import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Pressable,
  StyleSheet,
  RefreshControl,
  Alert,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Image } from 'expo-image';
import { 
  MapPin, 
  Cloud, 
  Sun, 
  CloudRain,
  Snowflake,
  Wind,
  Droplets,
  Eye,
  Thermometer,
  Shirt,
  RefreshCw
} from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../../utils/theme';
import { useFonts, Inter_400Regular, Inter_500Medium, Inter_600SemiBold } from '@expo-google-fonts/inter';
import * as Location from 'expo-location';

// Sample outfit suggestions based on weather
const outfitSuggestions = {
  sunny: [
    {
      id: '1',
      name: 'Summer Casual',
      items: ['Light T-shirt', 'Shorts', 'Sneakers'],
      temperature: '25°C+',
      image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400'
    },
    {
      id: '2',
      name: 'Beach Day',
      items: ['Tank Top', 'Linen Pants', 'Sandals'],
      temperature: '30°C+',
      image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400'
    }
  ],
  cloudy: [
    {
      id: '3',
      name: 'Smart Casual',
      items: ['Light Sweater', 'Jeans', 'Loafers'],
      temperature: '15-20°C',
      image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=400'
    }
  ],
  rainy: [
    {
      id: '4',
      name: 'Rainy Day',
      items: ['Waterproof Jacket', 'Jeans', 'Boots'],
      temperature: '10-15°C',
      image: 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5e?w=400'
    }
  ],
  snowy: [
    {
      id: '5',
      name: 'Winter Warm',
      items: ['Heavy Coat', 'Thermal Wear', 'Winter Boots'],
      temperature: '0°C-',
      image: 'https://images.unsplash.com/photo-1551488831-00ddcb6c6bd3?w=400'
    }
  ]
};

export default function WeatherScreen() {
  const insets = useSafeAreaInsets();
  const { colors, isDark } = useTheme();
  const [location, setLocation] = useState(null);
  const [weather, setWeather] = useState(null);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const [fontsLoaded] = useFonts({
    Inter_400Regular,
    Inter_500Medium,
    Inter_600SemiBold,
  });

  if (!fontsLoaded) {
    return null;
  }

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Location permission is needed to get weather data');
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error requesting location permission:', error);
      return false;
    }
  };

  const getCurrentLocation = async () => {
    try {
      const hasPermission = await requestLocationPermission();
      if (!hasPermission) return;

      setLoading(true);
      const location = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = location.coords;
      
      // Get city name from coordinates
      const address = await Location.reverseGeocodeAsync({ latitude, longitude });
      const city = address[0]?.city || address[0]?.subregion || 'Unknown Location';
      
      setLocation({ latitude, longitude, city });
      
      // Simulate weather data (in a real app, you'd call a weather API)
      const weatherData = {
        temperature: 22,
        condition: 'sunny',
        humidity: 65,
        windSpeed: 12,
        visibility: 10,
        description: 'Sunny with clear skies',
        hourlyForecast: [
          { time: '12:00', temp: 22, condition: 'sunny' },
          { time: '15:00', temp: 25, condition: 'sunny' },
          { time: '18:00', temp: 23, condition: 'cloudy' },
          { time: '21:00', temp: 20, condition: 'cloudy' },
        ],
        dailyForecast: [
          { day: 'Today', high: 25, low: 18, condition: 'sunny' },
          { day: 'Tomorrow', high: 23, low: 16, condition: 'cloudy' },
          { day: 'Sunday', high: 21, low: 14, condition: 'rainy' },
          { day: 'Monday', high: 19, low: 12, condition: 'cloudy' },
        ]
      };
      
      setWeather(weatherData);
    } catch (error) {
      console.error('Error getting location/weather:', error);
      Alert.alert('Error', 'Failed to get location. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await getCurrentLocation();
    setRefreshing(false);
  };

  useEffect(() => {
    getCurrentLocation();
  }, []);

  const getWeatherIcon = (condition) => {
    switch (condition) {
      case 'sunny': return <Sun size={24} color="#F59E0B" />;
      case 'cloudy': return <Cloud size={24} color="#6B7280" />;
      case 'rainy': return <CloudRain size={24} color="#3B82F6" />;
      case 'snowy': return <Snowflake size={24} color="#60A5FA" />;
      default: return <Sun size={24} color="#F59E0B" />;
    }
  };

  const getOutfitSuggestions = () => {
    if (!weather) return [];
    return outfitSuggestions[weather.condition] || [];
  };

  const renderWeatherCard = () => {
    if (!weather || !location) {
      return (
        <View style={[styles.weatherCard, { backgroundColor: colors.cardBackground }]}>
          <Pressable
            style={[styles.locationButton, { backgroundColor: colors.primary }]}
            onPress={getCurrentLocation}
            disabled={loading}
          >
            <MapPin size={20} color={colors.buttonPrimaryText} />
            <Text style={[styles.locationButtonText, { color: colors.buttonPrimaryText }]}>
              {loading ? 'Getting Location...' : 'Get Weather'}
            </Text>
          </Pressable>
        </View>
      );
    }

    return (
      <View style={[styles.weatherCard, { backgroundColor: colors.cardBackground }]}>
        <View style={styles.weatherHeader}>
          <View style={styles.locationInfo}>
            <MapPin size={16} color={colors.textSecondary} />
            <Text style={[styles.locationText, { color: colors.textSecondary }]}>
              {location.city}
            </Text>
          </View>
          <Pressable onPress={onRefresh} disabled={refreshing}>
            <RefreshCw 
              size={20} 
              color={colors.textSecondary} 
              style={refreshing ? { transform: [{ rotate: '180deg' }] } : {}}
            />
          </Pressable>
        </View>

        <View style={styles.currentWeather}>
          <View style={styles.temperatureSection}>
            <Text style={[styles.temperature, { color: colors.text }]}>
              {weather.temperature}°C
            </Text>
            <Text style={[styles.weatherDescription, { color: colors.textSecondary }]}>
              {weather.description}
            </Text>
          </View>
          {getWeatherIcon(weather.condition)}
        </View>

        <View style={styles.weatherDetails}>
          <View style={styles.detailItem}>
            <Droplets size={16} color={colors.textSecondary} />
            <Text style={[styles.detailText, { color: colors.textSecondary }]}>
              {weather.humidity}%
            </Text>
          </View>
          <View style={styles.detailItem}>
            <Wind size={16} color={colors.textSecondary} />
            <Text style={[styles.detailText, { color: colors.textSecondary }]}>
              {weather.windSpeed} km/h
            </Text>
          </View>
          <View style={styles.detailItem}>
            <Eye size={16} color={colors.textSecondary} />
            <Text style={[styles.detailText, { color: colors.textSecondary }]}>
              {weather.visibility} km
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderHourlyForecast = () => {
    if (!weather?.hourlyForecast) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Today's Forecast
        </Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.hourlyContainer}
        >
          {weather.hourlyForecast.map((hour, index) => (
            <View
              key={index}
              style={[styles.hourlyCard, { backgroundColor: colors.surface }]}
            >
              <Text style={[styles.hourlyTime, { color: colors.textSecondary }]}>
                {hour.time}
              </Text>
              {getWeatherIcon(hour.condition)}
              <Text style={[styles.hourlyTemp, { color: colors.text }]}>
                {hour.temp}°
              </Text>
            </View>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderOutfitSuggestions = () => {
    const suggestions = getOutfitSuggestions();
    
    if (suggestions.length === 0) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Outfit Suggestions
        </Text>
        <Text style={[styles.sectionSubtitle, { color: colors.textSecondary }]}>
          Perfect outfits for today's weather
        </Text>
        
        <View style={styles.outfitGrid}>
          {suggestions.map((outfit) => (
            <Pressable
              key={outfit.id}
              style={[styles.outfitCard, { backgroundColor: colors.cardBackground }]}
            >
              <Image
                source={{ uri: outfit.image }}
                style={styles.outfitImage}
                contentFit="cover"
                transition={200}
              />
              <View style={styles.outfitInfo}>
                <Text style={[styles.outfitName, { color: colors.text }]}>
                  {outfit.name}
                </Text>
                <Text style={[styles.outfitTemp, { color: colors.textSecondary }]}>
                  {outfit.temperature}
                </Text>
                <View style={styles.outfitItems}>
                  {outfit.items.map((item, index) => (
                    <Text
                      key={index}
                      style={[styles.outfitItem, { color: colors.textTertiary }]}
                    >
                      • {item}
                    </Text>
                  ))}
                </View>
              </View>
            </Pressable>
          ))}
        </View>
      </View>
    );
  };

  const renderDailyForecast = () => {
    if (!weather?.dailyForecast) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          7-Day Forecast
        </Text>
        
        <View style={styles.dailyContainer}>
          {weather.dailyForecast.map((day, index) => (
            <View
              key={index}
              style={[
                styles.dailyCard,
                { borderBottomColor: colors.border },
                index === weather.dailyForecast.length - 1 && { borderBottomWidth: 0 }
              ]}
            >
              <Text style={[styles.dayName, { color: colors.text }]}>
                {day.day}
              </Text>
              <View style={styles.dailyWeather}>
                {getWeatherIcon(day.condition)}
                <View style={styles.dailyTemps}>
                  <Text style={[styles.dailyHigh, { color: colors.text }]}>
                    {day.high}°
                  </Text>
                  <Text style={[styles.dailyLow, { color: colors.textSecondary }]}>
                    {day.low}°
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      
      {/* Header */}
      <View style={[
        styles.header,
        { 
          paddingTop: insets.top + 16,
          backgroundColor: colors.background,
          borderBottomColor: colors.border
        }
      ]}>
        <Text style={[styles.title, { color: colors.text }]}>
          Weather & Style
        </Text>
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={[styles.contentContainer, { paddingBottom: insets.bottom + 20 }]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary}
          />
        }
      >
        {renderWeatherCard()}
        {renderHourlyForecast()}
        {renderOutfitSuggestions()}
        {renderDailyForecast()}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter_600SemiBold',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  weatherCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
  },
  weatherHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  locationText: {
    fontSize: 14,
    fontFamily: 'Inter_500Medium',
  },
  currentWeather: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  temperatureSection: {
    flex: 1,
  },
  temperature: {
    fontSize: 48,
    fontFamily: 'Inter_600SemiBold',
    lineHeight: 52,
  },
  weatherDescription: {
    fontSize: 16,
    fontFamily: 'Inter_400Regular',
    marginTop: 4,
  },
  weatherDetails: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  detailItem: {
    alignItems: 'center',
    gap: 4,
  },
  detailText: {
    fontSize: 12,
    fontFamily: 'Inter_500Medium',
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  locationButtonText: {
    fontSize: 16,
    fontFamily: 'Inter_500Medium',
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter_600SemiBold',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter_400Regular',
    marginBottom: 16,
  },
  hourlyContainer: {
    paddingRight: 20,
    gap: 12,
  },
  hourlyCard: {
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 8,
    minWidth: 70,
  },
  hourlyTime: {
    fontSize: 12,
    fontFamily: 'Inter_500Medium',
  },
  hourlyTemp: {
    fontSize: 14,
    fontFamily: 'Inter_600SemiBold',
  },
  outfitGrid: {
    gap: 16,
  },
  outfitCard: {
    flexDirection: 'row',
    borderRadius: 16,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  outfitImage: {
    width: 100,
    height: 120,
  },
  outfitInfo: {
    flex: 1,
    padding: 16,
  },
  outfitName: {
    fontSize: 16,
    fontFamily: 'Inter_600SemiBold',
    marginBottom: 4,
  },
  outfitTemp: {
    fontSize: 12,
    fontFamily: 'Inter_500Medium',
    marginBottom: 8,
  },
  outfitItems: {
    gap: 2,
  },
  outfitItem: {
    fontSize: 12,
    fontFamily: 'Inter_400Regular',
  },
  dailyContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  dailyCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
  },
  dayName: {
    fontSize: 16,
    fontFamily: 'Inter_500Medium',
    flex: 1,
  },
  dailyWeather: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  dailyTemps: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    minWidth: 60,
  },
  dailyHigh: {
    fontSize: 16,
    fontFamily: 'Inter_600SemiBold',
  },
  dailyLow: {
    fontSize: 14,
    fontFamily: 'Inter_400Regular',
  },
});