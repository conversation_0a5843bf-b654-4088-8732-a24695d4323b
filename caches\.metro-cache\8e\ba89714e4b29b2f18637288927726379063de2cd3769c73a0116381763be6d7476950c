{"dependencies": [{"name": "./use-colors.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 44, "index": 59}}], "key": "P5laot06ic+cvb+eUQH+rNIs90g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useDefaultStyles = void 0;\n  var _useColors = require(_dependencyMap[0], \"./use-colors.js\");\n  const useDefaultStyles = ({\n    invert,\n    richColors,\n    unstyled,\n    description,\n    variant: variantProps\n  }) => {\n    const colors = (0, _useColors.useColors)(invert);\n    const variant = variantProps === 'loading' ? 'info' : variantProps;\n    if (unstyled) {\n      return {\n        toast: {},\n        toastContent: {},\n        title: {},\n        description: {},\n        buttons: {},\n        actionButton: {},\n        actionButtonText: {},\n        cancelButton: {},\n        cancelButtonText: {},\n        closeButtonColor: colors['text-secondary'],\n        iconColor: colors[variant]\n      };\n    }\n    return {\n      toast: {\n        justifyContent: 'center',\n        padding: 16,\n        borderRadius: 16,\n        marginHorizontal: 16,\n        backgroundColor: richColors ? colors.rich[variant].background : colors['background-primary'],\n        borderCurve: 'continuous',\n        borderWidth: richColors ? 1 : undefined,\n        borderColor: richColors ? colors.rich[variant].border : undefined\n      },\n      toastContent: {\n        flexDirection: 'row',\n        gap: 16,\n        alignItems: description?.length === 0 ? 'center' : undefined\n      },\n      title: {\n        fontWeight: '600',\n        lineHeight: 20,\n        color: richColors ? colors.rich[variant].foreground : colors['text-primary']\n      },\n      description: {\n        fontSize: 14,\n        lineHeight: 20,\n        marginTop: 2,\n        color: richColors ? colors.rich[variant].foreground : colors['text-tertiary']\n      },\n      buttons: {\n        flexDirection: 'row',\n        alignItems: 'center',\n        gap: 16,\n        marginTop: 16\n      },\n      actionButton: {\n        flexGrow: 0,\n        alignSelf: 'flex-start',\n        borderRadius: 999,\n        borderWidth: 1,\n        borderColor: colors['border-secondary'],\n        paddingHorizontal: 14,\n        paddingVertical: 6,\n        borderCurve: 'continuous',\n        backgroundColor: colors['background-secondary']\n      },\n      actionButtonText: {\n        fontSize: 14,\n        lineHeight: 20,\n        fontWeight: '600',\n        alignSelf: 'flex-start',\n        color: colors['text-primary']\n      },\n      cancelButton: {\n        flexGrow: 0\n      },\n      cancelButtonText: {\n        fontSize: 14,\n        lineHeight: 20,\n        fontWeight: '600',\n        alignSelf: 'flex-start',\n        color: colors['text-secondary']\n      },\n      iconColor: richColors ? colors.rich[variant].foreground : colors[variant],\n      closeButtonColor: richColors ? colors.rich[variant].foreground : colors['text-secondary']\n    };\n  };\n  exports.useDefaultStyles = useDefaultStyles;\n});", "lineCount": 99, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useDefaultStyles"], [7, 26, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_useColors"], [8, 16, 3, 0], [8, 19, 3, 0, "require"], [8, 26, 3, 0], [8, 27, 3, 0, "_dependencyMap"], [8, 41, 3, 0], [9, 2, 4, 7], [9, 8, 4, 13, "useDefaultStyles"], [9, 24, 4, 29], [9, 27, 4, 32, "useDefaultStyles"], [9, 28, 4, 33], [10, 4, 5, 2, "invert"], [10, 10, 5, 8], [11, 4, 6, 2, "richColors"], [11, 14, 6, 12], [12, 4, 7, 2, "unstyled"], [12, 12, 7, 10], [13, 4, 8, 2, "description"], [13, 15, 8, 13], [14, 4, 9, 2, "variant"], [14, 11, 9, 9], [14, 13, 9, 11, "variantProps"], [15, 2, 10, 0], [15, 3, 10, 1], [15, 8, 10, 6], [16, 4, 11, 2], [16, 10, 11, 8, "colors"], [16, 16, 11, 14], [16, 19, 11, 17], [16, 23, 11, 17, "useColors"], [16, 43, 11, 26], [16, 45, 11, 27, "invert"], [16, 51, 11, 33], [16, 52, 11, 34], [17, 4, 12, 2], [17, 10, 12, 8, "variant"], [17, 17, 12, 15], [17, 20, 12, 18, "variantProps"], [17, 32, 12, 30], [17, 37, 12, 35], [17, 46, 12, 44], [17, 49, 12, 47], [17, 55, 12, 53], [17, 58, 12, 56, "variantProps"], [17, 70, 12, 68], [18, 4, 13, 2], [18, 8, 13, 6, "unstyled"], [18, 16, 13, 14], [18, 18, 13, 16], [19, 6, 14, 4], [19, 13, 14, 11], [20, 8, 15, 6, "toast"], [20, 13, 15, 11], [20, 15, 15, 13], [20, 16, 15, 14], [20, 17, 15, 15], [21, 8, 16, 6, "toastContent"], [21, 20, 16, 18], [21, 22, 16, 20], [21, 23, 16, 21], [21, 24, 16, 22], [22, 8, 17, 6, "title"], [22, 13, 17, 11], [22, 15, 17, 13], [22, 16, 17, 14], [22, 17, 17, 15], [23, 8, 18, 6, "description"], [23, 19, 18, 17], [23, 21, 18, 19], [23, 22, 18, 20], [23, 23, 18, 21], [24, 8, 19, 6, "buttons"], [24, 15, 19, 13], [24, 17, 19, 15], [24, 18, 19, 16], [24, 19, 19, 17], [25, 8, 20, 6, "actionButton"], [25, 20, 20, 18], [25, 22, 20, 20], [25, 23, 20, 21], [25, 24, 20, 22], [26, 8, 21, 6, "actionButtonText"], [26, 24, 21, 22], [26, 26, 21, 24], [26, 27, 21, 25], [26, 28, 21, 26], [27, 8, 22, 6, "cancelButton"], [27, 20, 22, 18], [27, 22, 22, 20], [27, 23, 22, 21], [27, 24, 22, 22], [28, 8, 23, 6, "cancelButtonText"], [28, 24, 23, 22], [28, 26, 23, 24], [28, 27, 23, 25], [28, 28, 23, 26], [29, 8, 24, 6, "closeButtonColor"], [29, 24, 24, 22], [29, 26, 24, 24, "colors"], [29, 32, 24, 30], [29, 33, 24, 31], [29, 49, 24, 47], [29, 50, 24, 48], [30, 8, 25, 6, "iconColor"], [30, 17, 25, 15], [30, 19, 25, 17, "colors"], [30, 25, 25, 23], [30, 26, 25, 24, "variant"], [30, 33, 25, 31], [31, 6, 26, 4], [31, 7, 26, 5], [32, 4, 27, 2], [33, 4, 28, 2], [33, 11, 28, 9], [34, 6, 29, 4, "toast"], [34, 11, 29, 9], [34, 13, 29, 11], [35, 8, 30, 6, "justifyContent"], [35, 22, 30, 20], [35, 24, 30, 22], [35, 32, 30, 30], [36, 8, 31, 6, "padding"], [36, 15, 31, 13], [36, 17, 31, 15], [36, 19, 31, 17], [37, 8, 32, 6, "borderRadius"], [37, 20, 32, 18], [37, 22, 32, 20], [37, 24, 32, 22], [38, 8, 33, 6, "marginHorizontal"], [38, 24, 33, 22], [38, 26, 33, 24], [38, 28, 33, 26], [39, 8, 34, 6, "backgroundColor"], [39, 23, 34, 21], [39, 25, 34, 23, "richColors"], [39, 35, 34, 33], [39, 38, 34, 36, "colors"], [39, 44, 34, 42], [39, 45, 34, 43, "rich"], [39, 49, 34, 47], [39, 50, 34, 48, "variant"], [39, 57, 34, 55], [39, 58, 34, 56], [39, 59, 34, 57, "background"], [39, 69, 34, 67], [39, 72, 34, 70, "colors"], [39, 78, 34, 76], [39, 79, 34, 77], [39, 99, 34, 97], [39, 100, 34, 98], [40, 8, 35, 6, "borderCurve"], [40, 19, 35, 17], [40, 21, 35, 19], [40, 33, 35, 31], [41, 8, 36, 6, "borderWidth"], [41, 19, 36, 17], [41, 21, 36, 19, "richColors"], [41, 31, 36, 29], [41, 34, 36, 32], [41, 35, 36, 33], [41, 38, 36, 36, "undefined"], [41, 47, 36, 45], [42, 8, 37, 6, "borderColor"], [42, 19, 37, 17], [42, 21, 37, 19, "richColors"], [42, 31, 37, 29], [42, 34, 37, 32, "colors"], [42, 40, 37, 38], [42, 41, 37, 39, "rich"], [42, 45, 37, 43], [42, 46, 37, 44, "variant"], [42, 53, 37, 51], [42, 54, 37, 52], [42, 55, 37, 53, "border"], [42, 61, 37, 59], [42, 64, 37, 62, "undefined"], [43, 6, 38, 4], [43, 7, 38, 5], [44, 6, 39, 4, "toastContent"], [44, 18, 39, 16], [44, 20, 39, 18], [45, 8, 40, 6, "flexDirection"], [45, 21, 40, 19], [45, 23, 40, 21], [45, 28, 40, 26], [46, 8, 41, 6, "gap"], [46, 11, 41, 9], [46, 13, 41, 11], [46, 15, 41, 13], [47, 8, 42, 6, "alignItems"], [47, 18, 42, 16], [47, 20, 42, 18, "description"], [47, 31, 42, 29], [47, 33, 42, 31, "length"], [47, 39, 42, 37], [47, 44, 42, 42], [47, 45, 42, 43], [47, 48, 42, 46], [47, 56, 42, 54], [47, 59, 42, 57, "undefined"], [48, 6, 43, 4], [48, 7, 43, 5], [49, 6, 44, 4, "title"], [49, 11, 44, 9], [49, 13, 44, 11], [50, 8, 45, 6, "fontWeight"], [50, 18, 45, 16], [50, 20, 45, 18], [50, 25, 45, 23], [51, 8, 46, 6, "lineHeight"], [51, 18, 46, 16], [51, 20, 46, 18], [51, 22, 46, 20], [52, 8, 47, 6, "color"], [52, 13, 47, 11], [52, 15, 47, 13, "richColors"], [52, 25, 47, 23], [52, 28, 47, 26, "colors"], [52, 34, 47, 32], [52, 35, 47, 33, "rich"], [52, 39, 47, 37], [52, 40, 47, 38, "variant"], [52, 47, 47, 45], [52, 48, 47, 46], [52, 49, 47, 47, "foreground"], [52, 59, 47, 57], [52, 62, 47, 60, "colors"], [52, 68, 47, 66], [52, 69, 47, 67], [52, 83, 47, 81], [53, 6, 48, 4], [53, 7, 48, 5], [54, 6, 49, 4, "description"], [54, 17, 49, 15], [54, 19, 49, 17], [55, 8, 50, 6, "fontSize"], [55, 16, 50, 14], [55, 18, 50, 16], [55, 20, 50, 18], [56, 8, 51, 6, "lineHeight"], [56, 18, 51, 16], [56, 20, 51, 18], [56, 22, 51, 20], [57, 8, 52, 6, "marginTop"], [57, 17, 52, 15], [57, 19, 52, 17], [57, 20, 52, 18], [58, 8, 53, 6, "color"], [58, 13, 53, 11], [58, 15, 53, 13, "richColors"], [58, 25, 53, 23], [58, 28, 53, 26, "colors"], [58, 34, 53, 32], [58, 35, 53, 33, "rich"], [58, 39, 53, 37], [58, 40, 53, 38, "variant"], [58, 47, 53, 45], [58, 48, 53, 46], [58, 49, 53, 47, "foreground"], [58, 59, 53, 57], [58, 62, 53, 60, "colors"], [58, 68, 53, 66], [58, 69, 53, 67], [58, 84, 53, 82], [59, 6, 54, 4], [59, 7, 54, 5], [60, 6, 55, 4, "buttons"], [60, 13, 55, 11], [60, 15, 55, 13], [61, 8, 56, 6, "flexDirection"], [61, 21, 56, 19], [61, 23, 56, 21], [61, 28, 56, 26], [62, 8, 57, 6, "alignItems"], [62, 18, 57, 16], [62, 20, 57, 18], [62, 28, 57, 26], [63, 8, 58, 6, "gap"], [63, 11, 58, 9], [63, 13, 58, 11], [63, 15, 58, 13], [64, 8, 59, 6, "marginTop"], [64, 17, 59, 15], [64, 19, 59, 17], [65, 6, 60, 4], [65, 7, 60, 5], [66, 6, 61, 4, "actionButton"], [66, 18, 61, 16], [66, 20, 61, 18], [67, 8, 62, 6, "flexGrow"], [67, 16, 62, 14], [67, 18, 62, 16], [67, 19, 62, 17], [68, 8, 63, 6, "alignSelf"], [68, 17, 63, 15], [68, 19, 63, 17], [68, 31, 63, 29], [69, 8, 64, 6, "borderRadius"], [69, 20, 64, 18], [69, 22, 64, 20], [69, 25, 64, 23], [70, 8, 65, 6, "borderWidth"], [70, 19, 65, 17], [70, 21, 65, 19], [70, 22, 65, 20], [71, 8, 66, 6, "borderColor"], [71, 19, 66, 17], [71, 21, 66, 19, "colors"], [71, 27, 66, 25], [71, 28, 66, 26], [71, 46, 66, 44], [71, 47, 66, 45], [72, 8, 67, 6, "paddingHorizontal"], [72, 25, 67, 23], [72, 27, 67, 25], [72, 29, 67, 27], [73, 8, 68, 6, "paddingVertical"], [73, 23, 68, 21], [73, 25, 68, 23], [73, 26, 68, 24], [74, 8, 69, 6, "borderCurve"], [74, 19, 69, 17], [74, 21, 69, 19], [74, 33, 69, 31], [75, 8, 70, 6, "backgroundColor"], [75, 23, 70, 21], [75, 25, 70, 23, "colors"], [75, 31, 70, 29], [75, 32, 70, 30], [75, 54, 70, 52], [76, 6, 71, 4], [76, 7, 71, 5], [77, 6, 72, 4, "actionButtonText"], [77, 22, 72, 20], [77, 24, 72, 22], [78, 8, 73, 6, "fontSize"], [78, 16, 73, 14], [78, 18, 73, 16], [78, 20, 73, 18], [79, 8, 74, 6, "lineHeight"], [79, 18, 74, 16], [79, 20, 74, 18], [79, 22, 74, 20], [80, 8, 75, 6, "fontWeight"], [80, 18, 75, 16], [80, 20, 75, 18], [80, 25, 75, 23], [81, 8, 76, 6, "alignSelf"], [81, 17, 76, 15], [81, 19, 76, 17], [81, 31, 76, 29], [82, 8, 77, 6, "color"], [82, 13, 77, 11], [82, 15, 77, 13, "colors"], [82, 21, 77, 19], [82, 22, 77, 20], [82, 36, 77, 34], [83, 6, 78, 4], [83, 7, 78, 5], [84, 6, 79, 4, "cancelButton"], [84, 18, 79, 16], [84, 20, 79, 18], [85, 8, 80, 6, "flexGrow"], [85, 16, 80, 14], [85, 18, 80, 16], [86, 6, 81, 4], [86, 7, 81, 5], [87, 6, 82, 4, "cancelButtonText"], [87, 22, 82, 20], [87, 24, 82, 22], [88, 8, 83, 6, "fontSize"], [88, 16, 83, 14], [88, 18, 83, 16], [88, 20, 83, 18], [89, 8, 84, 6, "lineHeight"], [89, 18, 84, 16], [89, 20, 84, 18], [89, 22, 84, 20], [90, 8, 85, 6, "fontWeight"], [90, 18, 85, 16], [90, 20, 85, 18], [90, 25, 85, 23], [91, 8, 86, 6, "alignSelf"], [91, 17, 86, 15], [91, 19, 86, 17], [91, 31, 86, 29], [92, 8, 87, 6, "color"], [92, 13, 87, 11], [92, 15, 87, 13, "colors"], [92, 21, 87, 19], [92, 22, 87, 20], [92, 38, 87, 36], [93, 6, 88, 4], [93, 7, 88, 5], [94, 6, 89, 4, "iconColor"], [94, 15, 89, 13], [94, 17, 89, 15, "richColors"], [94, 27, 89, 25], [94, 30, 89, 28, "colors"], [94, 36, 89, 34], [94, 37, 89, 35, "rich"], [94, 41, 89, 39], [94, 42, 89, 40, "variant"], [94, 49, 89, 47], [94, 50, 89, 48], [94, 51, 89, 49, "foreground"], [94, 61, 89, 59], [94, 64, 89, 62, "colors"], [94, 70, 89, 68], [94, 71, 89, 69, "variant"], [94, 78, 89, 76], [94, 79, 89, 77], [95, 6, 90, 4, "closeButtonColor"], [95, 22, 90, 20], [95, 24, 90, 22, "richColors"], [95, 34, 90, 32], [95, 37, 90, 35, "colors"], [95, 43, 90, 41], [95, 44, 90, 42, "rich"], [95, 48, 90, 46], [95, 49, 90, 47, "variant"], [95, 56, 90, 54], [95, 57, 90, 55], [95, 58, 90, 56, "foreground"], [95, 68, 90, 66], [95, 71, 90, 69, "colors"], [95, 77, 90, 75], [95, 78, 90, 76], [95, 94, 90, 92], [96, 4, 91, 2], [96, 5, 91, 3], [97, 2, 92, 0], [97, 3, 92, 1], [98, 2, 92, 2, "exports"], [98, 9, 92, 2], [98, 10, 92, 2, "useDefaultStyles"], [98, 26, 92, 2], [98, 29, 92, 2, "useDefaultStyles"], [98, 45, 92, 2], [99, 0, 92, 2], [99, 3]], "functionMap": {"names": ["<global>", "useDefaultStyles"], "mappings": "AAA;gCCG;CDwF"}}, "type": "js/module"}]}