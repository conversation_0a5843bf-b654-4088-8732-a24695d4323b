{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright © 2024 650 Industries.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  // React Native's error handling is full of bugs which cause the app to crash in production.\n  // We'll disable their handling in production native builds to ensure missing modules are shown to the user.\n  var disableReactNativeMissingModuleHandling = !__DEV__ && (true || typeof window === 'undefined');\n  globalThis.__webpack_chunk_load__ = id => {\n    return global[`${__METRO_GLOBAL_PREFIX__}__loadBundleAsync`](id);\n  };\n  globalThis.__webpack_require__ = id => {\n    // This logic can be tested by running a production iOS build without virtual client boundaries. This will result in all split chunks being missing and\n    // errors being thrown on RSC load.\n\n    var original = ErrorUtils.reportFatalError;\n    if (disableReactNativeMissingModuleHandling) {\n      ErrorUtils.reportFatalError = err => {\n        // Throw the error so the __r function exits as expected. The error will then be caught by the nearest error boundary.\n        throw err;\n      };\n    }\n    try {\n      return global[`${__METRO_GLOBAL_PREFIX__}__r`](id);\n    } finally {\n      // Restore the original error handling.\n      if (disableReactNativeMissingModuleHandling) {\n        ErrorUtils.reportFatalError = original;\n      }\n    }\n  };\n});", "lineCount": 35, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [9, 2, 8, 0], [10, 2, 9, 0], [11, 2, 10, 0], [11, 6, 10, 6, "disableReactNativeMissingModuleHandling"], [11, 45, 10, 45], [11, 48, 11, 2], [11, 49, 11, 3, "__DEV__"], [11, 56, 11, 10], [11, 61, 11, 15], [11, 69, 11, 48], [11, 76, 11, 55, "window"], [11, 82, 11, 61], [11, 87, 11, 66], [11, 98, 11, 77], [11, 99, 11, 78], [12, 2, 13, 0, "globalThis"], [12, 12, 13, 10], [12, 13, 13, 11, "__webpack_chunk_load__"], [12, 35, 13, 33], [12, 38, 13, 37, "id"], [12, 40, 13, 39], [12, 44, 13, 44], [13, 4, 14, 2], [13, 11, 14, 9, "global"], [13, 17, 14, 15], [13, 18, 14, 16], [13, 21, 14, 19, "__METRO_GLOBAL_PREFIX__"], [13, 44, 14, 42], [13, 63, 14, 61], [13, 64, 14, 62], [13, 65, 14, 63, "id"], [13, 67, 14, 65], [13, 68, 14, 66], [14, 2, 15, 0], [14, 3, 15, 1], [15, 2, 17, 0, "globalThis"], [15, 12, 17, 10], [15, 13, 17, 11, "__webpack_require__"], [15, 32, 17, 30], [15, 35, 17, 34, "id"], [15, 37, 17, 36], [15, 41, 17, 41], [16, 4, 18, 2], [17, 4, 19, 2], [19, 4, 21, 2], [19, 8, 21, 8, "original"], [19, 16, 21, 16], [19, 19, 21, 19, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [19, 29, 21, 29], [19, 30, 21, 30, "reportFatalError"], [19, 46, 21, 46], [20, 4, 22, 2], [20, 8, 22, 6, "disableReactNativeMissingModuleHandling"], [20, 47, 22, 45], [20, 49, 22, 47], [21, 6, 23, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [21, 16, 23, 14], [21, 17, 23, 15, "reportFatalError"], [21, 33, 23, 31], [21, 36, 23, 35, "err"], [21, 39, 23, 38], [21, 43, 23, 43], [22, 8, 24, 6], [23, 8, 25, 6], [23, 14, 25, 12, "err"], [23, 17, 25, 15], [24, 6, 26, 4], [24, 7, 26, 5], [25, 4, 27, 2], [26, 4, 28, 2], [26, 8, 28, 6], [27, 6, 29, 4], [27, 13, 29, 11, "global"], [27, 19, 29, 17], [27, 20, 29, 18], [27, 23, 29, 21, "__METRO_GLOBAL_PREFIX__"], [27, 46, 29, 44], [27, 51, 29, 49], [27, 52, 29, 50], [27, 53, 29, 51, "id"], [27, 55, 29, 53], [27, 56, 29, 54], [28, 4, 30, 2], [28, 5, 30, 3], [28, 14, 30, 12], [29, 6, 31, 4], [30, 6, 32, 4], [30, 10, 32, 8, "disableReactNativeMissingModuleHandling"], [30, 49, 32, 47], [30, 51, 32, 49], [31, 8, 33, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [31, 18, 33, 16], [31, 19, 33, 17, "reportFatalError"], [31, 35, 33, 33], [31, 38, 33, 36, "original"], [31, 46, 33, 44], [32, 6, 34, 4], [33, 4, 35, 2], [34, 2, 36, 0], [34, 3, 36, 1], [35, 0, 36, 2], [35, 3]], "functionMap": {"names": ["<global>", "globalThis.__webpack_chunk_load__", "globalThis.__webpack_require__", "ErrorUtils.reportFatalError"], "mappings": "AAA;oCCY;CDE;iCEE;kCCM;KDG;CFU"}}, "type": "js/module"}]}