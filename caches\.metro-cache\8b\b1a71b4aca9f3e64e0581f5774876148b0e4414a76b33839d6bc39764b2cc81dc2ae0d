{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Components/View/View", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 21, "column": 13}, "end": {"line": 21, "column": 63}}], "key": "H/3fvmiHyIdASS62Hfb3a4a54KU=", "exportNames": ["*"]}}, {"name": "../../../Libraries/StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 22, "column": 19}, "end": {"line": 22, "column": 70}}], "key": "Nurrv5y9ebtgGhUjBt0E/GEpaGk=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Text/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 23, "column": 13}, "end": {"line": 23, "column": 52}}], "key": "MoPWeVCFlo44fZk7PVmQmJJxnfs=", "exportNames": ["*"]}}, {"name": "./resolveBoxStyle", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 24, "column": 24}, "end": {"line": 24, "column": 52}}], "key": "s1PkX5FW7oAloTQiiqOkan+M2fQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[2], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native/src/private/inspector/BoxInspector.js\";\n  var View = require(_dependencyMap[3], \"../../../Libraries/Components/View/View\").default;\n  var StyleSheet = require(_dependencyMap[4], \"../../../Libraries/StyleSheet/StyleSheet\").default;\n  var Text = require(_dependencyMap[5], \"../../../Libraries/Text/Text\").default;\n  var resolveBoxStyle = require(_dependencyMap[6], \"./resolveBoxStyle\").default;\n  var blank = {\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0\n  };\n  function BoxInspector(_ref) {\n    var style = _ref.style,\n      frame = _ref.frame;\n    var margin = style && resolveBoxStyle('margin', style) || blank;\n    var padding = style && resolveBoxStyle('padding', style) || blank;\n    return (0, _jsxRuntime.jsx)(BoxContainer, {\n      title: \"margin\",\n      titleStyle: styles.marginLabel,\n      box: margin,\n      children: (0, _jsxRuntime.jsx)(BoxContainer, {\n        title: \"padding\",\n        box: padding,\n        children: (0, _jsxRuntime.jsxs)(View, {\n          children: [(0, _jsxRuntime.jsxs)(Text, {\n            style: styles.innerText,\n            children: [\"(\", (frame?.left || 0).toFixed(1), \", \", (frame?.top || 0).toFixed(1), \")\"]\n          }), (0, _jsxRuntime.jsxs)(Text, {\n            style: styles.innerText,\n            children: [(frame?.width || 0).toFixed(1), \" \\xD7\", ' ', (frame?.height || 0).toFixed(1)]\n          })]\n        })\n      })\n    });\n  }\n  function BoxContainer(_ref2) {\n    var title = _ref2.title,\n      titleStyle = _ref2.titleStyle,\n      box = _ref2.box,\n      children = _ref2.children;\n    return (0, _jsxRuntime.jsxs)(View, {\n      style: styles.box,\n      children: [(0, _jsxRuntime.jsxs)(View, {\n        style: styles.row,\n        children: [(0, _jsxRuntime.jsx)(Text, {\n          style: [titleStyle, styles.label],\n          children: title\n        }), (0, _jsxRuntime.jsx)(Text, {\n          style: styles.boxText,\n          children: box.top\n        })]\n      }), (0, _jsxRuntime.jsxs)(View, {\n        style: styles.row,\n        children: [(0, _jsxRuntime.jsx)(Text, {\n          style: styles.boxText,\n          children: box.left\n        }), children, (0, _jsxRuntime.jsx)(Text, {\n          style: styles.boxText,\n          children: box.right\n        })]\n      }), (0, _jsxRuntime.jsx)(Text, {\n        style: styles.boxText,\n        children: box.bottom\n      })]\n    });\n  }\n  var styles = StyleSheet.create({\n    row: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      justifyContent: 'space-around'\n    },\n    marginLabel: {\n      width: 60\n    },\n    label: {\n      fontSize: 10,\n      color: 'rgb(255,100,0)',\n      marginLeft: 5,\n      flex: 1,\n      textAlign: 'left',\n      top: -3\n    },\n    innerText: {\n      color: 'yellow',\n      fontSize: 12,\n      textAlign: 'center',\n      width: 70\n    },\n    box: {\n      borderWidth: 1,\n      borderColor: 'grey'\n    },\n    boxText: {\n      color: 'white',\n      fontSize: 12,\n      marginHorizontal: 3,\n      marginVertical: 2,\n      textAlign: 'center'\n    }\n  });\n  var _default = exports.default = BoxInspector;\n});", "lineCount": 113, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 19, 0], [9, 6, 19, 0, "_react"], [9, 12, 19, 0], [9, 15, 19, 0, "_interopRequireDefault"], [9, 37, 19, 0], [9, 38, 19, 0, "require"], [9, 45, 19, 0], [9, 46, 19, 0, "_dependencyMap"], [9, 60, 19, 0], [10, 2, 19, 26], [10, 6, 19, 26, "_jsxRuntime"], [10, 17, 19, 26], [10, 20, 19, 26, "require"], [10, 27, 19, 26], [10, 28, 19, 26, "_dependencyMap"], [10, 42, 19, 26], [11, 2, 19, 26], [11, 6, 19, 26, "_jsxFileName"], [11, 18, 19, 26], [12, 2, 21, 0], [12, 6, 21, 6, "View"], [12, 10, 21, 10], [12, 13, 21, 13, "require"], [12, 20, 21, 20], [12, 21, 21, 20, "_dependencyMap"], [12, 35, 21, 20], [12, 81, 21, 62], [12, 82, 21, 63], [12, 83, 21, 64, "default"], [12, 90, 21, 71], [13, 2, 22, 0], [13, 6, 22, 6, "StyleSheet"], [13, 16, 22, 16], [13, 19, 22, 19, "require"], [13, 26, 22, 26], [13, 27, 22, 26, "_dependencyMap"], [13, 41, 22, 26], [13, 88, 22, 69], [13, 89, 22, 70], [13, 90, 22, 71, "default"], [13, 97, 22, 78], [14, 2, 23, 0], [14, 6, 23, 6, "Text"], [14, 10, 23, 10], [14, 13, 23, 13, "require"], [14, 20, 23, 20], [14, 21, 23, 20, "_dependencyMap"], [14, 35, 23, 20], [14, 70, 23, 51], [14, 71, 23, 52], [14, 72, 23, 53, "default"], [14, 79, 23, 60], [15, 2, 24, 0], [15, 6, 24, 6, "resolveBoxStyle"], [15, 21, 24, 21], [15, 24, 24, 24, "require"], [15, 31, 24, 31], [15, 32, 24, 31, "_dependencyMap"], [15, 46, 24, 31], [15, 70, 24, 51], [15, 71, 24, 52], [15, 72, 24, 53, "default"], [15, 79, 24, 60], [16, 2, 26, 0], [16, 6, 26, 6, "blank"], [16, 11, 26, 11], [16, 14, 26, 14], [17, 4, 27, 2, "top"], [17, 7, 27, 5], [17, 9, 27, 7], [17, 10, 27, 8], [18, 4, 28, 2, "left"], [18, 8, 28, 6], [18, 10, 28, 8], [18, 11, 28, 9], [19, 4, 29, 2, "right"], [19, 9, 29, 7], [19, 11, 29, 9], [19, 12, 29, 10], [20, 4, 30, 2, "bottom"], [20, 10, 30, 8], [20, 12, 30, 10], [21, 2, 31, 0], [21, 3, 31, 1], [22, 2, 38, 0], [22, 11, 38, 9, "BoxInspector"], [22, 23, 38, 21, "BoxInspector"], [22, 24, 38, 21, "_ref"], [22, 28, 38, 21], [22, 30, 38, 69], [23, 4, 38, 69], [23, 8, 38, 23, "style"], [23, 13, 38, 28], [23, 16, 38, 28, "_ref"], [23, 20, 38, 28], [23, 21, 38, 23, "style"], [23, 26, 38, 28], [24, 6, 38, 30, "frame"], [24, 11, 38, 35], [24, 14, 38, 35, "_ref"], [24, 18, 38, 35], [24, 19, 38, 30, "frame"], [24, 24, 38, 35], [25, 4, 39, 2], [25, 8, 39, 8, "margin"], [25, 14, 39, 14], [25, 17, 39, 18, "style"], [25, 22, 39, 23], [25, 26, 39, 27, "resolveBoxStyle"], [25, 41, 39, 42], [25, 42, 39, 43], [25, 50, 39, 51], [25, 52, 39, 53, "style"], [25, 57, 39, 58], [25, 58, 39, 59], [25, 62, 39, 64, "blank"], [25, 67, 39, 69], [26, 4, 40, 2], [26, 8, 40, 8, "padding"], [26, 15, 40, 15], [26, 18, 40, 19, "style"], [26, 23, 40, 24], [26, 27, 40, 28, "resolveBoxStyle"], [26, 42, 40, 43], [26, 43, 40, 44], [26, 52, 40, 53], [26, 54, 40, 55, "style"], [26, 59, 40, 60], [26, 60, 40, 61], [26, 64, 40, 66, "blank"], [26, 69, 40, 71], [27, 4, 42, 2], [27, 11, 43, 4], [27, 15, 43, 4, "_jsxRuntime"], [27, 26, 43, 4], [27, 27, 43, 4, "jsx"], [27, 30, 43, 4], [27, 32, 43, 5, "BoxContainer"], [27, 44, 43, 17], [28, 6, 43, 18, "title"], [28, 11, 43, 23], [28, 13, 43, 24], [28, 21, 43, 32], [29, 6, 43, 33, "titleStyle"], [29, 16, 43, 43], [29, 18, 43, 45, "styles"], [29, 24, 43, 51], [29, 25, 43, 52, "margin<PERSON>abel"], [29, 36, 43, 64], [30, 6, 43, 65, "box"], [30, 9, 43, 68], [30, 11, 43, 70, "margin"], [30, 17, 43, 77], [31, 6, 43, 77, "children"], [31, 14, 43, 77], [31, 16, 44, 6], [31, 20, 44, 6, "_jsxRuntime"], [31, 31, 44, 6], [31, 32, 44, 6, "jsx"], [31, 35, 44, 6], [31, 37, 44, 7, "BoxContainer"], [31, 49, 44, 19], [32, 8, 44, 20, "title"], [32, 13, 44, 25], [32, 15, 44, 26], [32, 24, 44, 35], [33, 8, 44, 36, "box"], [33, 11, 44, 39], [33, 13, 44, 41, "padding"], [33, 20, 44, 49], [34, 8, 44, 49, "children"], [34, 16, 44, 49], [34, 18, 45, 8], [34, 22, 45, 8, "_jsxRuntime"], [34, 33, 45, 8], [34, 34, 45, 8, "jsxs"], [34, 38, 45, 8], [34, 40, 45, 9, "View"], [34, 44, 45, 13], [35, 10, 45, 13, "children"], [35, 18, 45, 13], [35, 21, 46, 10], [35, 25, 46, 10, "_jsxRuntime"], [35, 36, 46, 10], [35, 37, 46, 10, "jsxs"], [35, 41, 46, 10], [35, 43, 46, 11, "Text"], [35, 47, 46, 15], [36, 12, 46, 16, "style"], [36, 17, 46, 21], [36, 19, 46, 23, "styles"], [36, 25, 46, 29], [36, 26, 46, 30, "innerText"], [36, 35, 46, 40], [37, 12, 46, 40, "children"], [37, 20, 46, 40], [37, 23, 46, 41], [37, 26, 47, 13], [37, 28, 47, 14], [37, 29, 47, 15, "frame"], [37, 34, 47, 20], [37, 36, 47, 22, "left"], [37, 40, 47, 26], [37, 44, 47, 30], [37, 45, 47, 31], [37, 47, 47, 33, "toFixed"], [37, 54, 47, 40], [37, 55, 47, 41], [37, 56, 47, 42], [37, 57, 47, 43], [37, 59, 47, 44], [37, 63, 47, 46], [37, 65, 47, 47], [37, 66, 47, 48, "frame"], [37, 71, 47, 53], [37, 73, 47, 55, "top"], [37, 76, 47, 58], [37, 80, 47, 62], [37, 81, 47, 63], [37, 83, 47, 65, "toFixed"], [37, 90, 47, 72], [37, 91, 47, 73], [37, 92, 47, 74], [37, 93, 47, 75], [37, 95, 47, 76], [37, 98, 48, 10], [38, 10, 48, 10], [38, 11, 48, 16], [38, 12, 48, 17], [38, 14, 49, 10], [38, 18, 49, 10, "_jsxRuntime"], [38, 29, 49, 10], [38, 30, 49, 10, "jsxs"], [38, 34, 49, 10], [38, 36, 49, 11, "Text"], [38, 40, 49, 15], [39, 12, 49, 16, "style"], [39, 17, 49, 21], [39, 19, 49, 23, "styles"], [39, 25, 49, 29], [39, 26, 49, 30, "innerText"], [39, 35, 49, 40], [40, 12, 49, 40, "children"], [40, 20, 49, 40], [40, 23, 50, 13], [40, 24, 50, 14, "frame"], [40, 29, 50, 19], [40, 31, 50, 21, "width"], [40, 36, 50, 26], [40, 40, 50, 30], [40, 41, 50, 31], [40, 43, 50, 33, "toFixed"], [40, 50, 50, 40], [40, 51, 50, 41], [40, 52, 50, 42], [40, 53, 50, 43], [40, 55, 50, 44], [40, 62, 50, 52], [40, 64, 50, 53], [40, 67, 50, 56], [40, 69, 51, 13], [40, 70, 51, 14, "frame"], [40, 75, 51, 19], [40, 77, 51, 21, "height"], [40, 83, 51, 27], [40, 87, 51, 31], [40, 88, 51, 32], [40, 90, 51, 34, "toFixed"], [40, 97, 51, 41], [40, 98, 51, 42], [40, 99, 51, 43], [40, 100, 51, 44], [41, 10, 51, 44], [41, 11, 52, 16], [41, 12, 52, 17], [42, 8, 52, 17], [42, 9, 53, 14], [43, 6, 53, 15], [43, 7, 54, 20], [44, 4, 54, 21], [44, 5, 55, 18], [44, 6, 55, 19], [45, 2, 57, 0], [46, 2, 71, 0], [46, 11, 71, 9, "BoxContainer"], [46, 23, 71, 21, "BoxContainer"], [46, 24, 71, 21, "_ref2"], [46, 29, 71, 21], [46, 31, 76, 34], [47, 4, 76, 34], [47, 8, 72, 2, "title"], [47, 13, 72, 7], [47, 16, 72, 7, "_ref2"], [47, 21, 72, 7], [47, 22, 72, 2, "title"], [47, 27, 72, 7], [48, 6, 73, 2, "titleStyle"], [48, 16, 73, 12], [48, 19, 73, 12, "_ref2"], [48, 24, 73, 12], [48, 25, 73, 2, "titleStyle"], [48, 35, 73, 12], [49, 6, 74, 2, "box"], [49, 9, 74, 5], [49, 12, 74, 5, "_ref2"], [49, 17, 74, 5], [49, 18, 74, 2, "box"], [49, 21, 74, 5], [50, 6, 75, 2, "children"], [50, 14, 75, 10], [50, 17, 75, 10, "_ref2"], [50, 22, 75, 10], [50, 23, 75, 2, "children"], [50, 31, 75, 10], [51, 4, 77, 2], [51, 11, 78, 4], [51, 15, 78, 4, "_jsxRuntime"], [51, 26, 78, 4], [51, 27, 78, 4, "jsxs"], [51, 31, 78, 4], [51, 33, 78, 5, "View"], [51, 37, 78, 9], [52, 6, 78, 10, "style"], [52, 11, 78, 15], [52, 13, 78, 17, "styles"], [52, 19, 78, 23], [52, 20, 78, 24, "box"], [52, 23, 78, 28], [53, 6, 78, 28, "children"], [53, 14, 78, 28], [53, 17, 79, 6], [53, 21, 79, 6, "_jsxRuntime"], [53, 32, 79, 6], [53, 33, 79, 6, "jsxs"], [53, 37, 79, 6], [53, 39, 79, 7, "View"], [53, 43, 79, 11], [54, 8, 79, 12, "style"], [54, 13, 79, 17], [54, 15, 79, 19, "styles"], [54, 21, 79, 25], [54, 22, 79, 26, "row"], [54, 25, 79, 30], [55, 8, 79, 30, "children"], [55, 16, 79, 30], [55, 19, 81, 8], [55, 23, 81, 8, "_jsxRuntime"], [55, 34, 81, 8], [55, 35, 81, 8, "jsx"], [55, 38, 81, 8], [55, 40, 81, 9, "Text"], [55, 44, 81, 13], [56, 10, 81, 14, "style"], [56, 15, 81, 19], [56, 17, 81, 21], [56, 18, 81, 22, "titleStyle"], [56, 28, 81, 32], [56, 30, 81, 34, "styles"], [56, 36, 81, 40], [56, 37, 81, 41, "label"], [56, 42, 81, 46], [56, 43, 81, 48], [57, 10, 81, 48, "children"], [57, 18, 81, 48], [57, 20, 81, 50, "title"], [58, 8, 81, 55], [58, 9, 81, 62], [58, 10, 81, 63], [58, 12, 82, 8], [58, 16, 82, 8, "_jsxRuntime"], [58, 27, 82, 8], [58, 28, 82, 8, "jsx"], [58, 31, 82, 8], [58, 33, 82, 9, "Text"], [58, 37, 82, 13], [59, 10, 82, 14, "style"], [59, 15, 82, 19], [59, 17, 82, 21, "styles"], [59, 23, 82, 27], [59, 24, 82, 28, "boxText"], [59, 31, 82, 36], [60, 10, 82, 36, "children"], [60, 18, 82, 36], [60, 20, 82, 38, "box"], [60, 23, 82, 41], [60, 24, 82, 42, "top"], [61, 8, 82, 45], [61, 9, 82, 52], [61, 10, 82, 53], [62, 6, 82, 53], [62, 7, 83, 12], [62, 8, 83, 13], [62, 10, 84, 6], [62, 14, 84, 6, "_jsxRuntime"], [62, 25, 84, 6], [62, 26, 84, 6, "jsxs"], [62, 30, 84, 6], [62, 32, 84, 7, "View"], [62, 36, 84, 11], [63, 8, 84, 12, "style"], [63, 13, 84, 17], [63, 15, 84, 19, "styles"], [63, 21, 84, 25], [63, 22, 84, 26, "row"], [63, 25, 84, 30], [64, 8, 84, 30, "children"], [64, 16, 84, 30], [64, 19, 85, 8], [64, 23, 85, 8, "_jsxRuntime"], [64, 34, 85, 8], [64, 35, 85, 8, "jsx"], [64, 38, 85, 8], [64, 40, 85, 9, "Text"], [64, 44, 85, 13], [65, 10, 85, 14, "style"], [65, 15, 85, 19], [65, 17, 85, 21, "styles"], [65, 23, 85, 27], [65, 24, 85, 28, "boxText"], [65, 31, 85, 36], [66, 10, 85, 36, "children"], [66, 18, 85, 36], [66, 20, 85, 38, "box"], [66, 23, 85, 41], [66, 24, 85, 42, "left"], [67, 8, 85, 46], [67, 9, 85, 53], [67, 10, 85, 54], [67, 12, 86, 9, "children"], [67, 20, 86, 17], [67, 22, 87, 8], [67, 26, 87, 8, "_jsxRuntime"], [67, 37, 87, 8], [67, 38, 87, 8, "jsx"], [67, 41, 87, 8], [67, 43, 87, 9, "Text"], [67, 47, 87, 13], [68, 10, 87, 14, "style"], [68, 15, 87, 19], [68, 17, 87, 21, "styles"], [68, 23, 87, 27], [68, 24, 87, 28, "boxText"], [68, 31, 87, 36], [69, 10, 87, 36, "children"], [69, 18, 87, 36], [69, 20, 87, 38, "box"], [69, 23, 87, 41], [69, 24, 87, 42, "right"], [70, 8, 87, 47], [70, 9, 87, 54], [70, 10, 87, 55], [71, 6, 87, 55], [71, 7, 88, 12], [71, 8, 88, 13], [71, 10, 89, 6], [71, 14, 89, 6, "_jsxRuntime"], [71, 25, 89, 6], [71, 26, 89, 6, "jsx"], [71, 29, 89, 6], [71, 31, 89, 7, "Text"], [71, 35, 89, 11], [72, 8, 89, 12, "style"], [72, 13, 89, 17], [72, 15, 89, 19, "styles"], [72, 21, 89, 25], [72, 22, 89, 26, "boxText"], [72, 29, 89, 34], [73, 8, 89, 34, "children"], [73, 16, 89, 34], [73, 18, 89, 36, "box"], [73, 21, 89, 39], [73, 22, 89, 40, "bottom"], [74, 6, 89, 46], [74, 7, 89, 53], [74, 8, 89, 54], [75, 4, 89, 54], [75, 5, 90, 10], [75, 6, 90, 11], [76, 2, 92, 0], [77, 2, 94, 0], [77, 6, 94, 6, "styles"], [77, 12, 94, 12], [77, 15, 94, 15, "StyleSheet"], [77, 25, 94, 25], [77, 26, 94, 26, "create"], [77, 32, 94, 32], [77, 33, 94, 33], [78, 4, 95, 2, "row"], [78, 7, 95, 5], [78, 9, 95, 7], [79, 6, 96, 4, "flexDirection"], [79, 19, 96, 17], [79, 21, 96, 19], [79, 26, 96, 24], [80, 6, 97, 4, "alignItems"], [80, 16, 97, 14], [80, 18, 97, 16], [80, 26, 97, 24], [81, 6, 98, 4, "justifyContent"], [81, 20, 98, 18], [81, 22, 98, 20], [82, 4, 99, 2], [82, 5, 99, 3], [83, 4, 100, 2, "margin<PERSON>abel"], [83, 15, 100, 13], [83, 17, 100, 15], [84, 6, 101, 4, "width"], [84, 11, 101, 9], [84, 13, 101, 11], [85, 4, 102, 2], [85, 5, 102, 3], [86, 4, 103, 2, "label"], [86, 9, 103, 7], [86, 11, 103, 9], [87, 6, 104, 4, "fontSize"], [87, 14, 104, 12], [87, 16, 104, 14], [87, 18, 104, 16], [88, 6, 105, 4, "color"], [88, 11, 105, 9], [88, 13, 105, 11], [88, 29, 105, 27], [89, 6, 106, 4, "marginLeft"], [89, 16, 106, 14], [89, 18, 106, 16], [89, 19, 106, 17], [90, 6, 107, 4, "flex"], [90, 10, 107, 8], [90, 12, 107, 10], [90, 13, 107, 11], [91, 6, 108, 4, "textAlign"], [91, 15, 108, 13], [91, 17, 108, 15], [91, 23, 108, 21], [92, 6, 109, 4, "top"], [92, 9, 109, 7], [92, 11, 109, 9], [92, 12, 109, 10], [93, 4, 110, 2], [93, 5, 110, 3], [94, 4, 111, 2, "innerText"], [94, 13, 111, 11], [94, 15, 111, 13], [95, 6, 112, 4, "color"], [95, 11, 112, 9], [95, 13, 112, 11], [95, 21, 112, 19], [96, 6, 113, 4, "fontSize"], [96, 14, 113, 12], [96, 16, 113, 14], [96, 18, 113, 16], [97, 6, 114, 4, "textAlign"], [97, 15, 114, 13], [97, 17, 114, 15], [97, 25, 114, 23], [98, 6, 115, 4, "width"], [98, 11, 115, 9], [98, 13, 115, 11], [99, 4, 116, 2], [99, 5, 116, 3], [100, 4, 117, 2, "box"], [100, 7, 117, 5], [100, 9, 117, 7], [101, 6, 118, 4, "borderWidth"], [101, 17, 118, 15], [101, 19, 118, 17], [101, 20, 118, 18], [102, 6, 119, 4, "borderColor"], [102, 17, 119, 15], [102, 19, 119, 17], [103, 4, 120, 2], [103, 5, 120, 3], [104, 4, 121, 2, "boxText"], [104, 11, 121, 9], [104, 13, 121, 11], [105, 6, 122, 4, "color"], [105, 11, 122, 9], [105, 13, 122, 11], [105, 20, 122, 18], [106, 6, 123, 4, "fontSize"], [106, 14, 123, 12], [106, 16, 123, 14], [106, 18, 123, 16], [107, 6, 124, 4, "marginHorizontal"], [107, 22, 124, 20], [107, 24, 124, 22], [107, 25, 124, 23], [108, 6, 125, 4, "marginVertical"], [108, 20, 125, 18], [108, 22, 125, 20], [108, 23, 125, 21], [109, 6, 126, 4, "textAlign"], [109, 15, 126, 13], [109, 17, 126, 15], [110, 4, 127, 2], [111, 2, 128, 0], [111, 3, 128, 1], [111, 4, 128, 2], [112, 2, 128, 3], [112, 6, 128, 3, "_default"], [112, 14, 128, 3], [112, 17, 128, 3, "exports"], [112, 24, 128, 3], [112, 25, 128, 3, "default"], [112, 32, 128, 3], [112, 35, 130, 15, "BoxInspector"], [112, 47, 130, 27], [113, 0, 130, 27], [113, 3]], "functionMap": {"names": ["<global>", "BoxInspector", "BoxContainer"], "mappings": "AAA;ACqC;CDmB;AEc;CFqB"}}, "type": "js/module"}]}