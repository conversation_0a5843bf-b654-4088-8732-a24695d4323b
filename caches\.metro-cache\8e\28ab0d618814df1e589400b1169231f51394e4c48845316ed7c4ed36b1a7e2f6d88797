{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.convertObjectFitToResizeMode = convertObjectFitToResizeMode;\n  var objectFitMap = {\n    contain: 'contain',\n    cover: 'cover',\n    fill: 'stretch',\n    'scale-down': 'contain',\n    none: 'none'\n  };\n  function convertObjectFitToResizeMode(objectFit) {\n    return objectFit != null ? objectFitMap[objectFit] : undefined;\n  }\n});", "lineCount": 16, "map": [[6, 2, 13, 0], [6, 6, 13, 6, "objectFitMap"], [6, 18, 13, 47], [6, 21, 13, 50], [7, 4, 14, 2, "contain"], [7, 11, 14, 9], [7, 13, 14, 11], [7, 22, 14, 20], [8, 4, 15, 2, "cover"], [8, 9, 15, 7], [8, 11, 15, 9], [8, 18, 15, 16], [9, 4, 16, 2, "fill"], [9, 8, 16, 6], [9, 10, 16, 8], [9, 19, 16, 17], [10, 4, 17, 2], [10, 16, 17, 14], [10, 18, 17, 16], [10, 27, 17, 25], [11, 4, 18, 2, "none"], [11, 8, 18, 6], [11, 10, 18, 8], [12, 2, 19, 0], [12, 3, 19, 1], [13, 2, 21, 7], [13, 11, 21, 16, "convertObjectFitToResizeMode"], [13, 39, 21, 44, "convertObjectFitToResizeMode"], [13, 40, 22, 2, "objectFit"], [13, 49, 22, 20], [13, 51, 23, 20], [14, 4, 24, 2], [14, 11, 24, 9, "objectFit"], [14, 20, 24, 18], [14, 24, 24, 22], [14, 28, 24, 26], [14, 31, 24, 29, "objectFitMap"], [14, 43, 24, 41], [14, 44, 24, 42, "objectFit"], [14, 53, 24, 51], [14, 54, 24, 52], [14, 57, 24, 55, "undefined"], [14, 66, 24, 64], [15, 2, 25, 0], [16, 0, 25, 1], [16, 3]], "functionMap": {"names": ["<global>", "convertObjectFitToResizeMode"], "mappings": "AAA;OCoB"}}, "type": "js/module"}]}