{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  function emptyFunction() {}\n  var BackHandler = {\n    exitApp: emptyFunction,\n    addEventListener: () => ({\n      remove: emptyFunction\n    }),\n    removeEventListener: emptyFunction\n  };\n  var _default = exports.default = BackHandler;\n});", "lineCount": 15, "map": [[6, 2, 1, 0], [6, 11, 1, 9, "emptyFunction"], [6, 24, 1, 22, "emptyFunction"], [6, 25, 1, 22], [6, 27, 1, 25], [6, 28, 1, 26], [7, 2, 2, 0], [7, 6, 2, 4, "<PERSON><PERSON><PERSON><PERSON>"], [7, 17, 2, 15], [7, 20, 2, 18], [8, 4, 3, 2, "exitApp"], [8, 11, 3, 9], [8, 13, 3, 11, "emptyFunction"], [8, 26, 3, 24], [9, 4, 4, 2, "addEventListener"], [9, 20, 4, 18], [9, 22, 4, 20, "addEventListener"], [9, 23, 4, 20], [9, 29, 4, 27], [10, 6, 4, 29, "remove"], [10, 12, 4, 35], [10, 14, 4, 37, "emptyFunction"], [11, 4, 4, 51], [11, 5, 4, 52], [11, 6, 4, 53], [12, 4, 5, 2, "removeEventListener"], [12, 23, 5, 21], [12, 25, 5, 23, "emptyFunction"], [13, 2, 6, 0], [13, 3, 6, 1], [14, 2, 6, 2], [14, 6, 6, 2, "_default"], [14, 14, 6, 2], [14, 17, 6, 2, "exports"], [14, 24, 6, 2], [14, 25, 6, 2, "default"], [14, 32, 6, 2], [14, 35, 7, 15, "<PERSON><PERSON><PERSON><PERSON>"], [14, 46, 7, 26], [15, 0, 7, 26], [15, 3]], "functionMap": {"names": ["emptyFunction", "<global>", "BackHandler.addEventListener"], "mappings": "AAA,2BC;oBCG,iCD"}}, "type": "js/module"}]}