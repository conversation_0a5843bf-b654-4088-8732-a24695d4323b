{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-css-interop", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "GojJv+4gs9E3DINH6CdbjjB/YmY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 277}, "end": {"line": 2, "column": 31, "index": 308}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "warn-once", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 362}, "end": {"line": 4, "column": 33, "index": 395}}], "key": "vWcOfkIsCMxiS31CEQqA0rEMOUM=", "exportNames": ["*"]}}, {"name": "./DebugContainer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 396}, "end": {"line": 5, "column": 46, "index": 442}}], "key": "00nR495UyKRlwAAkSGQ6ISyX2ng=", "exportNames": ["*"]}}, {"name": "./ScreenStackHeaderConfig", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 443}, "end": {"line": 6, "column": 68, "index": 511}}], "key": "ODmM8Zo4+r0I2znLfxVk65uhabc=", "exportNames": ["*"]}}, {"name": "./Screen", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 512}, "end": {"line": 7, "column": 30, "index": 542}}], "key": "Ddkhns95vV7IG/j2ilBD9xB8a68=", "exportNames": ["*"]}}, {"name": "./ScreenStack", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 543}, "end": {"line": 8, "column": 40, "index": 583}}], "key": "0/etDhxVbASMuuXiHzxxRmW0HzY=", "exportNames": ["*"]}}, {"name": "../contexts", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 584}, "end": {"line": 9, "column": 51, "index": 635}}], "key": "wd0eGeDAyqj2GVxPDras6etTTUQ=", "exportNames": ["*"]}}, {"name": "./ScreenFooter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 636}, "end": {"line": 10, "column": 49, "index": 685}}], "key": "SG8niKWihz3sf8/QHPXl74IHs08=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _ReactNativeCSSInterop = _interopRequireWildcard(require(_dependencyMap[1], \"react-native-css-interop\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/StyleSheet\"));\n  var _warnOnce = _interopRequireDefault(require(_dependencyMap[5], \"warn-once\"));\n  var _DebugContainer = _interopRequireDefault(require(_dependencyMap[6], \"./DebugContainer\"));\n  var _ScreenStackHeaderConfig = require(_dependencyMap[7], \"./ScreenStackHeaderConfig\");\n  var _Screen = _interopRequireDefault(require(_dependencyMap[8], \"./Screen\"));\n  var _ScreenStack = _interopRequireDefault(require(_dependencyMap[9], \"./ScreenStack\"));\n  var _contexts = require(_dependencyMap[10], \"../contexts\");\n  var _ScreenFooter = require(_dependencyMap[11], \"./ScreenFooter\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function _extends() {\n    return _extends = Object.assign ? Object.assign.bind() : function (n) {\n      for (var e = 1; e < arguments.length; e++) {\n        var t = arguments[e];\n        for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n      }\n      return n;\n    }, _extends.apply(null, arguments);\n  }\n  function ScreenStackItem({\n    children,\n    headerConfig,\n    activityState,\n    shouldFreeze,\n    stackPresentation,\n    sheetAllowedDetents,\n    contentStyle,\n    style,\n    screenId,\n    // eslint-disable-next-line camelcase\n    unstable_sheetFooter,\n    ...rest\n  }, ref) {\n    const currentScreenRef = React.useRef(null);\n    const screenRefs = React.useContext(_contexts.RNSScreensRefContext);\n    React.useImperativeHandle(ref, () => currentScreenRef.current);\n    const isHeaderInModal = _Platform.default.OS === 'android' ? false : stackPresentation !== 'push' && headerConfig?.hidden === false;\n    const headerHiddenPreviousRef = React.useRef(headerConfig?.hidden);\n    React.useEffect(() => {\n      (0, _warnOnce.default)(_Platform.default.OS !== 'android' && stackPresentation !== 'push' && headerHiddenPreviousRef.current !== headerConfig?.hidden, `Dynamically changing header's visibility in modals will result in remounting the screen and losing all local state.`);\n      headerHiddenPreviousRef.current = headerConfig?.hidden;\n    }, [headerConfig?.hidden, stackPresentation]);\n    const content = /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(React.Fragment, null, /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_DebugContainer.default, {\n      style: [stackPresentation === 'formSheet' ? _Platform.default.OS === 'ios' ? styles.absolute : sheetAllowedDetents === 'fitToContents' ? null : styles.container : styles.container, contentStyle],\n      stackPresentation: stackPresentation ?? 'push'\n    }, children), /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_ScreenStackHeaderConfig.ScreenStackHeaderConfig, headerConfig), stackPresentation === 'formSheet' && unstable_sheetFooter && /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_ScreenFooter.FooterComponent, null, unstable_sheetFooter()));\n\n    // We take backgroundColor from contentStyle and apply it on Screen.\n    // This allows to workaround one issue with truncated\n    // content with formSheet presentation.\n    let internalScreenStyle;\n    if (stackPresentation === 'formSheet' && contentStyle) {\n      const flattenContentStyles = _StyleSheet.default.flatten(contentStyle);\n      internalScreenStyle = {\n        backgroundColor: flattenContentStyles?.backgroundColor\n      };\n    }\n    return /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_Screen.default, _extends({\n      ref: node => {\n        currentScreenRef.current = node;\n        if (screenRefs === null) {\n          console.warn('Looks like RNSScreensRefContext is missing. Make sure the ScreenStack component is wrapped in it');\n          return;\n        }\n        const currentRefs = screenRefs.current;\n        if (node === null) {\n          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n          delete currentRefs[screenId];\n        } else {\n          currentRefs[screenId] = {\n            current: node\n          };\n        }\n      },\n      enabled: true,\n      isNativeStack: true,\n      activityState: activityState,\n      shouldFreeze: shouldFreeze,\n      stackPresentation: stackPresentation,\n      hasLargeHeader: headerConfig?.largeTitle ?? false,\n      sheetAllowedDetents: sheetAllowedDetents,\n      style: [style, internalScreenStyle]\n    }, rest), isHeaderInModal ? /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_ScreenStack.default, {\n      style: styles.container\n    }, /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_Screen.default, {\n      enabled: true,\n      isNativeStack: true,\n      activityState: activityState,\n      shouldFreeze: shouldFreeze,\n      hasLargeHeader: headerConfig?.largeTitle ?? false,\n      style: _StyleSheet.default.absoluteFill\n    }, content)) : content);\n  }\n  var _default = exports.default = /*#__PURE__*/React.forwardRef(ScreenStackItem);\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1\n    },\n    absolute: {\n      position: 'absolute',\n      top: 0,\n      start: 0,\n      end: 0\n    }\n  });\n});", "lineCount": 114, "map": [[8, 2, 2, 0], [8, 6, 2, 0, "React"], [8, 11, 2, 0], [8, 14, 2, 0, "_interopRequireWildcard"], [8, 37, 2, 0], [8, 38, 2, 0, "require"], [8, 45, 2, 0], [8, 46, 2, 0, "_dependencyMap"], [8, 60, 2, 0], [9, 2, 2, 31], [9, 6, 2, 31, "_Platform"], [9, 15, 2, 31], [9, 18, 2, 31, "_interopRequireDefault"], [9, 40, 2, 31], [9, 41, 2, 31, "require"], [9, 48, 2, 31], [9, 49, 2, 31, "_dependencyMap"], [9, 63, 2, 31], [10, 2, 2, 31], [10, 6, 2, 31, "_StyleSheet"], [10, 17, 2, 31], [10, 20, 2, 31, "_interopRequireDefault"], [10, 42, 2, 31], [10, 43, 2, 31, "require"], [10, 50, 2, 31], [10, 51, 2, 31, "_dependencyMap"], [10, 65, 2, 31], [11, 2, 4, 0], [11, 6, 4, 0, "_warnOnce"], [11, 15, 4, 0], [11, 18, 4, 0, "_interopRequireDefault"], [11, 40, 4, 0], [11, 41, 4, 0, "require"], [11, 48, 4, 0], [11, 49, 4, 0, "_dependencyMap"], [11, 63, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_DebugContainer"], [12, 21, 5, 0], [12, 24, 5, 0, "_interopRequireDefault"], [12, 46, 5, 0], [12, 47, 5, 0, "require"], [12, 54, 5, 0], [12, 55, 5, 0, "_dependencyMap"], [12, 69, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_ScreenStackHeaderConfig"], [13, 30, 6, 0], [13, 33, 6, 0, "require"], [13, 40, 6, 0], [13, 41, 6, 0, "_dependencyMap"], [13, 55, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_Screen"], [14, 13, 7, 0], [14, 16, 7, 0, "_interopRequireDefault"], [14, 38, 7, 0], [14, 39, 7, 0, "require"], [14, 46, 7, 0], [14, 47, 7, 0, "_dependencyMap"], [14, 61, 7, 0], [15, 2, 8, 0], [15, 6, 8, 0, "_ScreenStack"], [15, 18, 8, 0], [15, 21, 8, 0, "_interopRequireDefault"], [15, 43, 8, 0], [15, 44, 8, 0, "require"], [15, 51, 8, 0], [15, 52, 8, 0, "_dependencyMap"], [15, 66, 8, 0], [16, 2, 9, 0], [16, 6, 9, 0, "_contexts"], [16, 15, 9, 0], [16, 18, 9, 0, "require"], [16, 25, 9, 0], [16, 26, 9, 0, "_dependencyMap"], [16, 40, 9, 0], [17, 2, 10, 0], [17, 6, 10, 0, "_ScreenFooter"], [17, 19, 10, 0], [17, 22, 10, 0, "require"], [17, 29, 10, 0], [17, 30, 10, 0, "_dependencyMap"], [17, 44, 10, 0], [18, 2, 10, 49], [18, 11, 10, 49, "_interopRequireWildcard"], [18, 35, 10, 49, "e"], [18, 36, 10, 49], [18, 38, 10, 49, "t"], [18, 39, 10, 49], [18, 68, 10, 49, "WeakMap"], [18, 75, 10, 49], [18, 81, 10, 49, "r"], [18, 82, 10, 49], [18, 89, 10, 49, "WeakMap"], [18, 96, 10, 49], [18, 100, 10, 49, "n"], [18, 101, 10, 49], [18, 108, 10, 49, "WeakMap"], [18, 115, 10, 49], [18, 127, 10, 49, "_interopRequireWildcard"], [18, 150, 10, 49], [18, 162, 10, 49, "_interopRequireWildcard"], [18, 163, 10, 49, "e"], [18, 164, 10, 49], [18, 166, 10, 49, "t"], [18, 167, 10, 49], [18, 176, 10, 49, "t"], [18, 177, 10, 49], [18, 181, 10, 49, "e"], [18, 182, 10, 49], [18, 186, 10, 49, "e"], [18, 187, 10, 49], [18, 188, 10, 49, "__esModule"], [18, 198, 10, 49], [18, 207, 10, 49, "e"], [18, 208, 10, 49], [18, 214, 10, 49, "o"], [18, 215, 10, 49], [18, 217, 10, 49, "i"], [18, 218, 10, 49], [18, 220, 10, 49, "f"], [18, 221, 10, 49], [18, 226, 10, 49, "__proto__"], [18, 235, 10, 49], [18, 243, 10, 49, "default"], [18, 250, 10, 49], [18, 252, 10, 49, "e"], [18, 253, 10, 49], [18, 270, 10, 49, "e"], [18, 271, 10, 49], [18, 294, 10, 49, "e"], [18, 295, 10, 49], [18, 320, 10, 49, "e"], [18, 321, 10, 49], [18, 330, 10, 49, "f"], [18, 331, 10, 49], [18, 337, 10, 49, "o"], [18, 338, 10, 49], [18, 341, 10, 49, "t"], [18, 342, 10, 49], [18, 345, 10, 49, "n"], [18, 346, 10, 49], [18, 349, 10, 49, "r"], [18, 350, 10, 49], [18, 358, 10, 49, "o"], [18, 359, 10, 49], [18, 360, 10, 49, "has"], [18, 363, 10, 49], [18, 364, 10, 49, "e"], [18, 365, 10, 49], [18, 375, 10, 49, "o"], [18, 376, 10, 49], [18, 377, 10, 49, "get"], [18, 380, 10, 49], [18, 381, 10, 49, "e"], [18, 382, 10, 49], [18, 385, 10, 49, "o"], [18, 386, 10, 49], [18, 387, 10, 49, "set"], [18, 390, 10, 49], [18, 391, 10, 49, "e"], [18, 392, 10, 49], [18, 394, 10, 49, "f"], [18, 395, 10, 49], [18, 411, 10, 49, "t"], [18, 412, 10, 49], [18, 416, 10, 49, "e"], [18, 417, 10, 49], [18, 433, 10, 49, "t"], [18, 434, 10, 49], [18, 441, 10, 49, "hasOwnProperty"], [18, 455, 10, 49], [18, 456, 10, 49, "call"], [18, 460, 10, 49], [18, 461, 10, 49, "e"], [18, 462, 10, 49], [18, 464, 10, 49, "t"], [18, 465, 10, 49], [18, 472, 10, 49, "i"], [18, 473, 10, 49], [18, 477, 10, 49, "o"], [18, 478, 10, 49], [18, 481, 10, 49, "Object"], [18, 487, 10, 49], [18, 488, 10, 49, "defineProperty"], [18, 502, 10, 49], [18, 507, 10, 49, "Object"], [18, 513, 10, 49], [18, 514, 10, 49, "getOwnPropertyDescriptor"], [18, 538, 10, 49], [18, 539, 10, 49, "e"], [18, 540, 10, 49], [18, 542, 10, 49, "t"], [18, 543, 10, 49], [18, 550, 10, 49, "i"], [18, 551, 10, 49], [18, 552, 10, 49, "get"], [18, 555, 10, 49], [18, 559, 10, 49, "i"], [18, 560, 10, 49], [18, 561, 10, 49, "set"], [18, 564, 10, 49], [18, 568, 10, 49, "o"], [18, 569, 10, 49], [18, 570, 10, 49, "f"], [18, 571, 10, 49], [18, 573, 10, 49, "t"], [18, 574, 10, 49], [18, 576, 10, 49, "i"], [18, 577, 10, 49], [18, 581, 10, 49, "f"], [18, 582, 10, 49], [18, 583, 10, 49, "t"], [18, 584, 10, 49], [18, 588, 10, 49, "e"], [18, 589, 10, 49], [18, 590, 10, 49, "t"], [18, 591, 10, 49], [18, 602, 10, 49, "f"], [18, 603, 10, 49], [18, 608, 10, 49, "e"], [18, 609, 10, 49], [18, 611, 10, 49, "t"], [18, 612, 10, 49], [19, 2, 1, 0], [19, 11, 1, 9, "_extends"], [19, 19, 1, 17, "_extends"], [19, 20, 1, 17], [19, 22, 1, 20], [20, 4, 1, 22], [20, 11, 1, 29, "_extends"], [20, 19, 1, 37], [20, 22, 1, 40, "Object"], [20, 28, 1, 46], [20, 29, 1, 47, "assign"], [20, 35, 1, 53], [20, 38, 1, 56, "Object"], [20, 44, 1, 62], [20, 45, 1, 63, "assign"], [20, 51, 1, 69], [20, 52, 1, 70, "bind"], [20, 56, 1, 74], [20, 57, 1, 75], [20, 58, 1, 76], [20, 61, 1, 79], [20, 71, 1, 89, "n"], [20, 72, 1, 90], [20, 74, 1, 92], [21, 6, 1, 94], [21, 11, 1, 99], [21, 15, 1, 103, "e"], [21, 16, 1, 104], [21, 19, 1, 107], [21, 20, 1, 108], [21, 22, 1, 110, "e"], [21, 23, 1, 111], [21, 26, 1, 114, "arguments"], [21, 35, 1, 123], [21, 36, 1, 124, "length"], [21, 42, 1, 130], [21, 44, 1, 132, "e"], [21, 45, 1, 133], [21, 47, 1, 135], [21, 49, 1, 137], [22, 8, 1, 139], [22, 12, 1, 143, "t"], [22, 13, 1, 144], [22, 16, 1, 147, "arguments"], [22, 25, 1, 156], [22, 26, 1, 157, "e"], [22, 27, 1, 158], [22, 28, 1, 159], [23, 8, 1, 161], [23, 13, 1, 166], [23, 17, 1, 170, "r"], [23, 18, 1, 171], [23, 22, 1, 175, "t"], [23, 23, 1, 176], [23, 25, 1, 178], [23, 26, 1, 179], [23, 27, 1, 180], [23, 28, 1, 181], [23, 30, 1, 183, "hasOwnProperty"], [23, 44, 1, 197], [23, 45, 1, 198, "call"], [23, 49, 1, 202], [23, 50, 1, 203, "t"], [23, 51, 1, 204], [23, 53, 1, 206, "r"], [23, 54, 1, 207], [23, 55, 1, 208], [23, 60, 1, 213, "n"], [23, 61, 1, 214], [23, 62, 1, 215, "r"], [23, 63, 1, 216], [23, 64, 1, 217], [23, 67, 1, 220, "t"], [23, 68, 1, 221], [23, 69, 1, 222, "r"], [23, 70, 1, 223], [23, 71, 1, 224], [23, 72, 1, 225], [24, 6, 1, 227], [25, 6, 1, 229], [25, 13, 1, 236, "n"], [25, 14, 1, 237], [26, 4, 1, 239], [26, 5, 1, 240], [26, 7, 1, 242, "_extends"], [26, 15, 1, 250], [26, 16, 1, 251, "apply"], [26, 21, 1, 256], [26, 22, 1, 257], [26, 26, 1, 261], [26, 28, 1, 263, "arguments"], [26, 37, 1, 272], [26, 38, 1, 273], [27, 2, 1, 275], [28, 2, 11, 0], [28, 11, 11, 9, "ScreenStackItem"], [28, 26, 11, 24, "ScreenStackItem"], [28, 27, 11, 25], [29, 4, 12, 2, "children"], [29, 12, 12, 10], [30, 4, 13, 2, "headerConfig"], [30, 16, 13, 14], [31, 4, 14, 2, "activityState"], [31, 17, 14, 15], [32, 4, 15, 2, "shouldFreeze"], [32, 16, 15, 14], [33, 4, 16, 2, "stackPresentation"], [33, 21, 16, 19], [34, 4, 17, 2, "sheetAllowedDetents"], [34, 23, 17, 21], [35, 4, 18, 2, "contentStyle"], [35, 16, 18, 14], [36, 4, 19, 2, "style"], [36, 9, 19, 7], [37, 4, 20, 2, "screenId"], [37, 12, 20, 10], [38, 4, 21, 2], [39, 4, 22, 2, "unstable_sheetFooter"], [39, 24, 22, 22], [40, 4, 23, 2], [40, 7, 23, 5, "rest"], [41, 2, 24, 0], [41, 3, 24, 1], [41, 5, 24, 3, "ref"], [41, 8, 24, 6], [41, 10, 24, 8], [42, 4, 25, 2], [42, 10, 25, 8, "currentScreenRef"], [42, 26, 25, 24], [42, 29, 25, 27, "React"], [42, 34, 25, 32], [42, 35, 25, 33, "useRef"], [42, 41, 25, 39], [42, 42, 25, 40], [42, 46, 25, 44], [42, 47, 25, 45], [43, 4, 26, 2], [43, 10, 26, 8, "screenRefs"], [43, 20, 26, 18], [43, 23, 26, 21, "React"], [43, 28, 26, 26], [43, 29, 26, 27, "useContext"], [43, 39, 26, 37], [43, 40, 26, 38, "RNSScreensRefContext"], [43, 70, 26, 58], [43, 71, 26, 59], [44, 4, 27, 2, "React"], [44, 9, 27, 7], [44, 10, 27, 8, "useImperativeHandle"], [44, 29, 27, 27], [44, 30, 27, 28, "ref"], [44, 33, 27, 31], [44, 35, 27, 33], [44, 41, 27, 39, "currentScreenRef"], [44, 57, 27, 55], [44, 58, 27, 56, "current"], [44, 65, 27, 63], [44, 66, 27, 64], [45, 4, 28, 2], [45, 10, 28, 8, "isHeaderInModal"], [45, 25, 28, 23], [45, 28, 28, 26, "Platform"], [45, 45, 28, 34], [45, 46, 28, 35, "OS"], [45, 48, 28, 37], [45, 53, 28, 42], [45, 62, 28, 51], [45, 65, 28, 54], [45, 70, 28, 59], [45, 73, 28, 62, "stackPresentation"], [45, 90, 28, 79], [45, 95, 28, 84], [45, 101, 28, 90], [45, 105, 28, 94, "headerConfig"], [45, 117, 28, 106], [45, 119, 28, 108, "hidden"], [45, 125, 28, 114], [45, 130, 28, 119], [45, 135, 28, 124], [46, 4, 29, 2], [46, 10, 29, 8, "headerHiddenPreviousRef"], [46, 33, 29, 31], [46, 36, 29, 34, "React"], [46, 41, 29, 39], [46, 42, 29, 40, "useRef"], [46, 48, 29, 46], [46, 49, 29, 47, "headerConfig"], [46, 61, 29, 59], [46, 63, 29, 61, "hidden"], [46, 69, 29, 67], [46, 70, 29, 68], [47, 4, 30, 2, "React"], [47, 9, 30, 7], [47, 10, 30, 8, "useEffect"], [47, 19, 30, 17], [47, 20, 30, 18], [47, 26, 30, 24], [48, 6, 31, 4], [48, 10, 31, 4, "warnOnce"], [48, 27, 31, 12], [48, 29, 31, 13, "Platform"], [48, 46, 31, 21], [48, 47, 31, 22, "OS"], [48, 49, 31, 24], [48, 54, 31, 29], [48, 63, 31, 38], [48, 67, 31, 42, "stackPresentation"], [48, 84, 31, 59], [48, 89, 31, 64], [48, 95, 31, 70], [48, 99, 31, 74, "headerHiddenPreviousRef"], [48, 122, 31, 97], [48, 123, 31, 98, "current"], [48, 130, 31, 105], [48, 135, 31, 110, "headerConfig"], [48, 147, 31, 122], [48, 149, 31, 124, "hidden"], [48, 155, 31, 130], [48, 157, 31, 132], [48, 274, 31, 249], [48, 275, 31, 250], [49, 6, 32, 4, "headerHiddenPreviousRef"], [49, 29, 32, 27], [49, 30, 32, 28, "current"], [49, 37, 32, 35], [49, 40, 32, 38, "headerConfig"], [49, 52, 32, 50], [49, 54, 32, 52, "hidden"], [49, 60, 32, 58], [50, 4, 33, 2], [50, 5, 33, 3], [50, 7, 33, 5], [50, 8, 33, 6, "headerConfig"], [50, 20, 33, 18], [50, 22, 33, 20, "hidden"], [50, 28, 33, 26], [50, 30, 33, 28, "stackPresentation"], [50, 47, 33, 45], [50, 48, 33, 46], [50, 49, 33, 47], [51, 4, 34, 2], [51, 10, 34, 8, "content"], [51, 17, 34, 15], [51, 20, 34, 18], [51, 33, 34, 31, "_ReactNativeCSSInterop"], [51, 55, 34, 31], [51, 56, 34, 31, "createInteropElement"], [51, 76, 34, 31], [51, 77, 34, 51, "React"], [51, 82, 34, 56], [51, 83, 34, 57, "Fragment"], [51, 91, 34, 65], [51, 93, 34, 67], [51, 97, 34, 71], [51, 99, 34, 73], [51, 112, 34, 86, "_ReactNativeCSSInterop"], [51, 134, 34, 86], [51, 135, 34, 86, "createInteropElement"], [51, 155, 34, 86], [51, 156, 34, 106, "DebugContainer"], [51, 179, 34, 120], [51, 181, 34, 122], [52, 6, 35, 4, "style"], [52, 11, 35, 9], [52, 13, 35, 11], [52, 14, 35, 12, "stackPresentation"], [52, 31, 35, 29], [52, 36, 35, 34], [52, 47, 35, 45], [52, 50, 35, 48, "Platform"], [52, 67, 35, 56], [52, 68, 35, 57, "OS"], [52, 70, 35, 59], [52, 75, 35, 64], [52, 80, 35, 69], [52, 83, 35, 72, "styles"], [52, 89, 35, 78], [52, 90, 35, 79, "absolute"], [52, 98, 35, 87], [52, 101, 35, 90, "sheetAllowedDetents"], [52, 120, 35, 109], [52, 125, 35, 114], [52, 140, 35, 129], [52, 143, 35, 132], [52, 147, 35, 136], [52, 150, 35, 139, "styles"], [52, 156, 35, 145], [52, 157, 35, 146, "container"], [52, 166, 35, 155], [52, 169, 35, 158, "styles"], [52, 175, 35, 164], [52, 176, 35, 165, "container"], [52, 185, 35, 174], [52, 187, 35, 176, "contentStyle"], [52, 199, 35, 188], [52, 200, 35, 189], [53, 6, 36, 4, "stackPresentation"], [53, 23, 36, 21], [53, 25, 36, 23, "stackPresentation"], [53, 42, 36, 40], [53, 46, 36, 44], [54, 4, 37, 2], [54, 5, 37, 3], [54, 7, 37, 5, "children"], [54, 15, 37, 13], [54, 16, 37, 14], [54, 18, 37, 16], [54, 31, 37, 29, "_ReactNativeCSSInterop"], [54, 53, 37, 29], [54, 54, 37, 29, "createInteropElement"], [54, 74, 37, 29], [54, 75, 37, 49, "ScreenStackHeaderConfig"], [54, 123, 37, 72], [54, 125, 37, 74, "headerConfig"], [54, 137, 37, 86], [54, 138, 37, 87], [54, 140, 37, 89, "stackPresentation"], [54, 157, 37, 106], [54, 162, 37, 111], [54, 173, 37, 122], [54, 177, 37, 126, "unstable_sheetFooter"], [54, 197, 37, 146], [54, 201, 37, 150], [54, 214, 37, 163, "_ReactNativeCSSInterop"], [54, 236, 37, 163], [54, 237, 37, 163, "createInteropElement"], [54, 257, 37, 163], [54, 258, 37, 183, "FooterComponent"], [54, 287, 37, 198], [54, 289, 37, 200], [54, 293, 37, 204], [54, 295, 37, 206, "unstable_sheetFooter"], [54, 315, 37, 226], [54, 316, 37, 227], [54, 317, 37, 228], [54, 318, 37, 229], [54, 319, 37, 230], [56, 4, 39, 2], [57, 4, 40, 2], [58, 4, 41, 2], [59, 4, 42, 2], [59, 8, 42, 6, "internalScreenStyle"], [59, 27, 42, 25], [60, 4, 43, 2], [60, 8, 43, 6, "stackPresentation"], [60, 25, 43, 23], [60, 30, 43, 28], [60, 41, 43, 39], [60, 45, 43, 43, "contentStyle"], [60, 57, 43, 55], [60, 59, 43, 57], [61, 6, 44, 4], [61, 12, 44, 10, "flattenContentStyles"], [61, 32, 44, 30], [61, 35, 44, 33, "StyleSheet"], [61, 54, 44, 43], [61, 55, 44, 44, "flatten"], [61, 62, 44, 51], [61, 63, 44, 52, "contentStyle"], [61, 75, 44, 64], [61, 76, 44, 65], [62, 6, 45, 4, "internalScreenStyle"], [62, 25, 45, 23], [62, 28, 45, 26], [63, 8, 46, 6, "backgroundColor"], [63, 23, 46, 21], [63, 25, 46, 23, "flattenContentStyles"], [63, 45, 46, 43], [63, 47, 46, 45, "backgroundColor"], [64, 6, 47, 4], [64, 7, 47, 5], [65, 4, 48, 2], [66, 4, 49, 2], [66, 11, 49, 9], [66, 24, 49, 22, "_ReactNativeCSSInterop"], [66, 46, 49, 22], [66, 47, 49, 22, "createInteropElement"], [66, 67, 49, 22], [66, 68, 49, 42, "Screen"], [66, 83, 49, 48], [66, 85, 49, 50, "_extends"], [66, 93, 49, 58], [66, 94, 49, 59], [67, 6, 50, 4, "ref"], [67, 9, 50, 7], [67, 11, 50, 9, "node"], [67, 15, 50, 13], [67, 19, 50, 17], [68, 8, 51, 6, "currentScreenRef"], [68, 24, 51, 22], [68, 25, 51, 23, "current"], [68, 32, 51, 30], [68, 35, 51, 33, "node"], [68, 39, 51, 37], [69, 8, 52, 6], [69, 12, 52, 10, "screenRefs"], [69, 22, 52, 20], [69, 27, 52, 25], [69, 31, 52, 29], [69, 33, 52, 31], [70, 10, 53, 8, "console"], [70, 17, 53, 15], [70, 18, 53, 16, "warn"], [70, 22, 53, 20], [70, 23, 53, 21], [70, 121, 53, 119], [70, 122, 53, 120], [71, 10, 54, 8], [72, 8, 55, 6], [73, 8, 56, 6], [73, 14, 56, 12, "currentRefs"], [73, 25, 56, 23], [73, 28, 56, 26, "screenRefs"], [73, 38, 56, 36], [73, 39, 56, 37, "current"], [73, 46, 56, 44], [74, 8, 57, 6], [74, 12, 57, 10, "node"], [74, 16, 57, 14], [74, 21, 57, 19], [74, 25, 57, 23], [74, 27, 57, 25], [75, 10, 58, 8], [76, 10, 59, 8], [76, 17, 59, 15, "currentRefs"], [76, 28, 59, 26], [76, 29, 59, 27, "screenId"], [76, 37, 59, 35], [76, 38, 59, 36], [77, 8, 60, 6], [77, 9, 60, 7], [77, 15, 60, 13], [78, 10, 61, 8, "currentRefs"], [78, 21, 61, 19], [78, 22, 61, 20, "screenId"], [78, 30, 61, 28], [78, 31, 61, 29], [78, 34, 61, 32], [79, 12, 62, 10, "current"], [79, 19, 62, 17], [79, 21, 62, 19, "node"], [80, 10, 63, 8], [80, 11, 63, 9], [81, 8, 64, 6], [82, 6, 65, 4], [82, 7, 65, 5], [83, 6, 66, 4, "enabled"], [83, 13, 66, 11], [83, 15, 66, 13], [83, 19, 66, 17], [84, 6, 67, 4, "isNativeStack"], [84, 19, 67, 17], [84, 21, 67, 19], [84, 25, 67, 23], [85, 6, 68, 4, "activityState"], [85, 19, 68, 17], [85, 21, 68, 19, "activityState"], [85, 34, 68, 32], [86, 6, 69, 4, "shouldFreeze"], [86, 18, 69, 16], [86, 20, 69, 18, "shouldFreeze"], [86, 32, 69, 30], [87, 6, 70, 4, "stackPresentation"], [87, 23, 70, 21], [87, 25, 70, 23, "stackPresentation"], [87, 42, 70, 40], [88, 6, 71, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [88, 20, 71, 18], [88, 22, 71, 20, "headerConfig"], [88, 34, 71, 32], [88, 36, 71, 34, "largeTitle"], [88, 46, 71, 44], [88, 50, 71, 48], [88, 55, 71, 53], [89, 6, 72, 4, "sheetAllowedDetents"], [89, 25, 72, 23], [89, 27, 72, 25, "sheetAllowedDetents"], [89, 46, 72, 44], [90, 6, 73, 4, "style"], [90, 11, 73, 9], [90, 13, 73, 11], [90, 14, 73, 12, "style"], [90, 19, 73, 17], [90, 21, 73, 19, "internalScreenStyle"], [90, 40, 73, 38], [91, 4, 74, 2], [91, 5, 74, 3], [91, 7, 74, 5, "rest"], [91, 11, 74, 9], [91, 12, 74, 10], [91, 14, 74, 12, "isHeaderInModal"], [91, 29, 74, 27], [91, 32, 74, 30], [91, 45, 74, 43, "_ReactNativeCSSInterop"], [91, 67, 74, 43], [91, 68, 74, 43, "createInteropElement"], [91, 88, 74, 43], [91, 89, 74, 63, "ScreenStack"], [91, 109, 74, 74], [91, 111, 74, 76], [92, 6, 75, 4, "style"], [92, 11, 75, 9], [92, 13, 75, 11, "styles"], [92, 19, 75, 17], [92, 20, 75, 18, "container"], [93, 4, 76, 2], [93, 5, 76, 3], [93, 7, 76, 5], [93, 20, 76, 18, "_ReactNativeCSSInterop"], [93, 42, 76, 18], [93, 43, 76, 18, "createInteropElement"], [93, 63, 76, 18], [93, 64, 76, 38, "Screen"], [93, 79, 76, 44], [93, 81, 76, 46], [94, 6, 77, 4, "enabled"], [94, 13, 77, 11], [94, 15, 77, 13], [94, 19, 77, 17], [95, 6, 78, 4, "isNativeStack"], [95, 19, 78, 17], [95, 21, 78, 19], [95, 25, 78, 23], [96, 6, 79, 4, "activityState"], [96, 19, 79, 17], [96, 21, 79, 19, "activityState"], [96, 34, 79, 32], [97, 6, 80, 4, "shouldFreeze"], [97, 18, 80, 16], [97, 20, 80, 18, "shouldFreeze"], [97, 32, 80, 30], [98, 6, 81, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [98, 20, 81, 18], [98, 22, 81, 20, "headerConfig"], [98, 34, 81, 32], [98, 36, 81, 34, "largeTitle"], [98, 46, 81, 44], [98, 50, 81, 48], [98, 55, 81, 53], [99, 6, 82, 4, "style"], [99, 11, 82, 9], [99, 13, 82, 11, "StyleSheet"], [99, 32, 82, 21], [99, 33, 82, 22, "absoluteFill"], [100, 4, 83, 2], [100, 5, 83, 3], [100, 7, 83, 5, "content"], [100, 14, 83, 12], [100, 15, 83, 13], [100, 16, 83, 14], [100, 19, 83, 17, "content"], [100, 26, 83, 24], [100, 27, 83, 25], [101, 2, 84, 0], [102, 2, 84, 1], [102, 6, 84, 1, "_default"], [102, 14, 84, 1], [102, 17, 84, 1, "exports"], [102, 24, 84, 1], [102, 25, 84, 1, "default"], [102, 32, 84, 1], [102, 35, 85, 15], [102, 48, 85, 28, "React"], [102, 53, 85, 33], [102, 54, 85, 34, "forwardRef"], [102, 64, 85, 44], [102, 65, 85, 45, "ScreenStackItem"], [102, 80, 85, 60], [102, 81, 85, 61], [103, 2, 86, 0], [103, 8, 86, 6, "styles"], [103, 14, 86, 12], [103, 17, 86, 15, "StyleSheet"], [103, 36, 86, 25], [103, 37, 86, 26, "create"], [103, 43, 86, 32], [103, 44, 86, 33], [104, 4, 87, 2, "container"], [104, 13, 87, 11], [104, 15, 87, 13], [105, 6, 88, 4, "flex"], [105, 10, 88, 8], [105, 12, 88, 10], [106, 4, 89, 2], [106, 5, 89, 3], [107, 4, 90, 2, "absolute"], [107, 12, 90, 10], [107, 14, 90, 12], [108, 6, 91, 4, "position"], [108, 14, 91, 12], [108, 16, 91, 14], [108, 26, 91, 24], [109, 6, 92, 4, "top"], [109, 9, 92, 7], [109, 11, 92, 9], [109, 12, 92, 10], [110, 6, 93, 4, "start"], [110, 11, 93, 9], [110, 13, 93, 11], [110, 14, 93, 12], [111, 6, 94, 4, "end"], [111, 9, 94, 7], [111, 11, 94, 9], [112, 4, 95, 2], [113, 2, 96, 0], [113, 3, 96, 1], [113, 4, 96, 2], [114, 0, 96, 3], [114, 3]], "functionMap": {"names": ["_extends", "<anonymous>", "<global>", "ScreenStackItem", "React.useImperativeHandle$argument_1", "React.useEffect$argument_0", "_extends$argument_0.ref"], "mappings": "AAA,+EC,iKD,oCE;ACU;iCCgB,8BD;kBEG;GFG;SGiB;KHe;CDmB"}}, "type": "js/module"}]}