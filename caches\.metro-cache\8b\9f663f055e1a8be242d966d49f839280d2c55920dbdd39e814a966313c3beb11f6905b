{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 199}, "end": {"line": 10, "column": 31, "index": 230}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../mergeRefs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 231}, "end": {"line": 11, "column": 37, "index": 268}}], "key": "i+GmHg+UO5diLxu/OBImkphmzQw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = useMergeRefs;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _mergeRefs = _interopRequireDefault(require(_dependencyMap[2], \"../mergeRefs\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  function useMergeRefs() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return React.useMemo(() => (0, _mergeRefs.default)(...args),\n    // eslint-disable-next-line\n    [...args]);\n  }\n});", "lineCount": 27, "map": [[7, 2, 10, 0], [7, 6, 10, 0, "React"], [7, 11, 10, 0], [7, 14, 10, 0, "_interopRequireWildcard"], [7, 37, 10, 0], [7, 38, 10, 0, "require"], [7, 45, 10, 0], [7, 46, 10, 0, "_dependencyMap"], [7, 60, 10, 0], [8, 2, 11, 0], [8, 6, 11, 0, "_mergeRefs"], [8, 16, 11, 0], [8, 19, 11, 0, "_interopRequireDefault"], [8, 41, 11, 0], [8, 42, 11, 0, "require"], [8, 49, 11, 0], [8, 50, 11, 0, "_dependencyMap"], [8, 64, 11, 0], [9, 2, 11, 37], [9, 11, 11, 37, "_interopRequireWildcard"], [9, 35, 11, 37, "e"], [9, 36, 11, 37], [9, 38, 11, 37, "t"], [9, 39, 11, 37], [9, 68, 11, 37, "WeakMap"], [9, 75, 11, 37], [9, 81, 11, 37, "r"], [9, 82, 11, 37], [9, 89, 11, 37, "WeakMap"], [9, 96, 11, 37], [9, 100, 11, 37, "n"], [9, 101, 11, 37], [9, 108, 11, 37, "WeakMap"], [9, 115, 11, 37], [9, 127, 11, 37, "_interopRequireWildcard"], [9, 150, 11, 37], [9, 162, 11, 37, "_interopRequireWildcard"], [9, 163, 11, 37, "e"], [9, 164, 11, 37], [9, 166, 11, 37, "t"], [9, 167, 11, 37], [9, 176, 11, 37, "t"], [9, 177, 11, 37], [9, 181, 11, 37, "e"], [9, 182, 11, 37], [9, 186, 11, 37, "e"], [9, 187, 11, 37], [9, 188, 11, 37, "__esModule"], [9, 198, 11, 37], [9, 207, 11, 37, "e"], [9, 208, 11, 37], [9, 214, 11, 37, "o"], [9, 215, 11, 37], [9, 217, 11, 37, "i"], [9, 218, 11, 37], [9, 220, 11, 37, "f"], [9, 221, 11, 37], [9, 226, 11, 37, "__proto__"], [9, 235, 11, 37], [9, 243, 11, 37, "default"], [9, 250, 11, 37], [9, 252, 11, 37, "e"], [9, 253, 11, 37], [9, 270, 11, 37, "e"], [9, 271, 11, 37], [9, 294, 11, 37, "e"], [9, 295, 11, 37], [9, 320, 11, 37, "e"], [9, 321, 11, 37], [9, 330, 11, 37, "f"], [9, 331, 11, 37], [9, 337, 11, 37, "o"], [9, 338, 11, 37], [9, 341, 11, 37, "t"], [9, 342, 11, 37], [9, 345, 11, 37, "n"], [9, 346, 11, 37], [9, 349, 11, 37, "r"], [9, 350, 11, 37], [9, 358, 11, 37, "o"], [9, 359, 11, 37], [9, 360, 11, 37, "has"], [9, 363, 11, 37], [9, 364, 11, 37, "e"], [9, 365, 11, 37], [9, 375, 11, 37, "o"], [9, 376, 11, 37], [9, 377, 11, 37, "get"], [9, 380, 11, 37], [9, 381, 11, 37, "e"], [9, 382, 11, 37], [9, 385, 11, 37, "o"], [9, 386, 11, 37], [9, 387, 11, 37, "set"], [9, 390, 11, 37], [9, 391, 11, 37, "e"], [9, 392, 11, 37], [9, 394, 11, 37, "f"], [9, 395, 11, 37], [9, 411, 11, 37, "t"], [9, 412, 11, 37], [9, 416, 11, 37, "e"], [9, 417, 11, 37], [9, 433, 11, 37, "t"], [9, 434, 11, 37], [9, 441, 11, 37, "hasOwnProperty"], [9, 455, 11, 37], [9, 456, 11, 37, "call"], [9, 460, 11, 37], [9, 461, 11, 37, "e"], [9, 462, 11, 37], [9, 464, 11, 37, "t"], [9, 465, 11, 37], [9, 472, 11, 37, "i"], [9, 473, 11, 37], [9, 477, 11, 37, "o"], [9, 478, 11, 37], [9, 481, 11, 37, "Object"], [9, 487, 11, 37], [9, 488, 11, 37, "defineProperty"], [9, 502, 11, 37], [9, 507, 11, 37, "Object"], [9, 513, 11, 37], [9, 514, 11, 37, "getOwnPropertyDescriptor"], [9, 538, 11, 37], [9, 539, 11, 37, "e"], [9, 540, 11, 37], [9, 542, 11, 37, "t"], [9, 543, 11, 37], [9, 550, 11, 37, "i"], [9, 551, 11, 37], [9, 552, 11, 37, "get"], [9, 555, 11, 37], [9, 559, 11, 37, "i"], [9, 560, 11, 37], [9, 561, 11, 37, "set"], [9, 564, 11, 37], [9, 568, 11, 37, "o"], [9, 569, 11, 37], [9, 570, 11, 37, "f"], [9, 571, 11, 37], [9, 573, 11, 37, "t"], [9, 574, 11, 37], [9, 576, 11, 37, "i"], [9, 577, 11, 37], [9, 581, 11, 37, "f"], [9, 582, 11, 37], [9, 583, 11, 37, "t"], [9, 584, 11, 37], [9, 588, 11, 37, "e"], [9, 589, 11, 37], [9, 590, 11, 37, "t"], [9, 591, 11, 37], [9, 602, 11, 37, "f"], [9, 603, 11, 37], [9, 608, 11, 37, "e"], [9, 609, 11, 37], [9, 611, 11, 37, "t"], [9, 612, 11, 37], [10, 2, 1, 0], [11, 0, 2, 0], [12, 0, 3, 0], [13, 0, 4, 0], [14, 0, 5, 0], [15, 0, 6, 0], [16, 0, 7, 0], [17, 0, 8, 0], [19, 2, 12, 15], [19, 11, 12, 24, "useMergeRefs"], [19, 23, 12, 36, "useMergeRefs"], [19, 24, 12, 36], [19, 26, 12, 39], [20, 4, 13, 2], [20, 9, 13, 7], [20, 13, 13, 11, "_len"], [20, 17, 13, 15], [20, 20, 13, 18, "arguments"], [20, 29, 13, 27], [20, 30, 13, 28, "length"], [20, 36, 13, 34], [20, 38, 13, 36, "args"], [20, 42, 13, 40], [20, 45, 13, 43], [20, 49, 13, 47, "Array"], [20, 54, 13, 52], [20, 55, 13, 53, "_len"], [20, 59, 13, 57], [20, 60, 13, 58], [20, 62, 13, 60, "_key"], [20, 66, 13, 64], [20, 69, 13, 67], [20, 70, 13, 68], [20, 72, 13, 70, "_key"], [20, 76, 13, 74], [20, 79, 13, 77, "_len"], [20, 83, 13, 81], [20, 85, 13, 83, "_key"], [20, 89, 13, 87], [20, 91, 13, 89], [20, 93, 13, 91], [21, 6, 14, 4, "args"], [21, 10, 14, 8], [21, 11, 14, 9, "_key"], [21, 15, 14, 13], [21, 16, 14, 14], [21, 19, 14, 17, "arguments"], [21, 28, 14, 26], [21, 29, 14, 27, "_key"], [21, 33, 14, 31], [21, 34, 14, 32], [22, 4, 15, 2], [23, 4, 16, 2], [23, 11, 16, 9, "React"], [23, 16, 16, 14], [23, 17, 16, 15, "useMemo"], [23, 24, 16, 22], [23, 25, 16, 23], [23, 31, 16, 29], [23, 35, 16, 29, "mergeRefs"], [23, 53, 16, 38], [23, 55, 16, 39], [23, 58, 16, 42, "args"], [23, 62, 16, 46], [23, 63, 16, 47], [24, 4, 17, 2], [25, 4, 18, 2], [25, 5, 18, 3], [25, 8, 18, 6, "args"], [25, 12, 18, 10], [25, 13, 18, 11], [25, 14, 18, 12], [26, 2, 19, 0], [27, 0, 19, 1], [27, 3]], "functionMap": {"names": ["<global>", "useMergeRefs", "React.useMemo$argument_0"], "mappings": "AAA;eCW;uBCI,wBD"}}, "type": "js/module"}]}