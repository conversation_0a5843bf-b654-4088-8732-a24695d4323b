{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "./infoLog", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 32}}], "key": "PiNqERstAurHS3euL+PQR03s66E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = createPerformanceLogger;\n  exports.getCurrentTimestamp = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _infoLog = _interopRequireDefault(require(_dependencyMap[3], \"./infoLog\"));\n  var PRINT_TO_CONSOLE = false;\n  var getCurrentTimestamp = exports.getCurrentTimestamp = global.nativeQPLTimestamp ?? (() => global.performance.now());\n  var PerformanceLogger = /*#__PURE__*/function () {\n    function PerformanceLogger() {\n      (0, _classCallCheck2.default)(this, PerformanceLogger);\n      this._timespans = {};\n      this._extras = {};\n      this._points = {};\n      this._pointExtras = {};\n      this._closed = false;\n    }\n    return (0, _createClass2.default)(PerformanceLogger, [{\n      key: \"addTimespan\",\n      value: function addTimespan(key, startTime, endTime, startExtras, endExtras) {\n        if (this._closed) {\n          if (PRINT_TO_CONSOLE && __DEV__) {\n            (0, _infoLog.default)('PerformanceLogger: addTimespan - has closed ignoring: ', key);\n          }\n          return;\n        }\n        if (this._timespans[key]) {\n          if (PRINT_TO_CONSOLE && __DEV__) {\n            (0, _infoLog.default)('PerformanceLogger: Attempting to add a timespan that already exists ', key);\n          }\n          return;\n        }\n        this._timespans[key] = {\n          startTime,\n          endTime,\n          totalTime: endTime - (startTime || 0),\n          startExtras,\n          endExtras\n        };\n      }\n    }, {\n      key: \"append\",\n      value: function append(performanceLogger) {\n        this._timespans = {\n          ...performanceLogger.getTimespans(),\n          ...this._timespans\n        };\n        this._extras = {\n          ...performanceLogger.getExtras(),\n          ...this._extras\n        };\n        this._points = {\n          ...performanceLogger.getPoints(),\n          ...this._points\n        };\n        this._pointExtras = {\n          ...performanceLogger.getPointExtras(),\n          ...this._pointExtras\n        };\n      }\n    }, {\n      key: \"clear\",\n      value: function clear() {\n        this._timespans = {};\n        this._extras = {};\n        this._points = {};\n        if (PRINT_TO_CONSOLE) {\n          (0, _infoLog.default)('PerformanceLogger.js', 'clear');\n        }\n      }\n    }, {\n      key: \"clearCompleted\",\n      value: function clearCompleted() {\n        for (var _key in this._timespans) {\n          if (this._timespans[_key]?.totalTime != null) {\n            delete this._timespans[_key];\n          }\n        }\n        this._extras = {};\n        this._points = {};\n        if (PRINT_TO_CONSOLE) {\n          (0, _infoLog.default)('PerformanceLogger.js', 'clearCompleted');\n        }\n      }\n    }, {\n      key: \"close\",\n      value: function close() {\n        this._closed = true;\n      }\n    }, {\n      key: \"currentTimestamp\",\n      value: function currentTimestamp() {\n        return getCurrentTimestamp();\n      }\n    }, {\n      key: \"getExtras\",\n      value: function getExtras() {\n        return this._extras;\n      }\n    }, {\n      key: \"getPoints\",\n      value: function getPoints() {\n        return this._points;\n      }\n    }, {\n      key: \"getPointExtras\",\n      value: function getPointExtras() {\n        return this._pointExtras;\n      }\n    }, {\n      key: \"getTimespans\",\n      value: function getTimespans() {\n        return this._timespans;\n      }\n    }, {\n      key: \"hasTimespan\",\n      value: function hasTimespan(key) {\n        return !!this._timespans[key];\n      }\n    }, {\n      key: \"isClosed\",\n      value: function isClosed() {\n        return this._closed;\n      }\n    }, {\n      key: \"logEverything\",\n      value: function logEverything() {\n        if (PRINT_TO_CONSOLE) {\n          for (var _key2 in this._timespans) {\n            if (this._timespans[_key2]?.totalTime != null) {\n              (0, _infoLog.default)(_key2 + ': ' + this._timespans[_key2].totalTime + 'ms');\n            }\n          }\n          (0, _infoLog.default)(this._extras);\n          for (var _key3 in this._points) {\n            if (this._points[_key3] != null) {\n              (0, _infoLog.default)(_key3 + ': ' + this._points[_key3] + 'ms');\n            }\n          }\n        }\n      }\n    }, {\n      key: \"markPoint\",\n      value: function markPoint(key) {\n        var timestamp = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : getCurrentTimestamp();\n        var extras = arguments.length > 2 ? arguments[2] : undefined;\n        if (this._closed) {\n          if (PRINT_TO_CONSOLE && __DEV__) {\n            (0, _infoLog.default)('PerformanceLogger: markPoint - has closed ignoring: ', key);\n          }\n          return;\n        }\n        if (this._points[key] != null) {\n          if (PRINT_TO_CONSOLE && __DEV__) {\n            (0, _infoLog.default)('PerformanceLogger: Attempting to mark a point that has been already logged ', key);\n          }\n          return;\n        }\n        this._points[key] = timestamp;\n        if (extras) {\n          this._pointExtras[key] = extras;\n        }\n      }\n    }, {\n      key: \"removeExtra\",\n      value: function removeExtra(key) {\n        var value = this._extras[key];\n        delete this._extras[key];\n        return value;\n      }\n    }, {\n      key: \"setExtra\",\n      value: function setExtra(key, value) {\n        if (this._closed) {\n          if (PRINT_TO_CONSOLE && __DEV__) {\n            (0, _infoLog.default)('PerformanceLogger: setExtra - has closed ignoring: ', key);\n          }\n          return;\n        }\n        if (this._extras.hasOwnProperty(key)) {\n          if (PRINT_TO_CONSOLE && __DEV__) {\n            (0, _infoLog.default)('PerformanceLogger: Attempting to set an extra that already exists ', {\n              key,\n              currentValue: this._extras[key],\n              attemptedValue: value\n            });\n          }\n          return;\n        }\n        this._extras[key] = value;\n      }\n    }, {\n      key: \"startTimespan\",\n      value: function startTimespan(key) {\n        var timestamp = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : getCurrentTimestamp();\n        var extras = arguments.length > 2 ? arguments[2] : undefined;\n        if (this._closed) {\n          if (PRINT_TO_CONSOLE && __DEV__) {\n            (0, _infoLog.default)('PerformanceLogger: startTimespan - has closed ignoring: ', key);\n          }\n          return;\n        }\n        if (this._timespans[key]) {\n          if (PRINT_TO_CONSOLE && __DEV__) {\n            (0, _infoLog.default)('PerformanceLogger: Attempting to start a timespan that already exists ', key);\n          }\n          return;\n        }\n        this._timespans[key] = {\n          startTime: timestamp,\n          startExtras: extras\n        };\n        if (PRINT_TO_CONSOLE) {\n          (0, _infoLog.default)('PerformanceLogger.js', 'start: ' + key);\n        }\n      }\n    }, {\n      key: \"stopTimespan\",\n      value: function stopTimespan(key) {\n        var timestamp = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : getCurrentTimestamp();\n        var extras = arguments.length > 2 ? arguments[2] : undefined;\n        if (this._closed) {\n          if (PRINT_TO_CONSOLE && __DEV__) {\n            (0, _infoLog.default)('PerformanceLogger: stopTimespan - has closed ignoring: ', key);\n          }\n          return;\n        }\n        var timespan = this._timespans[key];\n        if (!timespan || timespan.startTime == null) {\n          if (PRINT_TO_CONSOLE && __DEV__) {\n            (0, _infoLog.default)('PerformanceLogger: Attempting to end a timespan that has not started ', key);\n          }\n          return;\n        }\n        if (timespan.endTime != null) {\n          if (PRINT_TO_CONSOLE && __DEV__) {\n            (0, _infoLog.default)('PerformanceLogger: Attempting to end a timespan that has already ended ', key);\n          }\n          return;\n        }\n        timespan.endExtras = extras;\n        timespan.endTime = timestamp;\n        timespan.totalTime = timespan.endTime - (timespan.startTime || 0);\n        if (PRINT_TO_CONSOLE) {\n          (0, _infoLog.default)('PerformanceLogger.js', 'end: ' + key);\n        }\n      }\n    }]);\n  }();\n  function createPerformanceLogger() {\n    return new PerformanceLogger();\n  }\n});", "lineCount": 257, "map": [[10, 2, 18, 0], [10, 6, 18, 0, "_infoLog"], [10, 14, 18, 0], [10, 17, 18, 0, "_interopRequireDefault"], [10, 39, 18, 0], [10, 40, 18, 0, "require"], [10, 47, 18, 0], [10, 48, 18, 0, "_dependencyMap"], [10, 62, 18, 0], [11, 2, 20, 0], [11, 6, 20, 6, "PRINT_TO_CONSOLE"], [11, 22, 20, 29], [11, 25, 20, 32], [11, 30, 20, 37], [12, 2, 22, 7], [12, 6, 22, 13, "getCurrentTimestamp"], [12, 25, 22, 46], [12, 28, 22, 46, "exports"], [12, 35, 22, 46], [12, 36, 22, 46, "getCurrentTimestamp"], [12, 55, 22, 46], [12, 58, 23, 2, "global"], [12, 64, 23, 8], [12, 65, 23, 9, "nativeQPLTimestamp"], [12, 83, 23, 27], [12, 88, 23, 32], [12, 94, 23, 38, "global"], [12, 100, 23, 44], [12, 101, 23, 45, "performance"], [12, 112, 23, 56], [12, 113, 23, 57, "now"], [12, 116, 23, 60], [12, 117, 23, 61], [12, 118, 23, 62], [12, 119, 23, 63], [13, 2, 23, 64], [13, 6, 25, 6, "PerformanceLogger"], [13, 23, 25, 23], [14, 4, 25, 23], [14, 13, 25, 23, "PerformanceLogger"], [14, 31, 25, 23], [15, 6, 25, 23], [15, 10, 25, 23, "_classCallCheck2"], [15, 26, 25, 23], [15, 27, 25, 23, "default"], [15, 34, 25, 23], [15, 42, 25, 23, "PerformanceLogger"], [15, 59, 25, 23], [16, 6, 25, 23], [16, 11, 26, 2, "_timespans"], [16, 21, 26, 12], [16, 24, 26, 43], [16, 25, 26, 44], [16, 26, 26, 45], [17, 6, 26, 45], [17, 11, 27, 2, "_extras"], [17, 18, 27, 9], [17, 21, 27, 42], [17, 22, 27, 43], [17, 23, 27, 44], [18, 6, 27, 44], [18, 11, 28, 2, "_points"], [18, 18, 28, 9], [18, 21, 28, 38], [18, 22, 28, 39], [18, 23, 28, 40], [19, 6, 28, 40], [19, 11, 29, 2, "_pointExtras"], [19, 23, 29, 14], [19, 26, 29, 48], [19, 27, 29, 49], [19, 28, 29, 50], [20, 6, 29, 50], [20, 11, 30, 2, "_closed"], [20, 18, 30, 9], [20, 21, 30, 21], [20, 26, 30, 26], [21, 4, 30, 26], [22, 4, 30, 26], [22, 15, 30, 26, "_createClass2"], [22, 28, 30, 26], [22, 29, 30, 26, "default"], [22, 36, 30, 26], [22, 38, 30, 26, "PerformanceLogger"], [22, 55, 30, 26], [23, 6, 30, 26, "key"], [23, 9, 30, 26], [24, 6, 30, 26, "value"], [24, 11, 30, 26], [24, 13, 32, 2], [24, 22, 32, 2, "addTimespan"], [24, 33, 32, 13, "addTimespan"], [24, 34, 33, 4, "key"], [24, 37, 33, 15], [24, 39, 34, 4, "startTime"], [24, 48, 34, 21], [24, 50, 35, 4, "endTime"], [24, 57, 35, 19], [24, 59, 36, 4, "startExtras"], [24, 70, 36, 24], [24, 72, 37, 4, "endExtras"], [24, 81, 37, 22], [24, 83, 38, 4], [25, 8, 39, 4], [25, 12, 39, 8], [25, 16, 39, 12], [25, 17, 39, 13, "_closed"], [25, 24, 39, 20], [25, 26, 39, 22], [26, 10, 40, 6], [26, 14, 40, 10, "PRINT_TO_CONSOLE"], [26, 30, 40, 26], [26, 34, 40, 30, "__DEV__"], [26, 41, 40, 37], [26, 43, 40, 39], [27, 12, 41, 8], [27, 16, 41, 8, "infoLog"], [27, 32, 41, 15], [27, 34, 41, 16], [27, 90, 41, 72], [27, 92, 41, 74, "key"], [27, 95, 41, 77], [27, 96, 41, 78], [28, 10, 42, 6], [29, 10, 43, 6], [30, 8, 44, 4], [31, 8, 45, 4], [31, 12, 45, 8], [31, 16, 45, 12], [31, 17, 45, 13, "_timespans"], [31, 27, 45, 23], [31, 28, 45, 24, "key"], [31, 31, 45, 27], [31, 32, 45, 28], [31, 34, 45, 30], [32, 10, 46, 6], [32, 14, 46, 10, "PRINT_TO_CONSOLE"], [32, 30, 46, 26], [32, 34, 46, 30, "__DEV__"], [32, 41, 46, 37], [32, 43, 46, 39], [33, 12, 47, 8], [33, 16, 47, 8, "infoLog"], [33, 32, 47, 15], [33, 34, 48, 10], [33, 104, 48, 80], [33, 106, 49, 10, "key"], [33, 109, 50, 8], [33, 110, 50, 9], [34, 10, 51, 6], [35, 10, 52, 6], [36, 8, 53, 4], [37, 8, 55, 4], [37, 12, 55, 8], [37, 13, 55, 9, "_timespans"], [37, 23, 55, 19], [37, 24, 55, 20, "key"], [37, 27, 55, 23], [37, 28, 55, 24], [37, 31, 55, 27], [38, 10, 56, 6, "startTime"], [38, 19, 56, 15], [39, 10, 57, 6, "endTime"], [39, 17, 57, 13], [40, 10, 58, 6, "totalTime"], [40, 19, 58, 15], [40, 21, 58, 17, "endTime"], [40, 28, 58, 24], [40, 32, 58, 28, "startTime"], [40, 41, 58, 37], [40, 45, 58, 41], [40, 46, 58, 42], [40, 47, 58, 43], [41, 10, 59, 6, "startExtras"], [41, 21, 59, 17], [42, 10, 60, 6, "endExtras"], [43, 8, 61, 4], [43, 9, 61, 5], [44, 6, 62, 2], [45, 4, 62, 3], [46, 6, 62, 3, "key"], [46, 9, 62, 3], [47, 6, 62, 3, "value"], [47, 11, 62, 3], [47, 13, 64, 2], [47, 22, 64, 2, "append"], [47, 28, 64, 8, "append"], [47, 29, 64, 9, "performanceLogger"], [47, 46, 64, 46], [47, 48, 64, 48], [48, 8, 65, 4], [48, 12, 65, 8], [48, 13, 65, 9, "_timespans"], [48, 23, 65, 19], [48, 26, 65, 22], [49, 10, 66, 6], [49, 13, 66, 9, "performanceLogger"], [49, 30, 66, 26], [49, 31, 66, 27, "getTimespans"], [49, 43, 66, 39], [49, 44, 66, 40], [49, 45, 66, 41], [50, 10, 67, 6], [50, 13, 67, 9], [50, 17, 67, 13], [50, 18, 67, 14, "_timespans"], [51, 8, 68, 4], [51, 9, 68, 5], [52, 8, 69, 4], [52, 12, 69, 8], [52, 13, 69, 9, "_extras"], [52, 20, 69, 16], [52, 23, 69, 19], [53, 10, 69, 20], [53, 13, 69, 23, "performanceLogger"], [53, 30, 69, 40], [53, 31, 69, 41, "getExtras"], [53, 40, 69, 50], [53, 41, 69, 51], [53, 42, 69, 52], [54, 10, 69, 54], [54, 13, 69, 57], [54, 17, 69, 61], [54, 18, 69, 62, "_extras"], [55, 8, 69, 69], [55, 9, 69, 70], [56, 8, 70, 4], [56, 12, 70, 8], [56, 13, 70, 9, "_points"], [56, 20, 70, 16], [56, 23, 70, 19], [57, 10, 70, 20], [57, 13, 70, 23, "performanceLogger"], [57, 30, 70, 40], [57, 31, 70, 41, "getPoints"], [57, 40, 70, 50], [57, 41, 70, 51], [57, 42, 70, 52], [58, 10, 70, 54], [58, 13, 70, 57], [58, 17, 70, 61], [58, 18, 70, 62, "_points"], [59, 8, 70, 69], [59, 9, 70, 70], [60, 8, 71, 4], [60, 12, 71, 8], [60, 13, 71, 9, "_pointExtras"], [60, 25, 71, 21], [60, 28, 71, 24], [61, 10, 72, 6], [61, 13, 72, 9, "performanceLogger"], [61, 30, 72, 26], [61, 31, 72, 27, "getPointExtras"], [61, 45, 72, 41], [61, 46, 72, 42], [61, 47, 72, 43], [62, 10, 73, 6], [62, 13, 73, 9], [62, 17, 73, 13], [62, 18, 73, 14, "_pointExtras"], [63, 8, 74, 4], [63, 9, 74, 5], [64, 6, 75, 2], [65, 4, 75, 3], [66, 6, 75, 3, "key"], [66, 9, 75, 3], [67, 6, 75, 3, "value"], [67, 11, 75, 3], [67, 13, 77, 2], [67, 22, 77, 2, "clear"], [67, 27, 77, 7, "clear"], [67, 28, 77, 7], [67, 30, 77, 10], [68, 8, 78, 4], [68, 12, 78, 8], [68, 13, 78, 9, "_timespans"], [68, 23, 78, 19], [68, 26, 78, 22], [68, 27, 78, 23], [68, 28, 78, 24], [69, 8, 79, 4], [69, 12, 79, 8], [69, 13, 79, 9, "_extras"], [69, 20, 79, 16], [69, 23, 79, 19], [69, 24, 79, 20], [69, 25, 79, 21], [70, 8, 80, 4], [70, 12, 80, 8], [70, 13, 80, 9, "_points"], [70, 20, 80, 16], [70, 23, 80, 19], [70, 24, 80, 20], [70, 25, 80, 21], [71, 8, 81, 4], [71, 12, 81, 8, "PRINT_TO_CONSOLE"], [71, 28, 81, 24], [71, 30, 81, 26], [72, 10, 82, 6], [72, 14, 82, 6, "infoLog"], [72, 30, 82, 13], [72, 32, 82, 14], [72, 54, 82, 36], [72, 56, 82, 38], [72, 63, 82, 45], [72, 64, 82, 46], [73, 8, 83, 4], [74, 6, 84, 2], [75, 4, 84, 3], [76, 6, 84, 3, "key"], [76, 9, 84, 3], [77, 6, 84, 3, "value"], [77, 11, 84, 3], [77, 13, 86, 2], [77, 22, 86, 2, "clearCompleted"], [77, 36, 86, 16, "clearCompleted"], [77, 37, 86, 16], [77, 39, 86, 19], [78, 8, 87, 4], [78, 13, 87, 9], [78, 17, 87, 15, "key"], [78, 21, 87, 18], [78, 25, 87, 22], [78, 29, 87, 26], [78, 30, 87, 27, "_timespans"], [78, 40, 87, 37], [78, 42, 87, 39], [79, 10, 88, 6], [79, 14, 88, 10], [79, 18, 88, 14], [79, 19, 88, 15, "_timespans"], [79, 29, 88, 25], [79, 30, 88, 26, "key"], [79, 34, 88, 29], [79, 35, 88, 30], [79, 37, 88, 32, "totalTime"], [79, 46, 88, 41], [79, 50, 88, 45], [79, 54, 88, 49], [79, 56, 88, 51], [80, 12, 89, 8], [80, 19, 89, 15], [80, 23, 89, 19], [80, 24, 89, 20, "_timespans"], [80, 34, 89, 30], [80, 35, 89, 31, "key"], [80, 39, 89, 34], [80, 40, 89, 35], [81, 10, 90, 6], [82, 8, 91, 4], [83, 8, 92, 4], [83, 12, 92, 8], [83, 13, 92, 9, "_extras"], [83, 20, 92, 16], [83, 23, 92, 19], [83, 24, 92, 20], [83, 25, 92, 21], [84, 8, 93, 4], [84, 12, 93, 8], [84, 13, 93, 9, "_points"], [84, 20, 93, 16], [84, 23, 93, 19], [84, 24, 93, 20], [84, 25, 93, 21], [85, 8, 94, 4], [85, 12, 94, 8, "PRINT_TO_CONSOLE"], [85, 28, 94, 24], [85, 30, 94, 26], [86, 10, 95, 6], [86, 14, 95, 6, "infoLog"], [86, 30, 95, 13], [86, 32, 95, 14], [86, 54, 95, 36], [86, 56, 95, 38], [86, 72, 95, 54], [86, 73, 95, 55], [87, 8, 96, 4], [88, 6, 97, 2], [89, 4, 97, 3], [90, 6, 97, 3, "key"], [90, 9, 97, 3], [91, 6, 97, 3, "value"], [91, 11, 97, 3], [91, 13, 99, 2], [91, 22, 99, 2, "close"], [91, 27, 99, 7, "close"], [91, 28, 99, 7], [91, 30, 99, 10], [92, 8, 100, 4], [92, 12, 100, 8], [92, 13, 100, 9, "_closed"], [92, 20, 100, 16], [92, 23, 100, 19], [92, 27, 100, 23], [93, 6, 101, 2], [94, 4, 101, 3], [95, 6, 101, 3, "key"], [95, 9, 101, 3], [96, 6, 101, 3, "value"], [96, 11, 101, 3], [96, 13, 103, 2], [96, 22, 103, 2, "currentTimestamp"], [96, 38, 103, 18, "currentTimestamp"], [96, 39, 103, 18], [96, 41, 103, 29], [97, 8, 104, 4], [97, 15, 104, 11, "getCurrentTimestamp"], [97, 34, 104, 30], [97, 35, 104, 31], [97, 36, 104, 32], [98, 6, 105, 2], [99, 4, 105, 3], [100, 6, 105, 3, "key"], [100, 9, 105, 3], [101, 6, 105, 3, "value"], [101, 11, 105, 3], [101, 13, 107, 2], [101, 22, 107, 2, "getExtras"], [101, 31, 107, 11, "getExtras"], [101, 32, 107, 11], [101, 34, 107, 44], [102, 8, 108, 4], [102, 15, 108, 11], [102, 19, 108, 15], [102, 20, 108, 16, "_extras"], [102, 27, 108, 23], [103, 6, 109, 2], [104, 4, 109, 3], [105, 6, 109, 3, "key"], [105, 9, 109, 3], [106, 6, 109, 3, "value"], [106, 11, 109, 3], [106, 13, 111, 2], [106, 22, 111, 2, "getPoints"], [106, 31, 111, 11, "getPoints"], [106, 32, 111, 11], [106, 34, 111, 40], [107, 8, 112, 4], [107, 15, 112, 11], [107, 19, 112, 15], [107, 20, 112, 16, "_points"], [107, 27, 112, 23], [108, 6, 113, 2], [109, 4, 113, 3], [110, 6, 113, 3, "key"], [110, 9, 113, 3], [111, 6, 113, 3, "value"], [111, 11, 113, 3], [111, 13, 115, 2], [111, 22, 115, 2, "getPointExtras"], [111, 36, 115, 16, "getPointExtras"], [111, 37, 115, 16], [111, 39, 115, 45], [112, 8, 116, 4], [112, 15, 116, 11], [112, 19, 116, 15], [112, 20, 116, 16, "_pointExtras"], [112, 32, 116, 28], [113, 6, 117, 2], [114, 4, 117, 3], [115, 6, 117, 3, "key"], [115, 9, 117, 3], [116, 6, 117, 3, "value"], [116, 11, 117, 3], [116, 13, 119, 2], [116, 22, 119, 2, "getTimespans"], [116, 34, 119, 14, "getTimespans"], [116, 35, 119, 14], [116, 37, 119, 45], [117, 8, 120, 4], [117, 15, 120, 11], [117, 19, 120, 15], [117, 20, 120, 16, "_timespans"], [117, 30, 120, 26], [118, 6, 121, 2], [119, 4, 121, 3], [120, 6, 121, 3, "key"], [120, 9, 121, 3], [121, 6, 121, 3, "value"], [121, 11, 121, 3], [121, 13, 123, 2], [121, 22, 123, 2, "hasTimespan"], [121, 33, 123, 13, "hasTimespan"], [121, 34, 123, 14, "key"], [121, 37, 123, 25], [121, 39, 123, 36], [122, 8, 124, 4], [122, 15, 124, 11], [122, 16, 124, 12], [122, 17, 124, 13], [122, 21, 124, 17], [122, 22, 124, 18, "_timespans"], [122, 32, 124, 28], [122, 33, 124, 29, "key"], [122, 36, 124, 32], [122, 37, 124, 33], [123, 6, 125, 2], [124, 4, 125, 3], [125, 6, 125, 3, "key"], [125, 9, 125, 3], [126, 6, 125, 3, "value"], [126, 11, 125, 3], [126, 13, 127, 2], [126, 22, 127, 2, "isClosed"], [126, 30, 127, 10, "isClosed"], [126, 31, 127, 10], [126, 33, 127, 22], [127, 8, 128, 4], [127, 15, 128, 11], [127, 19, 128, 15], [127, 20, 128, 16, "_closed"], [127, 27, 128, 23], [128, 6, 129, 2], [129, 4, 129, 3], [130, 6, 129, 3, "key"], [130, 9, 129, 3], [131, 6, 129, 3, "value"], [131, 11, 129, 3], [131, 13, 131, 2], [131, 22, 131, 2, "logEverything"], [131, 35, 131, 15, "logEverything"], [131, 36, 131, 15], [131, 38, 131, 18], [132, 8, 132, 4], [132, 12, 132, 8, "PRINT_TO_CONSOLE"], [132, 28, 132, 24], [132, 30, 132, 26], [133, 10, 134, 6], [133, 15, 134, 11], [133, 19, 134, 17, "key"], [133, 24, 134, 20], [133, 28, 134, 24], [133, 32, 134, 28], [133, 33, 134, 29, "_timespans"], [133, 43, 134, 39], [133, 45, 134, 41], [134, 12, 135, 8], [134, 16, 135, 12], [134, 20, 135, 16], [134, 21, 135, 17, "_timespans"], [134, 31, 135, 27], [134, 32, 135, 28, "key"], [134, 37, 135, 31], [134, 38, 135, 32], [134, 40, 135, 34, "totalTime"], [134, 49, 135, 43], [134, 53, 135, 47], [134, 57, 135, 51], [134, 59, 135, 53], [135, 14, 136, 10], [135, 18, 136, 10, "infoLog"], [135, 34, 136, 17], [135, 36, 136, 18, "key"], [135, 41, 136, 21], [135, 44, 136, 24], [135, 48, 136, 28], [135, 51, 136, 31], [135, 55, 136, 35], [135, 56, 136, 36, "_timespans"], [135, 66, 136, 46], [135, 67, 136, 47, "key"], [135, 72, 136, 50], [135, 73, 136, 51], [135, 74, 136, 52, "totalTime"], [135, 83, 136, 61], [135, 86, 136, 64], [135, 90, 136, 68], [135, 91, 136, 69], [136, 12, 137, 8], [137, 10, 138, 6], [138, 10, 141, 6], [138, 14, 141, 6, "infoLog"], [138, 30, 141, 13], [138, 32, 141, 14], [138, 36, 141, 18], [138, 37, 141, 19, "_extras"], [138, 44, 141, 26], [138, 45, 141, 27], [139, 10, 144, 6], [139, 15, 144, 11], [139, 19, 144, 17, "key"], [139, 24, 144, 20], [139, 28, 144, 24], [139, 32, 144, 28], [139, 33, 144, 29, "_points"], [139, 40, 144, 36], [139, 42, 144, 38], [140, 12, 145, 8], [140, 16, 145, 12], [140, 20, 145, 16], [140, 21, 145, 17, "_points"], [140, 28, 145, 24], [140, 29, 145, 25, "key"], [140, 34, 145, 28], [140, 35, 145, 29], [140, 39, 145, 33], [140, 43, 145, 37], [140, 45, 145, 39], [141, 14, 146, 10], [141, 18, 146, 10, "infoLog"], [141, 34, 146, 17], [141, 36, 146, 18, "key"], [141, 41, 146, 21], [141, 44, 146, 24], [141, 48, 146, 28], [141, 51, 146, 31], [141, 55, 146, 35], [141, 56, 146, 36, "_points"], [141, 63, 146, 43], [141, 64, 146, 44, "key"], [141, 69, 146, 47], [141, 70, 146, 48], [141, 73, 146, 51], [141, 77, 146, 55], [141, 78, 146, 56], [142, 12, 147, 8], [143, 10, 148, 6], [144, 8, 149, 4], [145, 6, 150, 2], [146, 4, 150, 3], [147, 6, 150, 3, "key"], [147, 9, 150, 3], [148, 6, 150, 3, "value"], [148, 11, 150, 3], [148, 13, 152, 2], [148, 22, 152, 2, "markPoint"], [148, 31, 152, 11, "markPoint"], [148, 32, 153, 4, "key"], [148, 35, 153, 15], [148, 37, 156, 4], [149, 8, 156, 4], [149, 12, 154, 4, "timestamp"], [149, 21, 154, 22], [149, 24, 154, 22, "arguments"], [149, 33, 154, 22], [149, 34, 154, 22, "length"], [149, 40, 154, 22], [149, 48, 154, 22, "arguments"], [149, 57, 154, 22], [149, 65, 154, 22, "undefined"], [149, 74, 154, 22], [149, 77, 154, 22, "arguments"], [149, 86, 154, 22], [149, 92, 154, 25, "getCurrentTimestamp"], [149, 111, 154, 44], [149, 112, 154, 45], [149, 113, 154, 46], [150, 8, 154, 46], [150, 12, 155, 4, "extras"], [150, 18, 155, 19], [150, 21, 155, 19, "arguments"], [150, 30, 155, 19], [150, 31, 155, 19, "length"], [150, 37, 155, 19], [150, 44, 155, 19, "arguments"], [150, 53, 155, 19], [150, 59, 155, 19, "undefined"], [150, 68, 155, 19], [151, 8, 157, 4], [151, 12, 157, 8], [151, 16, 157, 12], [151, 17, 157, 13, "_closed"], [151, 24, 157, 20], [151, 26, 157, 22], [152, 10, 158, 6], [152, 14, 158, 10, "PRINT_TO_CONSOLE"], [152, 30, 158, 26], [152, 34, 158, 30, "__DEV__"], [152, 41, 158, 37], [152, 43, 158, 39], [153, 12, 159, 8], [153, 16, 159, 8, "infoLog"], [153, 32, 159, 15], [153, 34, 159, 16], [153, 88, 159, 70], [153, 90, 159, 72, "key"], [153, 93, 159, 75], [153, 94, 159, 76], [154, 10, 160, 6], [155, 10, 161, 6], [156, 8, 162, 4], [157, 8, 163, 4], [157, 12, 163, 8], [157, 16, 163, 12], [157, 17, 163, 13, "_points"], [157, 24, 163, 20], [157, 25, 163, 21, "key"], [157, 28, 163, 24], [157, 29, 163, 25], [157, 33, 163, 29], [157, 37, 163, 33], [157, 39, 163, 35], [158, 10, 164, 6], [158, 14, 164, 10, "PRINT_TO_CONSOLE"], [158, 30, 164, 26], [158, 34, 164, 30, "__DEV__"], [158, 41, 164, 37], [158, 43, 164, 39], [159, 12, 165, 8], [159, 16, 165, 8, "infoLog"], [159, 32, 165, 15], [159, 34, 166, 10], [159, 111, 166, 87], [159, 113, 167, 10, "key"], [159, 116, 168, 8], [159, 117, 168, 9], [160, 10, 169, 6], [161, 10, 170, 6], [162, 8, 171, 4], [163, 8, 172, 4], [163, 12, 172, 8], [163, 13, 172, 9, "_points"], [163, 20, 172, 16], [163, 21, 172, 17, "key"], [163, 24, 172, 20], [163, 25, 172, 21], [163, 28, 172, 24, "timestamp"], [163, 37, 172, 33], [164, 8, 173, 4], [164, 12, 173, 8, "extras"], [164, 18, 173, 14], [164, 20, 173, 16], [165, 10, 174, 6], [165, 14, 174, 10], [165, 15, 174, 11, "_pointExtras"], [165, 27, 174, 23], [165, 28, 174, 24, "key"], [165, 31, 174, 27], [165, 32, 174, 28], [165, 35, 174, 31, "extras"], [165, 41, 174, 37], [166, 8, 175, 4], [167, 6, 176, 2], [168, 4, 176, 3], [169, 6, 176, 3, "key"], [169, 9, 176, 3], [170, 6, 176, 3, "value"], [170, 11, 176, 3], [170, 13, 178, 2], [170, 22, 178, 2, "removeExtra"], [170, 33, 178, 13, "removeExtra"], [170, 34, 178, 14, "key"], [170, 37, 178, 25], [170, 39, 178, 40], [171, 8, 179, 4], [171, 12, 179, 10, "value"], [171, 17, 179, 15], [171, 20, 179, 18], [171, 24, 179, 22], [171, 25, 179, 23, "_extras"], [171, 32, 179, 30], [171, 33, 179, 31, "key"], [171, 36, 179, 34], [171, 37, 179, 35], [172, 8, 180, 4], [172, 15, 180, 11], [172, 19, 180, 15], [172, 20, 180, 16, "_extras"], [172, 27, 180, 23], [172, 28, 180, 24, "key"], [172, 31, 180, 27], [172, 32, 180, 28], [173, 8, 181, 4], [173, 15, 181, 11, "value"], [173, 20, 181, 16], [174, 6, 182, 2], [175, 4, 182, 3], [176, 6, 182, 3, "key"], [176, 9, 182, 3], [177, 6, 182, 3, "value"], [177, 11, 182, 3], [177, 13, 184, 2], [177, 22, 184, 2, "setExtra"], [177, 30, 184, 10, "setExtra"], [177, 31, 184, 11, "key"], [177, 34, 184, 22], [177, 36, 184, 24, "value"], [177, 41, 184, 41], [177, 43, 184, 43], [178, 8, 185, 4], [178, 12, 185, 8], [178, 16, 185, 12], [178, 17, 185, 13, "_closed"], [178, 24, 185, 20], [178, 26, 185, 22], [179, 10, 186, 6], [179, 14, 186, 10, "PRINT_TO_CONSOLE"], [179, 30, 186, 26], [179, 34, 186, 30, "__DEV__"], [179, 41, 186, 37], [179, 43, 186, 39], [180, 12, 187, 8], [180, 16, 187, 8, "infoLog"], [180, 32, 187, 15], [180, 34, 187, 16], [180, 87, 187, 69], [180, 89, 187, 71, "key"], [180, 92, 187, 74], [180, 93, 187, 75], [181, 10, 188, 6], [182, 10, 189, 6], [183, 8, 190, 4], [184, 8, 192, 4], [184, 12, 192, 8], [184, 16, 192, 12], [184, 17, 192, 13, "_extras"], [184, 24, 192, 20], [184, 25, 192, 21, "hasOwnProperty"], [184, 39, 192, 35], [184, 40, 192, 36, "key"], [184, 43, 192, 39], [184, 44, 192, 40], [184, 46, 192, 42], [185, 10, 193, 6], [185, 14, 193, 10, "PRINT_TO_CONSOLE"], [185, 30, 193, 26], [185, 34, 193, 30, "__DEV__"], [185, 41, 193, 37], [185, 43, 193, 39], [186, 12, 194, 8], [186, 16, 194, 8, "infoLog"], [186, 32, 194, 15], [186, 34, 195, 10], [186, 102, 195, 78], [186, 104, 196, 10], [187, 14, 196, 11, "key"], [187, 17, 196, 14], [188, 14, 196, 16, "currentValue"], [188, 26, 196, 28], [188, 28, 196, 30], [188, 32, 196, 34], [188, 33, 196, 35, "_extras"], [188, 40, 196, 42], [188, 41, 196, 43, "key"], [188, 44, 196, 46], [188, 45, 196, 47], [189, 14, 196, 49, "attemptedValue"], [189, 28, 196, 63], [189, 30, 196, 65, "value"], [190, 12, 196, 70], [190, 13, 197, 8], [190, 14, 197, 9], [191, 10, 198, 6], [192, 10, 199, 6], [193, 8, 200, 4], [194, 8, 201, 4], [194, 12, 201, 8], [194, 13, 201, 9, "_extras"], [194, 20, 201, 16], [194, 21, 201, 17, "key"], [194, 24, 201, 20], [194, 25, 201, 21], [194, 28, 201, 24, "value"], [194, 33, 201, 29], [195, 6, 202, 2], [196, 4, 202, 3], [197, 6, 202, 3, "key"], [197, 9, 202, 3], [198, 6, 202, 3, "value"], [198, 11, 202, 3], [198, 13, 204, 2], [198, 22, 204, 2, "startTimespan"], [198, 35, 204, 15, "startTimespan"], [198, 36, 205, 4, "key"], [198, 39, 205, 15], [198, 41, 208, 4], [199, 8, 208, 4], [199, 12, 206, 4, "timestamp"], [199, 21, 206, 22], [199, 24, 206, 22, "arguments"], [199, 33, 206, 22], [199, 34, 206, 22, "length"], [199, 40, 206, 22], [199, 48, 206, 22, "arguments"], [199, 57, 206, 22], [199, 65, 206, 22, "undefined"], [199, 74, 206, 22], [199, 77, 206, 22, "arguments"], [199, 86, 206, 22], [199, 92, 206, 25, "getCurrentTimestamp"], [199, 111, 206, 44], [199, 112, 206, 45], [199, 113, 206, 46], [200, 8, 206, 46], [200, 12, 207, 4, "extras"], [200, 18, 207, 19], [200, 21, 207, 19, "arguments"], [200, 30, 207, 19], [200, 31, 207, 19, "length"], [200, 37, 207, 19], [200, 44, 207, 19, "arguments"], [200, 53, 207, 19], [200, 59, 207, 19, "undefined"], [200, 68, 207, 19], [201, 8, 209, 4], [201, 12, 209, 8], [201, 16, 209, 12], [201, 17, 209, 13, "_closed"], [201, 24, 209, 20], [201, 26, 209, 22], [202, 10, 210, 6], [202, 14, 210, 10, "PRINT_TO_CONSOLE"], [202, 30, 210, 26], [202, 34, 210, 30, "__DEV__"], [202, 41, 210, 37], [202, 43, 210, 39], [203, 12, 211, 8], [203, 16, 211, 8, "infoLog"], [203, 32, 211, 15], [203, 34, 212, 10], [203, 92, 212, 68], [203, 94, 213, 10, "key"], [203, 97, 214, 8], [203, 98, 214, 9], [204, 10, 215, 6], [205, 10, 216, 6], [206, 8, 217, 4], [207, 8, 219, 4], [207, 12, 219, 8], [207, 16, 219, 12], [207, 17, 219, 13, "_timespans"], [207, 27, 219, 23], [207, 28, 219, 24, "key"], [207, 31, 219, 27], [207, 32, 219, 28], [207, 34, 219, 30], [208, 10, 220, 6], [208, 14, 220, 10, "PRINT_TO_CONSOLE"], [208, 30, 220, 26], [208, 34, 220, 30, "__DEV__"], [208, 41, 220, 37], [208, 43, 220, 39], [209, 12, 221, 8], [209, 16, 221, 8, "infoLog"], [209, 32, 221, 15], [209, 34, 222, 10], [209, 106, 222, 82], [209, 108, 223, 10, "key"], [209, 111, 224, 8], [209, 112, 224, 9], [210, 10, 225, 6], [211, 10, 226, 6], [212, 8, 227, 4], [213, 8, 229, 4], [213, 12, 229, 8], [213, 13, 229, 9, "_timespans"], [213, 23, 229, 19], [213, 24, 229, 20, "key"], [213, 27, 229, 23], [213, 28, 229, 24], [213, 31, 229, 27], [214, 10, 230, 6, "startTime"], [214, 19, 230, 15], [214, 21, 230, 17, "timestamp"], [214, 30, 230, 26], [215, 10, 231, 6, "startExtras"], [215, 21, 231, 17], [215, 23, 231, 19, "extras"], [216, 8, 232, 4], [216, 9, 232, 5], [217, 8, 233, 4], [217, 12, 233, 8, "PRINT_TO_CONSOLE"], [217, 28, 233, 24], [217, 30, 233, 26], [218, 10, 234, 6], [218, 14, 234, 6, "infoLog"], [218, 30, 234, 13], [218, 32, 234, 14], [218, 54, 234, 36], [218, 56, 234, 38], [218, 65, 234, 47], [218, 68, 234, 50, "key"], [218, 71, 234, 53], [218, 72, 234, 54], [219, 8, 235, 4], [220, 6, 236, 2], [221, 4, 236, 3], [222, 6, 236, 3, "key"], [222, 9, 236, 3], [223, 6, 236, 3, "value"], [223, 11, 236, 3], [223, 13, 238, 2], [223, 22, 238, 2, "stopTimespan"], [223, 34, 238, 14, "stopTimespan"], [223, 35, 239, 4, "key"], [223, 38, 239, 15], [223, 40, 242, 4], [224, 8, 242, 4], [224, 12, 240, 4, "timestamp"], [224, 21, 240, 22], [224, 24, 240, 22, "arguments"], [224, 33, 240, 22], [224, 34, 240, 22, "length"], [224, 40, 240, 22], [224, 48, 240, 22, "arguments"], [224, 57, 240, 22], [224, 65, 240, 22, "undefined"], [224, 74, 240, 22], [224, 77, 240, 22, "arguments"], [224, 86, 240, 22], [224, 92, 240, 25, "getCurrentTimestamp"], [224, 111, 240, 44], [224, 112, 240, 45], [224, 113, 240, 46], [225, 8, 240, 46], [225, 12, 241, 4, "extras"], [225, 18, 241, 19], [225, 21, 241, 19, "arguments"], [225, 30, 241, 19], [225, 31, 241, 19, "length"], [225, 37, 241, 19], [225, 44, 241, 19, "arguments"], [225, 53, 241, 19], [225, 59, 241, 19, "undefined"], [225, 68, 241, 19], [226, 8, 243, 4], [226, 12, 243, 8], [226, 16, 243, 12], [226, 17, 243, 13, "_closed"], [226, 24, 243, 20], [226, 26, 243, 22], [227, 10, 244, 6], [227, 14, 244, 10, "PRINT_TO_CONSOLE"], [227, 30, 244, 26], [227, 34, 244, 30, "__DEV__"], [227, 41, 244, 37], [227, 43, 244, 39], [228, 12, 245, 8], [228, 16, 245, 8, "infoLog"], [228, 32, 245, 15], [228, 34, 245, 16], [228, 91, 245, 73], [228, 93, 245, 75, "key"], [228, 96, 245, 78], [228, 97, 245, 79], [229, 10, 246, 6], [230, 10, 247, 6], [231, 8, 248, 4], [232, 8, 250, 4], [232, 12, 250, 10, "timespan"], [232, 20, 250, 18], [232, 23, 250, 21], [232, 27, 250, 25], [232, 28, 250, 26, "_timespans"], [232, 38, 250, 36], [232, 39, 250, 37, "key"], [232, 42, 250, 40], [232, 43, 250, 41], [233, 8, 251, 4], [233, 12, 251, 8], [233, 13, 251, 9, "timespan"], [233, 21, 251, 17], [233, 25, 251, 21, "timespan"], [233, 33, 251, 29], [233, 34, 251, 30, "startTime"], [233, 43, 251, 39], [233, 47, 251, 43], [233, 51, 251, 47], [233, 53, 251, 49], [234, 10, 252, 6], [234, 14, 252, 10, "PRINT_TO_CONSOLE"], [234, 30, 252, 26], [234, 34, 252, 30, "__DEV__"], [234, 41, 252, 37], [234, 43, 252, 39], [235, 12, 253, 8], [235, 16, 253, 8, "infoLog"], [235, 32, 253, 15], [235, 34, 254, 10], [235, 105, 254, 81], [235, 107, 255, 10, "key"], [235, 110, 256, 8], [235, 111, 256, 9], [236, 10, 257, 6], [237, 10, 258, 6], [238, 8, 259, 4], [239, 8, 260, 4], [239, 12, 260, 8, "timespan"], [239, 20, 260, 16], [239, 21, 260, 17, "endTime"], [239, 28, 260, 24], [239, 32, 260, 28], [239, 36, 260, 32], [239, 38, 260, 34], [240, 10, 261, 6], [240, 14, 261, 10, "PRINT_TO_CONSOLE"], [240, 30, 261, 26], [240, 34, 261, 30, "__DEV__"], [240, 41, 261, 37], [240, 43, 261, 39], [241, 12, 262, 8], [241, 16, 262, 8, "infoLog"], [241, 32, 262, 15], [241, 34, 263, 10], [241, 107, 263, 83], [241, 109, 264, 10, "key"], [241, 112, 265, 8], [241, 113, 265, 9], [242, 10, 266, 6], [243, 10, 267, 6], [244, 8, 268, 4], [245, 8, 270, 4, "timespan"], [245, 16, 270, 12], [245, 17, 270, 13, "endExtras"], [245, 26, 270, 22], [245, 29, 270, 25, "extras"], [245, 35, 270, 31], [246, 8, 271, 4, "timespan"], [246, 16, 271, 12], [246, 17, 271, 13, "endTime"], [246, 24, 271, 20], [246, 27, 271, 23, "timestamp"], [246, 36, 271, 32], [247, 8, 272, 4, "timespan"], [247, 16, 272, 12], [247, 17, 272, 13, "totalTime"], [247, 26, 272, 22], [247, 29, 272, 25, "timespan"], [247, 37, 272, 33], [247, 38, 272, 34, "endTime"], [247, 45, 272, 41], [247, 49, 272, 45, "timespan"], [247, 57, 272, 53], [247, 58, 272, 54, "startTime"], [247, 67, 272, 63], [247, 71, 272, 67], [247, 72, 272, 68], [247, 73, 272, 69], [248, 8, 273, 4], [248, 12, 273, 8, "PRINT_TO_CONSOLE"], [248, 28, 273, 24], [248, 30, 273, 26], [249, 10, 274, 6], [249, 14, 274, 6, "infoLog"], [249, 30, 274, 13], [249, 32, 274, 14], [249, 54, 274, 36], [249, 56, 274, 38], [249, 63, 274, 45], [249, 66, 274, 48, "key"], [249, 69, 274, 51], [249, 70, 274, 52], [250, 8, 275, 4], [251, 6, 276, 2], [252, 4, 276, 3], [253, 2, 276, 3], [254, 2, 288, 15], [254, 11, 288, 24, "createPerformanceLogger"], [254, 34, 288, 47, "createPerformanceLogger"], [254, 35, 288, 47], [254, 37, 288, 70], [255, 4, 289, 2], [255, 11, 289, 9], [255, 15, 289, 13, "PerformanceLogger"], [255, 32, 289, 30], [255, 33, 289, 31], [255, 34, 289, 32], [256, 2, 290, 0], [257, 0, 290, 1], [257, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "PerformanceLogger", "PerformanceLogger#addTimespan", "PerformanceLogger#append", "PerformanceLogger#clear", "PerformanceLogger#clearCompleted", "PerformanceLogger#close", "PerformanceLogger#currentTimestamp", "PerformanceLogger#getExtras", "PerformanceLogger#getPoints", "PerformanceLogger#getPointExtras", "PerformanceLogger#getTimespans", "PerformanceLogger#hasTimespan", "PerformanceLogger#isClosed", "PerformanceLogger#logEverything", "PerformanceLogger#markPoint", "PerformanceLogger#removeExtra", "PerformanceLogger#setExtra", "PerformanceLogger#startTimespan", "PerformanceLogger#stopTimespan", "createPerformanceLogger"], "mappings": "AAA;gCCsB,8BD;AEE;ECO;GD8B;EEE;GFW;EGE;GHO;EIE;GJW;EKE;GLE;EME;GNE;EOE;GPE;EQE;GRE;ESE;GTE;EUE;GVE;EWE;GXE;EYE;GZE;EaE;GbmB;EcE;GdwB;EeE;GfI;EgBE;GhBkB;EiBE;GjBgC;EkBE;GlBsC;CFC;eqBW"}}, "type": "js/module"}]}