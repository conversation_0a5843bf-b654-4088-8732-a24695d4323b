{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-css-interop", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "GojJv+4gs9E3DINH6CdbjjB/YmY=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Animated", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dNPzxVfn0yBoRxvhD+vE+lN7k4Q=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 339}, "end": {"line": 5, "column": 26, "index": 365}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 366}, "end": {"line": 6, "column": 41, "index": 407}}], "key": "OSA8xsmyvVLjxZOJ/QFvle2ua2I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.ScreenContext = exports.NativeScreen = exports.InnerScreen = void 0;\n  var _ReactNativeCSSInterop = _interopRequireWildcard(require(_dependencyMap[1], \"react-native-css-interop\"));\n  var _Animated = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Animated\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[4], \"react\"));\n  var _core = require(_dependencyMap[5], \"../core\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function _extends() {\n    return _extends = Object.assign ? Object.assign.bind() : function (n) {\n      for (var e = 1; e < arguments.length; e++) {\n        var t = arguments[e];\n        for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n      }\n      return n;\n    }, _extends.apply(null, arguments);\n  }\n  const InnerScreen = exports.InnerScreen = _View.default;\n\n  // We're using class component here because of the error from reanimated:\n  // createAnimatedComponent` does not support stateless functional components; use a class component instead.\n  // NOTE: React Server Components do not support class components.\n  class NativeScreen extends _react.default.Component {\n    render() {\n      let {\n        active,\n        activityState,\n        style,\n        enabled = (0, _core.screensEnabled)(),\n        ...rest\n      } = this.props;\n      if (enabled) {\n        if (active !== undefined && activityState === undefined) {\n          activityState = active !== 0 ? 2 : 0; // change taken from index.native.tsx\n        }\n        return /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_View.default\n        // @ts-expect-error: hidden exists on web, but not in React Native\n        , _extends({\n          hidden: activityState === 0,\n          style: [style, {\n            display: activityState !== 0 ? 'flex' : 'none'\n          }]\n        }, rest));\n      }\n      return /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_View.default, rest);\n    }\n  }\n  exports.NativeScreen = NativeScreen;\n  const Screen = _Animated.default.createAnimatedComponent(NativeScreen);\n  const ScreenContext = exports.ScreenContext = /*#__PURE__*/_react.default.createContext(Screen);\n  var _default = exports.default = Screen;\n});", "lineCount": 58, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [8, 20, 1, 13, "exports"], [8, 27, 1, 13], [8, 28, 1, 13, "ScreenContext"], [8, 41, 1, 13], [8, 44, 1, 13, "exports"], [8, 51, 1, 13], [8, 52, 1, 13, "NativeScreen"], [8, 64, 1, 13], [8, 67, 1, 13, "exports"], [8, 74, 1, 13], [8, 75, 1, 13, "InnerScreen"], [8, 86, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_ReactNativeCSSInterop"], [9, 28, 1, 13], [9, 31, 1, 13, "_interopRequireWildcard"], [9, 54, 1, 13], [9, 55, 1, 13, "require"], [9, 62, 1, 13], [9, 63, 1, 13, "_dependencyMap"], [9, 77, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_Animated"], [10, 15, 1, 13], [10, 18, 1, 13, "_interopRequireDefault"], [10, 40, 1, 13], [10, 41, 1, 13, "require"], [10, 48, 1, 13], [10, 49, 1, 13, "_dependencyMap"], [10, 63, 1, 13], [11, 2, 1, 13], [11, 6, 1, 13, "_View"], [11, 11, 1, 13], [11, 14, 1, 13, "_interopRequireDefault"], [11, 36, 1, 13], [11, 37, 1, 13, "require"], [11, 44, 1, 13], [11, 45, 1, 13, "_dependencyMap"], [11, 59, 1, 13], [12, 2, 5, 0], [12, 6, 5, 0, "_react"], [12, 12, 5, 0], [12, 15, 5, 0, "_interopRequireDefault"], [12, 37, 5, 0], [12, 38, 5, 0, "require"], [12, 45, 5, 0], [12, 46, 5, 0, "_dependencyMap"], [12, 60, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_core"], [13, 11, 6, 0], [13, 14, 6, 0, "require"], [13, 21, 6, 0], [13, 22, 6, 0, "_dependencyMap"], [13, 36, 6, 0], [14, 2, 6, 41], [14, 11, 6, 41, "_interopRequireWildcard"], [14, 35, 6, 41, "e"], [14, 36, 6, 41], [14, 38, 6, 41, "t"], [14, 39, 6, 41], [14, 68, 6, 41, "WeakMap"], [14, 75, 6, 41], [14, 81, 6, 41, "r"], [14, 82, 6, 41], [14, 89, 6, 41, "WeakMap"], [14, 96, 6, 41], [14, 100, 6, 41, "n"], [14, 101, 6, 41], [14, 108, 6, 41, "WeakMap"], [14, 115, 6, 41], [14, 127, 6, 41, "_interopRequireWildcard"], [14, 150, 6, 41], [14, 162, 6, 41, "_interopRequireWildcard"], [14, 163, 6, 41, "e"], [14, 164, 6, 41], [14, 166, 6, 41, "t"], [14, 167, 6, 41], [14, 176, 6, 41, "t"], [14, 177, 6, 41], [14, 181, 6, 41, "e"], [14, 182, 6, 41], [14, 186, 6, 41, "e"], [14, 187, 6, 41], [14, 188, 6, 41, "__esModule"], [14, 198, 6, 41], [14, 207, 6, 41, "e"], [14, 208, 6, 41], [14, 214, 6, 41, "o"], [14, 215, 6, 41], [14, 217, 6, 41, "i"], [14, 218, 6, 41], [14, 220, 6, 41, "f"], [14, 221, 6, 41], [14, 226, 6, 41, "__proto__"], [14, 235, 6, 41], [14, 243, 6, 41, "default"], [14, 250, 6, 41], [14, 252, 6, 41, "e"], [14, 253, 6, 41], [14, 270, 6, 41, "e"], [14, 271, 6, 41], [14, 294, 6, 41, "e"], [14, 295, 6, 41], [14, 320, 6, 41, "e"], [14, 321, 6, 41], [14, 330, 6, 41, "f"], [14, 331, 6, 41], [14, 337, 6, 41, "o"], [14, 338, 6, 41], [14, 341, 6, 41, "t"], [14, 342, 6, 41], [14, 345, 6, 41, "n"], [14, 346, 6, 41], [14, 349, 6, 41, "r"], [14, 350, 6, 41], [14, 358, 6, 41, "o"], [14, 359, 6, 41], [14, 360, 6, 41, "has"], [14, 363, 6, 41], [14, 364, 6, 41, "e"], [14, 365, 6, 41], [14, 375, 6, 41, "o"], [14, 376, 6, 41], [14, 377, 6, 41, "get"], [14, 380, 6, 41], [14, 381, 6, 41, "e"], [14, 382, 6, 41], [14, 385, 6, 41, "o"], [14, 386, 6, 41], [14, 387, 6, 41, "set"], [14, 390, 6, 41], [14, 391, 6, 41, "e"], [14, 392, 6, 41], [14, 394, 6, 41, "f"], [14, 395, 6, 41], [14, 411, 6, 41, "t"], [14, 412, 6, 41], [14, 416, 6, 41, "e"], [14, 417, 6, 41], [14, 433, 6, 41, "t"], [14, 434, 6, 41], [14, 441, 6, 41, "hasOwnProperty"], [14, 455, 6, 41], [14, 456, 6, 41, "call"], [14, 460, 6, 41], [14, 461, 6, 41, "e"], [14, 462, 6, 41], [14, 464, 6, 41, "t"], [14, 465, 6, 41], [14, 472, 6, 41, "i"], [14, 473, 6, 41], [14, 477, 6, 41, "o"], [14, 478, 6, 41], [14, 481, 6, 41, "Object"], [14, 487, 6, 41], [14, 488, 6, 41, "defineProperty"], [14, 502, 6, 41], [14, 507, 6, 41, "Object"], [14, 513, 6, 41], [14, 514, 6, 41, "getOwnPropertyDescriptor"], [14, 538, 6, 41], [14, 539, 6, 41, "e"], [14, 540, 6, 41], [14, 542, 6, 41, "t"], [14, 543, 6, 41], [14, 550, 6, 41, "i"], [14, 551, 6, 41], [14, 552, 6, 41, "get"], [14, 555, 6, 41], [14, 559, 6, 41, "i"], [14, 560, 6, 41], [14, 561, 6, 41, "set"], [14, 564, 6, 41], [14, 568, 6, 41, "o"], [14, 569, 6, 41], [14, 570, 6, 41, "f"], [14, 571, 6, 41], [14, 573, 6, 41, "t"], [14, 574, 6, 41], [14, 576, 6, 41, "i"], [14, 577, 6, 41], [14, 581, 6, 41, "f"], [14, 582, 6, 41], [14, 583, 6, 41, "t"], [14, 584, 6, 41], [14, 588, 6, 41, "e"], [14, 589, 6, 41], [14, 590, 6, 41, "t"], [14, 591, 6, 41], [14, 602, 6, 41, "f"], [14, 603, 6, 41], [14, 608, 6, 41, "e"], [14, 609, 6, 41], [14, 611, 6, 41, "t"], [14, 612, 6, 41], [15, 2, 3, 0], [15, 11, 3, 9, "_extends"], [15, 19, 3, 17, "_extends"], [15, 20, 3, 17], [15, 22, 3, 20], [16, 4, 3, 22], [16, 11, 3, 29, "_extends"], [16, 19, 3, 37], [16, 22, 3, 40, "Object"], [16, 28, 3, 46], [16, 29, 3, 47, "assign"], [16, 35, 3, 53], [16, 38, 3, 56, "Object"], [16, 44, 3, 62], [16, 45, 3, 63, "assign"], [16, 51, 3, 69], [16, 52, 3, 70, "bind"], [16, 56, 3, 74], [16, 57, 3, 75], [16, 58, 3, 76], [16, 61, 3, 79], [16, 71, 3, 89, "n"], [16, 72, 3, 90], [16, 74, 3, 92], [17, 6, 3, 94], [17, 11, 3, 99], [17, 15, 3, 103, "e"], [17, 16, 3, 104], [17, 19, 3, 107], [17, 20, 3, 108], [17, 22, 3, 110, "e"], [17, 23, 3, 111], [17, 26, 3, 114, "arguments"], [17, 35, 3, 123], [17, 36, 3, 124, "length"], [17, 42, 3, 130], [17, 44, 3, 132, "e"], [17, 45, 3, 133], [17, 47, 3, 135], [17, 49, 3, 137], [18, 8, 3, 139], [18, 12, 3, 143, "t"], [18, 13, 3, 144], [18, 16, 3, 147, "arguments"], [18, 25, 3, 156], [18, 26, 3, 157, "e"], [18, 27, 3, 158], [18, 28, 3, 159], [19, 8, 3, 161], [19, 13, 3, 166], [19, 17, 3, 170, "r"], [19, 18, 3, 171], [19, 22, 3, 175, "t"], [19, 23, 3, 176], [19, 25, 3, 178], [19, 26, 3, 179], [19, 27, 3, 180], [19, 28, 3, 181], [19, 30, 3, 183, "hasOwnProperty"], [19, 44, 3, 197], [19, 45, 3, 198, "call"], [19, 49, 3, 202], [19, 50, 3, 203, "t"], [19, 51, 3, 204], [19, 53, 3, 206, "r"], [19, 54, 3, 207], [19, 55, 3, 208], [19, 60, 3, 213, "n"], [19, 61, 3, 214], [19, 62, 3, 215, "r"], [19, 63, 3, 216], [19, 64, 3, 217], [19, 67, 3, 220, "t"], [19, 68, 3, 221], [19, 69, 3, 222, "r"], [19, 70, 3, 223], [19, 71, 3, 224], [19, 72, 3, 225], [20, 6, 3, 227], [21, 6, 3, 229], [21, 13, 3, 236, "n"], [21, 14, 3, 237], [22, 4, 3, 239], [22, 5, 3, 240], [22, 7, 3, 242, "_extends"], [22, 15, 3, 250], [22, 16, 3, 251, "apply"], [22, 21, 3, 256], [22, 22, 3, 257], [22, 26, 3, 261], [22, 28, 3, 263, "arguments"], [22, 37, 3, 272], [22, 38, 3, 273], [23, 2, 3, 275], [24, 2, 7, 7], [24, 8, 7, 13, "InnerScreen"], [24, 19, 7, 24], [24, 22, 7, 24, "exports"], [24, 29, 7, 24], [24, 30, 7, 24, "InnerScreen"], [24, 41, 7, 24], [24, 44, 7, 27, "View"], [24, 57, 7, 31], [26, 2, 9, 0], [27, 2, 10, 0], [28, 2, 11, 0], [29, 2, 12, 7], [29, 8, 12, 13, "NativeScreen"], [29, 20, 12, 25], [29, 29, 12, 34, "React"], [29, 43, 12, 39], [29, 44, 12, 40, "Component"], [29, 53, 12, 49], [29, 54, 12, 50], [30, 4, 13, 2, "render"], [30, 10, 13, 8, "render"], [30, 11, 13, 8], [30, 13, 13, 11], [31, 6, 14, 4], [31, 10, 14, 8], [32, 8, 15, 6, "active"], [32, 14, 15, 12], [33, 8, 16, 6, "activityState"], [33, 21, 16, 19], [34, 8, 17, 6, "style"], [34, 13, 17, 11], [35, 8, 18, 6, "enabled"], [35, 15, 18, 13], [35, 18, 18, 16], [35, 22, 18, 16, "screensEnabled"], [35, 42, 18, 30], [35, 44, 18, 31], [35, 45, 18, 32], [36, 8, 19, 6], [36, 11, 19, 9, "rest"], [37, 6, 20, 4], [37, 7, 20, 5], [37, 10, 20, 8], [37, 14, 20, 12], [37, 15, 20, 13, "props"], [37, 20, 20, 18], [38, 6, 21, 4], [38, 10, 21, 8, "enabled"], [38, 17, 21, 15], [38, 19, 21, 17], [39, 8, 22, 6], [39, 12, 22, 10, "active"], [39, 18, 22, 16], [39, 23, 22, 21, "undefined"], [39, 32, 22, 30], [39, 36, 22, 34, "activityState"], [39, 49, 22, 47], [39, 54, 22, 52, "undefined"], [39, 63, 22, 61], [39, 65, 22, 63], [40, 10, 23, 8, "activityState"], [40, 23, 23, 21], [40, 26, 23, 24, "active"], [40, 32, 23, 30], [40, 37, 23, 35], [40, 38, 23, 36], [40, 41, 23, 39], [40, 42, 23, 40], [40, 45, 23, 43], [40, 46, 23, 44], [40, 47, 23, 45], [40, 48, 23, 46], [41, 8, 24, 6], [42, 8, 25, 6], [42, 15, 25, 13], [42, 28, 25, 26, "_ReactNativeCSSInterop"], [42, 50, 25, 26], [42, 51, 25, 26, "createInteropElement"], [42, 71, 25, 26], [42, 72, 25, 46, "View"], [43, 8, 26, 6], [44, 8, 26, 6], [44, 10, 27, 8, "_extends"], [44, 18, 27, 16], [44, 19, 27, 17], [45, 10, 28, 8, "hidden"], [45, 16, 28, 14], [45, 18, 28, 16, "activityState"], [45, 31, 28, 29], [45, 36, 28, 34], [45, 37, 28, 35], [46, 10, 29, 8, "style"], [46, 15, 29, 13], [46, 17, 29, 15], [46, 18, 29, 16, "style"], [46, 23, 29, 21], [46, 25, 29, 23], [47, 12, 30, 10, "display"], [47, 19, 30, 17], [47, 21, 30, 19, "activityState"], [47, 34, 30, 32], [47, 39, 30, 37], [47, 40, 30, 38], [47, 43, 30, 41], [47, 49, 30, 47], [47, 52, 30, 50], [48, 10, 31, 8], [48, 11, 31, 9], [49, 8, 32, 6], [49, 9, 32, 7], [49, 11, 32, 9, "rest"], [49, 15, 32, 13], [49, 16, 32, 14], [49, 17, 32, 15], [50, 6, 33, 4], [51, 6, 34, 4], [51, 13, 34, 11], [51, 26, 34, 24, "_ReactNativeCSSInterop"], [51, 48, 34, 24], [51, 49, 34, 24, "createInteropElement"], [51, 69, 34, 24], [51, 70, 34, 44, "View"], [51, 83, 34, 48], [51, 85, 34, 50, "rest"], [51, 89, 34, 54], [51, 90, 34, 55], [52, 4, 35, 2], [53, 2, 36, 0], [54, 2, 36, 1, "exports"], [54, 9, 36, 1], [54, 10, 36, 1, "NativeScreen"], [54, 22, 36, 1], [54, 25, 36, 1, "NativeScreen"], [54, 37, 36, 1], [55, 2, 37, 0], [55, 8, 37, 6, "Screen"], [55, 14, 37, 12], [55, 17, 37, 15, "Animated"], [55, 34, 37, 23], [55, 35, 37, 24, "createAnimatedComponent"], [55, 58, 37, 47], [55, 59, 37, 48, "NativeScreen"], [55, 71, 37, 60], [55, 72, 37, 61], [56, 2, 38, 7], [56, 8, 38, 13, "ScreenContext"], [56, 21, 38, 26], [56, 24, 38, 26, "exports"], [56, 31, 38, 26], [56, 32, 38, 26, "ScreenContext"], [56, 45, 38, 26], [56, 48, 38, 29], [56, 61, 38, 42, "React"], [56, 75, 38, 47], [56, 76, 38, 48, "createContext"], [56, 89, 38, 61], [56, 90, 38, 62, "Screen"], [56, 96, 38, 68], [56, 97, 38, 69], [57, 2, 38, 70], [57, 6, 38, 70, "_default"], [57, 14, 38, 70], [57, 17, 38, 70, "exports"], [57, 24, 38, 70], [57, 25, 38, 70, "default"], [57, 32, 38, 70], [57, 35, 39, 15, "Screen"], [57, 41, 39, 21], [58, 0, 39, 21], [58, 3]], "functionMap": {"names": ["<global>", "_extends", "<anonymous>", "NativeScreen", "NativeScreen#render"], "mappings": "AAA;ACE,+EC,iKD,oCD;OGS;ECC;GDsB;CHC"}}, "type": "js/module"}]}