{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 37, "index": 51}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../createAnimatedComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 53}, "end": {"line": 4, "column": 69, "index": 122}}], "key": "e2Y7i0GjZ0FYhc0zsmE7V0rtFCw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.AnimatedImage = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var _createAnimatedComponent = require(_dependencyMap[1], \"../createAnimatedComponent\");\n  // Since createAnimatedComponent return type is ComponentClass that has the props of the argument,\n  // but not things like NativeMethods, etc. we need to add them manually by extending the type.\n\n  var AnimatedImage = exports.AnimatedImage = (0, _createAnimatedComponent.createAnimatedComponent)(_reactNative.Image);\n});", "lineCount": 14, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "AnimatedImage"], [7, 23, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_reactNative"], [8, 18, 2, 0], [8, 21, 2, 0, "require"], [8, 28, 2, 0], [8, 29, 2, 0, "_dependencyMap"], [8, 43, 2, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_createAnimatedComponent"], [9, 30, 4, 0], [9, 33, 4, 0, "require"], [9, 40, 4, 0], [9, 41, 4, 0, "_dependencyMap"], [9, 55, 4, 0], [10, 2, 6, 0], [11, 2, 7, 0], [13, 2, 12, 7], [13, 6, 12, 13, "AnimatedImage"], [13, 19, 12, 26], [13, 22, 12, 26, "exports"], [13, 29, 12, 26], [13, 30, 12, 26, "AnimatedImage"], [13, 43, 12, 26], [13, 46, 12, 29], [13, 50, 12, 29, "createAnimatedComponent"], [13, 98, 12, 52], [13, 100, 12, 53, "Image"], [13, 118, 12, 58], [13, 119, 12, 59], [14, 0, 12, 60], [14, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}