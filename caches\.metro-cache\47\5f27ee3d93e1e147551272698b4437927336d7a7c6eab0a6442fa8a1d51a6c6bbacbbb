{"dependencies": [{"name": "../../errors.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 50, "index": 65}}], "key": "Jq1DcLPs1AjY3ygtzPUe4D8IdoQ=", "exportNames": ["*"]}}, {"name": "../util.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 66}, "end": {"line": 4, "column": 74, "index": 140}}], "key": "kanFP5HUYYYoVhDf3d7saim1ZXY=", "exportNames": ["*"]}}, {"name": "./rigidDecay.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 141}, "end": {"line": 5, "column": 45, "index": 186}}], "key": "ds4zLEsxFdZCfu2+88IBuGYYdaw=", "exportNames": ["*"]}}, {"name": "./rubberBandDecay.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 187}, "end": {"line": 6, "column": 55, "index": 242}}], "key": "p4eGNT1V3kOjA6GH/2dkuvI1djU=", "exportNames": ["*"]}}, {"name": "./utils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 243}, "end": {"line": 7, "column": 53, "index": 296}}], "key": "NIaSEHO1E48gsZc7jH9Ex1xTHgE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.withDecay = void 0;\n  var _errors = require(_dependencyMap[0], \"../../errors.js\");\n  var _util = require(_dependencyMap[1], \"../util.js\");\n  var _rigidDecay = require(_dependencyMap[2], \"./rigidDecay.js\");\n  var _rubberBandDecay = require(_dependencyMap[3], \"./rubberBandDecay.js\");\n  var _utils = require(_dependencyMap[4], \"./utils.js\");\n  // TODO TYPESCRIPT This is a temporary type to get rid of .d.ts file.\n  const _worklet_3357555836691_init_data = {\n    code: \"function validateConfig_reactNativeReanimated_decayJs1(config){if(config.clamp){if(!Array.isArray(config.clamp)){throw new ReanimatedError(\\\"`config.clamp` must be an array but is \\\"+typeof config.clamp+\\\".\\\");}if(config.clamp.length!==2){throw new ReanimatedError(\\\"`clamp array` must contain 2 items but is given \\\"+config.clamp.length+\\\".\\\");}}if(config.velocityFactor<=0){throw new ReanimatedError(\\\"`config.velocityFactor` must be greater then 0 but is \\\"+config.velocityFactor+\\\".\\\");}if(config.rubberBandEffect&&!config.clamp){throw new ReanimatedError('You need to set `clamp` property when using `rubberBandEffect`.');}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/decay/decay.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"validateConfig_reactNativeReanimated_decayJs1\\\",\\\"config\\\",\\\"clamp\\\",\\\"ReanimatedError\\\",\\\"length\\\",\\\"velocityFactor\\\",\\\"rubberBandEffect\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/decay/decay.js\\\"],\\\"mappings\\\":\\\"AAQA,SAAAA,8CAAAC,MAAA,KAAAA,MAAA,CAAAC,KAAA,E,iCAEA,KAAS,KAAAC,eAAqB,CAAE,iDAAAF,MAAA,CAAAC,KAAA,MAG9B,CACE,GAAID,MAAM,CAACC,KAAA,CAAAE,MAAQ,GAAO,EAAK,CAC7B,KAAM,IAAI,CAAAD,eAAe,mDAA6C,CAAOF,MAAO,CAAAC,KAAK,CAAAE,MAAI,MAC/F,C,IAEEH,MAAM,CAAAI,cAAI,EAAe,GAC3B,UAAAF,eAAA,0DAAAF,MAAA,CAAAI,cAAA,MACF,CACA,GAAIJ,MAAM,CAACK,gBAAc,EAAK,CAAEL,MAAA,CAAAC,KAAA,EAC9B,KAAM,IAAI,CAAAC,eAAe,kEAAkE,CAAC,CAC9F,C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const validateConfig = function () {\n    const _e = [new global.Error(), 1, -27];\n    const validateConfig = function (config) {\n      if (config.clamp) {\n        if (!Array.isArray(config.clamp)) {\n          throw new _errors.ReanimatedError(`\\`config.clamp\\` must be an array but is ${typeof config.clamp}.`);\n        }\n        if (config.clamp.length !== 2) {\n          throw new _errors.ReanimatedError(`\\`clamp array\\` must contain 2 items but is given ${config.clamp.length}.`);\n        }\n      }\n      if (config.velocityFactor <= 0) {\n        throw new _errors.ReanimatedError(`\\`config.velocityFactor\\` must be greater then 0 but is ${config.velocityFactor}.`);\n      }\n      if (config.rubberBandEffect && !config.clamp) {\n        throw new _errors.ReanimatedError('You need to set `clamp` property when using `rubberBandEffect`.');\n      }\n    };\n    validateConfig.__closure = {};\n    validateConfig.__workletHash = 3357555836691;\n    validateConfig.__initData = _worklet_3357555836691_init_data;\n    validateConfig.__stackDetails = _e;\n    return validateConfig;\n  }();\n  /**\n   * Lets you create animations that mimic objects in motion with friction.\n   *\n   * @param config - The decay animation configuration - {@link DecayConfig}.\n   * @param callback - A function called upon animation completion -\n   *   {@link AnimationCallback}.\n   * @returns An [animation\n   *   object](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animation-object)\n   *   which holds the current state of the animation.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withDecay\n   */\n  const _worklet_17529448934006_init_data = {\n    code: \"function reactNativeReanimated_decayJs2(userConfig,callback){const{defineAnimation,isValidRubberBandConfig,rubberBandDecay,rigidDecay,validateConfig,getReduceMotionForAnimation}=this.__closure;return defineAnimation(0,function(){'worklet';var _config$velocity;const config={deceleration:0.998,velocityFactor:1,velocity:0,rubberBandFactor:0.6};if(userConfig){Object.keys(userConfig).forEach(function(key){return config[key]=userConfig[key];});}const decay=isValidRubberBandConfig(config)?function(animation,now){return rubberBandDecay(animation,now,config);}:function(animation,now){return rigidDecay(animation,now,config);};function onStart(animation,value,now){const initialVelocity=config.velocity;animation.current=value;animation.lastTimestamp=now;animation.startTimestamp=now;animation.initialVelocity=initialVelocity;animation.velocity=initialVelocity;validateConfig(config);if(animation.reduceMotion&&config.clamp){if(value<config.clamp[0]){animation.current=config.clamp[0];}else if(value>config.clamp[1]){animation.current=config.clamp[1];}}}return{onFrame:decay,onStart:onStart,callback:callback,velocity:(_config$velocity=config.velocity)!==null&&_config$velocity!==void 0?_config$velocity:0,initialVelocity:0,current:undefined,lastTimestamp:0,startTimestamp:0,reduceMotion:getReduceMotionForAnimation(config.reduceMotion)};});}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/decay/decay.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_decayJs2\\\",\\\"userConfig\\\",\\\"callback\\\",\\\"defineAnimation\\\",\\\"isValidRubberBandConfig\\\",\\\"rubberBandDecay\\\",\\\"rigidDecay\\\",\\\"validateConfig\\\",\\\"getReduceMotionForAnimation\\\",\\\"__closure\\\",\\\"_config$velocity\\\",\\\"config\\\",\\\"deceleration\\\",\\\"velocityFactor\\\",\\\"velocity\\\",\\\"rubberBandFactor\\\",\\\"Object\\\",\\\"keys\\\",\\\"forEach\\\",\\\"key\\\",\\\"decay\\\",\\\"animation\\\",\\\"now\\\",\\\"onStart\\\",\\\"value\\\",\\\"initialVelocity\\\",\\\"current\\\",\\\"lastTimestamp\\\",\\\"startTimestamp\\\",\\\"reduceMotion\\\",\\\"clamp\\\",\\\"onFrame\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/decay/decay.js\\\"],\\\"mappings\\\":\\\"AAwCyB,SAAAA,8BAAgCA,CAAAC,UAAA,CAAAC,QAAA,QAAAC,eAAA,CAAAC,uBAAA,CAAAC,eAAA,CAAAC,UAAA,CAAAC,cAAA,CAAAC,2BAAA,OAAAC,SAAA,CAGvD,MAAO,CAAAN,eAAe,CAAC,CAAC,CAAE,UAAM,CAC9B,SAAS,KAAAO,gBAAA,CAET,KAAM,CAAAC,MAAM,CAAG,CACbC,YAAY,CAAE,KAAK,CACnBC,cAAc,CAAE,CAAC,CACjBC,QAAQ,CAAE,CAAC,CACXC,gBAAgB,CAAE,GACpB,CAAC,CACD,GAAId,UAAU,CAAE,CACde,MAAM,CAACC,IAAI,CAAChB,UAAU,CAAC,CAACiB,OAAO,CAAC,SAAAC,GAAG,QAAI,CAAAR,MAAM,CAACQ,GAAG,CAAC,CAAGlB,UAAU,CAACkB,GAAG,CAAC,GAAC,CACvE,CACA,KAAM,CAAAC,KAAK,CAAGhB,uBAAuB,CAACO,MAAM,CAAC,CAAG,SAACU,SAAS,CAAEC,GAAG,QAAK,CAAAjB,eAAe,CAACgB,SAAS,CAAEC,GAAG,CAAEX,MAAM,CAAC,GAAG,SAACU,SAAS,CAAEC,GAAG,QAAK,CAAAhB,UAAU,CAACe,SAAS,CAAEC,GAAG,CAAEX,MAAM,CAAC,GACpK,QAAS,CAAAY,OAAOA,CAACF,SAAS,CAAEG,KAAK,CAAEF,GAAG,CAAE,CACtC,KAAM,CAAAG,eAAe,CAAGd,MAAM,CAACG,QAAQ,CACvCO,SAAS,CAACK,OAAO,CAAGF,KAAK,CACzBH,SAAS,CAACM,aAAa,CAAGL,GAAG,CAC7BD,SAAS,CAACO,cAAc,CAAGN,GAAG,CAC9BD,SAAS,CAACI,eAAe,CAAGA,eAAe,CAC3CJ,SAAS,CAACP,QAAQ,CAAGW,eAAe,CACpClB,cAAc,CAACI,MAAM,CAAC,CACtB,GAAIU,SAAS,CAACQ,YAAY,EAAIlB,MAAM,CAACmB,KAAK,CAAE,CAC1C,GAAIN,KAAK,CAAGb,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAE,CAC3BT,SAAS,CAACK,OAAO,CAAGf,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CACrC,CAAC,IAAM,IAAIN,KAAK,CAAGb,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAE,CAClCT,SAAS,CAACK,OAAO,CAAGf,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CACrC,CACF,CACF,CAKA,MAAO,CACLC,OAAO,CAAEX,KAAK,CACdG,OAAO,CAAPA,OAAO,CACPrB,QAAQ,CAARA,QAAQ,CACRY,QAAQ,EAAAJ,gBAAA,CAAEC,MAAM,CAACG,QAAQ,UAAAJ,gBAAA,UAAAA,gBAAA,CAAI,CAAC,CAC9Be,eAAe,CAAE,CAAC,CAClBC,OAAO,CAAEM,SAAS,CAClBL,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAAC,CACjBC,YAAY,CAAErB,2BAA2B,CAACG,MAAM,CAACkB,YAAY,CAC/D,CAAC,CACH,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_3756010046105_init_data = {\n    code: \"function reactNativeReanimated_decayJs3(){const{userConfig,isValidRubberBandConfig,rubberBandDecay,rigidDecay,validateConfig,callback,getReduceMotionForAnimation}=this.__closure;var _config$velocity;const config={deceleration:0.998,velocityFactor:1,velocity:0,rubberBandFactor:0.6};if(userConfig){Object.keys(userConfig).forEach(function(key){return config[key]=userConfig[key];});}const decay=isValidRubberBandConfig(config)?function(animation,now){return rubberBandDecay(animation,now,config);}:function(animation,now){return rigidDecay(animation,now,config);};function onStart(animation,value,now){const initialVelocity=config.velocity;animation.current=value;animation.lastTimestamp=now;animation.startTimestamp=now;animation.initialVelocity=initialVelocity;animation.velocity=initialVelocity;validateConfig(config);if(animation.reduceMotion&&config.clamp){if(value<config.clamp[0]){animation.current=config.clamp[0];}else if(value>config.clamp[1]){animation.current=config.clamp[1];}}}return{onFrame:decay,onStart:onStart,callback:callback,velocity:(_config$velocity=config.velocity)!==null&&_config$velocity!==void 0?_config$velocity:0,initialVelocity:0,current:undefined,lastTimestamp:0,startTimestamp:0,reduceMotion:getReduceMotionForAnimation(config.reduceMotion)};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/decay/decay.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_decayJs3\\\",\\\"userConfig\\\",\\\"isValidRubberBandConfig\\\",\\\"rubberBandDecay\\\",\\\"rigidDecay\\\",\\\"validateConfig\\\",\\\"callback\\\",\\\"getReduceMotionForAnimation\\\",\\\"__closure\\\",\\\"_config$velocity\\\",\\\"config\\\",\\\"deceleration\\\",\\\"velocityFactor\\\",\\\"velocity\\\",\\\"rubberBandFactor\\\",\\\"Object\\\",\\\"keys\\\",\\\"forEach\\\",\\\"key\\\",\\\"decay\\\",\\\"animation\\\",\\\"now\\\",\\\"onStart\\\",\\\"value\\\",\\\"initialVelocity\\\",\\\"current\\\",\\\"lastTimestamp\\\",\\\"startTimestamp\\\",\\\"reduceMotion\\\",\\\"clamp\\\",\\\"onFrame\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/decay/decay.js\\\"],\\\"mappings\\\":\\\"AA2C4B,SAAAA,8BAAMA,CAAA,QAAAC,UAAA,CAAAC,uBAAA,CAAAC,eAAA,CAAAC,UAAA,CAAAC,cAAA,CAAAC,QAAA,CAAAC,2BAAA,OAAAC,SAAA,KAAAC,gBAAA,CAG9B,KAAM,CAAAC,MAAM,CAAG,CACbC,YAAY,CAAE,KAAK,CACnBC,cAAc,CAAE,CAAC,CACjBC,QAAQ,CAAE,CAAC,CACXC,gBAAgB,CAAE,GACpB,CAAC,CACD,GAAIb,UAAU,CAAE,CACdc,MAAM,CAACC,IAAI,CAACf,UAAU,CAAC,CAACgB,OAAO,CAAC,SAAAC,GAAG,QAAI,CAAAR,MAAM,CAACQ,GAAG,CAAC,CAAGjB,UAAU,CAACiB,GAAG,CAAC,GAAC,CACvE,CACA,KAAM,CAAAC,KAAK,CAAGjB,uBAAuB,CAACQ,MAAM,CAAC,CAAG,SAACU,SAAS,CAAEC,GAAG,QAAK,CAAAlB,eAAe,CAACiB,SAAS,CAAEC,GAAG,CAAEX,MAAM,CAAC,GAAG,SAACU,SAAS,CAAEC,GAAG,QAAK,CAAAjB,UAAU,CAACgB,SAAS,CAAEC,GAAG,CAAEX,MAAM,CAAC,GACpK,QAAS,CAAAY,OAAOA,CAACF,SAAS,CAAEG,KAAK,CAAEF,GAAG,CAAE,CACtC,KAAM,CAAAG,eAAe,CAAGd,MAAM,CAACG,QAAQ,CACvCO,SAAS,CAACK,OAAO,CAAGF,KAAK,CACzBH,SAAS,CAACM,aAAa,CAAGL,GAAG,CAC7BD,SAAS,CAACO,cAAc,CAAGN,GAAG,CAC9BD,SAAS,CAACI,eAAe,CAAGA,eAAe,CAC3CJ,SAAS,CAACP,QAAQ,CAAGW,eAAe,CACpCnB,cAAc,CAACK,MAAM,CAAC,CACtB,GAAIU,SAAS,CAACQ,YAAY,EAAIlB,MAAM,CAACmB,KAAK,CAAE,CAC1C,GAAIN,KAAK,CAAGb,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAE,CAC3BT,SAAS,CAACK,OAAO,CAAGf,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CACrC,CAAC,IAAM,IAAIN,KAAK,CAAGb,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAE,CAClCT,SAAS,CAACK,OAAO,CAAGf,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CACrC,CACF,CACF,CAKA,MAAO,CACLC,OAAO,CAAEX,KAAK,CACdG,OAAO,CAAPA,OAAO,CACPhB,QAAQ,CAARA,QAAQ,CACRO,QAAQ,EAAAJ,gBAAA,CAAEC,MAAM,CAACG,QAAQ,UAAAJ,gBAAA,UAAAA,gBAAA,CAAI,CAAC,CAC9Be,eAAe,CAAE,CAAC,CAClBC,OAAO,CAAEM,SAAS,CAClBL,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAAC,CACjBC,YAAY,CAAErB,2BAA2B,CAACG,MAAM,CAACkB,YAAY,CAC/D,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const withDecay = exports.withDecay = function () {\n    const _e = [new global.Error(), -7, -27];\n    const reactNativeReanimated_decayJs2 = function (userConfig, callback) {\n      return (0, _util.defineAnimation)(0, function () {\n        const _e = [new global.Error(), -8, -27];\n        const reactNativeReanimated_decayJs3 = function () {\n          const config = {\n            deceleration: 0.998,\n            velocityFactor: 1,\n            velocity: 0,\n            rubberBandFactor: 0.6\n          };\n          if (userConfig) {\n            Object.keys(userConfig).forEach(key => config[key] = userConfig[key]);\n          }\n          const decay = (0, _utils.isValidRubberBandConfig)(config) ? (animation, now) => (0, _rubberBandDecay.rubberBandDecay)(animation, now, config) : (animation, now) => (0, _rigidDecay.rigidDecay)(animation, now, config);\n          function onStart(animation, value, now) {\n            const initialVelocity = config.velocity;\n            animation.current = value;\n            animation.lastTimestamp = now;\n            animation.startTimestamp = now;\n            animation.initialVelocity = initialVelocity;\n            animation.velocity = initialVelocity;\n            validateConfig(config);\n            if (animation.reduceMotion && config.clamp) {\n              if (value < config.clamp[0]) {\n                animation.current = config.clamp[0];\n              } else if (value > config.clamp[1]) {\n                animation.current = config.clamp[1];\n              }\n            }\n          }\n\n          // To ensure the animation is correctly initialized and starts as expected\n          // we need to set its current value to undefined.\n          // Setting current to 0 breaks the animation.\n          return {\n            onFrame: decay,\n            onStart,\n            callback,\n            velocity: config.velocity ?? 0,\n            initialVelocity: 0,\n            current: undefined,\n            lastTimestamp: 0,\n            startTimestamp: 0,\n            reduceMotion: (0, _util.getReduceMotionForAnimation)(config.reduceMotion)\n          };\n        };\n        reactNativeReanimated_decayJs3.__closure = {\n          userConfig,\n          isValidRubberBandConfig: _utils.isValidRubberBandConfig,\n          rubberBandDecay: _rubberBandDecay.rubberBandDecay,\n          rigidDecay: _rigidDecay.rigidDecay,\n          validateConfig,\n          callback,\n          getReduceMotionForAnimation: _util.getReduceMotionForAnimation\n        };\n        reactNativeReanimated_decayJs3.__workletHash = 3756010046105;\n        reactNativeReanimated_decayJs3.__initData = _worklet_3756010046105_init_data;\n        reactNativeReanimated_decayJs3.__stackDetails = _e;\n        return reactNativeReanimated_decayJs3;\n      }());\n    };\n    reactNativeReanimated_decayJs2.__closure = {\n      defineAnimation: _util.defineAnimation,\n      isValidRubberBandConfig: _utils.isValidRubberBandConfig,\n      rubberBandDecay: _rubberBandDecay.rubberBandDecay,\n      rigidDecay: _rigidDecay.rigidDecay,\n      validateConfig,\n      getReduceMotionForAnimation: _util.getReduceMotionForAnimation\n    };\n    reactNativeReanimated_decayJs2.__workletHash = 17529448934006;\n    reactNativeReanimated_decayJs2.__initData = _worklet_17529448934006_init_data;\n    reactNativeReanimated_decayJs2.__stackDetails = _e;\n    return reactNativeReanimated_decayJs2;\n  }();\n});", "lineCount": 143, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "<PERSON><PERSON><PERSON><PERSON>"], [7, 19, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_errors"], [8, 13, 3, 0], [8, 16, 3, 0, "require"], [8, 23, 3, 0], [8, 24, 3, 0, "_dependencyMap"], [8, 38, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_util"], [9, 11, 4, 0], [9, 14, 4, 0, "require"], [9, 21, 4, 0], [9, 22, 4, 0, "_dependencyMap"], [9, 36, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_rigidDecay"], [10, 17, 5, 0], [10, 20, 5, 0, "require"], [10, 27, 5, 0], [10, 28, 5, 0, "_dependencyMap"], [10, 42, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_rubberBandDecay"], [11, 22, 6, 0], [11, 25, 6, 0, "require"], [11, 32, 6, 0], [11, 33, 6, 0, "_dependencyMap"], [11, 47, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_utils"], [12, 12, 7, 0], [12, 15, 7, 0, "require"], [12, 22, 7, 0], [12, 23, 7, 0, "_dependencyMap"], [12, 37, 7, 0], [13, 2, 9, 0], [14, 2, 9, 0], [14, 8, 9, 0, "_worklet_3357555836691_init_data"], [14, 40, 9, 0], [15, 4, 9, 0, "code"], [15, 8, 9, 0], [16, 4, 9, 0, "location"], [16, 12, 9, 0], [17, 4, 9, 0, "sourceMap"], [17, 13, 9, 0], [18, 4, 9, 0, "version"], [18, 11, 9, 0], [19, 2, 9, 0], [20, 2, 9, 0], [20, 8, 9, 0, "validateConfig"], [20, 22, 9, 0], [20, 25, 11, 0], [21, 4, 11, 0], [21, 10, 11, 0, "_e"], [21, 12, 11, 0], [21, 20, 11, 0, "global"], [21, 26, 11, 0], [21, 27, 11, 0, "Error"], [21, 32, 11, 0], [22, 4, 11, 0], [22, 10, 11, 0, "validateConfig"], [22, 24, 11, 0], [22, 36, 11, 0, "validateConfig"], [22, 37, 11, 24, "config"], [22, 43, 11, 30], [22, 45, 11, 32], [23, 6, 14, 2], [23, 10, 14, 6, "config"], [23, 16, 14, 12], [23, 17, 14, 13, "clamp"], [23, 22, 14, 18], [23, 24, 14, 20], [24, 8, 15, 4], [24, 12, 15, 8], [24, 13, 15, 9, "Array"], [24, 18, 15, 14], [24, 19, 15, 15, "isArray"], [24, 26, 15, 22], [24, 27, 15, 23, "config"], [24, 33, 15, 29], [24, 34, 15, 30, "clamp"], [24, 39, 15, 35], [24, 40, 15, 36], [24, 42, 15, 38], [25, 10, 16, 6], [25, 16, 16, 12], [25, 20, 16, 16, "ReanimatedError"], [25, 43, 16, 31], [25, 44, 16, 32], [25, 88, 16, 76], [25, 95, 16, 83, "config"], [25, 101, 16, 89], [25, 102, 16, 90, "clamp"], [25, 107, 16, 95], [25, 110, 16, 98], [25, 111, 16, 99], [26, 8, 17, 4], [27, 8, 18, 4], [27, 12, 18, 8, "config"], [27, 18, 18, 14], [27, 19, 18, 15, "clamp"], [27, 24, 18, 20], [27, 25, 18, 21, "length"], [27, 31, 18, 27], [27, 36, 18, 32], [27, 37, 18, 33], [27, 39, 18, 35], [28, 10, 19, 6], [28, 16, 19, 12], [28, 20, 19, 16, "ReanimatedError"], [28, 43, 19, 31], [28, 44, 19, 32], [28, 97, 19, 85, "config"], [28, 103, 19, 91], [28, 104, 19, 92, "clamp"], [28, 109, 19, 97], [28, 110, 19, 98, "length"], [28, 116, 19, 104], [28, 119, 19, 107], [28, 120, 19, 108], [29, 8, 20, 4], [30, 6, 21, 2], [31, 6, 22, 2], [31, 10, 22, 6, "config"], [31, 16, 22, 12], [31, 17, 22, 13, "velocityFactor"], [31, 31, 22, 27], [31, 35, 22, 31], [31, 36, 22, 32], [31, 38, 22, 34], [32, 8, 23, 4], [32, 14, 23, 10], [32, 18, 23, 14, "ReanimatedError"], [32, 41, 23, 29], [32, 42, 23, 30], [32, 101, 23, 89, "config"], [32, 107, 23, 95], [32, 108, 23, 96, "velocityFactor"], [32, 122, 23, 110], [32, 125, 23, 113], [32, 126, 23, 114], [33, 6, 24, 2], [34, 6, 25, 2], [34, 10, 25, 6, "config"], [34, 16, 25, 12], [34, 17, 25, 13, "rubberBandEffect"], [34, 33, 25, 29], [34, 37, 25, 33], [34, 38, 25, 34, "config"], [34, 44, 25, 40], [34, 45, 25, 41, "clamp"], [34, 50, 25, 46], [34, 52, 25, 48], [35, 8, 26, 4], [35, 14, 26, 10], [35, 18, 26, 14, "ReanimatedError"], [35, 41, 26, 29], [35, 42, 26, 30], [35, 107, 26, 95], [35, 108, 26, 96], [36, 6, 27, 2], [37, 4, 28, 0], [37, 5, 28, 1], [38, 4, 28, 1, "validateConfig"], [38, 18, 28, 1], [38, 19, 28, 1, "__closure"], [38, 28, 28, 1], [39, 4, 28, 1, "validateConfig"], [39, 18, 28, 1], [39, 19, 28, 1, "__workletHash"], [39, 32, 28, 1], [40, 4, 28, 1, "validateConfig"], [40, 18, 28, 1], [40, 19, 28, 1, "__initData"], [40, 29, 28, 1], [40, 32, 28, 1, "_worklet_3357555836691_init_data"], [40, 64, 28, 1], [41, 4, 28, 1, "validateConfig"], [41, 18, 28, 1], [41, 19, 28, 1, "__stackDetails"], [41, 33, 28, 1], [41, 36, 28, 1, "_e"], [41, 38, 28, 1], [42, 4, 28, 1], [42, 11, 28, 1, "validateConfig"], [42, 25, 28, 1], [43, 2, 28, 1], [43, 3, 11, 0], [44, 2, 30, 0], [45, 0, 31, 0], [46, 0, 32, 0], [47, 0, 33, 0], [48, 0, 34, 0], [49, 0, 35, 0], [50, 0, 36, 0], [51, 0, 37, 0], [52, 0, 38, 0], [53, 0, 39, 0], [54, 0, 40, 0], [55, 2, 30, 0], [55, 8, 30, 0, "_worklet_17529448934006_init_data"], [55, 41, 30, 0], [56, 4, 30, 0, "code"], [56, 8, 30, 0], [57, 4, 30, 0, "location"], [57, 12, 30, 0], [58, 4, 30, 0, "sourceMap"], [58, 13, 30, 0], [59, 4, 30, 0, "version"], [59, 11, 30, 0], [60, 2, 30, 0], [61, 2, 30, 0], [61, 8, 30, 0, "_worklet_3756010046105_init_data"], [61, 40, 30, 0], [62, 4, 30, 0, "code"], [62, 8, 30, 0], [63, 4, 30, 0, "location"], [63, 12, 30, 0], [64, 4, 30, 0, "sourceMap"], [64, 13, 30, 0], [65, 4, 30, 0, "version"], [65, 11, 30, 0], [66, 2, 30, 0], [67, 2, 41, 7], [67, 8, 41, 13, "<PERSON><PERSON><PERSON><PERSON>"], [67, 17, 41, 22], [67, 20, 41, 22, "exports"], [67, 27, 41, 22], [67, 28, 41, 22, "<PERSON><PERSON><PERSON><PERSON>"], [67, 37, 41, 22], [67, 40, 41, 25], [68, 4, 41, 25], [68, 10, 41, 25, "_e"], [68, 12, 41, 25], [68, 20, 41, 25, "global"], [68, 26, 41, 25], [68, 27, 41, 25, "Error"], [68, 32, 41, 25], [69, 4, 41, 25], [69, 10, 41, 25, "reactNativeReanimated_decayJs2"], [69, 40, 41, 25], [69, 52, 41, 25, "reactNativeReanimated_decayJs2"], [69, 53, 41, 35, "userConfig"], [69, 63, 41, 45], [69, 65, 41, 47, "callback"], [69, 73, 41, 55], [69, 75, 41, 57], [70, 6, 44, 2], [70, 13, 44, 9], [70, 17, 44, 9, "defineAnimation"], [70, 38, 44, 24], [70, 40, 44, 25], [70, 41, 44, 26], [70, 43, 44, 28], [71, 8, 44, 28], [71, 14, 44, 28, "_e"], [71, 16, 44, 28], [71, 24, 44, 28, "global"], [71, 30, 44, 28], [71, 31, 44, 28, "Error"], [71, 36, 44, 28], [72, 8, 44, 28], [72, 14, 44, 28, "reactNativeReanimated_decayJs3"], [72, 44, 44, 28], [72, 56, 44, 28, "reactNativeReanimated_decayJs3"], [72, 57, 44, 28], [72, 59, 44, 34], [73, 10, 47, 4], [73, 16, 47, 10, "config"], [73, 22, 47, 16], [73, 25, 47, 19], [74, 12, 48, 6, "deceleration"], [74, 24, 48, 18], [74, 26, 48, 20], [74, 31, 48, 25], [75, 12, 49, 6, "velocityFactor"], [75, 26, 49, 20], [75, 28, 49, 22], [75, 29, 49, 23], [76, 12, 50, 6, "velocity"], [76, 20, 50, 14], [76, 22, 50, 16], [76, 23, 50, 17], [77, 12, 51, 6, "rubberBandFactor"], [77, 28, 51, 22], [77, 30, 51, 24], [78, 10, 52, 4], [78, 11, 52, 5], [79, 10, 53, 4], [79, 14, 53, 8, "userConfig"], [79, 24, 53, 18], [79, 26, 53, 20], [80, 12, 54, 6, "Object"], [80, 18, 54, 12], [80, 19, 54, 13, "keys"], [80, 23, 54, 17], [80, 24, 54, 18, "userConfig"], [80, 34, 54, 28], [80, 35, 54, 29], [80, 36, 54, 30, "for<PERSON>ach"], [80, 43, 54, 37], [80, 44, 54, 38, "key"], [80, 47, 54, 41], [80, 51, 54, 45, "config"], [80, 57, 54, 51], [80, 58, 54, 52, "key"], [80, 61, 54, 55], [80, 62, 54, 56], [80, 65, 54, 59, "userConfig"], [80, 75, 54, 69], [80, 76, 54, 70, "key"], [80, 79, 54, 73], [80, 80, 54, 74], [80, 81, 54, 75], [81, 10, 55, 4], [82, 10, 56, 4], [82, 16, 56, 10, "decay"], [82, 21, 56, 15], [82, 24, 56, 18], [82, 28, 56, 18, "isValidRubberBandConfig"], [82, 58, 56, 41], [82, 60, 56, 42, "config"], [82, 66, 56, 48], [82, 67, 56, 49], [82, 70, 56, 52], [82, 71, 56, 53, "animation"], [82, 80, 56, 62], [82, 82, 56, 64, "now"], [82, 85, 56, 67], [82, 90, 56, 72], [82, 94, 56, 72, "rubberBandDecay"], [82, 126, 56, 87], [82, 128, 56, 88, "animation"], [82, 137, 56, 97], [82, 139, 56, 99, "now"], [82, 142, 56, 102], [82, 144, 56, 104, "config"], [82, 150, 56, 110], [82, 151, 56, 111], [82, 154, 56, 114], [82, 155, 56, 115, "animation"], [82, 164, 56, 124], [82, 166, 56, 126, "now"], [82, 169, 56, 129], [82, 174, 56, 134], [82, 178, 56, 134, "rigidDecay"], [82, 200, 56, 144], [82, 202, 56, 145, "animation"], [82, 211, 56, 154], [82, 213, 56, 156, "now"], [82, 216, 56, 159], [82, 218, 56, 161, "config"], [82, 224, 56, 167], [82, 225, 56, 168], [83, 10, 57, 4], [83, 19, 57, 13, "onStart"], [83, 26, 57, 20, "onStart"], [83, 27, 57, 21, "animation"], [83, 36, 57, 30], [83, 38, 57, 32, "value"], [83, 43, 57, 37], [83, 45, 57, 39, "now"], [83, 48, 57, 42], [83, 50, 57, 44], [84, 12, 58, 6], [84, 18, 58, 12, "initialVelocity"], [84, 33, 58, 27], [84, 36, 58, 30, "config"], [84, 42, 58, 36], [84, 43, 58, 37, "velocity"], [84, 51, 58, 45], [85, 12, 59, 6, "animation"], [85, 21, 59, 15], [85, 22, 59, 16, "current"], [85, 29, 59, 23], [85, 32, 59, 26, "value"], [85, 37, 59, 31], [86, 12, 60, 6, "animation"], [86, 21, 60, 15], [86, 22, 60, 16, "lastTimestamp"], [86, 35, 60, 29], [86, 38, 60, 32, "now"], [86, 41, 60, 35], [87, 12, 61, 6, "animation"], [87, 21, 61, 15], [87, 22, 61, 16, "startTimestamp"], [87, 36, 61, 30], [87, 39, 61, 33, "now"], [87, 42, 61, 36], [88, 12, 62, 6, "animation"], [88, 21, 62, 15], [88, 22, 62, 16, "initialVelocity"], [88, 37, 62, 31], [88, 40, 62, 34, "initialVelocity"], [88, 55, 62, 49], [89, 12, 63, 6, "animation"], [89, 21, 63, 15], [89, 22, 63, 16, "velocity"], [89, 30, 63, 24], [89, 33, 63, 27, "initialVelocity"], [89, 48, 63, 42], [90, 12, 64, 6, "validateConfig"], [90, 26, 64, 20], [90, 27, 64, 21, "config"], [90, 33, 64, 27], [90, 34, 64, 28], [91, 12, 65, 6], [91, 16, 65, 10, "animation"], [91, 25, 65, 19], [91, 26, 65, 20, "reduceMotion"], [91, 38, 65, 32], [91, 42, 65, 36, "config"], [91, 48, 65, 42], [91, 49, 65, 43, "clamp"], [91, 54, 65, 48], [91, 56, 65, 50], [92, 14, 66, 8], [92, 18, 66, 12, "value"], [92, 23, 66, 17], [92, 26, 66, 20, "config"], [92, 32, 66, 26], [92, 33, 66, 27, "clamp"], [92, 38, 66, 32], [92, 39, 66, 33], [92, 40, 66, 34], [92, 41, 66, 35], [92, 43, 66, 37], [93, 16, 67, 10, "animation"], [93, 25, 67, 19], [93, 26, 67, 20, "current"], [93, 33, 67, 27], [93, 36, 67, 30, "config"], [93, 42, 67, 36], [93, 43, 67, 37, "clamp"], [93, 48, 67, 42], [93, 49, 67, 43], [93, 50, 67, 44], [93, 51, 67, 45], [94, 14, 68, 8], [94, 15, 68, 9], [94, 21, 68, 15], [94, 25, 68, 19, "value"], [94, 30, 68, 24], [94, 33, 68, 27, "config"], [94, 39, 68, 33], [94, 40, 68, 34, "clamp"], [94, 45, 68, 39], [94, 46, 68, 40], [94, 47, 68, 41], [94, 48, 68, 42], [94, 50, 68, 44], [95, 16, 69, 10, "animation"], [95, 25, 69, 19], [95, 26, 69, 20, "current"], [95, 33, 69, 27], [95, 36, 69, 30, "config"], [95, 42, 69, 36], [95, 43, 69, 37, "clamp"], [95, 48, 69, 42], [95, 49, 69, 43], [95, 50, 69, 44], [95, 51, 69, 45], [96, 14, 70, 8], [97, 12, 71, 6], [98, 10, 72, 4], [100, 10, 74, 4], [101, 10, 75, 4], [102, 10, 76, 4], [103, 10, 77, 4], [103, 17, 77, 11], [104, 12, 78, 6, "onFrame"], [104, 19, 78, 13], [104, 21, 78, 15, "decay"], [104, 26, 78, 20], [105, 12, 79, 6, "onStart"], [105, 19, 79, 13], [106, 12, 80, 6, "callback"], [106, 20, 80, 14], [107, 12, 81, 6, "velocity"], [107, 20, 81, 14], [107, 22, 81, 16, "config"], [107, 28, 81, 22], [107, 29, 81, 23, "velocity"], [107, 37, 81, 31], [107, 41, 81, 35], [107, 42, 81, 36], [108, 12, 82, 6, "initialVelocity"], [108, 27, 82, 21], [108, 29, 82, 23], [108, 30, 82, 24], [109, 12, 83, 6, "current"], [109, 19, 83, 13], [109, 21, 83, 15, "undefined"], [109, 30, 83, 24], [110, 12, 84, 6, "lastTimestamp"], [110, 25, 84, 19], [110, 27, 84, 21], [110, 28, 84, 22], [111, 12, 85, 6, "startTimestamp"], [111, 26, 85, 20], [111, 28, 85, 22], [111, 29, 85, 23], [112, 12, 86, 6, "reduceMotion"], [112, 24, 86, 18], [112, 26, 86, 20], [112, 30, 86, 20, "getReduceMotionForAnimation"], [112, 63, 86, 47], [112, 65, 86, 48, "config"], [112, 71, 86, 54], [112, 72, 86, 55, "reduceMotion"], [112, 84, 86, 67], [113, 10, 87, 4], [113, 11, 87, 5], [114, 8, 88, 2], [114, 9, 88, 3], [115, 8, 88, 3, "reactNativeReanimated_decayJs3"], [115, 38, 88, 3], [115, 39, 88, 3, "__closure"], [115, 48, 88, 3], [116, 10, 88, 3, "userConfig"], [116, 20, 88, 3], [117, 10, 88, 3, "isValidRubberBandConfig"], [117, 33, 88, 3], [117, 35, 56, 18, "isValidRubberBandConfig"], [117, 65, 56, 41], [118, 10, 56, 41, "rubberBandDecay"], [118, 25, 56, 41], [118, 27, 56, 72, "rubberBandDecay"], [118, 59, 56, 87], [119, 10, 56, 87, "rigidDecay"], [119, 20, 56, 87], [119, 22, 56, 134, "rigidDecay"], [119, 44, 56, 144], [120, 10, 56, 144, "validateConfig"], [120, 24, 56, 144], [121, 10, 56, 144, "callback"], [121, 18, 56, 144], [122, 10, 56, 144, "getReduceMotionForAnimation"], [122, 37, 56, 144], [122, 39, 86, 20, "getReduceMotionForAnimation"], [123, 8, 86, 47], [124, 8, 86, 47, "reactNativeReanimated_decayJs3"], [124, 38, 86, 47], [124, 39, 86, 47, "__workletHash"], [124, 52, 86, 47], [125, 8, 86, 47, "reactNativeReanimated_decayJs3"], [125, 38, 86, 47], [125, 39, 86, 47, "__initData"], [125, 49, 86, 47], [125, 52, 86, 47, "_worklet_3756010046105_init_data"], [125, 84, 86, 47], [126, 8, 86, 47, "reactNativeReanimated_decayJs3"], [126, 38, 86, 47], [126, 39, 86, 47, "__stackDetails"], [126, 53, 86, 47], [126, 56, 86, 47, "_e"], [126, 58, 86, 47], [127, 8, 86, 47], [127, 15, 86, 47, "reactNativeReanimated_decayJs3"], [127, 45, 86, 47], [128, 6, 86, 47], [128, 7, 44, 28], [128, 9, 88, 3], [128, 10, 88, 4], [129, 4, 89, 0], [129, 5, 89, 1], [130, 4, 89, 1, "reactNativeReanimated_decayJs2"], [130, 34, 89, 1], [130, 35, 89, 1, "__closure"], [130, 44, 89, 1], [131, 6, 89, 1, "defineAnimation"], [131, 21, 89, 1], [131, 23, 44, 9, "defineAnimation"], [131, 44, 44, 24], [132, 6, 44, 24, "isValidRubberBandConfig"], [132, 29, 44, 24], [132, 31, 56, 18, "isValidRubberBandConfig"], [132, 61, 56, 41], [133, 6, 56, 41, "rubberBandDecay"], [133, 21, 56, 41], [133, 23, 56, 72, "rubberBandDecay"], [133, 55, 56, 87], [134, 6, 56, 87, "rigidDecay"], [134, 16, 56, 87], [134, 18, 56, 134, "rigidDecay"], [134, 40, 56, 144], [135, 6, 56, 144, "validateConfig"], [135, 20, 56, 144], [136, 6, 56, 144, "getReduceMotionForAnimation"], [136, 33, 56, 144], [136, 35, 86, 20, "getReduceMotionForAnimation"], [137, 4, 86, 47], [138, 4, 86, 47, "reactNativeReanimated_decayJs2"], [138, 34, 86, 47], [138, 35, 86, 47, "__workletHash"], [138, 48, 86, 47], [139, 4, 86, 47, "reactNativeReanimated_decayJs2"], [139, 34, 86, 47], [139, 35, 86, 47, "__initData"], [139, 45, 86, 47], [139, 48, 86, 47, "_worklet_17529448934006_init_data"], [139, 81, 86, 47], [140, 4, 86, 47, "reactNativeReanimated_decayJs2"], [140, 34, 86, 47], [140, 35, 86, 47, "__stackDetails"], [140, 49, 86, 47], [140, 52, 86, 47, "_e"], [140, 54, 86, 47], [141, 4, 86, 47], [141, 11, 86, 47, "reactNativeReanimated_decayJs2"], [141, 41, 86, 47], [142, 2, 86, 47], [142, 3, 41, 25], [142, 5, 89, 1], [143, 0, 89, 2], [143, 3]], "functionMap": {"names": ["<global>", "validateConfig", "<PERSON><PERSON><PERSON><PERSON>", "defineAnimation$argument_1", "Object.keys.forEach$argument_0", "<anonymous>", "onStart"], "mappings": "AAA;ACU;CDiB;yBEa;4BCG;sCCU,oCD;oDEE,2DF,GE,sDF;IGC;KHe;GDgB;CFC"}}, "type": "js/module"}]}