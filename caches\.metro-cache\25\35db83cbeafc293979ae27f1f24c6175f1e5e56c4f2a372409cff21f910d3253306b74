{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 224}, "end": {"line": 8, "column": 26, "index": 250}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LogBoxMessage = LogBoxMessage;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Text\"));\n  var _jsxRuntime = require(_dependencyMap[3], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/@expo/metro-runtime/src/error-overlay/UI/LogBoxMessage.tsx\";\n  /**\n   * Copyright (c) 650 Industries.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  const cleanContent = content => content.replace(/^(TransformError |Warning: (Warning: )?|Error: )/g, '');\n  function LogBoxMessage(props) {\n    const {\n      content,\n      substitutions\n    } = props.message;\n    if (props.plaintext === true) {\n      return (0, _jsxRuntime.jsx)(_Text.default, {\n        children: cleanContent(content)\n      });\n    }\n    const maxLength = props.maxLength != null ? props.maxLength : Infinity;\n    const substitutionStyle = props.style;\n    const elements = [];\n    let length = 0;\n    const createUnderLength = (key, message, style) => {\n      let cleanMessage = cleanContent(message);\n      if (props.maxLength != null) {\n        cleanMessage = cleanMessage.slice(0, props.maxLength - length);\n      }\n      if (length < maxLength) {\n        elements.push((0, _jsxRuntime.jsx)(_Text.default, {\n          style: style,\n          children: cleanMessage\n        }, key));\n      }\n      length += cleanMessage.length;\n    };\n    const lastOffset = substitutions.reduce((prevOffset, substitution, index) => {\n      const key = String(index);\n      if (substitution.offset > prevOffset) {\n        const prevPart = content.substr(prevOffset, substitution.offset - prevOffset);\n        createUnderLength(key, prevPart);\n      }\n      const substititionPart = content.substr(substitution.offset, substitution.length);\n      createUnderLength(key + '.5', substititionPart, substitutionStyle);\n      return substitution.offset + substitution.length;\n    }, 0);\n    if (lastOffset < content.length) {\n      const lastPart = content.substr(lastOffset);\n      createUnderLength('-1', lastPart);\n    }\n    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {\n      children: elements\n    });\n  }\n});", "lineCount": 64, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_react"], [7, 12, 8, 0], [7, 15, 8, 0, "_interopRequireDefault"], [7, 37, 8, 0], [7, 38, 8, 0, "require"], [7, 45, 8, 0], [7, 46, 8, 0, "_dependencyMap"], [7, 60, 8, 0], [8, 2, 8, 26], [8, 6, 8, 26, "_Text"], [8, 11, 8, 26], [8, 14, 8, 26, "_interopRequireDefault"], [8, 36, 8, 26], [8, 37, 8, 26, "require"], [8, 44, 8, 26], [8, 45, 8, 26, "_dependencyMap"], [8, 59, 8, 26], [9, 2, 8, 26], [9, 6, 8, 26, "_jsxRuntime"], [9, 17, 8, 26], [9, 20, 8, 26, "require"], [9, 27, 8, 26], [9, 28, 8, 26, "_dependencyMap"], [9, 42, 8, 26], [10, 2, 8, 26], [10, 6, 8, 26, "_jsxFileName"], [10, 18, 8, 26], [11, 2, 1, 0], [12, 0, 2, 0], [13, 0, 3, 0], [14, 0, 4, 0], [15, 0, 5, 0], [16, 0, 6, 0], [17, 0, 7, 0], [18, 2, 20, 0], [18, 8, 20, 6, "cleanContent"], [18, 20, 20, 18], [18, 23, 20, 22, "content"], [18, 30, 20, 37], [18, 34, 21, 2, "content"], [18, 41, 21, 9], [18, 42, 21, 10, "replace"], [18, 49, 21, 17], [18, 50, 21, 18], [18, 101, 21, 69], [18, 103, 21, 71], [18, 105, 21, 73], [18, 106, 21, 74], [19, 2, 23, 7], [19, 11, 23, 16, "LogBoxMessage"], [19, 24, 23, 29, "LogBoxMessage"], [19, 25, 23, 30, "props"], [19, 30, 23, 42], [19, 32, 23, 64], [20, 4, 24, 2], [20, 10, 24, 8], [21, 6, 24, 10, "content"], [21, 13, 24, 17], [22, 6, 24, 19, "substitutions"], [23, 4, 24, 42], [23, 5, 24, 43], [23, 8, 24, 46, "props"], [23, 13, 24, 51], [23, 14, 24, 52, "message"], [23, 21, 24, 59], [24, 4, 26, 2], [24, 8, 26, 6, "props"], [24, 13, 26, 11], [24, 14, 26, 12, "plaintext"], [24, 23, 26, 21], [24, 28, 26, 26], [24, 32, 26, 30], [24, 34, 26, 32], [25, 6, 27, 4], [25, 13, 27, 11], [25, 17, 27, 11, "_jsxRuntime"], [25, 28, 27, 11], [25, 29, 27, 11, "jsx"], [25, 32, 27, 11], [25, 34, 27, 12, "_Text"], [25, 39, 27, 12], [25, 40, 27, 12, "default"], [25, 47, 27, 16], [26, 8, 27, 16, "children"], [26, 16, 27, 16], [26, 18, 27, 18, "cleanContent"], [26, 30, 27, 30], [26, 31, 27, 31, "content"], [26, 38, 27, 38], [27, 6, 27, 39], [27, 7, 27, 46], [27, 8, 27, 47], [28, 4, 28, 2], [29, 4, 30, 2], [29, 10, 30, 8, "max<PERSON><PERSON><PERSON>"], [29, 19, 30, 17], [29, 22, 30, 20, "props"], [29, 27, 30, 25], [29, 28, 30, 26, "max<PERSON><PERSON><PERSON>"], [29, 37, 30, 35], [29, 41, 30, 39], [29, 45, 30, 43], [29, 48, 30, 46, "props"], [29, 53, 30, 51], [29, 54, 30, 52, "max<PERSON><PERSON><PERSON>"], [29, 63, 30, 61], [29, 66, 30, 64, "Infinity"], [29, 74, 30, 72], [30, 4, 31, 2], [30, 10, 31, 8, "substitutionStyle"], [30, 27, 31, 47], [30, 30, 31, 50, "props"], [30, 35, 31, 55], [30, 36, 31, 56, "style"], [30, 41, 31, 61], [31, 4, 32, 2], [31, 10, 32, 8, "elements"], [31, 18, 32, 38], [31, 21, 32, 41], [31, 23, 32, 43], [32, 4, 33, 2], [32, 8, 33, 6, "length"], [32, 14, 33, 12], [32, 17, 33, 15], [32, 18, 33, 16], [33, 4, 34, 2], [33, 10, 34, 8, "createUnderLength"], [33, 27, 34, 25], [33, 30, 34, 28, "createUnderLength"], [33, 31, 34, 29, "key"], [33, 34, 34, 47], [33, 36, 34, 49, "message"], [33, 43, 34, 64], [33, 45, 34, 66, "style"], [33, 50, 34, 94], [33, 55, 34, 99], [34, 6, 35, 4], [34, 10, 35, 8, "cleanMessage"], [34, 22, 35, 20], [34, 25, 35, 23, "cleanContent"], [34, 37, 35, 35], [34, 38, 35, 36, "message"], [34, 45, 35, 43], [34, 46, 35, 44], [35, 6, 37, 4], [35, 10, 37, 8, "props"], [35, 15, 37, 13], [35, 16, 37, 14, "max<PERSON><PERSON><PERSON>"], [35, 25, 37, 23], [35, 29, 37, 27], [35, 33, 37, 31], [35, 35, 37, 33], [36, 8, 38, 6, "cleanMessage"], [36, 20, 38, 18], [36, 23, 38, 21, "cleanMessage"], [36, 35, 38, 33], [36, 36, 38, 34, "slice"], [36, 41, 38, 39], [36, 42, 38, 40], [36, 43, 38, 41], [36, 45, 38, 43, "props"], [36, 50, 38, 48], [36, 51, 38, 49, "max<PERSON><PERSON><PERSON>"], [36, 60, 38, 58], [36, 63, 38, 61, "length"], [36, 69, 38, 67], [36, 70, 38, 68], [37, 6, 39, 4], [38, 6, 41, 4], [38, 10, 41, 8, "length"], [38, 16, 41, 14], [38, 19, 41, 17, "max<PERSON><PERSON><PERSON>"], [38, 28, 41, 26], [38, 30, 41, 28], [39, 8, 42, 6, "elements"], [39, 16, 42, 14], [39, 17, 42, 15, "push"], [39, 21, 42, 19], [39, 22, 43, 8], [39, 26, 43, 8, "_jsxRuntime"], [39, 37, 43, 8], [39, 38, 43, 8, "jsx"], [39, 41, 43, 8], [39, 43, 43, 9, "_Text"], [39, 48, 43, 9], [39, 49, 43, 9, "default"], [39, 56, 43, 13], [40, 10, 43, 24, "style"], [40, 15, 43, 29], [40, 17, 43, 31, "style"], [40, 22, 43, 37], [41, 10, 43, 37, "children"], [41, 18, 43, 37], [41, 20, 44, 11, "cleanMessage"], [42, 8, 44, 23], [42, 11, 43, 19, "key"], [42, 14, 45, 14], [42, 15, 46, 6], [42, 16, 46, 7], [43, 6, 47, 4], [44, 6, 49, 4, "length"], [44, 12, 49, 10], [44, 16, 49, 14, "cleanMessage"], [44, 28, 49, 26], [44, 29, 49, 27, "length"], [44, 35, 49, 33], [45, 4, 50, 2], [45, 5, 50, 3], [46, 4, 52, 2], [46, 10, 52, 8, "lastOffset"], [46, 20, 52, 18], [46, 23, 52, 21, "substitutions"], [46, 36, 52, 34], [46, 37, 52, 35, "reduce"], [46, 43, 52, 41], [46, 44, 52, 42], [46, 45, 52, 43, "prevOffset"], [46, 55, 52, 53], [46, 57, 52, 55, "substitution"], [46, 69, 52, 67], [46, 71, 52, 69, "index"], [46, 76, 52, 74], [46, 81, 52, 79], [47, 6, 53, 4], [47, 12, 53, 10, "key"], [47, 15, 53, 13], [47, 18, 53, 16, "String"], [47, 24, 53, 22], [47, 25, 53, 23, "index"], [47, 30, 53, 28], [47, 31, 53, 29], [48, 6, 55, 4], [48, 10, 55, 8, "substitution"], [48, 22, 55, 20], [48, 23, 55, 21, "offset"], [48, 29, 55, 27], [48, 32, 55, 30, "prevOffset"], [48, 42, 55, 40], [48, 44, 55, 42], [49, 8, 56, 6], [49, 14, 56, 12, "prevPart"], [49, 22, 56, 20], [49, 25, 56, 23, "content"], [49, 32, 56, 30], [49, 33, 56, 31, "substr"], [49, 39, 56, 37], [49, 40, 56, 38, "prevOffset"], [49, 50, 56, 48], [49, 52, 56, 50, "substitution"], [49, 64, 56, 62], [49, 65, 56, 63, "offset"], [49, 71, 56, 69], [49, 74, 56, 72, "prevOffset"], [49, 84, 56, 82], [49, 85, 56, 83], [50, 8, 58, 6, "createUnderLength"], [50, 25, 58, 23], [50, 26, 58, 24, "key"], [50, 29, 58, 27], [50, 31, 58, 29, "prevPart"], [50, 39, 58, 37], [50, 40, 58, 38], [51, 6, 59, 4], [52, 6, 61, 4], [52, 12, 61, 10, "substititionPart"], [52, 28, 61, 26], [52, 31, 61, 29, "content"], [52, 38, 61, 36], [52, 39, 61, 37, "substr"], [52, 45, 61, 43], [52, 46, 61, 44, "substitution"], [52, 58, 61, 56], [52, 59, 61, 57, "offset"], [52, 65, 61, 63], [52, 67, 61, 65, "substitution"], [52, 79, 61, 77], [52, 80, 61, 78, "length"], [52, 86, 61, 84], [52, 87, 61, 85], [53, 6, 63, 4, "createUnderLength"], [53, 23, 63, 21], [53, 24, 63, 22, "key"], [53, 27, 63, 25], [53, 30, 63, 28], [53, 34, 63, 32], [53, 36, 63, 34, "substititionPart"], [53, 52, 63, 50], [53, 54, 63, 52, "substitutionStyle"], [53, 71, 63, 69], [53, 72, 63, 70], [54, 6, 64, 4], [54, 13, 64, 11, "substitution"], [54, 25, 64, 23], [54, 26, 64, 24, "offset"], [54, 32, 64, 30], [54, 35, 64, 33, "substitution"], [54, 47, 64, 45], [54, 48, 64, 46, "length"], [54, 54, 64, 52], [55, 4, 65, 2], [55, 5, 65, 3], [55, 7, 65, 5], [55, 8, 65, 6], [55, 9, 65, 7], [56, 4, 67, 2], [56, 8, 67, 6, "lastOffset"], [56, 18, 67, 16], [56, 21, 67, 19, "content"], [56, 28, 67, 26], [56, 29, 67, 27, "length"], [56, 35, 67, 33], [56, 37, 67, 35], [57, 6, 68, 4], [57, 12, 68, 10, "lastPart"], [57, 20, 68, 18], [57, 23, 68, 21, "content"], [57, 30, 68, 28], [57, 31, 68, 29, "substr"], [57, 37, 68, 35], [57, 38, 68, 36, "lastOffset"], [57, 48, 68, 46], [57, 49, 68, 47], [58, 6, 69, 4, "createUnderLength"], [58, 23, 69, 21], [58, 24, 69, 22], [58, 28, 69, 26], [58, 30, 69, 28, "lastPart"], [58, 38, 69, 36], [58, 39, 69, 37], [59, 4, 70, 2], [60, 4, 72, 2], [60, 11, 72, 9], [60, 15, 72, 9, "_jsxRuntime"], [60, 26, 72, 9], [60, 27, 72, 9, "jsx"], [60, 30, 72, 9], [60, 32, 72, 9, "_jsxRuntime"], [60, 43, 72, 9], [60, 44, 72, 9, "Fragment"], [60, 52, 72, 9], [61, 6, 72, 9, "children"], [61, 14, 72, 9], [61, 16, 72, 12, "elements"], [62, 4, 72, 20], [62, 5, 72, 23], [62, 6, 72, 24], [63, 2, 73, 0], [64, 0, 73, 1], [64, 3]], "functionMap": {"names": ["<global>", "cleanContent", "LogBoxMessage", "createUnderLength", "substitutions.reduce$argument_0"], "mappings": "AAA;qBCmB;0EDC;OEE;4BCW;GDgB;0CEE;GFa;CFQ"}}, "type": "js/module"}]}