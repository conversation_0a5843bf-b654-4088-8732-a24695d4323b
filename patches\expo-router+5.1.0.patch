diff --git a/node_modules/expo-router/build/views/Sitemap.js b/node_modules/expo-router/build/views/Sitemap.js
index a61428d..063c280 100644
--- a/node_modules/expo-router/build/views/Sitemap.js
+++ b/node_modules/expo-router/build/views/Sitemap.js
@@ -136,6 +136,12 @@ const styles = react_native_1.StyleSheet.create({
         paddingVertical: 16,
         borderBottomWidth: 1,
         borderColor: '#313538',
+        ...(react_native_1.Platform.OS === 'web'
+    ? {
+        // CSS-compatible shadow on web
+        boxShadow: '0px 3px 3px rgba(0, 0, 0, 0.33)',
+      }
+    : {
         shadowColor: '#000',
         shadowOffset: {
             width: 0,
@@ -143,6 +149,7 @@ const styles = react_native_1.StyleSheet.create({
         },
         shadowOpacity: 0.33,
         shadowRadius: 3,
+    }),
         elevation: 8,
     },
     headerContent: {
