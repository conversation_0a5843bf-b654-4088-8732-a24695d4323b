{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 50, "index": 50}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createSnapshotFriendlyRef = createSnapshotFriendlyRef;\n  var _react = require(_dependencyMap[0], \"react\");\n  /**\n   * Create a React ref object that is friendly for snapshots.\n   * It will be represented as `[React.ref]` in snapshots.\n   * @returns A React ref object.\n   */\n  function createSnapshotFriendlyRef() {\n    return /*#__PURE__*/(0, _react.createRef)();\n  }\n});", "lineCount": 15, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 3, 0], [8, 0, 4, 0], [9, 0, 5, 0], [10, 0, 6, 0], [11, 0, 7, 0], [12, 2, 8, 7], [12, 11, 8, 16, "createSnapshotFriendlyRef"], [12, 36, 8, 41, "createSnapshotFriendlyRef"], [12, 37, 8, 41], [12, 39, 8, 68], [13, 4, 9, 2], [13, 24, 9, 9], [13, 28, 9, 9, "createRef"], [13, 44, 9, 18], [13, 46, 9, 22], [13, 47, 9, 23], [14, 2, 10, 0], [15, 0, 10, 1], [15, 3]], "functionMap": {"names": ["<global>", "createSnapshotFriendlyRef"], "mappings": "AAA;OCO;CDE"}}, "type": "js/module"}]}