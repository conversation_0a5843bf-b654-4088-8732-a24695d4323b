{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PointerType = void 0;\n  let PointerType = exports.PointerType = void 0;\n  (function (PointerType) {\n    PointerType[PointerType[\"TOUCH\"] = 0] = \"TOUCH\";\n    PointerType[PointerType[\"STYLUS\"] = 1] = \"STYLUS\";\n    PointerType[PointerType[\"MOUSE\"] = 2] = \"MOUSE\";\n    PointerType[PointerType[\"KEY\"] = 3] = \"KEY\";\n    PointerType[PointerType[\"OTHER\"] = 4] = \"OTHER\";\n  })(PointerType || (exports.PointerType = PointerType = {}));\n});", "lineCount": 14, "map": [[6, 2, 1, 7], [6, 6, 1, 11, "PointerType"], [6, 17, 1, 22], [6, 20, 1, 22, "exports"], [6, 27, 1, 22], [6, 28, 1, 22, "PointerType"], [6, 39, 1, 22], [7, 2, 3, 0], [7, 3, 3, 1], [7, 13, 3, 11, "PointerType"], [7, 24, 3, 22], [7, 26, 3, 24], [8, 4, 4, 2, "PointerType"], [8, 15, 4, 13], [8, 16, 4, 14, "PointerType"], [8, 27, 4, 25], [8, 28, 4, 26], [8, 35, 4, 33], [8, 36, 4, 34], [8, 39, 4, 37], [8, 40, 4, 38], [8, 41, 4, 39], [8, 44, 4, 42], [8, 51, 4, 49], [9, 4, 5, 2, "PointerType"], [9, 15, 5, 13], [9, 16, 5, 14, "PointerType"], [9, 27, 5, 25], [9, 28, 5, 26], [9, 36, 5, 34], [9, 37, 5, 35], [9, 40, 5, 38], [9, 41, 5, 39], [9, 42, 5, 40], [9, 45, 5, 43], [9, 53, 5, 51], [10, 4, 6, 2, "PointerType"], [10, 15, 6, 13], [10, 16, 6, 14, "PointerType"], [10, 27, 6, 25], [10, 28, 6, 26], [10, 35, 6, 33], [10, 36, 6, 34], [10, 39, 6, 37], [10, 40, 6, 38], [10, 41, 6, 39], [10, 44, 6, 42], [10, 51, 6, 49], [11, 4, 7, 2, "PointerType"], [11, 15, 7, 13], [11, 16, 7, 14, "PointerType"], [11, 27, 7, 25], [11, 28, 7, 26], [11, 33, 7, 31], [11, 34, 7, 32], [11, 37, 7, 35], [11, 38, 7, 36], [11, 39, 7, 37], [11, 42, 7, 40], [11, 47, 7, 45], [12, 4, 8, 2, "PointerType"], [12, 15, 8, 13], [12, 16, 8, 14, "PointerType"], [12, 27, 8, 25], [12, 28, 8, 26], [12, 35, 8, 33], [12, 36, 8, 34], [12, 39, 8, 37], [12, 40, 8, 38], [12, 41, 8, 39], [12, 44, 8, 42], [12, 51, 8, 49], [13, 2, 9, 0], [13, 3, 9, 1], [13, 5, 9, 3, "PointerType"], [13, 16, 9, 14], [13, 21, 9, 14, "exports"], [13, 28, 9, 14], [13, 29, 9, 14, "PointerType"], [13, 40, 9, 14], [13, 43, 9, 19, "PointerType"], [13, 54, 9, 30], [13, 57, 9, 33], [13, 58, 9, 34], [13, 59, 9, 35], [13, 60, 9, 36], [13, 61, 9, 37], [14, 0, 9, 38], [14, 3]], "functionMap": {"names": ["<global>", "<anonymous>"], "mappings": "AAA;CCE;CDM"}}, "type": "js/module"}]}