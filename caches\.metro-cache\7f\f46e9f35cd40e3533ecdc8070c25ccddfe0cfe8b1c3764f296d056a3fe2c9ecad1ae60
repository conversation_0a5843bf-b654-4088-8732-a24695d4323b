{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./unitlessNumbers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 181}, "end": {"line": 10, "column": 48, "index": 229}}], "key": "as9rTONYBOFjI3SZ5rmDXjH3bXo=", "exportNames": ["*"]}}, {"name": "./normalizeColor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 230}, "end": {"line": 11, "column": 46, "index": 276}}], "key": "GyNQ1bhL9QIDrzZO2OBdzbX9ikY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = normalizeValueWithProperty;\n  var _unitlessNumbers = _interopRequireDefault(require(_dependencyMap[1], \"./unitlessNumbers\"));\n  var _normalizeColor = _interopRequireDefault(require(_dependencyMap[2], \"./normalizeColor\"));\n  /**\n   * Copyright (c) Nicolas <PERSON>.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var colorProps = {\n    backgroundColor: true,\n    borderColor: true,\n    borderTopColor: true,\n    borderRightColor: true,\n    borderBottomColor: true,\n    borderLeftColor: true,\n    color: true,\n    shadowColor: true,\n    textDecorationColor: true,\n    textShadowColor: true\n  };\n  function normalizeValueWithProperty(value, property) {\n    var returnValue = value;\n    if ((property == null || !_unitlessNumbers.default[property]) && typeof value === 'number') {\n      returnValue = value + \"px\";\n    } else if (property != null && colorProps[property]) {\n      returnValue = (0, _normalizeColor.default)(value);\n    }\n    return returnValue;\n  }\n});", "lineCount": 39, "map": [[7, 2, 10, 0], [7, 6, 10, 0, "_unitlessNumbers"], [7, 22, 10, 0], [7, 25, 10, 0, "_interopRequireDefault"], [7, 47, 10, 0], [7, 48, 10, 0, "require"], [7, 55, 10, 0], [7, 56, 10, 0, "_dependencyMap"], [7, 70, 10, 0], [8, 2, 11, 0], [8, 6, 11, 0, "_normalizeColor"], [8, 21, 11, 0], [8, 24, 11, 0, "_interopRequireDefault"], [8, 46, 11, 0], [8, 47, 11, 0, "require"], [8, 54, 11, 0], [8, 55, 11, 0, "_dependencyMap"], [8, 69, 11, 0], [9, 2, 1, 0], [10, 0, 2, 0], [11, 0, 3, 0], [12, 0, 4, 0], [13, 0, 5, 0], [14, 0, 6, 0], [15, 0, 7, 0], [16, 0, 8, 0], [18, 2, 12, 0], [18, 6, 12, 4, "colorProps"], [18, 16, 12, 14], [18, 19, 12, 17], [19, 4, 13, 2, "backgroundColor"], [19, 19, 13, 17], [19, 21, 13, 19], [19, 25, 13, 23], [20, 4, 14, 2, "borderColor"], [20, 15, 14, 13], [20, 17, 14, 15], [20, 21, 14, 19], [21, 4, 15, 2, "borderTopColor"], [21, 18, 15, 16], [21, 20, 15, 18], [21, 24, 15, 22], [22, 4, 16, 2, "borderRightColor"], [22, 20, 16, 18], [22, 22, 16, 20], [22, 26, 16, 24], [23, 4, 17, 2, "borderBottomColor"], [23, 21, 17, 19], [23, 23, 17, 21], [23, 27, 17, 25], [24, 4, 18, 2, "borderLeftColor"], [24, 19, 18, 17], [24, 21, 18, 19], [24, 25, 18, 23], [25, 4, 19, 2, "color"], [25, 9, 19, 7], [25, 11, 19, 9], [25, 15, 19, 13], [26, 4, 20, 2, "shadowColor"], [26, 15, 20, 13], [26, 17, 20, 15], [26, 21, 20, 19], [27, 4, 21, 2, "textDecorationColor"], [27, 23, 21, 21], [27, 25, 21, 23], [27, 29, 21, 27], [28, 4, 22, 2, "textShadowColor"], [28, 19, 22, 17], [28, 21, 22, 19], [29, 2, 23, 0], [29, 3, 23, 1], [30, 2, 24, 15], [30, 11, 24, 24, "normalizeValueWithProperty"], [30, 37, 24, 50, "normalizeValueWithProperty"], [30, 38, 24, 51, "value"], [30, 43, 24, 56], [30, 45, 24, 58, "property"], [30, 53, 24, 66], [30, 55, 24, 68], [31, 4, 25, 2], [31, 8, 25, 6, "returnValue"], [31, 19, 25, 17], [31, 22, 25, 20, "value"], [31, 27, 25, 25], [32, 4, 26, 2], [32, 8, 26, 6], [32, 9, 26, 7, "property"], [32, 17, 26, 15], [32, 21, 26, 19], [32, 25, 26, 23], [32, 29, 26, 27], [32, 30, 26, 28, "unitlessNumbers"], [32, 54, 26, 43], [32, 55, 26, 44, "property"], [32, 63, 26, 52], [32, 64, 26, 53], [32, 69, 26, 58], [32, 76, 26, 65, "value"], [32, 81, 26, 70], [32, 86, 26, 75], [32, 94, 26, 83], [32, 96, 26, 85], [33, 6, 27, 4, "returnValue"], [33, 17, 27, 15], [33, 20, 27, 18, "value"], [33, 25, 27, 23], [33, 28, 27, 26], [33, 32, 27, 30], [34, 4, 28, 2], [34, 5, 28, 3], [34, 11, 28, 9], [34, 15, 28, 13, "property"], [34, 23, 28, 21], [34, 27, 28, 25], [34, 31, 28, 29], [34, 35, 28, 33, "colorProps"], [34, 45, 28, 43], [34, 46, 28, 44, "property"], [34, 54, 28, 52], [34, 55, 28, 53], [34, 57, 28, 55], [35, 6, 29, 4, "returnValue"], [35, 17, 29, 15], [35, 20, 29, 18], [35, 24, 29, 18, "normalizeColor"], [35, 47, 29, 32], [35, 49, 29, 33, "value"], [35, 54, 29, 38], [35, 55, 29, 39], [36, 4, 30, 2], [37, 4, 31, 2], [37, 11, 31, 9, "returnValue"], [37, 22, 31, 20], [38, 2, 32, 0], [39, 0, 32, 1], [39, 3]], "functionMap": {"names": ["<global>", "normalizeValueWithProperty"], "mappings": "AAA;eCuB"}}, "type": "js/module"}]}