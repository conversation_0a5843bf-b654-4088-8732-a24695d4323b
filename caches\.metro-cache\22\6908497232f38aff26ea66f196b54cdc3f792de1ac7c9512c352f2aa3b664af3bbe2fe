{"dependencies": [{"name": "./dist/transform-localize-style", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 17, "index": 190}, "end": {"line": 8, "column": 59, "index": 232}}], "key": "2hueGXY0WUdke0LR5YNmhmU2sc4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) <PERSON>\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  module.exports = require(_dependencyMap[0], \"./dist/transform-localize-style\");\n});", "lineCount": 10, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [9, 2, 8, 0, "module"], [9, 8, 8, 6], [9, 9, 8, 7, "exports"], [9, 16, 8, 14], [9, 19, 8, 17, "require"], [9, 26, 8, 24], [9, 27, 8, 24, "_dependencyMap"], [9, 41, 8, 24], [9, 79, 8, 58], [9, 80, 8, 59], [10, 0, 8, 60], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}