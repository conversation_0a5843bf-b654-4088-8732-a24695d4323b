{"dependencies": [{"name": "react-native-web/dist/index", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "55efhPHw3gz2FoQtoN2yI1VuhbM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.StyleSheet = void 0;\n  const react_native_1 = require(_dependencyMap[0], \"react-native-web/dist/index\");\n  const documentStyle = globalThis.window?.getComputedStyle(globalThis.window?.document.documentElement);\n  const commonStyleSheet = {\n    getFlag(name) {\n      return documentStyle?.getPropertyValue(`--css-interop-${name}`);\n    },\n    unstable_hook_onClassName() {},\n    register(_options) {\n      throw new Error(\"Stylesheet.register is not available on web\");\n    },\n    registerCompiled(_options) {\n      throw new Error(\"Stylesheet.registerCompiled is not available on web\");\n    },\n    getGlobalStyle() {\n      throw new Error(\"Stylesheet.getGlobalStyle is not available on web\");\n    }\n  };\n  exports.StyleSheet = Object.assign({}, commonStyleSheet, react_native_1.StyleSheet);\n});", "lineCount": 26, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "StyleSheet"], [7, 20, 3, 18], [7, 23, 3, 21], [7, 28, 3, 26], [7, 29, 3, 27], [8, 2, 3, 28], [8, 8, 3, 28, "react_native_1"], [8, 22, 3, 28], [8, 25, 3, 28, "require"], [8, 32, 3, 28], [8, 33, 3, 28, "_dependencyMap"], [8, 47, 3, 28], [9, 2, 5, 0], [9, 8, 5, 6, "documentStyle"], [9, 21, 5, 19], [9, 24, 5, 22, "globalThis"], [9, 34, 5, 32], [9, 35, 5, 33, "window"], [9, 41, 5, 39], [9, 43, 5, 41, "getComputedStyle"], [9, 59, 5, 57], [9, 60, 5, 58, "globalThis"], [9, 70, 5, 68], [9, 71, 5, 69, "window"], [9, 77, 5, 75], [9, 79, 5, 77, "document"], [9, 87, 5, 85], [9, 88, 5, 86, "documentElement"], [9, 103, 5, 101], [9, 104, 5, 102], [10, 2, 6, 0], [10, 8, 6, 6, "commonStyleSheet"], [10, 24, 6, 22], [10, 27, 6, 25], [11, 4, 7, 4, "getFlag"], [11, 11, 7, 11, "getFlag"], [11, 12, 7, 12, "name"], [11, 16, 7, 16], [11, 18, 7, 18], [12, 6, 8, 8], [12, 13, 8, 15, "documentStyle"], [12, 26, 8, 28], [12, 28, 8, 30, "getPropertyValue"], [12, 44, 8, 46], [12, 45, 8, 47], [12, 62, 8, 64, "name"], [12, 66, 8, 68], [12, 68, 8, 70], [12, 69, 8, 71], [13, 4, 9, 4], [13, 5, 9, 5], [14, 4, 10, 4, "unstable_hook_onClassName"], [14, 29, 10, 29, "unstable_hook_onClassName"], [14, 30, 10, 29], [14, 32, 10, 32], [14, 33, 10, 34], [14, 34, 10, 35], [15, 4, 11, 4, "register"], [15, 12, 11, 12, "register"], [15, 13, 11, 13, "_options"], [15, 21, 11, 21], [15, 23, 11, 23], [16, 6, 12, 8], [16, 12, 12, 14], [16, 16, 12, 18, "Error"], [16, 21, 12, 23], [16, 22, 12, 24], [16, 67, 12, 69], [16, 68, 12, 70], [17, 4, 13, 4], [17, 5, 13, 5], [18, 4, 14, 4, "registerCompiled"], [18, 20, 14, 20, "registerCompiled"], [18, 21, 14, 21, "_options"], [18, 29, 14, 29], [18, 31, 14, 31], [19, 6, 15, 8], [19, 12, 15, 14], [19, 16, 15, 18, "Error"], [19, 21, 15, 23], [19, 22, 15, 24], [19, 75, 15, 77], [19, 76, 15, 78], [20, 4, 16, 4], [20, 5, 16, 5], [21, 4, 17, 4, "getGlobalStyle"], [21, 18, 17, 18, "getGlobalStyle"], [21, 19, 17, 18], [21, 21, 17, 21], [22, 6, 18, 8], [22, 12, 18, 14], [22, 16, 18, 18, "Error"], [22, 21, 18, 23], [22, 22, 18, 24], [22, 73, 18, 75], [22, 74, 18, 76], [23, 4, 19, 4], [24, 2, 20, 0], [24, 3, 20, 1], [25, 2, 21, 0, "exports"], [25, 9, 21, 7], [25, 10, 21, 8, "StyleSheet"], [25, 20, 21, 18], [25, 23, 21, 21, "Object"], [25, 29, 21, 27], [25, 30, 21, 28, "assign"], [25, 36, 21, 34], [25, 37, 21, 35], [25, 38, 21, 36], [25, 39, 21, 37], [25, 41, 21, 39, "commonStyleSheet"], [25, 57, 21, 55], [25, 59, 21, 57, "react_native_1"], [25, 73, 21, 71], [25, 74, 21, 72, "StyleSheet"], [25, 84, 21, 82], [25, 85, 21, 83], [26, 0, 21, 84], [26, 3]], "functionMap": {"names": ["<global>", "commonStyleSheet.getFlag", "commonStyleSheet.unstable_hook_onClassName", "commonStyleSheet.register", "commonStyleSheet.registerCompiled", "commonStyleSheet.getGlobalStyle"], "mappings": "AAA;ICM;KDE;IEC,+BF;IGC;KHE;IIC;KJE;IKC;KLE"}}, "type": "js/module"}]}