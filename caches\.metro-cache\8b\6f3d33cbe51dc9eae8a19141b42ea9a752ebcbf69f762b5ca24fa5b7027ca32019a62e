{"dependencies": [{"name": "./dom-hooks", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 16}, "end": {"line": 3, "column": 28, "index": 44}}], "key": "RacIiQ19659ITo7DJIPHCSYbE6o=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {\n    IS_DOM: true\n  };\n  exports.IS_DOM = void 0;\n  var _domHooks = require(_dependencyMap[0], \"./dom-hooks\");\n  Object.keys(_domHooks).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _domHooks[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _domHooks[key];\n      }\n    });\n  });\n  // Native file\n\n  // TODO: Maybe this could be a bundler global instead.\n  /** @returns `true` when the current JS running in a DOM Component environment. */\n  var IS_DOM = exports.IS_DOM = false;\n});", "lineCount": 26, "map": [[9, 2, 3, 0], [9, 6, 3, 0, "_dom<PERSON><PERSON>s"], [9, 15, 3, 0], [9, 18, 3, 0, "require"], [9, 25, 3, 0], [9, 26, 3, 0, "_dependencyMap"], [9, 40, 3, 0], [10, 2, 3, 0, "Object"], [10, 8, 3, 0], [10, 9, 3, 0, "keys"], [10, 13, 3, 0], [10, 14, 3, 0, "_dom<PERSON><PERSON>s"], [10, 23, 3, 0], [10, 25, 3, 0, "for<PERSON>ach"], [10, 32, 3, 0], [10, 43, 3, 0, "key"], [10, 46, 3, 0], [11, 4, 3, 0], [11, 8, 3, 0, "key"], [11, 11, 3, 0], [11, 29, 3, 0, "key"], [11, 32, 3, 0], [12, 4, 3, 0], [12, 8, 3, 0, "Object"], [12, 14, 3, 0], [12, 15, 3, 0, "prototype"], [12, 24, 3, 0], [12, 25, 3, 0, "hasOwnProperty"], [12, 39, 3, 0], [12, 40, 3, 0, "call"], [12, 44, 3, 0], [12, 45, 3, 0, "_exportNames"], [12, 57, 3, 0], [12, 59, 3, 0, "key"], [12, 62, 3, 0], [13, 4, 3, 0], [13, 8, 3, 0, "key"], [13, 11, 3, 0], [13, 15, 3, 0, "exports"], [13, 22, 3, 0], [13, 26, 3, 0, "exports"], [13, 33, 3, 0], [13, 34, 3, 0, "key"], [13, 37, 3, 0], [13, 43, 3, 0, "_dom<PERSON><PERSON>s"], [13, 52, 3, 0], [13, 53, 3, 0, "key"], [13, 56, 3, 0], [14, 4, 3, 0, "Object"], [14, 10, 3, 0], [14, 11, 3, 0, "defineProperty"], [14, 25, 3, 0], [14, 26, 3, 0, "exports"], [14, 33, 3, 0], [14, 35, 3, 0, "key"], [14, 38, 3, 0], [15, 6, 3, 0, "enumerable"], [15, 16, 3, 0], [16, 6, 3, 0, "get"], [16, 9, 3, 0], [16, 20, 3, 0, "get"], [16, 21, 3, 0], [17, 8, 3, 0], [17, 15, 3, 0, "_dom<PERSON><PERSON>s"], [17, 24, 3, 0], [17, 25, 3, 0, "key"], [17, 28, 3, 0], [18, 6, 3, 0], [19, 4, 3, 0], [20, 2, 3, 0], [21, 2, 1, 0], [23, 2, 6, 0], [24, 2, 7, 0], [25, 2, 8, 7], [25, 6, 8, 13, "IS_DOM"], [25, 12, 8, 19], [25, 15, 8, 19, "exports"], [25, 22, 8, 19], [25, 23, 8, 19, "IS_DOM"], [25, 29, 8, 19], [25, 32, 8, 22], [25, 37, 8, 27], [26, 0, 8, 28], [26, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}