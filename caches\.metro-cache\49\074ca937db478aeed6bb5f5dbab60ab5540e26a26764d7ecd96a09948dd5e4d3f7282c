{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../web/tools/NodeManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 54, "index": 54}}], "key": "VzkDGPV9K3TFfIVgurtLnB7A4YA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.GestureStateManager = void 0;\n  var _NodeManager = _interopRequireDefault(require(_dependencyMap[1], \"../../web/tools/NodeManager\"));\n  const GestureStateManager = exports.GestureStateManager = {\n    create(handlerTag) {\n      return {\n        begin: () => {\n          _NodeManager.default.getHandler(handlerTag).begin();\n        },\n        activate: () => {\n          _NodeManager.default.getHandler(handlerTag).activate(true);\n        },\n        fail: () => {\n          _NodeManager.default.getHandler(handlerTag).fail();\n        },\n        end: () => {\n          _NodeManager.default.getHandler(handlerTag).end();\n        }\n      };\n    }\n  };\n});", "lineCount": 26, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_NodeManager"], [7, 18, 1, 0], [7, 21, 1, 0, "_interopRequireDefault"], [7, 43, 1, 0], [7, 44, 1, 0, "require"], [7, 51, 1, 0], [7, 52, 1, 0, "_dependencyMap"], [7, 66, 1, 0], [8, 2, 2, 7], [8, 8, 2, 13, "GestureStateManager"], [8, 27, 2, 32], [8, 30, 2, 32, "exports"], [8, 37, 2, 32], [8, 38, 2, 32, "GestureStateManager"], [8, 57, 2, 32], [8, 60, 2, 35], [9, 4, 3, 2, "create"], [9, 10, 3, 8, "create"], [9, 11, 3, 9, "handlerTag"], [9, 21, 3, 19], [9, 23, 3, 21], [10, 6, 4, 4], [10, 13, 4, 11], [11, 8, 5, 6, "begin"], [11, 13, 5, 11], [11, 15, 5, 13, "begin"], [11, 16, 5, 13], [11, 21, 5, 19], [12, 10, 6, 8, "NodeManager"], [12, 30, 6, 19], [12, 31, 6, 20, "<PERSON><PERSON><PERSON><PERSON>"], [12, 41, 6, 30], [12, 42, 6, 31, "handlerTag"], [12, 52, 6, 41], [12, 53, 6, 42], [12, 54, 6, 43, "begin"], [12, 59, 6, 48], [12, 60, 6, 49], [12, 61, 6, 50], [13, 8, 7, 6], [13, 9, 7, 7], [14, 8, 8, 6, "activate"], [14, 16, 8, 14], [14, 18, 8, 16, "activate"], [14, 19, 8, 16], [14, 24, 8, 22], [15, 10, 9, 8, "NodeManager"], [15, 30, 9, 19], [15, 31, 9, 20, "<PERSON><PERSON><PERSON><PERSON>"], [15, 41, 9, 30], [15, 42, 9, 31, "handlerTag"], [15, 52, 9, 41], [15, 53, 9, 42], [15, 54, 9, 43, "activate"], [15, 62, 9, 51], [15, 63, 9, 52], [15, 67, 9, 56], [15, 68, 9, 57], [16, 8, 10, 6], [16, 9, 10, 7], [17, 8, 11, 6, "fail"], [17, 12, 11, 10], [17, 14, 11, 12, "fail"], [17, 15, 11, 12], [17, 20, 11, 18], [18, 10, 12, 8, "NodeManager"], [18, 30, 12, 19], [18, 31, 12, 20, "<PERSON><PERSON><PERSON><PERSON>"], [18, 41, 12, 30], [18, 42, 12, 31, "handlerTag"], [18, 52, 12, 41], [18, 53, 12, 42], [18, 54, 12, 43, "fail"], [18, 58, 12, 47], [18, 59, 12, 48], [18, 60, 12, 49], [19, 8, 13, 6], [19, 9, 13, 7], [20, 8, 14, 6, "end"], [20, 11, 14, 9], [20, 13, 14, 11, "end"], [20, 14, 14, 11], [20, 19, 14, 17], [21, 10, 15, 8, "NodeManager"], [21, 30, 15, 19], [21, 31, 15, 20, "<PERSON><PERSON><PERSON><PERSON>"], [21, 41, 15, 30], [21, 42, 15, 31, "handlerTag"], [21, 52, 15, 41], [21, 53, 15, 42], [21, 54, 15, 43, "end"], [21, 57, 15, 46], [21, 58, 15, 47], [21, 59, 15, 48], [22, 8, 16, 6], [23, 6, 17, 4], [23, 7, 17, 5], [24, 4, 18, 2], [25, 2, 20, 0], [25, 3, 20, 1], [26, 0, 20, 2], [26, 3]], "functionMap": {"names": ["<global>", "GestureStateManager.create", "begin", "activate", "fail", "end"], "mappings": "AAA;ECE;aCE;ODE;gBEC;OFE;YGC;OHE;WIC;OJE;GDE"}}, "type": "js/module"}]}