{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 6, "column": 15, "index": 93}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 94}, "end": {"line": 7, "column": 79, "index": 173}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 174}, "end": {"line": 8, "column": 34, "index": 208}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}, {"name": "./RNCWebViewNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 210}, "end": {"line": 10, "column": 80, "index": 290}}], "key": "bvGkhef05Lvftp1h1myCK4Uk5Ng=", "exportNames": ["*"]}}, {"name": "./NativeRNCWebViewModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 291}, "end": {"line": 11, "column": 56, "index": 347}}], "key": "aiDFCWW8WOyFXCYU+cAmycjjiq8=", "exportNames": ["*"]}}, {"name": "./WebViewShared", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 349}, "end": {"line": 18, "column": 25, "index": 474}}], "key": "CwFGpcSSrpSAhfpTFlpOM/82cxQ=", "exportNames": ["*"]}}, {"name": "./WebView.styles", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 577}, "end": {"line": 25, "column": 38, "index": 615}}], "key": "HTBpZF7AYpj3txNIUSqg9DLCWlg=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/slicedToArray\"));\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[4], \"react\"));\n  var _reactNative = require(_dependencyMap[5], \"react-native\");\n  var _invariant = _interopRequireDefault(require(_dependencyMap[6], \"invariant\"));\n  var _RNCWebViewNativeComponent = _interopRequireWildcard(require(_dependencyMap[7], \"./RNCWebViewNativeComponent\"));\n  var _NativeRNCWebViewModule = _interopRequireDefault(require(_dependencyMap[8], \"./NativeRNCWebViewModule\"));\n  var _WebViewShared = require(_dependencyMap[9], \"./WebViewShared\");\n  var _WebView = _interopRequireDefault(require(_dependencyMap[10], \"./WebView.styles\"));\n  var _jsxRuntime = require(_dependencyMap[11], \"react-native-css-interop/jsx-runtime\");\n  var _excluded = [\"fraudulentWebsiteWarningEnabled\", \"javaScriptEnabled\", \"cacheEnabled\", \"originWhitelist\", \"useSharedProcessPool\", \"textInteractionEnabled\", \"injectedJavaScript\", \"injectedJavaScriptBeforeContentLoaded\", \"injectedJavaScriptForMainFrameOnly\", \"injectedJavaScriptBeforeContentLoadedForMainFrameOnly\", \"injectedJavaScriptObject\", \"startInLoadingState\", \"onNavigationStateChange\", \"onLoadStart\", \"onError\", \"onLoad\", \"onLoadEnd\", \"onLoadProgress\", \"onContentProcessDidTerminate\", \"onFileDownload\", \"onHttpError\", \"onMessage\", \"onOpenWindow\", \"renderLoading\", \"renderError\", \"style\", \"containerStyle\", \"source\", \"nativeConfig\", \"allowsInlineMediaPlayback\", \"allowsPictureInPictureMediaPlayback\", \"allowsAirPlayForMediaPlayback\", \"mediaPlaybackRequiresUserAction\", \"dataDetectorTypes\", \"incognito\", \"decelerationRate\", \"onShouldStartLoadWithRequest\"];\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native-webview/src/WebView.ios.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var resolveAssetSource = _reactNative.Image.resolveAssetSource;\n  var processDecelerationRate = decelerationRate => {\n    var newDecelerationRate = decelerationRate;\n    if (newDecelerationRate === 'normal') {\n      newDecelerationRate = 0.998;\n    } else if (newDecelerationRate === 'fast') {\n      newDecelerationRate = 0.99;\n    }\n    return newDecelerationRate;\n  };\n  var useWarnIfChanges = (value, name) => {\n    var ref = (0, _react.useRef)(value);\n    if (ref.current !== value) {\n      console.warn(`Changes to property ${name} do nothing after the initial render.`);\n      ref.current = value;\n    }\n  };\n  var WebViewComponent = /*#__PURE__*/(0, _react.forwardRef)((_ref, ref) => {\n    var _ref$fraudulentWebsit = _ref.fraudulentWebsiteWarningEnabled,\n      fraudulentWebsiteWarningEnabled = _ref$fraudulentWebsit === void 0 ? true : _ref$fraudulentWebsit,\n      _ref$javaScriptEnable = _ref.javaScriptEnabled,\n      javaScriptEnabled = _ref$javaScriptEnable === void 0 ? true : _ref$javaScriptEnable,\n      _ref$cacheEnabled = _ref.cacheEnabled,\n      cacheEnabled = _ref$cacheEnabled === void 0 ? true : _ref$cacheEnabled,\n      _ref$originWhitelist = _ref.originWhitelist,\n      originWhitelist = _ref$originWhitelist === void 0 ? _WebViewShared.defaultOriginWhitelist : _ref$originWhitelist,\n      _ref$useSharedProcess = _ref.useSharedProcessPool,\n      useSharedProcessPool = _ref$useSharedProcess === void 0 ? true : _ref$useSharedProcess,\n      _ref$textInteractionE = _ref.textInteractionEnabled,\n      textInteractionEnabled = _ref$textInteractionE === void 0 ? true : _ref$textInteractionE,\n      injectedJavaScript = _ref.injectedJavaScript,\n      injectedJavaScriptBeforeContentLoaded = _ref.injectedJavaScriptBeforeContentLoaded,\n      _ref$injectedJavaScri = _ref.injectedJavaScriptForMainFrameOnly,\n      injectedJavaScriptForMainFrameOnly = _ref$injectedJavaScri === void 0 ? true : _ref$injectedJavaScri,\n      _ref$injectedJavaScri2 = _ref.injectedJavaScriptBeforeContentLoadedForMainFrameOnly,\n      injectedJavaScriptBeforeContentLoadedForMainFrameOnly = _ref$injectedJavaScri2 === void 0 ? true : _ref$injectedJavaScri2,\n      injectedJavaScriptObject = _ref.injectedJavaScriptObject,\n      startInLoadingState = _ref.startInLoadingState,\n      onNavigationStateChange = _ref.onNavigationStateChange,\n      onLoadStart = _ref.onLoadStart,\n      onError = _ref.onError,\n      onLoad = _ref.onLoad,\n      onLoadEnd = _ref.onLoadEnd,\n      onLoadProgress = _ref.onLoadProgress,\n      onContentProcessDidTerminateProp = _ref.onContentProcessDidTerminate,\n      onFileDownload = _ref.onFileDownload,\n      onHttpErrorProp = _ref.onHttpError,\n      onMessageProp = _ref.onMessage,\n      onOpenWindowProp = _ref.onOpenWindow,\n      renderLoading = _ref.renderLoading,\n      renderError = _ref.renderError,\n      style = _ref.style,\n      containerStyle = _ref.containerStyle,\n      source = _ref.source,\n      nativeConfig = _ref.nativeConfig,\n      allowsInlineMediaPlayback = _ref.allowsInlineMediaPlayback,\n      _ref$allowsPictureInP = _ref.allowsPictureInPictureMediaPlayback,\n      allowsPictureInPictureMediaPlayback = _ref$allowsPictureInP === void 0 ? true : _ref$allowsPictureInP,\n      allowsAirPlayForMediaPlayback = _ref.allowsAirPlayForMediaPlayback,\n      mediaPlaybackRequiresUserAction = _ref.mediaPlaybackRequiresUserAction,\n      dataDetectorTypes = _ref.dataDetectorTypes,\n      incognito = _ref.incognito,\n      decelerationRateProp = _ref.decelerationRate,\n      onShouldStartLoadWithRequestProp = _ref.onShouldStartLoadWithRequest,\n      otherProps = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var webViewRef = (0, _react.useRef)(null);\n    var onShouldStartLoadWithRequestCallback = (0, _react.useCallback)(function (shouldStart, _url) {\n      var lockIdentifier = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n      _NativeRNCWebViewModule.default.shouldStartLoadWithLockIdentifier(shouldStart, lockIdentifier);\n    }, []);\n    var _useWebViewLogic = (0, _WebViewShared.useWebViewLogic)({\n        onNavigationStateChange,\n        onLoad,\n        onError,\n        onHttpErrorProp,\n        onLoadEnd,\n        onLoadProgress,\n        onLoadStart,\n        onMessageProp,\n        onOpenWindowProp,\n        startInLoadingState,\n        originWhitelist,\n        onShouldStartLoadWithRequestProp,\n        onShouldStartLoadWithRequestCallback,\n        onContentProcessDidTerminateProp\n      }),\n      onLoadingStart = _useWebViewLogic.onLoadingStart,\n      onShouldStartLoadWithRequest = _useWebViewLogic.onShouldStartLoadWithRequest,\n      onMessage = _useWebViewLogic.onMessage,\n      viewState = _useWebViewLogic.viewState,\n      setViewState = _useWebViewLogic.setViewState,\n      lastErrorEvent = _useWebViewLogic.lastErrorEvent,\n      onHttpError = _useWebViewLogic.onHttpError,\n      onLoadingError = _useWebViewLogic.onLoadingError,\n      onLoadingFinish = _useWebViewLogic.onLoadingFinish,\n      onLoadingProgress = _useWebViewLogic.onLoadingProgress,\n      onOpenWindow = _useWebViewLogic.onOpenWindow,\n      onContentProcessDidTerminate = _useWebViewLogic.onContentProcessDidTerminate;\n    (0, _react.useImperativeHandle)(ref, () => ({\n      goForward: () => webViewRef.current && _RNCWebViewNativeComponent.Commands.goForward(webViewRef.current),\n      goBack: () => webViewRef.current && _RNCWebViewNativeComponent.Commands.goBack(webViewRef.current),\n      reload: () => {\n        setViewState('LOADING');\n        if (webViewRef.current) {\n          _RNCWebViewNativeComponent.Commands.reload(webViewRef.current);\n        }\n      },\n      stopLoading: () => webViewRef.current && _RNCWebViewNativeComponent.Commands.stopLoading(webViewRef.current),\n      postMessage: data => webViewRef.current && _RNCWebViewNativeComponent.Commands.postMessage(webViewRef.current, data),\n      injectJavaScript: data => webViewRef.current && _RNCWebViewNativeComponent.Commands.injectJavaScript(webViewRef.current, data),\n      requestFocus: () => webViewRef.current && _RNCWebViewNativeComponent.Commands.requestFocus(webViewRef.current),\n      clearCache: includeDiskFiles => webViewRef.current && _RNCWebViewNativeComponent.Commands.clearCache(webViewRef.current, includeDiskFiles)\n    }), [setViewState, webViewRef]);\n    useWarnIfChanges(allowsInlineMediaPlayback, 'allowsInlineMediaPlayback');\n    useWarnIfChanges(allowsPictureInPictureMediaPlayback, 'allowsPictureInPictureMediaPlayback');\n    useWarnIfChanges(allowsAirPlayForMediaPlayback, 'allowsAirPlayForMediaPlayback');\n    useWarnIfChanges(incognito, 'incognito');\n    useWarnIfChanges(mediaPlaybackRequiresUserAction, 'mediaPlaybackRequiresUserAction');\n    useWarnIfChanges(dataDetectorTypes, 'dataDetectorTypes');\n    var otherView = null;\n    if (viewState === 'LOADING') {\n      otherView = (renderLoading || _WebViewShared.defaultRenderLoading)();\n    } else if (viewState === 'ERROR') {\n      (0, _invariant.default)(lastErrorEvent != null, 'lastErrorEvent expected to be non-null');\n      otherView = (renderError || _WebViewShared.defaultRenderError)(lastErrorEvent?.domain, lastErrorEvent?.code ?? 0, lastErrorEvent?.description ?? '');\n    } else if (viewState !== 'IDLE') {\n      console.error(`RNCWebView invalid state encountered: ${viewState}`);\n    }\n    var webViewStyles = [_WebView.default.container, _WebView.default.webView, style];\n    var webViewContainerStyle = [_WebView.default.container, containerStyle];\n    var decelerationRate = processDecelerationRate(decelerationRateProp);\n    var NativeWebView = nativeConfig?.component || _RNCWebViewNativeComponent.default;\n    var sourceResolved = resolveAssetSource(source);\n    var newSource = typeof sourceResolved === 'object' ? Object.entries(sourceResolved).reduce((prev, _ref2) => {\n      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),\n        currKey = _ref3[0],\n        currValue = _ref3[1];\n      return {\n        ...prev,\n        [currKey]: currKey === 'headers' && currValue && typeof currValue === 'object' ? Object.entries(currValue).map(_ref4 => {\n          var _ref5 = (0, _slicedToArray2.default)(_ref4, 2),\n            key = _ref5[0],\n            value = _ref5[1];\n          return {\n            name: key,\n            value\n          };\n        }) : currValue\n      };\n    }, {}) : sourceResolved;\n    var webView = (0, _jsxRuntime.jsx)(NativeWebView, {\n      ...otherProps,\n      fraudulentWebsiteWarningEnabled: fraudulentWebsiteWarningEnabled,\n      javaScriptEnabled: javaScriptEnabled,\n      cacheEnabled: cacheEnabled,\n      useSharedProcessPool: useSharedProcessPool,\n      textInteractionEnabled: textInteractionEnabled,\n      decelerationRate: decelerationRate,\n      messagingEnabled: typeof onMessageProp === 'function',\n      messagingModuleName: \"\" // android ONLY\n      ,\n      onLoadingError: onLoadingError,\n      onLoadingFinish: onLoadingFinish,\n      onLoadingProgress: onLoadingProgress,\n      onFileDownload: onFileDownload,\n      onLoadingStart: onLoadingStart,\n      onHttpError: onHttpError,\n      onMessage: onMessage,\n      onOpenWindow: onOpenWindowProp && onOpenWindow,\n      hasOnOpenWindowEvent: onOpenWindowProp !== undefined,\n      onShouldStartLoadWithRequest: onShouldStartLoadWithRequest,\n      onContentProcessDidTerminate: onContentProcessDidTerminate,\n      injectedJavaScript: injectedJavaScript,\n      injectedJavaScriptBeforeContentLoaded: injectedJavaScriptBeforeContentLoaded,\n      injectedJavaScriptForMainFrameOnly: injectedJavaScriptForMainFrameOnly,\n      injectedJavaScriptBeforeContentLoadedForMainFrameOnly: injectedJavaScriptBeforeContentLoadedForMainFrameOnly,\n      injectedJavaScriptObject: JSON.stringify(injectedJavaScriptObject),\n      dataDetectorTypes: !dataDetectorTypes || Array.isArray(dataDetectorTypes) ? dataDetectorTypes : [dataDetectorTypes],\n      allowsAirPlayForMediaPlayback: allowsAirPlayForMediaPlayback,\n      allowsInlineMediaPlayback: allowsInlineMediaPlayback,\n      allowsPictureInPictureMediaPlayback: allowsPictureInPictureMediaPlayback,\n      incognito: incognito,\n      mediaPlaybackRequiresUserAction: mediaPlaybackRequiresUserAction,\n      newSource: newSource,\n      style: webViewStyles,\n      hasOnFileDownload: !!onFileDownload,\n      ref: webViewRef\n      // @ts-expect-error old arch only\n      ,\n      source: sourceResolved,\n      ...nativeConfig?.props\n    }, \"webViewKey\");\n    return (0, _jsxRuntime.jsxs)(_reactNative.View, {\n      style: webViewContainerStyle,\n      children: [webView, otherView]\n    });\n  });\n\n  // no native implementation for iOS, depends only on permissions\n  var isFileUploadSupported = /*#__PURE__*/function () {\n    var _ref6 = (0, _asyncToGenerator2.default)(function* () {\n      return true;\n    });\n    return function isFileUploadSupported() {\n      return _ref6.apply(this, arguments);\n    };\n  }();\n  var WebView = Object.assign(WebViewComponent, {\n    isFileUploadSupported\n  });\n  var _default = exports.default = WebView;\n});", "lineCount": 232, "map": [[10, 2, 1, 0], [10, 6, 1, 0, "_react"], [10, 12, 1, 0], [10, 15, 1, 0, "_interopRequireWildcard"], [10, 38, 1, 0], [10, 39, 1, 0, "require"], [10, 46, 1, 0], [10, 47, 1, 0, "_dependencyMap"], [10, 61, 1, 0], [11, 2, 7, 0], [11, 6, 7, 0, "_reactNative"], [11, 18, 7, 0], [11, 21, 7, 0, "require"], [11, 28, 7, 0], [11, 29, 7, 0, "_dependencyMap"], [11, 43, 7, 0], [12, 2, 8, 0], [12, 6, 8, 0, "_invariant"], [12, 16, 8, 0], [12, 19, 8, 0, "_interopRequireDefault"], [12, 41, 8, 0], [12, 42, 8, 0, "require"], [12, 49, 8, 0], [12, 50, 8, 0, "_dependencyMap"], [12, 64, 8, 0], [13, 2, 10, 0], [13, 6, 10, 0, "_RNCWebViewNativeComponent"], [13, 32, 10, 0], [13, 35, 10, 0, "_interopRequireWildcard"], [13, 58, 10, 0], [13, 59, 10, 0, "require"], [13, 66, 10, 0], [13, 67, 10, 0, "_dependencyMap"], [13, 81, 10, 0], [14, 2, 11, 0], [14, 6, 11, 0, "_NativeRNCWebViewModule"], [14, 29, 11, 0], [14, 32, 11, 0, "_interopRequireDefault"], [14, 54, 11, 0], [14, 55, 11, 0, "require"], [14, 62, 11, 0], [14, 63, 11, 0, "_dependencyMap"], [14, 77, 11, 0], [15, 2, 13, 0], [15, 6, 13, 0, "_WebViewShared"], [15, 20, 13, 0], [15, 23, 13, 0, "require"], [15, 30, 13, 0], [15, 31, 13, 0, "_dependencyMap"], [15, 45, 13, 0], [16, 2, 25, 0], [16, 6, 25, 0, "_WebView"], [16, 14, 25, 0], [16, 17, 25, 0, "_interopRequireDefault"], [16, 39, 25, 0], [16, 40, 25, 0, "require"], [16, 47, 25, 0], [16, 48, 25, 0, "_dependencyMap"], [16, 62, 25, 0], [17, 2, 25, 38], [17, 6, 25, 38, "_jsxRuntime"], [17, 17, 25, 38], [17, 20, 25, 38, "require"], [17, 27, 25, 38], [17, 28, 25, 38, "_dependencyMap"], [17, 42, 25, 38], [18, 2, 25, 38], [18, 6, 25, 38, "_excluded"], [18, 15, 25, 38], [19, 2, 25, 38], [19, 6, 25, 38, "_jsxFileName"], [19, 18, 25, 38], [20, 2, 25, 38], [20, 11, 25, 38, "_interopRequireWildcard"], [20, 35, 25, 38, "e"], [20, 36, 25, 38], [20, 38, 25, 38, "t"], [20, 39, 25, 38], [20, 68, 25, 38, "WeakMap"], [20, 75, 25, 38], [20, 81, 25, 38, "r"], [20, 82, 25, 38], [20, 89, 25, 38, "WeakMap"], [20, 96, 25, 38], [20, 100, 25, 38, "n"], [20, 101, 25, 38], [20, 108, 25, 38, "WeakMap"], [20, 115, 25, 38], [20, 127, 25, 38, "_interopRequireWildcard"], [20, 150, 25, 38], [20, 162, 25, 38, "_interopRequireWildcard"], [20, 163, 25, 38, "e"], [20, 164, 25, 38], [20, 166, 25, 38, "t"], [20, 167, 25, 38], [20, 176, 25, 38, "t"], [20, 177, 25, 38], [20, 181, 25, 38, "e"], [20, 182, 25, 38], [20, 186, 25, 38, "e"], [20, 187, 25, 38], [20, 188, 25, 38, "__esModule"], [20, 198, 25, 38], [20, 207, 25, 38, "e"], [20, 208, 25, 38], [20, 214, 25, 38, "o"], [20, 215, 25, 38], [20, 217, 25, 38, "i"], [20, 218, 25, 38], [20, 220, 25, 38, "f"], [20, 221, 25, 38], [20, 226, 25, 38, "__proto__"], [20, 235, 25, 38], [20, 243, 25, 38, "default"], [20, 250, 25, 38], [20, 252, 25, 38, "e"], [20, 253, 25, 38], [20, 270, 25, 38, "e"], [20, 271, 25, 38], [20, 294, 25, 38, "e"], [20, 295, 25, 38], [20, 320, 25, 38, "e"], [20, 321, 25, 38], [20, 330, 25, 38, "f"], [20, 331, 25, 38], [20, 337, 25, 38, "o"], [20, 338, 25, 38], [20, 341, 25, 38, "t"], [20, 342, 25, 38], [20, 345, 25, 38, "n"], [20, 346, 25, 38], [20, 349, 25, 38, "r"], [20, 350, 25, 38], [20, 358, 25, 38, "o"], [20, 359, 25, 38], [20, 360, 25, 38, "has"], [20, 363, 25, 38], [20, 364, 25, 38, "e"], [20, 365, 25, 38], [20, 375, 25, 38, "o"], [20, 376, 25, 38], [20, 377, 25, 38, "get"], [20, 380, 25, 38], [20, 381, 25, 38, "e"], [20, 382, 25, 38], [20, 385, 25, 38, "o"], [20, 386, 25, 38], [20, 387, 25, 38, "set"], [20, 390, 25, 38], [20, 391, 25, 38, "e"], [20, 392, 25, 38], [20, 394, 25, 38, "f"], [20, 395, 25, 38], [20, 409, 25, 38, "_t"], [20, 411, 25, 38], [20, 415, 25, 38, "e"], [20, 416, 25, 38], [20, 432, 25, 38, "_t"], [20, 434, 25, 38], [20, 441, 25, 38, "hasOwnProperty"], [20, 455, 25, 38], [20, 456, 25, 38, "call"], [20, 460, 25, 38], [20, 461, 25, 38, "e"], [20, 462, 25, 38], [20, 464, 25, 38, "_t"], [20, 466, 25, 38], [20, 473, 25, 38, "i"], [20, 474, 25, 38], [20, 478, 25, 38, "o"], [20, 479, 25, 38], [20, 482, 25, 38, "Object"], [20, 488, 25, 38], [20, 489, 25, 38, "defineProperty"], [20, 503, 25, 38], [20, 508, 25, 38, "Object"], [20, 514, 25, 38], [20, 515, 25, 38, "getOwnPropertyDescriptor"], [20, 539, 25, 38], [20, 540, 25, 38, "e"], [20, 541, 25, 38], [20, 543, 25, 38, "_t"], [20, 545, 25, 38], [20, 552, 25, 38, "i"], [20, 553, 25, 38], [20, 554, 25, 38, "get"], [20, 557, 25, 38], [20, 561, 25, 38, "i"], [20, 562, 25, 38], [20, 563, 25, 38, "set"], [20, 566, 25, 38], [20, 570, 25, 38, "o"], [20, 571, 25, 38], [20, 572, 25, 38, "f"], [20, 573, 25, 38], [20, 575, 25, 38, "_t"], [20, 577, 25, 38], [20, 579, 25, 38, "i"], [20, 580, 25, 38], [20, 584, 25, 38, "f"], [20, 585, 25, 38], [20, 586, 25, 38, "_t"], [20, 588, 25, 38], [20, 592, 25, 38, "e"], [20, 593, 25, 38], [20, 594, 25, 38, "_t"], [20, 596, 25, 38], [20, 607, 25, 38, "f"], [20, 608, 25, 38], [20, 613, 25, 38, "e"], [20, 614, 25, 38], [20, 616, 25, 38, "t"], [20, 617, 25, 38], [21, 2, 27, 0], [21, 6, 27, 8, "resolveAssetSource"], [21, 24, 27, 26], [21, 27, 27, 31, "Image"], [21, 45, 27, 36], [21, 46, 27, 8, "resolveAssetSource"], [21, 64, 27, 26], [22, 2, 28, 0], [22, 6, 28, 6, "processDecelerationRate"], [22, 29, 28, 29], [22, 32, 29, 2, "decelerationRate"], [22, 48, 29, 65], [22, 52, 30, 5], [23, 4, 31, 2], [23, 8, 31, 6, "newDecelerationRate"], [23, 27, 31, 25], [23, 30, 31, 28, "decelerationRate"], [23, 46, 31, 44], [24, 4, 32, 2], [24, 8, 32, 6, "newDecelerationRate"], [24, 27, 32, 25], [24, 32, 32, 30], [24, 40, 32, 38], [24, 42, 32, 40], [25, 6, 33, 4, "newDecelerationRate"], [25, 25, 33, 23], [25, 28, 33, 26], [25, 33, 33, 31], [26, 4, 34, 2], [26, 5, 34, 3], [26, 11, 34, 9], [26, 15, 34, 13, "newDecelerationRate"], [26, 34, 34, 32], [26, 39, 34, 37], [26, 45, 34, 43], [26, 47, 34, 45], [27, 6, 35, 4, "newDecelerationRate"], [27, 25, 35, 23], [27, 28, 35, 26], [27, 32, 35, 30], [28, 4, 36, 2], [29, 4, 37, 2], [29, 11, 37, 9, "newDecelerationRate"], [29, 30, 37, 28], [30, 2, 38, 0], [30, 3, 38, 1], [31, 2, 40, 0], [31, 6, 40, 6, "useWarnIfChanges"], [31, 22, 40, 22], [31, 25, 40, 25, "useWarnIfChanges"], [31, 26, 40, 45, "value"], [31, 31, 40, 53], [31, 33, 40, 55, "name"], [31, 37, 40, 67], [31, 42, 40, 72], [32, 4, 41, 2], [32, 8, 41, 8, "ref"], [32, 11, 41, 11], [32, 14, 41, 14], [32, 18, 41, 14, "useRef"], [32, 31, 41, 20], [32, 33, 41, 21, "value"], [32, 38, 41, 26], [32, 39, 41, 27], [33, 4, 42, 2], [33, 8, 42, 6, "ref"], [33, 11, 42, 9], [33, 12, 42, 10, "current"], [33, 19, 42, 17], [33, 24, 42, 22, "value"], [33, 29, 42, 27], [33, 31, 42, 29], [34, 6, 43, 4, "console"], [34, 13, 43, 11], [34, 14, 43, 12, "warn"], [34, 18, 43, 16], [34, 19, 44, 6], [34, 42, 44, 29, "name"], [34, 46, 44, 33], [34, 85, 45, 4], [34, 86, 45, 5], [35, 6, 46, 4, "ref"], [35, 9, 46, 7], [35, 10, 46, 8, "current"], [35, 17, 46, 15], [35, 20, 46, 18, "value"], [35, 25, 46, 23], [36, 4, 47, 2], [37, 2, 48, 0], [37, 3, 48, 1], [38, 2, 50, 0], [38, 6, 50, 6, "WebViewComponent"], [38, 22, 50, 22], [38, 38, 50, 25], [38, 42, 50, 25, "forwardRef"], [38, 59, 50, 35], [38, 61, 51, 2], [38, 62, 51, 2, "_ref"], [38, 66, 51, 2], [38, 68, 92, 4, "ref"], [38, 71, 92, 7], [38, 76, 93, 7], [39, 4, 93, 7], [39, 8, 93, 7, "_ref$fraudulentWebsit"], [39, 29, 93, 7], [39, 32, 93, 7, "_ref"], [39, 36, 93, 7], [39, 37, 53, 6, "fraudulentWebsiteWarningEnabled"], [39, 68, 53, 37], [40, 6, 53, 6, "fraudulentWebsiteWarningEnabled"], [40, 37, 53, 37], [40, 40, 53, 37, "_ref$fraudulentWebsit"], [40, 61, 53, 37], [40, 75, 53, 40], [40, 79, 53, 44], [40, 82, 53, 44, "_ref$fraudulentWebsit"], [40, 103, 53, 44], [41, 6, 53, 44, "_ref$javaScriptEnable"], [41, 27, 53, 44], [41, 30, 53, 44, "_ref"], [41, 34, 53, 44], [41, 35, 54, 6, "javaScriptEnabled"], [41, 52, 54, 23], [42, 6, 54, 6, "javaScriptEnabled"], [42, 23, 54, 23], [42, 26, 54, 23, "_ref$javaScriptEnable"], [42, 47, 54, 23], [42, 61, 54, 26], [42, 65, 54, 30], [42, 68, 54, 30, "_ref$javaScriptEnable"], [42, 89, 54, 30], [43, 6, 54, 30, "_ref$cacheEnabled"], [43, 23, 54, 30], [43, 26, 54, 30, "_ref"], [43, 30, 54, 30], [43, 31, 55, 6, "cacheEnabled"], [43, 43, 55, 18], [44, 6, 55, 6, "cacheEnabled"], [44, 18, 55, 18], [44, 21, 55, 18, "_ref$cacheEnabled"], [44, 38, 55, 18], [44, 52, 55, 21], [44, 56, 55, 25], [44, 59, 55, 25, "_ref$cacheEnabled"], [44, 76, 55, 25], [45, 6, 55, 25, "_ref$originWhitelist"], [45, 26, 55, 25], [45, 29, 55, 25, "_ref"], [45, 33, 55, 25], [45, 34, 56, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [45, 49, 56, 21], [46, 6, 56, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [46, 21, 56, 21], [46, 24, 56, 21, "_ref$originWhitelist"], [46, 44, 56, 21], [46, 58, 56, 24, "<PERSON><PERSON><PERSON><PERSON>"], [46, 95, 56, 46], [46, 98, 56, 46, "_ref$originWhitelist"], [46, 118, 56, 46], [47, 6, 56, 46, "_ref$useSharedProcess"], [47, 27, 56, 46], [47, 30, 56, 46, "_ref"], [47, 34, 56, 46], [47, 35, 57, 6, "useSharedProcessPool"], [47, 55, 57, 26], [48, 6, 57, 6, "useSharedProcessPool"], [48, 26, 57, 26], [48, 29, 57, 26, "_ref$useSharedProcess"], [48, 50, 57, 26], [48, 64, 57, 29], [48, 68, 57, 33], [48, 71, 57, 33, "_ref$useSharedProcess"], [48, 92, 57, 33], [49, 6, 57, 33, "_ref$textInteractionE"], [49, 27, 57, 33], [49, 30, 57, 33, "_ref"], [49, 34, 57, 33], [49, 35, 58, 6, "textInteractionEnabled"], [49, 57, 58, 28], [50, 6, 58, 6, "textInteractionEnabled"], [50, 28, 58, 28], [50, 31, 58, 28, "_ref$textInteractionE"], [50, 52, 58, 28], [50, 66, 58, 31], [50, 70, 58, 35], [50, 73, 58, 35, "_ref$textInteractionE"], [50, 94, 58, 35], [51, 6, 59, 6, "injectedJavaScript"], [51, 24, 59, 24], [51, 27, 59, 24, "_ref"], [51, 31, 59, 24], [51, 32, 59, 6, "injectedJavaScript"], [51, 50, 59, 24], [52, 6, 60, 6, "injectedJavaScriptBeforeContentLoaded"], [52, 43, 60, 43], [52, 46, 60, 43, "_ref"], [52, 50, 60, 43], [52, 51, 60, 6, "injectedJavaScriptBeforeContentLoaded"], [52, 88, 60, 43], [53, 6, 60, 43, "_ref$injectedJavaScri"], [53, 27, 60, 43], [53, 30, 60, 43, "_ref"], [53, 34, 60, 43], [53, 35, 61, 6, "injectedJavaScriptForMainFrameOnly"], [53, 69, 61, 40], [54, 6, 61, 6, "injectedJavaScriptForMainFrameOnly"], [54, 40, 61, 40], [54, 43, 61, 40, "_ref$injectedJavaScri"], [54, 64, 61, 40], [54, 78, 61, 43], [54, 82, 61, 47], [54, 85, 61, 47, "_ref$injectedJavaScri"], [54, 106, 61, 47], [55, 6, 61, 47, "_ref$injectedJavaScri2"], [55, 28, 61, 47], [55, 31, 61, 47, "_ref"], [55, 35, 61, 47], [55, 36, 62, 6, "injectedJavaScriptBeforeContentLoadedForMainFrameOnly"], [55, 89, 62, 59], [56, 6, 62, 6, "injectedJavaScriptBeforeContentLoadedForMainFrameOnly"], [56, 59, 62, 59], [56, 62, 62, 59, "_ref$injectedJavaScri2"], [56, 84, 62, 59], [56, 98, 62, 62], [56, 102, 62, 66], [56, 105, 62, 66, "_ref$injectedJavaScri2"], [56, 127, 62, 66], [57, 6, 63, 6, "injectedJavaScriptObject"], [57, 30, 63, 30], [57, 33, 63, 30, "_ref"], [57, 37, 63, 30], [57, 38, 63, 6, "injectedJavaScriptObject"], [57, 62, 63, 30], [58, 6, 64, 6, "startInLoadingState"], [58, 25, 64, 25], [58, 28, 64, 25, "_ref"], [58, 32, 64, 25], [58, 33, 64, 6, "startInLoadingState"], [58, 52, 64, 25], [59, 6, 65, 6, "onNavigationStateChange"], [59, 29, 65, 29], [59, 32, 65, 29, "_ref"], [59, 36, 65, 29], [59, 37, 65, 6, "onNavigationStateChange"], [59, 60, 65, 29], [60, 6, 66, 6, "onLoadStart"], [60, 17, 66, 17], [60, 20, 66, 17, "_ref"], [60, 24, 66, 17], [60, 25, 66, 6, "onLoadStart"], [60, 36, 66, 17], [61, 6, 67, 6, "onError"], [61, 13, 67, 13], [61, 16, 67, 13, "_ref"], [61, 20, 67, 13], [61, 21, 67, 6, "onError"], [61, 28, 67, 13], [62, 6, 68, 6, "onLoad"], [62, 12, 68, 12], [62, 15, 68, 12, "_ref"], [62, 19, 68, 12], [62, 20, 68, 6, "onLoad"], [62, 26, 68, 12], [63, 6, 69, 6, "onLoadEnd"], [63, 15, 69, 15], [63, 18, 69, 15, "_ref"], [63, 22, 69, 15], [63, 23, 69, 6, "onLoadEnd"], [63, 32, 69, 15], [64, 6, 70, 6, "onLoadProgress"], [64, 20, 70, 20], [64, 23, 70, 20, "_ref"], [64, 27, 70, 20], [64, 28, 70, 6, "onLoadProgress"], [64, 42, 70, 20], [65, 6, 71, 36, "onContentProcessDidTerminateProp"], [65, 38, 71, 68], [65, 41, 71, 68, "_ref"], [65, 45, 71, 68], [65, 46, 71, 6, "onContentProcessDidTerminate"], [65, 74, 71, 34], [66, 6, 72, 6, "onFileDownload"], [66, 20, 72, 20], [66, 23, 72, 20, "_ref"], [66, 27, 72, 20], [66, 28, 72, 6, "onFileDownload"], [66, 42, 72, 20], [67, 6, 73, 19, "onHttpErrorProp"], [67, 21, 73, 34], [67, 24, 73, 34, "_ref"], [67, 28, 73, 34], [67, 29, 73, 6, "onHttpError"], [67, 40, 73, 17], [68, 6, 74, 17, "onMessageProp"], [68, 19, 74, 30], [68, 22, 74, 30, "_ref"], [68, 26, 74, 30], [68, 27, 74, 6, "onMessage"], [68, 36, 74, 15], [69, 6, 75, 20, "onOpenWindowProp"], [69, 22, 75, 36], [69, 25, 75, 36, "_ref"], [69, 29, 75, 36], [69, 30, 75, 6, "onOpenWindow"], [69, 42, 75, 18], [70, 6, 76, 6, "renderLoading"], [70, 19, 76, 19], [70, 22, 76, 19, "_ref"], [70, 26, 76, 19], [70, 27, 76, 6, "renderLoading"], [70, 40, 76, 19], [71, 6, 77, 6, "renderError"], [71, 17, 77, 17], [71, 20, 77, 17, "_ref"], [71, 24, 77, 17], [71, 25, 77, 6, "renderError"], [71, 36, 77, 17], [72, 6, 78, 6, "style"], [72, 11, 78, 11], [72, 14, 78, 11, "_ref"], [72, 18, 78, 11], [72, 19, 78, 6, "style"], [72, 24, 78, 11], [73, 6, 79, 6, "containerStyle"], [73, 20, 79, 20], [73, 23, 79, 20, "_ref"], [73, 27, 79, 20], [73, 28, 79, 6, "containerStyle"], [73, 42, 79, 20], [74, 6, 80, 6, "source"], [74, 12, 80, 12], [74, 15, 80, 12, "_ref"], [74, 19, 80, 12], [74, 20, 80, 6, "source"], [74, 26, 80, 12], [75, 6, 81, 6, "nativeConfig"], [75, 18, 81, 18], [75, 21, 81, 18, "_ref"], [75, 25, 81, 18], [75, 26, 81, 6, "nativeConfig"], [75, 38, 81, 18], [76, 6, 82, 6, "allowsInlineMediaPlayback"], [76, 31, 82, 31], [76, 34, 82, 31, "_ref"], [76, 38, 82, 31], [76, 39, 82, 6, "allowsInlineMediaPlayback"], [76, 64, 82, 31], [77, 6, 82, 31, "_ref$allowsPictureInP"], [77, 27, 82, 31], [77, 30, 82, 31, "_ref"], [77, 34, 82, 31], [77, 35, 83, 6, "allowsPictureInPictureMediaPlayback"], [77, 70, 83, 41], [78, 6, 83, 6, "allowsPictureInPictureMediaPlayback"], [78, 41, 83, 41], [78, 44, 83, 41, "_ref$allowsPictureInP"], [78, 65, 83, 41], [78, 79, 83, 44], [78, 83, 83, 48], [78, 86, 83, 48, "_ref$allowsPictureInP"], [78, 107, 83, 48], [79, 6, 84, 6, "allowsAirPlayForMediaPlayback"], [79, 35, 84, 35], [79, 38, 84, 35, "_ref"], [79, 42, 84, 35], [79, 43, 84, 6, "allowsAirPlayForMediaPlayback"], [79, 72, 84, 35], [80, 6, 85, 6, "mediaPlaybackRequiresUserAction"], [80, 37, 85, 37], [80, 40, 85, 37, "_ref"], [80, 44, 85, 37], [80, 45, 85, 6, "mediaPlaybackRequiresUserAction"], [80, 76, 85, 37], [81, 6, 86, 6, "dataDetectorTypes"], [81, 23, 86, 23], [81, 26, 86, 23, "_ref"], [81, 30, 86, 23], [81, 31, 86, 6, "dataDetectorTypes"], [81, 48, 86, 23], [82, 6, 87, 6, "incognito"], [82, 15, 87, 15], [82, 18, 87, 15, "_ref"], [82, 22, 87, 15], [82, 23, 87, 6, "incognito"], [82, 32, 87, 15], [83, 6, 88, 24, "decelerationRateProp"], [83, 26, 88, 44], [83, 29, 88, 44, "_ref"], [83, 33, 88, 44], [83, 34, 88, 6, "decelerationRate"], [83, 50, 88, 22], [84, 6, 89, 36, "onShouldStartLoadWithRequestProp"], [84, 38, 89, 68], [84, 41, 89, 68, "_ref"], [84, 45, 89, 68], [84, 46, 89, 6, "onShouldStartLoadWithRequest"], [84, 74, 89, 34], [85, 6, 90, 9, "otherProps"], [85, 16, 90, 19], [85, 23, 90, 19, "_objectWithoutProperties2"], [85, 48, 90, 19], [85, 49, 90, 19, "default"], [85, 56, 90, 19], [85, 58, 90, 19, "_ref"], [85, 62, 90, 19], [85, 64, 90, 19, "_excluded"], [85, 73, 90, 19], [86, 4, 94, 4], [86, 8, 94, 10, "webViewRef"], [86, 18, 94, 20], [86, 21, 94, 23], [86, 25, 94, 23, "useRef"], [86, 38, 94, 29], [86, 40, 96, 14], [86, 44, 96, 18], [86, 45, 96, 19], [87, 4, 98, 4], [87, 8, 98, 10, "onShouldStartLoadWithRequestCallback"], [87, 44, 98, 46], [87, 47, 98, 49], [87, 51, 98, 49, "useCallback"], [87, 69, 98, 60], [87, 71, 99, 6], [87, 81, 99, 7, "shouldStart"], [87, 92, 99, 27], [87, 94, 99, 29, "_url"], [87, 98, 99, 41], [87, 100, 99, 66], [88, 6, 99, 66], [88, 10, 99, 43, "lockIdentifier"], [88, 24, 99, 57], [88, 27, 99, 57, "arguments"], [88, 36, 99, 57], [88, 37, 99, 57, "length"], [88, 43, 99, 57], [88, 51, 99, 57, "arguments"], [88, 60, 99, 57], [88, 68, 99, 57, "undefined"], [88, 77, 99, 57], [88, 80, 99, 57, "arguments"], [88, 89, 99, 57], [88, 95, 99, 60], [88, 96, 99, 61], [89, 6, 100, 8, "RNCWebViewModule"], [89, 37, 100, 24], [89, 38, 100, 25, "shouldStartLoadWithLockIdentifier"], [89, 71, 100, 58], [89, 72, 101, 10, "shouldStart"], [89, 83, 101, 21], [89, 85, 102, 10, "lockIdentifier"], [89, 99, 103, 8], [89, 100, 103, 9], [90, 4, 104, 6], [90, 5, 104, 7], [90, 7, 105, 6], [90, 9, 106, 4], [90, 10, 106, 5], [91, 4, 108, 4], [91, 8, 108, 4, "_useWebViewLogic"], [91, 24, 108, 4], [91, 27, 121, 8], [91, 31, 121, 8, "useWebViewLogic"], [91, 61, 121, 23], [91, 63, 121, 24], [92, 8, 122, 6, "onNavigationStateChange"], [92, 31, 122, 29], [93, 8, 123, 6, "onLoad"], [93, 14, 123, 12], [94, 8, 124, 6, "onError"], [94, 15, 124, 13], [95, 8, 125, 6, "onHttpErrorProp"], [95, 23, 125, 21], [96, 8, 126, 6, "onLoadEnd"], [96, 17, 126, 15], [97, 8, 127, 6, "onLoadProgress"], [97, 22, 127, 20], [98, 8, 128, 6, "onLoadStart"], [98, 19, 128, 17], [99, 8, 129, 6, "onMessageProp"], [99, 21, 129, 19], [100, 8, 130, 6, "onOpenWindowProp"], [100, 24, 130, 22], [101, 8, 131, 6, "startInLoadingState"], [101, 27, 131, 25], [102, 8, 132, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [102, 23, 132, 21], [103, 8, 133, 6, "onShouldStartLoadWithRequestProp"], [103, 40, 133, 38], [104, 8, 134, 6, "onShouldStartLoadWithRequestCallback"], [104, 44, 134, 42], [105, 8, 135, 6, "onContentProcessDidTerminateProp"], [106, 6, 136, 4], [106, 7, 136, 5], [106, 8, 136, 6], [107, 6, 109, 6, "onLoadingStart"], [107, 20, 109, 20], [107, 23, 109, 20, "_useWebViewLogic"], [107, 39, 109, 20], [107, 40, 109, 6, "onLoadingStart"], [107, 54, 109, 20], [108, 6, 110, 6, "onShouldStartLoadWithRequest"], [108, 34, 110, 34], [108, 37, 110, 34, "_useWebViewLogic"], [108, 53, 110, 34], [108, 54, 110, 6, "onShouldStartLoadWithRequest"], [108, 82, 110, 34], [109, 6, 111, 6, "onMessage"], [109, 15, 111, 15], [109, 18, 111, 15, "_useWebViewLogic"], [109, 34, 111, 15], [109, 35, 111, 6, "onMessage"], [109, 44, 111, 15], [110, 6, 112, 6, "viewState"], [110, 15, 112, 15], [110, 18, 112, 15, "_useWebViewLogic"], [110, 34, 112, 15], [110, 35, 112, 6, "viewState"], [110, 44, 112, 15], [111, 6, 113, 6, "setViewState"], [111, 18, 113, 18], [111, 21, 113, 18, "_useWebViewLogic"], [111, 37, 113, 18], [111, 38, 113, 6, "setViewState"], [111, 50, 113, 18], [112, 6, 114, 6, "lastErrorEvent"], [112, 20, 114, 20], [112, 23, 114, 20, "_useWebViewLogic"], [112, 39, 114, 20], [112, 40, 114, 6, "lastErrorEvent"], [112, 54, 114, 20], [113, 6, 115, 6, "onHttpError"], [113, 17, 115, 17], [113, 20, 115, 17, "_useWebViewLogic"], [113, 36, 115, 17], [113, 37, 115, 6, "onHttpError"], [113, 48, 115, 17], [114, 6, 116, 6, "onLoadingError"], [114, 20, 116, 20], [114, 23, 116, 20, "_useWebViewLogic"], [114, 39, 116, 20], [114, 40, 116, 6, "onLoadingError"], [114, 54, 116, 20], [115, 6, 117, 6, "onLoadingFinish"], [115, 21, 117, 21], [115, 24, 117, 21, "_useWebViewLogic"], [115, 40, 117, 21], [115, 41, 117, 6, "onLoadingFinish"], [115, 56, 117, 21], [116, 6, 118, 6, "onLoadingProgress"], [116, 23, 118, 23], [116, 26, 118, 23, "_useWebViewLogic"], [116, 42, 118, 23], [116, 43, 118, 6, "onLoadingProgress"], [116, 60, 118, 23], [117, 6, 119, 6, "onOpenWindow"], [117, 18, 119, 18], [117, 21, 119, 18, "_useWebViewLogic"], [117, 37, 119, 18], [117, 38, 119, 6, "onOpenWindow"], [117, 50, 119, 18], [118, 6, 120, 6, "onContentProcessDidTerminate"], [118, 34, 120, 34], [118, 37, 120, 34, "_useWebViewLogic"], [118, 53, 120, 34], [118, 54, 120, 6, "onContentProcessDidTerminate"], [118, 82, 120, 34], [119, 4, 138, 4], [119, 8, 138, 4, "useImperativeHandle"], [119, 34, 138, 23], [119, 36, 139, 6, "ref"], [119, 39, 139, 9], [119, 41, 140, 6], [119, 48, 140, 13], [120, 6, 141, 8, "goForward"], [120, 15, 141, 17], [120, 17, 141, 19, "goForward"], [120, 18, 141, 19], [120, 23, 142, 10, "webViewRef"], [120, 33, 142, 20], [120, 34, 142, 21, "current"], [120, 41, 142, 28], [120, 45, 142, 32, "Commands"], [120, 80, 142, 40], [120, 81, 142, 41, "goForward"], [120, 90, 142, 50], [120, 91, 142, 51, "webViewRef"], [120, 101, 142, 61], [120, 102, 142, 62, "current"], [120, 109, 142, 69], [120, 110, 142, 70], [121, 6, 143, 8, "goBack"], [121, 12, 143, 14], [121, 14, 143, 16, "goBack"], [121, 15, 143, 16], [121, 20, 143, 22, "webViewRef"], [121, 30, 143, 32], [121, 31, 143, 33, "current"], [121, 38, 143, 40], [121, 42, 143, 44, "Commands"], [121, 77, 143, 52], [121, 78, 143, 53, "goBack"], [121, 84, 143, 59], [121, 85, 143, 60, "webViewRef"], [121, 95, 143, 70], [121, 96, 143, 71, "current"], [121, 103, 143, 78], [121, 104, 143, 79], [122, 6, 144, 8, "reload"], [122, 12, 144, 14], [122, 14, 144, 16, "reload"], [122, 15, 144, 16], [122, 20, 144, 22], [123, 8, 145, 10, "setViewState"], [123, 20, 145, 22], [123, 21, 145, 23], [123, 30, 145, 32], [123, 31, 145, 33], [124, 8, 146, 10], [124, 12, 146, 14, "webViewRef"], [124, 22, 146, 24], [124, 23, 146, 25, "current"], [124, 30, 146, 32], [124, 32, 146, 34], [125, 10, 147, 12, "Commands"], [125, 45, 147, 20], [125, 46, 147, 21, "reload"], [125, 52, 147, 27], [125, 53, 147, 28, "webViewRef"], [125, 63, 147, 38], [125, 64, 147, 39, "current"], [125, 71, 147, 46], [125, 72, 147, 47], [126, 8, 148, 10], [127, 6, 149, 8], [127, 7, 149, 9], [128, 6, 150, 8, "stopLoading"], [128, 17, 150, 19], [128, 19, 150, 21, "stopLoading"], [128, 20, 150, 21], [128, 25, 151, 10, "webViewRef"], [128, 35, 151, 20], [128, 36, 151, 21, "current"], [128, 43, 151, 28], [128, 47, 151, 32, "Commands"], [128, 82, 151, 40], [128, 83, 151, 41, "stopLoading"], [128, 94, 151, 52], [128, 95, 151, 53, "webViewRef"], [128, 105, 151, 63], [128, 106, 151, 64, "current"], [128, 113, 151, 71], [128, 114, 151, 72], [129, 6, 152, 8, "postMessage"], [129, 17, 152, 19], [129, 19, 152, 22, "data"], [129, 23, 152, 34], [129, 27, 153, 10, "webViewRef"], [129, 37, 153, 20], [129, 38, 153, 21, "current"], [129, 45, 153, 28], [129, 49, 153, 32, "Commands"], [129, 84, 153, 40], [129, 85, 153, 41, "postMessage"], [129, 96, 153, 52], [129, 97, 153, 53, "webViewRef"], [129, 107, 153, 63], [129, 108, 153, 64, "current"], [129, 115, 153, 71], [129, 117, 153, 73, "data"], [129, 121, 153, 77], [129, 122, 153, 78], [130, 6, 154, 8, "injectJavaScript"], [130, 22, 154, 24], [130, 24, 154, 27, "data"], [130, 28, 154, 39], [130, 32, 155, 10, "webViewRef"], [130, 42, 155, 20], [130, 43, 155, 21, "current"], [130, 50, 155, 28], [130, 54, 156, 10, "Commands"], [130, 89, 156, 18], [130, 90, 156, 19, "injectJavaScript"], [130, 106, 156, 35], [130, 107, 156, 36, "webViewRef"], [130, 117, 156, 46], [130, 118, 156, 47, "current"], [130, 125, 156, 54], [130, 127, 156, 56, "data"], [130, 131, 156, 60], [130, 132, 156, 61], [131, 6, 157, 8, "requestFocus"], [131, 18, 157, 20], [131, 20, 157, 22, "requestFocus"], [131, 21, 157, 22], [131, 26, 158, 10, "webViewRef"], [131, 36, 158, 20], [131, 37, 158, 21, "current"], [131, 44, 158, 28], [131, 48, 158, 32, "Commands"], [131, 83, 158, 40], [131, 84, 158, 41, "requestFocus"], [131, 96, 158, 53], [131, 97, 158, 54, "webViewRef"], [131, 107, 158, 64], [131, 108, 158, 65, "current"], [131, 115, 158, 72], [131, 116, 158, 73], [132, 6, 159, 8, "clearCache"], [132, 16, 159, 18], [132, 18, 159, 21, "includeDiskFiles"], [132, 34, 159, 46], [132, 38, 160, 10, "webViewRef"], [132, 48, 160, 20], [132, 49, 160, 21, "current"], [132, 56, 160, 28], [132, 60, 161, 10, "Commands"], [132, 95, 161, 18], [132, 96, 161, 19, "clearCache"], [132, 106, 161, 29], [132, 107, 161, 30, "webViewRef"], [132, 117, 161, 40], [132, 118, 161, 41, "current"], [132, 125, 161, 48], [132, 127, 161, 50, "includeDiskFiles"], [132, 143, 161, 66], [133, 4, 162, 6], [133, 5, 162, 7], [133, 6, 162, 8], [133, 8, 163, 6], [133, 9, 163, 7, "setViewState"], [133, 21, 163, 19], [133, 23, 163, 21, "webViewRef"], [133, 33, 163, 31], [133, 34, 164, 4], [133, 35, 164, 5], [134, 4, 166, 4, "useWarnIfChanges"], [134, 20, 166, 20], [134, 21, 166, 21, "allowsInlineMediaPlayback"], [134, 46, 166, 46], [134, 48, 166, 48], [134, 75, 166, 75], [134, 76, 166, 76], [135, 4, 167, 4, "useWarnIfChanges"], [135, 20, 167, 20], [135, 21, 168, 6, "allowsPictureInPictureMediaPlayback"], [135, 56, 168, 41], [135, 58, 169, 6], [135, 95, 170, 4], [135, 96, 170, 5], [136, 4, 171, 4, "useWarnIfChanges"], [136, 20, 171, 20], [136, 21, 172, 6, "allowsAirPlayForMediaPlayback"], [136, 50, 172, 35], [136, 52, 173, 6], [136, 83, 174, 4], [136, 84, 174, 5], [137, 4, 175, 4, "useWarnIfChanges"], [137, 20, 175, 20], [137, 21, 175, 21, "incognito"], [137, 30, 175, 30], [137, 32, 175, 32], [137, 43, 175, 43], [137, 44, 175, 44], [138, 4, 176, 4, "useWarnIfChanges"], [138, 20, 176, 20], [138, 21, 177, 6, "mediaPlaybackRequiresUserAction"], [138, 52, 177, 37], [138, 54, 178, 6], [138, 87, 179, 4], [138, 88, 179, 5], [139, 4, 180, 4, "useWarnIfChanges"], [139, 20, 180, 20], [139, 21, 180, 21, "dataDetectorTypes"], [139, 38, 180, 38], [139, 40, 180, 40], [139, 59, 180, 59], [139, 60, 180, 60], [140, 4, 182, 4], [140, 8, 182, 8, "otherView"], [140, 17, 182, 17], [140, 20, 182, 20], [140, 24, 182, 24], [141, 4, 183, 4], [141, 8, 183, 8, "viewState"], [141, 17, 183, 17], [141, 22, 183, 22], [141, 31, 183, 31], [141, 33, 183, 33], [142, 6, 184, 6, "otherView"], [142, 15, 184, 15], [142, 18, 184, 18], [142, 19, 184, 19, "renderLoading"], [142, 32, 184, 32], [142, 36, 184, 36, "defaultRenderLoading"], [142, 71, 184, 56], [142, 73, 184, 58], [142, 74, 184, 59], [143, 4, 185, 4], [143, 5, 185, 5], [143, 11, 185, 11], [143, 15, 185, 15, "viewState"], [143, 24, 185, 24], [143, 29, 185, 29], [143, 36, 185, 36], [143, 38, 185, 38], [144, 6, 186, 6], [144, 10, 186, 6, "invariant"], [144, 28, 186, 15], [144, 30, 187, 8, "lastErrorEvent"], [144, 44, 187, 22], [144, 48, 187, 26], [144, 52, 187, 30], [144, 54, 188, 8], [144, 94, 189, 6], [144, 95, 189, 7], [145, 6, 190, 6, "otherView"], [145, 15, 190, 15], [145, 18, 190, 18], [145, 19, 190, 19, "renderError"], [145, 30, 190, 30], [145, 34, 190, 34, "defaultRenderError"], [145, 67, 190, 52], [145, 69, 191, 8, "lastErrorEvent"], [145, 83, 191, 22], [145, 85, 191, 24, "domain"], [145, 91, 191, 30], [145, 93, 192, 8, "lastErrorEvent"], [145, 107, 192, 22], [145, 109, 192, 24, "code"], [145, 113, 192, 28], [145, 117, 192, 32], [145, 118, 192, 33], [145, 120, 193, 8, "lastErrorEvent"], [145, 134, 193, 22], [145, 136, 193, 24, "description"], [145, 147, 193, 35], [145, 151, 193, 39], [145, 153, 194, 6], [145, 154, 194, 7], [146, 4, 195, 4], [146, 5, 195, 5], [146, 11, 195, 11], [146, 15, 195, 15, "viewState"], [146, 24, 195, 24], [146, 29, 195, 29], [146, 35, 195, 35], [146, 37, 195, 37], [147, 6, 196, 6, "console"], [147, 13, 196, 13], [147, 14, 196, 14, "error"], [147, 19, 196, 19], [147, 20, 196, 20], [147, 61, 196, 61, "viewState"], [147, 70, 196, 70], [147, 72, 196, 72], [147, 73, 196, 73], [148, 4, 197, 4], [149, 4, 199, 4], [149, 8, 199, 10, "webViewStyles"], [149, 21, 199, 23], [149, 24, 199, 26], [149, 25, 199, 27, "styles"], [149, 41, 199, 33], [149, 42, 199, 34, "container"], [149, 51, 199, 43], [149, 53, 199, 45, "styles"], [149, 69, 199, 51], [149, 70, 199, 52, "webView"], [149, 77, 199, 59], [149, 79, 199, 61, "style"], [149, 84, 199, 66], [149, 85, 199, 67], [150, 4, 200, 4], [150, 8, 200, 10, "webViewContainerStyle"], [150, 29, 200, 31], [150, 32, 200, 34], [150, 33, 200, 35, "styles"], [150, 49, 200, 41], [150, 50, 200, 42, "container"], [150, 59, 200, 51], [150, 61, 200, 53, "containerStyle"], [150, 75, 200, 67], [150, 76, 200, 68], [151, 4, 202, 4], [151, 8, 202, 10, "decelerationRate"], [151, 24, 202, 26], [151, 27, 202, 29, "processDecelerationRate"], [151, 50, 202, 52], [151, 51, 202, 53, "decelerationRateProp"], [151, 71, 202, 73], [151, 72, 202, 74], [152, 4, 204, 4], [152, 8, 204, 10, "NativeWebView"], [152, 21, 204, 23], [152, 24, 205, 7, "nativeConfig"], [152, 36, 205, 19], [152, 38, 205, 21, "component"], [152, 47, 205, 30], [152, 51, 205, 68, "RNCWebView"], [152, 85, 205, 78], [153, 4, 207, 4], [153, 8, 207, 10, "sourceResolved"], [153, 22, 207, 24], [153, 25, 207, 27, "resolveAssetSource"], [153, 43, 207, 45], [153, 44, 207, 46, "source"], [153, 50, 207, 75], [153, 51, 207, 76], [154, 4, 208, 4], [154, 8, 208, 10, "newSource"], [154, 17, 208, 19], [154, 20, 209, 6], [154, 27, 209, 13, "sourceResolved"], [154, 41, 209, 27], [154, 46, 209, 32], [154, 54, 209, 40], [154, 57, 210, 10, "Object"], [154, 63, 210, 16], [154, 64, 210, 17, "entries"], [154, 71, 210, 24], [154, 72, 210, 25, "sourceResolved"], [154, 86, 210, 59], [154, 87, 210, 60], [154, 88, 210, 61, "reduce"], [154, 94, 210, 67], [154, 95, 211, 12], [154, 96, 211, 13, "prev"], [154, 100, 211, 17], [154, 102, 211, 17, "_ref2"], [154, 107, 211, 17], [154, 112, 211, 44], [155, 6, 211, 44], [155, 10, 211, 44, "_ref3"], [155, 15, 211, 44], [155, 22, 211, 44, "_slicedToArray2"], [155, 37, 211, 44], [155, 38, 211, 44, "default"], [155, 45, 211, 44], [155, 47, 211, 44, "_ref2"], [155, 52, 211, 44], [156, 8, 211, 20, "curr<PERSON><PERSON>"], [156, 15, 211, 27], [156, 18, 211, 27, "_ref3"], [156, 23, 211, 27], [157, 8, 211, 29, "currValue"], [157, 17, 211, 38], [157, 20, 211, 38, "_ref3"], [157, 25, 211, 38], [158, 6, 212, 14], [158, 13, 212, 21], [159, 8, 213, 16], [159, 11, 213, 19, "prev"], [159, 15, 213, 23], [160, 8, 214, 16], [160, 9, 214, 17, "curr<PERSON><PERSON>"], [160, 16, 214, 24], [160, 19, 215, 18, "curr<PERSON><PERSON>"], [160, 26, 215, 25], [160, 31, 215, 30], [160, 40, 215, 39], [160, 44, 216, 18, "currValue"], [160, 53, 216, 27], [160, 57, 217, 18], [160, 64, 217, 25, "currValue"], [160, 73, 217, 34], [160, 78, 217, 39], [160, 86, 217, 47], [160, 89, 218, 22, "Object"], [160, 95, 218, 28], [160, 96, 218, 29, "entries"], [160, 103, 218, 36], [160, 104, 218, 37, "currValue"], [160, 113, 218, 46], [160, 114, 218, 47], [160, 115, 218, 48, "map"], [160, 118, 218, 51], [160, 119, 218, 52, "_ref4"], [160, 124, 218, 52], [160, 128, 218, 70], [161, 10, 218, 70], [161, 14, 218, 70, "_ref5"], [161, 19, 218, 70], [161, 26, 218, 70, "_slicedToArray2"], [161, 41, 218, 70], [161, 42, 218, 70, "default"], [161, 49, 218, 70], [161, 51, 218, 70, "_ref4"], [161, 56, 218, 70], [162, 12, 218, 54, "key"], [162, 15, 218, 57], [162, 18, 218, 57, "_ref5"], [162, 23, 218, 57], [163, 12, 218, 59, "value"], [163, 17, 218, 64], [163, 20, 218, 64, "_ref5"], [163, 25, 218, 64], [164, 10, 219, 24], [164, 17, 219, 31], [165, 12, 220, 26, "name"], [165, 16, 220, 30], [165, 18, 220, 32, "key"], [165, 21, 220, 35], [166, 12, 221, 26, "value"], [167, 10, 222, 24], [167, 11, 222, 25], [168, 8, 223, 22], [168, 9, 223, 23], [168, 10, 223, 24], [168, 13, 224, 22, "currValue"], [169, 6, 225, 14], [169, 7, 225, 15], [170, 4, 226, 12], [170, 5, 226, 13], [170, 7, 227, 12], [170, 8, 227, 13], [170, 9, 228, 10], [170, 10, 228, 11], [170, 13, 229, 10, "sourceResolved"], [170, 27, 229, 24], [171, 4, 231, 4], [171, 8, 231, 10, "webView"], [171, 15, 231, 17], [171, 18, 232, 6], [171, 22, 232, 6, "_jsxRuntime"], [171, 33, 232, 6], [171, 34, 232, 6, "jsx"], [171, 37, 232, 6], [171, 39, 232, 7, "NativeWebView"], [171, 52, 232, 20], [172, 6, 232, 20], [172, 9, 234, 12, "otherProps"], [172, 19, 234, 22], [173, 6, 235, 8, "fraudulentWebsiteWarningEnabled"], [173, 37, 235, 39], [173, 39, 235, 41, "fraudulentWebsiteWarningEnabled"], [173, 70, 235, 73], [174, 6, 236, 8, "javaScriptEnabled"], [174, 23, 236, 25], [174, 25, 236, 27, "javaScriptEnabled"], [174, 42, 236, 45], [175, 6, 237, 8, "cacheEnabled"], [175, 18, 237, 20], [175, 20, 237, 22, "cacheEnabled"], [175, 32, 237, 35], [176, 6, 238, 8, "useSharedProcessPool"], [176, 26, 238, 28], [176, 28, 238, 30, "useSharedProcessPool"], [176, 48, 238, 51], [177, 6, 239, 8, "textInteractionEnabled"], [177, 28, 239, 30], [177, 30, 239, 32, "textInteractionEnabled"], [177, 52, 239, 55], [178, 6, 240, 8, "decelerationRate"], [178, 22, 240, 24], [178, 24, 240, 26, "decelerationRate"], [178, 40, 240, 43], [179, 6, 241, 8, "messagingEnabled"], [179, 22, 241, 24], [179, 24, 241, 26], [179, 31, 241, 33, "onMessageProp"], [179, 44, 241, 46], [179, 49, 241, 51], [179, 59, 241, 62], [180, 6, 242, 8, "messagingModuleName"], [180, 25, 242, 27], [180, 27, 242, 28], [180, 29, 242, 30], [180, 30, 242, 31], [181, 6, 242, 31], [182, 6, 243, 8, "onLoadingError"], [182, 20, 243, 22], [182, 22, 243, 24, "onLoadingError"], [182, 36, 243, 39], [183, 6, 244, 8, "onLoadingFinish"], [183, 21, 244, 23], [183, 23, 244, 25, "onLoadingFinish"], [183, 38, 244, 41], [184, 6, 245, 8, "onLoadingProgress"], [184, 23, 245, 25], [184, 25, 245, 27, "onLoadingProgress"], [184, 42, 245, 45], [185, 6, 246, 8, "onFileDownload"], [185, 20, 246, 22], [185, 22, 246, 24, "onFileDownload"], [185, 36, 246, 39], [186, 6, 247, 8, "onLoadingStart"], [186, 20, 247, 22], [186, 22, 247, 24, "onLoadingStart"], [186, 36, 247, 39], [187, 6, 248, 8, "onHttpError"], [187, 17, 248, 19], [187, 19, 248, 21, "onHttpError"], [187, 30, 248, 33], [188, 6, 249, 8, "onMessage"], [188, 15, 249, 17], [188, 17, 249, 19, "onMessage"], [188, 26, 249, 29], [189, 6, 250, 8, "onOpenWindow"], [189, 18, 250, 20], [189, 20, 250, 22, "onOpenWindowProp"], [189, 36, 250, 38], [189, 40, 250, 42, "onOpenWindow"], [189, 52, 250, 55], [190, 6, 251, 8, "hasOnOpenWindowEvent"], [190, 26, 251, 28], [190, 28, 251, 30, "onOpenWindowProp"], [190, 44, 251, 46], [190, 49, 251, 51, "undefined"], [190, 58, 251, 61], [191, 6, 252, 8, "onShouldStartLoadWithRequest"], [191, 34, 252, 36], [191, 36, 252, 38, "onShouldStartLoadWithRequest"], [191, 64, 252, 67], [192, 6, 253, 8, "onContentProcessDidTerminate"], [192, 34, 253, 36], [192, 36, 253, 38, "onContentProcessDidTerminate"], [192, 64, 253, 67], [193, 6, 254, 8, "injectedJavaScript"], [193, 24, 254, 26], [193, 26, 254, 28, "injectedJavaScript"], [193, 44, 254, 47], [194, 6, 255, 8, "injectedJavaScriptBeforeContentLoaded"], [194, 43, 255, 45], [194, 45, 256, 10, "injectedJavaScriptBeforeContentLoaded"], [194, 82, 257, 9], [195, 6, 258, 8, "injectedJavaScriptForMainFrameOnly"], [195, 40, 258, 42], [195, 42, 258, 44, "injectedJavaScriptForMainFrameOnly"], [195, 76, 258, 79], [196, 6, 259, 8, "injectedJavaScriptBeforeContentLoadedForMainFrameOnly"], [196, 59, 259, 61], [196, 61, 260, 10, "injectedJavaScriptBeforeContentLoadedForMainFrameOnly"], [196, 114, 261, 9], [197, 6, 262, 8, "injectedJavaScriptObject"], [197, 30, 262, 32], [197, 32, 262, 34, "JSON"], [197, 36, 262, 38], [197, 37, 262, 39, "stringify"], [197, 46, 262, 48], [197, 47, 262, 49, "injectedJavaScriptObject"], [197, 71, 262, 73], [197, 72, 262, 75], [198, 6, 263, 8, "dataDetectorTypes"], [198, 23, 263, 25], [198, 25, 264, 10], [198, 26, 264, 11, "dataDetectorTypes"], [198, 43, 264, 28], [198, 47, 264, 32, "Array"], [198, 52, 264, 37], [198, 53, 264, 38, "isArray"], [198, 60, 264, 45], [198, 61, 264, 46, "dataDetectorTypes"], [198, 78, 264, 63], [198, 79, 264, 64], [198, 82, 265, 14, "dataDetectorTypes"], [198, 99, 265, 31], [198, 102, 266, 14], [198, 103, 266, 15, "dataDetectorTypes"], [198, 120, 266, 32], [198, 121, 267, 9], [199, 6, 268, 8, "allowsAirPlayForMediaPlayback"], [199, 35, 268, 37], [199, 37, 268, 39, "allowsAirPlayForMediaPlayback"], [199, 66, 268, 69], [200, 6, 269, 8, "allowsInlineMediaPlayback"], [200, 31, 269, 33], [200, 33, 269, 35, "allowsInlineMediaPlayback"], [200, 58, 269, 61], [201, 6, 270, 8, "allowsPictureInPictureMediaPlayback"], [201, 41, 270, 43], [201, 43, 271, 10, "allowsPictureInPictureMediaPlayback"], [201, 78, 272, 9], [202, 6, 273, 8, "incognito"], [202, 15, 273, 17], [202, 17, 273, 19, "incognito"], [202, 26, 273, 29], [203, 6, 274, 8, "mediaPlaybackRequiresUserAction"], [203, 37, 274, 39], [203, 39, 274, 41, "mediaPlaybackRequiresUserAction"], [203, 70, 274, 73], [204, 6, 275, 8, "newSource"], [204, 15, 275, 17], [204, 17, 275, 19, "newSource"], [204, 26, 275, 29], [205, 6, 276, 8, "style"], [205, 11, 276, 13], [205, 13, 276, 15, "webViewStyles"], [205, 26, 276, 29], [206, 6, 277, 8, "hasOnFileDownload"], [206, 23, 277, 25], [206, 25, 277, 27], [206, 26, 277, 28], [206, 27, 277, 29, "onFileDownload"], [206, 41, 277, 44], [207, 6, 278, 8, "ref"], [207, 9, 278, 11], [207, 11, 278, 13, "webViewRef"], [208, 6, 279, 8], [209, 6, 279, 8], [210, 6, 280, 8, "source"], [210, 12, 280, 14], [210, 14, 280, 16, "sourceResolved"], [210, 28, 280, 31], [211, 6, 280, 31], [211, 9, 281, 12, "nativeConfig"], [211, 21, 281, 24], [211, 23, 281, 26, "props"], [212, 4, 281, 31], [212, 7, 233, 12], [212, 19, 282, 7], [212, 20, 283, 5], [213, 4, 285, 4], [213, 11, 286, 6], [213, 15, 286, 6, "_jsxRuntime"], [213, 26, 286, 6], [213, 27, 286, 6, "jsxs"], [213, 31, 286, 6], [213, 33, 286, 7, "_reactNative"], [213, 45, 286, 7], [213, 46, 286, 7, "View"], [213, 50, 286, 11], [214, 6, 286, 12, "style"], [214, 11, 286, 17], [214, 13, 286, 19, "webViewContainerStyle"], [214, 34, 286, 41], [215, 6, 286, 41, "children"], [215, 14, 286, 41], [215, 17, 287, 9, "webView"], [215, 24, 287, 16], [215, 26, 288, 9, "otherView"], [215, 35, 288, 18], [216, 4, 288, 18], [216, 5, 289, 12], [216, 6, 289, 13], [217, 2, 291, 2], [217, 3, 292, 0], [217, 4, 292, 1], [219, 2, 294, 0], [220, 2, 295, 0], [220, 6, 295, 6, "isFileUploadSupported"], [220, 27, 295, 51], [221, 4, 295, 51], [221, 8, 295, 51, "_ref6"], [221, 13, 295, 51], [221, 20, 295, 51, "_asyncToGenerator2"], [221, 38, 295, 51], [221, 39, 295, 51, "default"], [221, 46, 295, 51], [221, 48, 295, 54], [222, 6, 295, 54], [222, 13, 295, 66], [222, 17, 295, 70], [223, 4, 295, 70], [224, 4, 295, 70], [224, 20, 295, 6, "isFileUploadSupported"], [224, 41, 295, 51, "isFileUploadSupported"], [224, 42, 295, 51], [225, 6, 295, 51], [225, 13, 295, 51, "_ref6"], [225, 18, 295, 51], [225, 19, 295, 51, "apply"], [225, 24, 295, 51], [225, 31, 295, 51, "arguments"], [225, 40, 295, 51], [226, 4, 295, 51], [227, 2, 295, 51], [227, 5, 295, 70], [228, 2, 297, 0], [228, 6, 297, 6, "WebView"], [228, 13, 297, 13], [228, 16, 297, 16, "Object"], [228, 22, 297, 22], [228, 23, 297, 23, "assign"], [228, 29, 297, 29], [228, 30, 297, 30, "WebViewComponent"], [228, 46, 297, 46], [228, 48, 297, 48], [229, 4, 297, 50, "isFileUploadSupported"], [230, 2, 297, 72], [230, 3, 297, 73], [230, 4, 297, 74], [231, 2, 297, 75], [231, 6, 297, 75, "_default"], [231, 14, 297, 75], [231, 17, 297, 75, "exports"], [231, 24, 297, 75], [231, 25, 297, 75, "default"], [231, 32, 297, 75], [231, 35, 299, 15, "WebView"], [231, 42, 299, 22], [232, 0, 299, 22], [232, 3]], "functionMap": {"names": ["<global>", "processDecelerationRate", "useWarnIfChanges", "forwardRef$argument_0", "onShouldStartLoadWithRequestCallback", "useImperativeHandle$argument_1", "goForward", "goBack", "reload", "stopLoading", "postMessage", "injectJavaScript", "requestFocus", "clearCache", "Object.entries.reduce$argument_0", "Object.entries.map$argument_0", "isFileUploadSupported"], "mappings": "AAA;gCC2B;CDU;yBEE;CFQ;EGG;MCgD;ODK;MEoC;mBCC;sEDC;gBEC,+DF;gBGC;SHK;qBIC;wEJC;qBKC;8ELC;0BMC;6DNE;sBOC;yEPC;oBQC;mERE;QFC;YWiD;oDCO;uBDK;aXG;GHiE;sDgBI,gBhB"}}, "type": "js/module"}]}