{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@react-navigation/elements", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 73, "index": 88}}], "key": "LmqW7jh+SpCzQZMkzh+Awcuawt0=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 89}, "end": {"line": 4, "column": 63, "index": 152}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 153}, "end": {"line": 5, "column": 58, "index": 211}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-screens", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 212}, "end": {"line": 6, "column": 239, "index": 451}}], "key": "1mt/F1TLthnVYb8adgHW3gX1ZTg=", "exportNames": ["*"]}}, {"name": "./FontProcessor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 452}, "end": {"line": 7, "column": 47, "index": 499}}], "key": "cu1wZavk5MWZ2u4/0/21kgtBj9Y=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 500}, "end": {"line": 8, "column": 86, "index": 586}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useHeaderConfigProps = useHeaderConfigProps;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _elements = require(_dependencyMap[2], \"@react-navigation/elements\");\n  var _native = require(_dependencyMap[3], \"@react-navigation/native\");\n  var _reactNative = require(_dependencyMap[4], \"react-native\");\n  var _reactNativeScreens = require(_dependencyMap[5], \"react-native-screens\");\n  var _FontProcessor = require(_dependencyMap[6], \"./FontProcessor\");\n  var _jsxRuntime = require(_dependencyMap[7], \"react/jsx-runtime\");\n  function useHeaderConfigProps(_ref) {\n    var headerBackImageSource = _ref.headerBackImageSource,\n      headerBackButtonDisplayMode = _ref.headerBackButtonDisplayMode,\n      headerBackButtonMenuEnabled = _ref.headerBackButtonMenuEnabled,\n      headerBackTitle = _ref.headerBackTitle,\n      headerBackTitleStyle = _ref.headerBackTitleStyle,\n      headerBackVisible = _ref.headerBackVisible,\n      headerShadowVisible = _ref.headerShadowVisible,\n      headerLargeStyle = _ref.headerLargeStyle,\n      headerLargeTitle = _ref.headerLargeTitle,\n      headerLargeTitleShadowVisible = _ref.headerLargeTitleShadowVisible,\n      headerLargeTitleStyle = _ref.headerLargeTitleStyle,\n      headerBackground = _ref.headerBackground,\n      headerLeft = _ref.headerLeft,\n      headerRight = _ref.headerRight,\n      headerShown = _ref.headerShown,\n      headerStyle = _ref.headerStyle,\n      headerBlurEffect = _ref.headerBlurEffect,\n      headerTintColor = _ref.headerTintColor,\n      headerTitle = _ref.headerTitle,\n      headerTitleAlign = _ref.headerTitleAlign,\n      headerTitleStyle = _ref.headerTitleStyle,\n      headerTransparent = _ref.headerTransparent,\n      headerSearchBarOptions = _ref.headerSearchBarOptions,\n      headerTopInsetEnabled = _ref.headerTopInsetEnabled,\n      headerBack = _ref.headerBack,\n      route = _ref.route,\n      title = _ref.title;\n    var _useLocale = (0, _native.useLocale)(),\n      direction = _useLocale.direction;\n    var _useTheme = (0, _native.useTheme)(),\n      colors = _useTheme.colors,\n      fonts = _useTheme.fonts;\n    var tintColor = headerTintColor ?? (_reactNative.Platform.OS === 'ios' ? colors.primary : colors.text);\n    var headerBackTitleStyleFlattened = _reactNative.StyleSheet.flatten([fonts.regular, headerBackTitleStyle]) || {};\n    var headerLargeTitleStyleFlattened = _reactNative.StyleSheet.flatten([_reactNative.Platform.select({\n      ios: fonts.heavy,\n      default: fonts.medium\n    }), headerLargeTitleStyle]) || {};\n    var headerTitleStyleFlattened = _reactNative.StyleSheet.flatten([_reactNative.Platform.select({\n      ios: fonts.bold,\n      default: fonts.medium\n    }), headerTitleStyle]) || {};\n    var headerStyleFlattened = _reactNative.StyleSheet.flatten(headerStyle) || {};\n    var headerLargeStyleFlattened = _reactNative.StyleSheet.flatten(headerLargeStyle) || {};\n    var _processFonts = (0, _FontProcessor.processFonts)([headerBackTitleStyleFlattened.fontFamily, headerLargeTitleStyleFlattened.fontFamily, headerTitleStyleFlattened.fontFamily]),\n      _processFonts2 = (0, _slicedToArray2.default)(_processFonts, 3),\n      backTitleFontFamily = _processFonts2[0],\n      largeTitleFontFamily = _processFonts2[1],\n      titleFontFamily = _processFonts2[2];\n    var backTitleFontSize = 'fontSize' in headerBackTitleStyleFlattened ? headerBackTitleStyleFlattened.fontSize : undefined;\n    var titleText = (0, _elements.getHeaderTitle)({\n      title,\n      headerTitle\n    }, route.name);\n    var titleColor = 'color' in headerTitleStyleFlattened ? headerTitleStyleFlattened.color : headerTintColor ?? colors.text;\n    var titleFontSize = 'fontSize' in headerTitleStyleFlattened ? headerTitleStyleFlattened.fontSize : undefined;\n    var titleFontWeight = headerTitleStyleFlattened.fontWeight;\n    var largeTitleBackgroundColor = headerLargeStyleFlattened.backgroundColor;\n    var largeTitleColor = 'color' in headerLargeTitleStyleFlattened ? headerLargeTitleStyleFlattened.color : undefined;\n    var largeTitleFontSize = 'fontSize' in headerLargeTitleStyleFlattened ? headerLargeTitleStyleFlattened.fontSize : undefined;\n    var largeTitleFontWeight = headerLargeTitleStyleFlattened.fontWeight;\n    var headerTitleStyleSupported = {\n      color: titleColor\n    };\n    if (headerTitleStyleFlattened.fontFamily != null) {\n      headerTitleStyleSupported.fontFamily = headerTitleStyleFlattened.fontFamily;\n    }\n    if (titleFontSize != null) {\n      headerTitleStyleSupported.fontSize = titleFontSize;\n    }\n    if (titleFontWeight != null) {\n      headerTitleStyleSupported.fontWeight = titleFontWeight;\n    }\n    var headerBackgroundColor = headerStyleFlattened.backgroundColor ?? (headerBackground != null || headerTransparent ? 'transparent' : colors.card);\n    var canGoBack = headerBack != null;\n    var headerLeftElement = headerLeft?.({\n      tintColor,\n      canGoBack,\n      label: headerBackTitle ?? headerBack?.title,\n      // `href` is only applicable to web\n      href: undefined\n    });\n    var headerRightElement = headerRight?.({\n      tintColor,\n      canGoBack\n    });\n    var headerTitleElement = typeof headerTitle === 'function' ? headerTitle({\n      tintColor,\n      children: titleText\n    }) : null;\n    var supportsHeaderSearchBar = typeof _reactNativeScreens.isSearchBarAvailableForCurrentPlatform === 'boolean' ? _reactNativeScreens.isSearchBarAvailableForCurrentPlatform :\n    // Fallback for older versions of react-native-screens\n    _reactNative.Platform.OS === 'ios' && _reactNativeScreens.SearchBar != null;\n    var hasHeaderSearchBar = supportsHeaderSearchBar && headerSearchBarOptions != null;\n\n    /**\n     * We need to set this in if:\n     * - Back button should stay visible when `headerLeft` is specified\n     * - If `headerTitle` for Android is specified, so we only need to remove the title and keep the back button\n     */\n    var backButtonInCustomView = headerBackVisible || _reactNative.Platform.OS === 'android' && headerTitleElement != null && headerLeftElement == null;\n    var translucent = headerBackground != null || headerTransparent ||\n    // When using a SearchBar or large title, the header needs to be translucent for it to work on iOS\n    (hasHeaderSearchBar || headerLargeTitle) && _reactNative.Platform.OS === 'ios' && headerTransparent !== false;\n    var isBackButtonDisplayModeAvailable =\n    // On iOS 14+\n    _reactNative.Platform.OS === 'ios' && parseInt(_reactNative.Platform.Version, 10) >= 14 && (\n    // Doesn't have custom styling, by default System, see: https://github.com/software-mansion/react-native-screens/pull/2105#discussion_r1565222738\n    backTitleFontFamily == null || backTitleFontFamily === 'System') && backTitleFontSize == null &&\n    // Back button menu is not disabled\n    headerBackButtonMenuEnabled !== false;\n    var isCenterViewRenderedAndroid = headerTitleAlign === 'center';\n    var children = /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {\n      children: [_reactNative.Platform.OS === 'ios' ? /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {\n        children: [headerLeftElement != null ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenStackHeaderLeftView, {\n          children: headerLeftElement\n        }) : null, headerTitleElement != null ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenStackHeaderCenterView, {\n          children: headerTitleElement\n        }) : null]\n      }) : /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {\n        children: [headerLeftElement != null || typeof headerTitle === 'function' ? /*#__PURE__*/\n        // The style passed to header left, together with title element being wrapped\n        // in flex view is reqruied for proper header layout, in particular,\n        // for the text truncation to work.\n        (0, _jsxRuntime.jsxs)(_reactNativeScreens.ScreenStackHeaderLeftView, {\n          style: !isCenterViewRenderedAndroid ? {\n            flex: 1\n          } : null,\n          children: [headerLeftElement, headerTitleAlign !== 'center' ? typeof headerTitle === 'function' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n            style: {\n              flex: 1\n            },\n            children: headerTitleElement\n          }) : /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n            style: {\n              flex: 1\n            },\n            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.HeaderTitle, {\n              tintColor: tintColor,\n              style: headerTitleStyleSupported,\n              children: titleText\n            })\n          }) : null]\n        }) : null, isCenterViewRenderedAndroid ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenStackHeaderCenterView, {\n          children: typeof headerTitle === 'function' ? headerTitleElement : /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.HeaderTitle, {\n            tintColor: tintColor,\n            style: headerTitleStyleSupported,\n            children: titleText\n          })\n        }) : null]\n      }), headerBackImageSource !== undefined ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenStackHeaderBackButtonImage, {\n        source: headerBackImageSource\n      }) : null, headerRightElement != null ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenStackHeaderRightView, {\n        children: headerRightElement\n      }) : null, hasHeaderSearchBar ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenStackHeaderSearchBarView, {\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.SearchBar, {\n          ...headerSearchBarOptions\n        })\n      }) : null]\n    });\n    return {\n      backButtonInCustomView,\n      backgroundColor: headerBackgroundColor,\n      backTitle: headerBackTitle,\n      backTitleVisible: isBackButtonDisplayModeAvailable ? undefined : headerBackButtonDisplayMode !== 'minimal',\n      backButtonDisplayMode: isBackButtonDisplayModeAvailable ? headerBackButtonDisplayMode : undefined,\n      backTitleFontFamily,\n      backTitleFontSize,\n      blurEffect: headerBlurEffect,\n      color: tintColor,\n      direction,\n      disableBackButtonMenu: headerBackButtonMenuEnabled === false,\n      hidden: headerShown === false,\n      hideBackButton: headerBackVisible === false,\n      hideShadow: headerShadowVisible === false || headerBackground != null || headerTransparent && headerShadowVisible !== true,\n      largeTitle: headerLargeTitle,\n      largeTitleBackgroundColor,\n      largeTitleColor,\n      largeTitleFontFamily,\n      largeTitleFontSize,\n      largeTitleFontWeight,\n      largeTitleHideShadow: headerLargeTitleShadowVisible === false,\n      title: titleText,\n      titleColor,\n      titleFontFamily,\n      titleFontSize,\n      titleFontWeight: String(titleFontWeight),\n      topInsetEnabled: headerTopInsetEnabled,\n      translucent: translucent === true,\n      children\n    };\n  }\n});", "lineCount": 209, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "useHeaderConfigProps"], [8, 30, 1, 13], [8, 33, 1, 13, "useHeaderConfigProps"], [8, 53, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_elements"], [10, 15, 3, 0], [10, 18, 3, 0, "require"], [10, 25, 3, 0], [10, 26, 3, 0, "_dependencyMap"], [10, 40, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_native"], [11, 13, 4, 0], [11, 16, 4, 0, "require"], [11, 23, 4, 0], [11, 24, 4, 0, "_dependencyMap"], [11, 38, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_reactNative"], [12, 18, 5, 0], [12, 21, 5, 0, "require"], [12, 28, 5, 0], [12, 29, 5, 0, "_dependencyMap"], [12, 43, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_reactNativeScreens"], [13, 25, 6, 0], [13, 28, 6, 0, "require"], [13, 35, 6, 0], [13, 36, 6, 0, "_dependencyMap"], [13, 50, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_FontProcessor"], [14, 20, 7, 0], [14, 23, 7, 0, "require"], [14, 30, 7, 0], [14, 31, 7, 0, "_dependencyMap"], [14, 45, 7, 0], [15, 2, 8, 0], [15, 6, 8, 0, "_jsxRuntime"], [15, 17, 8, 0], [15, 20, 8, 0, "require"], [15, 27, 8, 0], [15, 28, 8, 0, "_dependencyMap"], [15, 42, 8, 0], [16, 2, 9, 7], [16, 11, 9, 16, "useHeaderConfigProps"], [16, 31, 9, 36, "useHeaderConfigProps"], [16, 32, 9, 36, "_ref"], [16, 36, 9, 36], [16, 38, 37, 3], [17, 4, 37, 3], [17, 8, 10, 2, "headerBackImageSource"], [17, 29, 10, 23], [17, 32, 10, 23, "_ref"], [17, 36, 10, 23], [17, 37, 10, 2, "headerBackImageSource"], [17, 58, 10, 23], [18, 6, 11, 2, "headerBackButtonDisplayMode"], [18, 33, 11, 29], [18, 36, 11, 29, "_ref"], [18, 40, 11, 29], [18, 41, 11, 2, "headerBackButtonDisplayMode"], [18, 68, 11, 29], [19, 6, 12, 2, "headerBackButtonMenuEnabled"], [19, 33, 12, 29], [19, 36, 12, 29, "_ref"], [19, 40, 12, 29], [19, 41, 12, 2, "headerBackButtonMenuEnabled"], [19, 68, 12, 29], [20, 6, 13, 2, "headerBackTitle"], [20, 21, 13, 17], [20, 24, 13, 17, "_ref"], [20, 28, 13, 17], [20, 29, 13, 2, "headerBackTitle"], [20, 44, 13, 17], [21, 6, 14, 2, "headerBackTitleStyle"], [21, 26, 14, 22], [21, 29, 14, 22, "_ref"], [21, 33, 14, 22], [21, 34, 14, 2, "headerBackTitleStyle"], [21, 54, 14, 22], [22, 6, 15, 2, "headerBackVisible"], [22, 23, 15, 19], [22, 26, 15, 19, "_ref"], [22, 30, 15, 19], [22, 31, 15, 2, "headerBackVisible"], [22, 48, 15, 19], [23, 6, 16, 2, "headerShadowVisible"], [23, 25, 16, 21], [23, 28, 16, 21, "_ref"], [23, 32, 16, 21], [23, 33, 16, 2, "headerShadowVisible"], [23, 52, 16, 21], [24, 6, 17, 2, "headerLargeStyle"], [24, 22, 17, 18], [24, 25, 17, 18, "_ref"], [24, 29, 17, 18], [24, 30, 17, 2, "headerLargeStyle"], [24, 46, 17, 18], [25, 6, 18, 2, "headerLargeTitle"], [25, 22, 18, 18], [25, 25, 18, 18, "_ref"], [25, 29, 18, 18], [25, 30, 18, 2, "headerLargeTitle"], [25, 46, 18, 18], [26, 6, 19, 2, "headerLargeTitleShadowVisible"], [26, 35, 19, 31], [26, 38, 19, 31, "_ref"], [26, 42, 19, 31], [26, 43, 19, 2, "headerLargeTitleShadowVisible"], [26, 72, 19, 31], [27, 6, 20, 2, "headerLargeTitleStyle"], [27, 27, 20, 23], [27, 30, 20, 23, "_ref"], [27, 34, 20, 23], [27, 35, 20, 2, "headerLargeTitleStyle"], [27, 56, 20, 23], [28, 6, 21, 2, "headerBackground"], [28, 22, 21, 18], [28, 25, 21, 18, "_ref"], [28, 29, 21, 18], [28, 30, 21, 2, "headerBackground"], [28, 46, 21, 18], [29, 6, 22, 2, "headerLeft"], [29, 16, 22, 12], [29, 19, 22, 12, "_ref"], [29, 23, 22, 12], [29, 24, 22, 2, "headerLeft"], [29, 34, 22, 12], [30, 6, 23, 2, "headerRight"], [30, 17, 23, 13], [30, 20, 23, 13, "_ref"], [30, 24, 23, 13], [30, 25, 23, 2, "headerRight"], [30, 36, 23, 13], [31, 6, 24, 2, "headerShown"], [31, 17, 24, 13], [31, 20, 24, 13, "_ref"], [31, 24, 24, 13], [31, 25, 24, 2, "headerShown"], [31, 36, 24, 13], [32, 6, 25, 2, "headerStyle"], [32, 17, 25, 13], [32, 20, 25, 13, "_ref"], [32, 24, 25, 13], [32, 25, 25, 2, "headerStyle"], [32, 36, 25, 13], [33, 6, 26, 2, "headerBlurEffect"], [33, 22, 26, 18], [33, 25, 26, 18, "_ref"], [33, 29, 26, 18], [33, 30, 26, 2, "headerBlurEffect"], [33, 46, 26, 18], [34, 6, 27, 2, "headerTintColor"], [34, 21, 27, 17], [34, 24, 27, 17, "_ref"], [34, 28, 27, 17], [34, 29, 27, 2, "headerTintColor"], [34, 44, 27, 17], [35, 6, 28, 2, "headerTitle"], [35, 17, 28, 13], [35, 20, 28, 13, "_ref"], [35, 24, 28, 13], [35, 25, 28, 2, "headerTitle"], [35, 36, 28, 13], [36, 6, 29, 2, "headerTitleAlign"], [36, 22, 29, 18], [36, 25, 29, 18, "_ref"], [36, 29, 29, 18], [36, 30, 29, 2, "headerTitleAlign"], [36, 46, 29, 18], [37, 6, 30, 2, "headerTitleStyle"], [37, 22, 30, 18], [37, 25, 30, 18, "_ref"], [37, 29, 30, 18], [37, 30, 30, 2, "headerTitleStyle"], [37, 46, 30, 18], [38, 6, 31, 2, "headerTransparent"], [38, 23, 31, 19], [38, 26, 31, 19, "_ref"], [38, 30, 31, 19], [38, 31, 31, 2, "headerTransparent"], [38, 48, 31, 19], [39, 6, 32, 2, "headerSearchBarOptions"], [39, 28, 32, 24], [39, 31, 32, 24, "_ref"], [39, 35, 32, 24], [39, 36, 32, 2, "headerSearchBarOptions"], [39, 58, 32, 24], [40, 6, 33, 2, "headerTopInsetEnabled"], [40, 27, 33, 23], [40, 30, 33, 23, "_ref"], [40, 34, 33, 23], [40, 35, 33, 2, "headerTopInsetEnabled"], [40, 56, 33, 23], [41, 6, 34, 2, "headerBack"], [41, 16, 34, 12], [41, 19, 34, 12, "_ref"], [41, 23, 34, 12], [41, 24, 34, 2, "headerBack"], [41, 34, 34, 12], [42, 6, 35, 2, "route"], [42, 11, 35, 7], [42, 14, 35, 7, "_ref"], [42, 18, 35, 7], [42, 19, 35, 2, "route"], [42, 24, 35, 7], [43, 6, 36, 2, "title"], [43, 11, 36, 7], [43, 14, 36, 7, "_ref"], [43, 18, 36, 7], [43, 19, 36, 2, "title"], [43, 24, 36, 7], [44, 4, 38, 2], [44, 8, 38, 2, "_useLocale"], [44, 18, 38, 2], [44, 21, 40, 6], [44, 25, 40, 6, "useLocale"], [44, 42, 40, 15], [44, 44, 40, 16], [44, 45, 40, 17], [45, 6, 39, 4, "direction"], [45, 15, 39, 13], [45, 18, 39, 13, "_useLocale"], [45, 28, 39, 13], [45, 29, 39, 4, "direction"], [45, 38, 39, 13], [46, 4, 41, 2], [46, 8, 41, 2, "_useTheme"], [46, 17, 41, 2], [46, 20, 44, 6], [46, 24, 44, 6, "useTheme"], [46, 40, 44, 14], [46, 42, 44, 15], [46, 43, 44, 16], [47, 6, 42, 4, "colors"], [47, 12, 42, 10], [47, 15, 42, 10, "_useTheme"], [47, 24, 42, 10], [47, 25, 42, 4, "colors"], [47, 31, 42, 10], [48, 6, 43, 4, "fonts"], [48, 11, 43, 9], [48, 14, 43, 9, "_useTheme"], [48, 23, 43, 9], [48, 24, 43, 4, "fonts"], [48, 29, 43, 9], [49, 4, 45, 2], [49, 8, 45, 8, "tintColor"], [49, 17, 45, 17], [49, 20, 45, 20, "headerTintColor"], [49, 35, 45, 35], [49, 40, 45, 40, "Platform"], [49, 61, 45, 48], [49, 62, 45, 49, "OS"], [49, 64, 45, 51], [49, 69, 45, 56], [49, 74, 45, 61], [49, 77, 45, 64, "colors"], [49, 83, 45, 70], [49, 84, 45, 71, "primary"], [49, 91, 45, 78], [49, 94, 45, 81, "colors"], [49, 100, 45, 87], [49, 101, 45, 88, "text"], [49, 105, 45, 92], [49, 106, 45, 93], [50, 4, 46, 2], [50, 8, 46, 8, "headerBackTitleStyleFlattened"], [50, 37, 46, 37], [50, 40, 46, 40, "StyleSheet"], [50, 63, 46, 50], [50, 64, 46, 51, "flatten"], [50, 71, 46, 58], [50, 72, 46, 59], [50, 73, 46, 60, "fonts"], [50, 78, 46, 65], [50, 79, 46, 66, "regular"], [50, 86, 46, 73], [50, 88, 46, 75, "headerBackTitleStyle"], [50, 108, 46, 95], [50, 109, 46, 96], [50, 110, 46, 97], [50, 114, 46, 101], [50, 115, 46, 102], [50, 116, 46, 103], [51, 4, 47, 2], [51, 8, 47, 8, "headerLargeTitleStyleFlattened"], [51, 38, 47, 38], [51, 41, 47, 41, "StyleSheet"], [51, 64, 47, 51], [51, 65, 47, 52, "flatten"], [51, 72, 47, 59], [51, 73, 47, 60], [51, 74, 47, 61, "Platform"], [51, 95, 47, 69], [51, 96, 47, 70, "select"], [51, 102, 47, 76], [51, 103, 47, 77], [52, 6, 48, 4, "ios"], [52, 9, 48, 7], [52, 11, 48, 9, "fonts"], [52, 16, 48, 14], [52, 17, 48, 15, "heavy"], [52, 22, 48, 20], [53, 6, 49, 4, "default"], [53, 13, 49, 11], [53, 15, 49, 13, "fonts"], [53, 20, 49, 18], [53, 21, 49, 19, "medium"], [54, 4, 50, 2], [54, 5, 50, 3], [54, 6, 50, 4], [54, 8, 50, 6, "headerLargeTitleStyle"], [54, 29, 50, 27], [54, 30, 50, 28], [54, 31, 50, 29], [54, 35, 50, 33], [54, 36, 50, 34], [54, 37, 50, 35], [55, 4, 51, 2], [55, 8, 51, 8, "headerTitleStyleFlattened"], [55, 33, 51, 33], [55, 36, 51, 36, "StyleSheet"], [55, 59, 51, 46], [55, 60, 51, 47, "flatten"], [55, 67, 51, 54], [55, 68, 51, 55], [55, 69, 51, 56, "Platform"], [55, 90, 51, 64], [55, 91, 51, 65, "select"], [55, 97, 51, 71], [55, 98, 51, 72], [56, 6, 52, 4, "ios"], [56, 9, 52, 7], [56, 11, 52, 9, "fonts"], [56, 16, 52, 14], [56, 17, 52, 15, "bold"], [56, 21, 52, 19], [57, 6, 53, 4, "default"], [57, 13, 53, 11], [57, 15, 53, 13, "fonts"], [57, 20, 53, 18], [57, 21, 53, 19, "medium"], [58, 4, 54, 2], [58, 5, 54, 3], [58, 6, 54, 4], [58, 8, 54, 6, "headerTitleStyle"], [58, 24, 54, 22], [58, 25, 54, 23], [58, 26, 54, 24], [58, 30, 54, 28], [58, 31, 54, 29], [58, 32, 54, 30], [59, 4, 55, 2], [59, 8, 55, 8, "headerStyleFlattened"], [59, 28, 55, 28], [59, 31, 55, 31, "StyleSheet"], [59, 54, 55, 41], [59, 55, 55, 42, "flatten"], [59, 62, 55, 49], [59, 63, 55, 50, "headerStyle"], [59, 74, 55, 61], [59, 75, 55, 62], [59, 79, 55, 66], [59, 80, 55, 67], [59, 81, 55, 68], [60, 4, 56, 2], [60, 8, 56, 8, "headerLargeStyleFlattened"], [60, 33, 56, 33], [60, 36, 56, 36, "StyleSheet"], [60, 59, 56, 46], [60, 60, 56, 47, "flatten"], [60, 67, 56, 54], [60, 68, 56, 55, "headerLargeStyle"], [60, 84, 56, 71], [60, 85, 56, 72], [60, 89, 56, 76], [60, 90, 56, 77], [60, 91, 56, 78], [61, 4, 57, 2], [61, 8, 57, 2, "_processFonts"], [61, 21, 57, 2], [61, 24, 57, 71], [61, 28, 57, 71, "processFonts"], [61, 55, 57, 83], [61, 57, 57, 84], [61, 58, 57, 85, "headerBackTitleStyleFlattened"], [61, 87, 57, 114], [61, 88, 57, 115, "fontFamily"], [61, 98, 57, 125], [61, 100, 57, 127, "headerLargeTitleStyleFlattened"], [61, 130, 57, 157], [61, 131, 57, 158, "fontFamily"], [61, 141, 57, 168], [61, 143, 57, 170, "headerTitleStyleFlattened"], [61, 168, 57, 195], [61, 169, 57, 196, "fontFamily"], [61, 179, 57, 206], [61, 180, 57, 207], [61, 181, 57, 208], [62, 6, 57, 208, "_processFonts2"], [62, 20, 57, 208], [62, 27, 57, 208, "_slicedToArray2"], [62, 42, 57, 208], [62, 43, 57, 208, "default"], [62, 50, 57, 208], [62, 52, 57, 208, "_processFonts"], [62, 65, 57, 208], [63, 6, 57, 9, "backTitleFontFamily"], [63, 25, 57, 28], [63, 28, 57, 28, "_processFonts2"], [63, 42, 57, 28], [64, 6, 57, 30, "largeTitleFontFamily"], [64, 26, 57, 50], [64, 29, 57, 50, "_processFonts2"], [64, 43, 57, 50], [65, 6, 57, 52, "titleFontFamily"], [65, 21, 57, 67], [65, 24, 57, 67, "_processFonts2"], [65, 38, 57, 67], [66, 4, 58, 2], [66, 8, 58, 8, "backTitleFontSize"], [66, 25, 58, 25], [66, 28, 58, 28], [66, 38, 58, 38], [66, 42, 58, 42, "headerBackTitleStyleFlattened"], [66, 71, 58, 71], [66, 74, 58, 74, "headerBackTitleStyleFlattened"], [66, 103, 58, 103], [66, 104, 58, 104, "fontSize"], [66, 112, 58, 112], [66, 115, 58, 115, "undefined"], [66, 124, 58, 124], [67, 4, 59, 2], [67, 8, 59, 8, "titleText"], [67, 17, 59, 17], [67, 20, 59, 20], [67, 24, 59, 20, "getHeaderTitle"], [67, 48, 59, 34], [67, 50, 59, 35], [68, 6, 60, 4, "title"], [68, 11, 60, 9], [69, 6, 61, 4, "headerTitle"], [70, 4, 62, 2], [70, 5, 62, 3], [70, 7, 62, 5, "route"], [70, 12, 62, 10], [70, 13, 62, 11, "name"], [70, 17, 62, 15], [70, 18, 62, 16], [71, 4, 63, 2], [71, 8, 63, 8, "titleColor"], [71, 18, 63, 18], [71, 21, 63, 21], [71, 28, 63, 28], [71, 32, 63, 32, "headerTitleStyleFlattened"], [71, 57, 63, 57], [71, 60, 63, 60, "headerTitleStyleFlattened"], [71, 85, 63, 85], [71, 86, 63, 86, "color"], [71, 91, 63, 91], [71, 94, 63, 94, "headerTintColor"], [71, 109, 63, 109], [71, 113, 63, 113, "colors"], [71, 119, 63, 119], [71, 120, 63, 120, "text"], [71, 124, 63, 124], [72, 4, 64, 2], [72, 8, 64, 8, "titleFontSize"], [72, 21, 64, 21], [72, 24, 64, 24], [72, 34, 64, 34], [72, 38, 64, 38, "headerTitleStyleFlattened"], [72, 63, 64, 63], [72, 66, 64, 66, "headerTitleStyleFlattened"], [72, 91, 64, 91], [72, 92, 64, 92, "fontSize"], [72, 100, 64, 100], [72, 103, 64, 103, "undefined"], [72, 112, 64, 112], [73, 4, 65, 2], [73, 8, 65, 8, "titleFontWeight"], [73, 23, 65, 23], [73, 26, 65, 26, "headerTitleStyleFlattened"], [73, 51, 65, 51], [73, 52, 65, 52, "fontWeight"], [73, 62, 65, 62], [74, 4, 66, 2], [74, 8, 66, 8, "largeTitleBackgroundColor"], [74, 33, 66, 33], [74, 36, 66, 36, "headerLargeStyleFlattened"], [74, 61, 66, 61], [74, 62, 66, 62, "backgroundColor"], [74, 77, 66, 77], [75, 4, 67, 2], [75, 8, 67, 8, "largeTitleColor"], [75, 23, 67, 23], [75, 26, 67, 26], [75, 33, 67, 33], [75, 37, 67, 37, "headerLargeTitleStyleFlattened"], [75, 67, 67, 67], [75, 70, 67, 70, "headerLargeTitleStyleFlattened"], [75, 100, 67, 100], [75, 101, 67, 101, "color"], [75, 106, 67, 106], [75, 109, 67, 109, "undefined"], [75, 118, 67, 118], [76, 4, 68, 2], [76, 8, 68, 8, "largeTitleFontSize"], [76, 26, 68, 26], [76, 29, 68, 29], [76, 39, 68, 39], [76, 43, 68, 43, "headerLargeTitleStyleFlattened"], [76, 73, 68, 73], [76, 76, 68, 76, "headerLargeTitleStyleFlattened"], [76, 106, 68, 106], [76, 107, 68, 107, "fontSize"], [76, 115, 68, 115], [76, 118, 68, 118, "undefined"], [76, 127, 68, 127], [77, 4, 69, 2], [77, 8, 69, 8, "largeTitleFontWeight"], [77, 28, 69, 28], [77, 31, 69, 31, "headerLargeTitleStyleFlattened"], [77, 61, 69, 61], [77, 62, 69, 62, "fontWeight"], [77, 72, 69, 72], [78, 4, 70, 2], [78, 8, 70, 8, "headerTitleStyleSupported"], [78, 33, 70, 33], [78, 36, 70, 36], [79, 6, 71, 4, "color"], [79, 11, 71, 9], [79, 13, 71, 11, "titleColor"], [80, 4, 72, 2], [80, 5, 72, 3], [81, 4, 73, 2], [81, 8, 73, 6, "headerTitleStyleFlattened"], [81, 33, 73, 31], [81, 34, 73, 32, "fontFamily"], [81, 44, 73, 42], [81, 48, 73, 46], [81, 52, 73, 50], [81, 54, 73, 52], [82, 6, 74, 4, "headerTitleStyleSupported"], [82, 31, 74, 29], [82, 32, 74, 30, "fontFamily"], [82, 42, 74, 40], [82, 45, 74, 43, "headerTitleStyleFlattened"], [82, 70, 74, 68], [82, 71, 74, 69, "fontFamily"], [82, 81, 74, 79], [83, 4, 75, 2], [84, 4, 76, 2], [84, 8, 76, 6, "titleFontSize"], [84, 21, 76, 19], [84, 25, 76, 23], [84, 29, 76, 27], [84, 31, 76, 29], [85, 6, 77, 4, "headerTitleStyleSupported"], [85, 31, 77, 29], [85, 32, 77, 30, "fontSize"], [85, 40, 77, 38], [85, 43, 77, 41, "titleFontSize"], [85, 56, 77, 54], [86, 4, 78, 2], [87, 4, 79, 2], [87, 8, 79, 6, "titleFontWeight"], [87, 23, 79, 21], [87, 27, 79, 25], [87, 31, 79, 29], [87, 33, 79, 31], [88, 6, 80, 4, "headerTitleStyleSupported"], [88, 31, 80, 29], [88, 32, 80, 30, "fontWeight"], [88, 42, 80, 40], [88, 45, 80, 43, "titleFontWeight"], [88, 60, 80, 58], [89, 4, 81, 2], [90, 4, 82, 2], [90, 8, 82, 8, "headerBackgroundColor"], [90, 29, 82, 29], [90, 32, 82, 32, "headerStyleFlattened"], [90, 52, 82, 52], [90, 53, 82, 53, "backgroundColor"], [90, 68, 82, 68], [90, 73, 82, 73, "headerBackground"], [90, 89, 82, 89], [90, 93, 82, 93], [90, 97, 82, 97], [90, 101, 82, 101, "headerTransparent"], [90, 118, 82, 118], [90, 121, 82, 121], [90, 134, 82, 134], [90, 137, 82, 137, "colors"], [90, 143, 82, 143], [90, 144, 82, 144, "card"], [90, 148, 82, 148], [90, 149, 82, 149], [91, 4, 83, 2], [91, 8, 83, 8, "canGoBack"], [91, 17, 83, 17], [91, 20, 83, 20, "headerBack"], [91, 30, 83, 30], [91, 34, 83, 34], [91, 38, 83, 38], [92, 4, 84, 2], [92, 8, 84, 8, "headerLeftElement"], [92, 25, 84, 25], [92, 28, 84, 28, "headerLeft"], [92, 38, 84, 38], [92, 41, 84, 41], [93, 6, 85, 4, "tintColor"], [93, 15, 85, 13], [94, 6, 86, 4, "canGoBack"], [94, 15, 86, 13], [95, 6, 87, 4, "label"], [95, 11, 87, 9], [95, 13, 87, 11, "headerBackTitle"], [95, 28, 87, 26], [95, 32, 87, 30, "headerBack"], [95, 42, 87, 40], [95, 44, 87, 42, "title"], [95, 49, 87, 47], [96, 6, 88, 4], [97, 6, 89, 4, "href"], [97, 10, 89, 8], [97, 12, 89, 10, "undefined"], [98, 4, 90, 2], [98, 5, 90, 3], [98, 6, 90, 4], [99, 4, 91, 2], [99, 8, 91, 8, "headerRightElement"], [99, 26, 91, 26], [99, 29, 91, 29, "headerRight"], [99, 40, 91, 40], [99, 43, 91, 43], [100, 6, 92, 4, "tintColor"], [100, 15, 92, 13], [101, 6, 93, 4, "canGoBack"], [102, 4, 94, 2], [102, 5, 94, 3], [102, 6, 94, 4], [103, 4, 95, 2], [103, 8, 95, 8, "headerTitleElement"], [103, 26, 95, 26], [103, 29, 95, 29], [103, 36, 95, 36, "headerTitle"], [103, 47, 95, 47], [103, 52, 95, 52], [103, 62, 95, 62], [103, 65, 95, 65, "headerTitle"], [103, 76, 95, 76], [103, 77, 95, 77], [104, 6, 96, 4, "tintColor"], [104, 15, 96, 13], [105, 6, 97, 4, "children"], [105, 14, 97, 12], [105, 16, 97, 14, "titleText"], [106, 4, 98, 2], [106, 5, 98, 3], [106, 6, 98, 4], [106, 9, 98, 7], [106, 13, 98, 11], [107, 4, 99, 2], [107, 8, 99, 8, "supportsHeaderSearchBar"], [107, 31, 99, 31], [107, 34, 99, 34], [107, 41, 99, 41, "isSearchBarAvailableForCurrentPlatform"], [107, 99, 99, 79], [107, 104, 99, 84], [107, 113, 99, 93], [107, 116, 99, 96, "isSearchBarAvailableForCurrentPlatform"], [107, 174, 99, 134], [108, 4, 100, 2], [109, 4, 101, 2, "Platform"], [109, 25, 101, 10], [109, 26, 101, 11, "OS"], [109, 28, 101, 13], [109, 33, 101, 18], [109, 38, 101, 23], [109, 42, 101, 27, "SearchBar"], [109, 71, 101, 36], [109, 75, 101, 40], [109, 79, 101, 44], [110, 4, 102, 2], [110, 8, 102, 8, "hasHeaderSearchBar"], [110, 26, 102, 26], [110, 29, 102, 29, "supportsHeaderSearchBar"], [110, 52, 102, 52], [110, 56, 102, 56, "headerSearchBarOptions"], [110, 78, 102, 78], [110, 82, 102, 82], [110, 86, 102, 86], [112, 4, 104, 2], [113, 0, 105, 0], [114, 0, 106, 0], [115, 0, 107, 0], [116, 0, 108, 0], [117, 4, 109, 2], [117, 8, 109, 8, "backButtonInCustomView"], [117, 30, 109, 30], [117, 33, 109, 33, "headerBackVisible"], [117, 50, 109, 50], [117, 54, 109, 54, "Platform"], [117, 75, 109, 62], [117, 76, 109, 63, "OS"], [117, 78, 109, 65], [117, 83, 109, 70], [117, 92, 109, 79], [117, 96, 109, 83, "headerTitleElement"], [117, 114, 109, 101], [117, 118, 109, 105], [117, 122, 109, 109], [117, 126, 109, 113, "headerLeftElement"], [117, 143, 109, 130], [117, 147, 109, 134], [117, 151, 109, 138], [118, 4, 110, 2], [118, 8, 110, 8, "translucent"], [118, 19, 110, 19], [118, 22, 110, 22, "headerBackground"], [118, 38, 110, 38], [118, 42, 110, 42], [118, 46, 110, 46], [118, 50, 110, 50, "headerTransparent"], [118, 67, 110, 67], [119, 4, 111, 2], [120, 4, 112, 2], [120, 5, 112, 3, "hasHeaderSearchBar"], [120, 23, 112, 21], [120, 27, 112, 25, "headerLargeTitle"], [120, 43, 112, 41], [120, 48, 112, 46, "Platform"], [120, 69, 112, 54], [120, 70, 112, 55, "OS"], [120, 72, 112, 57], [120, 77, 112, 62], [120, 82, 112, 67], [120, 86, 112, 71, "headerTransparent"], [120, 103, 112, 88], [120, 108, 112, 93], [120, 113, 112, 98], [121, 4, 113, 2], [121, 8, 113, 8, "isBackButtonDisplayModeAvailable"], [121, 40, 113, 40], [122, 4, 114, 2], [123, 4, 115, 2, "Platform"], [123, 25, 115, 10], [123, 26, 115, 11, "OS"], [123, 28, 115, 13], [123, 33, 115, 18], [123, 38, 115, 23], [123, 42, 115, 27, "parseInt"], [123, 50, 115, 35], [123, 51, 115, 36, "Platform"], [123, 72, 115, 44], [123, 73, 115, 45, "Version"], [123, 80, 115, 52], [123, 82, 115, 54], [123, 84, 115, 56], [123, 85, 115, 57], [123, 89, 115, 61], [123, 91, 115, 63], [124, 4, 116, 2], [125, 4, 117, 2, "backTitleFontFamily"], [125, 23, 117, 21], [125, 27, 117, 25], [125, 31, 117, 29], [125, 35, 117, 33, "backTitleFontFamily"], [125, 54, 117, 52], [125, 59, 117, 57], [125, 67, 117, 65], [125, 68, 117, 66], [125, 72, 117, 70, "backTitleFontSize"], [125, 89, 117, 87], [125, 93, 117, 91], [125, 97, 117, 95], [126, 4, 118, 2], [127, 4, 119, 2, "headerBackButtonMenuEnabled"], [127, 31, 119, 29], [127, 36, 119, 34], [127, 41, 119, 39], [128, 4, 120, 2], [128, 8, 120, 8, "isCenterViewRenderedAndroid"], [128, 35, 120, 35], [128, 38, 120, 38, "headerTitleAlign"], [128, 54, 120, 54], [128, 59, 120, 59], [128, 67, 120, 67], [129, 4, 121, 2], [129, 8, 121, 8, "children"], [129, 16, 121, 16], [129, 19, 121, 19], [129, 32, 121, 32], [129, 36, 121, 32, "_jsxs"], [129, 52, 121, 37], [129, 54, 121, 38, "_Fragment"], [129, 74, 121, 47], [129, 76, 121, 49], [130, 6, 122, 4, "children"], [130, 14, 122, 12], [130, 16, 122, 14], [130, 17, 122, 15, "Platform"], [130, 38, 122, 23], [130, 39, 122, 24, "OS"], [130, 41, 122, 26], [130, 46, 122, 31], [130, 51, 122, 36], [130, 54, 122, 39], [130, 67, 122, 52], [130, 71, 122, 52, "_jsxs"], [130, 87, 122, 57], [130, 89, 122, 58, "_Fragment"], [130, 109, 122, 67], [130, 111, 122, 69], [131, 8, 123, 6, "children"], [131, 16, 123, 14], [131, 18, 123, 16], [131, 19, 123, 17, "headerLeftElement"], [131, 36, 123, 34], [131, 40, 123, 38], [131, 44, 123, 42], [131, 47, 123, 45], [131, 60, 123, 58], [131, 64, 123, 58, "_jsx"], [131, 79, 123, 62], [131, 81, 123, 63, "ScreenStackHeaderLeftView"], [131, 126, 123, 88], [131, 128, 123, 90], [132, 10, 124, 8, "children"], [132, 18, 124, 16], [132, 20, 124, 18, "headerLeftElement"], [133, 8, 125, 6], [133, 9, 125, 7], [133, 10, 125, 8], [133, 13, 125, 11], [133, 17, 125, 15], [133, 19, 125, 17, "headerTitleElement"], [133, 37, 125, 35], [133, 41, 125, 39], [133, 45, 125, 43], [133, 48, 125, 46], [133, 61, 125, 59], [133, 65, 125, 59, "_jsx"], [133, 80, 125, 63], [133, 82, 125, 64, "ScreenStackHeaderCenterView"], [133, 129, 125, 91], [133, 131, 125, 93], [134, 10, 126, 8, "children"], [134, 18, 126, 16], [134, 20, 126, 18, "headerTitleElement"], [135, 8, 127, 6], [135, 9, 127, 7], [135, 10, 127, 8], [135, 13, 127, 11], [135, 17, 127, 15], [136, 6, 128, 4], [136, 7, 128, 5], [136, 8, 128, 6], [136, 11, 128, 9], [136, 24, 128, 22], [136, 28, 128, 22, "_jsxs"], [136, 44, 128, 27], [136, 46, 128, 28, "_Fragment"], [136, 66, 128, 37], [136, 68, 128, 39], [137, 8, 129, 6, "children"], [137, 16, 129, 14], [137, 18, 129, 16], [137, 19, 129, 17, "headerLeftElement"], [137, 36, 129, 34], [137, 40, 129, 38], [137, 44, 129, 42], [137, 48, 129, 46], [137, 55, 129, 53, "headerTitle"], [137, 66, 129, 64], [137, 71, 129, 69], [137, 81, 129, 79], [137, 84, 130, 6], [138, 8, 131, 6], [139, 8, 132, 6], [140, 8, 133, 6], [141, 8, 134, 6], [141, 12, 134, 6, "_jsxs"], [141, 28, 134, 11], [141, 30, 134, 12, "ScreenStackHeaderLeftView"], [141, 75, 134, 37], [141, 77, 134, 39], [142, 10, 135, 8, "style"], [142, 15, 135, 13], [142, 17, 135, 15], [142, 18, 135, 16, "isCenterViewRenderedAndroid"], [142, 45, 135, 43], [142, 48, 135, 46], [143, 12, 136, 10, "flex"], [143, 16, 136, 14], [143, 18, 136, 16], [144, 10, 137, 8], [144, 11, 137, 9], [144, 14, 137, 12], [144, 18, 137, 16], [145, 10, 138, 8, "children"], [145, 18, 138, 16], [145, 20, 138, 18], [145, 21, 138, 19, "headerLeftElement"], [145, 38, 138, 36], [145, 40, 138, 38, "headerTitleAlign"], [145, 56, 138, 54], [145, 61, 138, 59], [145, 69, 138, 67], [145, 72, 138, 70], [145, 79, 138, 77, "headerTitle"], [145, 90, 138, 88], [145, 95, 138, 93], [145, 105, 138, 103], [145, 108, 138, 106], [145, 121, 138, 119], [145, 125, 138, 119, "_jsx"], [145, 140, 138, 123], [145, 142, 138, 124, "View"], [145, 159, 138, 128], [145, 161, 138, 130], [146, 12, 139, 10, "style"], [146, 17, 139, 15], [146, 19, 139, 17], [147, 14, 140, 12, "flex"], [147, 18, 140, 16], [147, 20, 140, 18], [148, 12, 141, 10], [148, 13, 141, 11], [149, 12, 142, 10, "children"], [149, 20, 142, 18], [149, 22, 142, 20, "headerTitleElement"], [150, 10, 143, 8], [150, 11, 143, 9], [150, 12, 143, 10], [150, 15, 143, 13], [150, 28, 143, 26], [150, 32, 143, 26, "_jsx"], [150, 47, 143, 30], [150, 49, 143, 31, "View"], [150, 66, 143, 35], [150, 68, 143, 37], [151, 12, 144, 10, "style"], [151, 17, 144, 15], [151, 19, 144, 17], [152, 14, 145, 12, "flex"], [152, 18, 145, 16], [152, 20, 145, 18], [153, 12, 146, 10], [153, 13, 146, 11], [154, 12, 147, 10, "children"], [154, 20, 147, 18], [154, 22, 147, 20], [154, 35, 147, 33], [154, 39, 147, 33, "_jsx"], [154, 54, 147, 37], [154, 56, 147, 38, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [154, 77, 147, 49], [154, 79, 147, 51], [155, 14, 148, 12, "tintColor"], [155, 23, 148, 21], [155, 25, 148, 23, "tintColor"], [155, 34, 148, 32], [156, 14, 149, 12, "style"], [156, 19, 149, 17], [156, 21, 149, 19, "headerTitleStyleSupported"], [156, 46, 149, 44], [157, 14, 150, 12, "children"], [157, 22, 150, 20], [157, 24, 150, 22, "titleText"], [158, 12, 151, 10], [158, 13, 151, 11], [159, 10, 152, 8], [159, 11, 152, 9], [159, 12, 152, 10], [159, 15, 152, 13], [159, 19, 152, 17], [160, 8, 153, 6], [160, 9, 153, 7], [160, 10, 153, 8], [160, 13, 153, 11], [160, 17, 153, 15], [160, 19, 153, 17, "isCenterViewRenderedAndroid"], [160, 46, 153, 44], [160, 49, 153, 47], [160, 62, 153, 60], [160, 66, 153, 60, "_jsx"], [160, 81, 153, 64], [160, 83, 153, 65, "ScreenStackHeaderCenterView"], [160, 130, 153, 92], [160, 132, 153, 94], [161, 10, 154, 8, "children"], [161, 18, 154, 16], [161, 20, 154, 18], [161, 27, 154, 25, "headerTitle"], [161, 38, 154, 36], [161, 43, 154, 41], [161, 53, 154, 51], [161, 56, 154, 54, "headerTitleElement"], [161, 74, 154, 72], [161, 77, 154, 75], [161, 90, 154, 88], [161, 94, 154, 88, "_jsx"], [161, 109, 154, 92], [161, 111, 154, 93, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [161, 132, 154, 104], [161, 134, 154, 106], [162, 12, 155, 10, "tintColor"], [162, 21, 155, 19], [162, 23, 155, 21, "tintColor"], [162, 32, 155, 30], [163, 12, 156, 10, "style"], [163, 17, 156, 15], [163, 19, 156, 17, "headerTitleStyleSupported"], [163, 44, 156, 42], [164, 12, 157, 10, "children"], [164, 20, 157, 18], [164, 22, 157, 20, "titleText"], [165, 10, 158, 8], [165, 11, 158, 9], [166, 8, 159, 6], [166, 9, 159, 7], [166, 10, 159, 8], [166, 13, 159, 11], [166, 17, 159, 15], [167, 6, 160, 4], [167, 7, 160, 5], [167, 8, 160, 6], [167, 10, 160, 8, "headerBackImageSource"], [167, 31, 160, 29], [167, 36, 160, 34, "undefined"], [167, 45, 160, 43], [167, 48, 160, 46], [167, 61, 160, 59], [167, 65, 160, 59, "_jsx"], [167, 80, 160, 63], [167, 82, 160, 64, "ScreenStackHeaderBackButtonImage"], [167, 134, 160, 96], [167, 136, 160, 98], [168, 8, 161, 6, "source"], [168, 14, 161, 12], [168, 16, 161, 14, "headerBackImageSource"], [169, 6, 162, 4], [169, 7, 162, 5], [169, 8, 162, 6], [169, 11, 162, 9], [169, 15, 162, 13], [169, 17, 162, 15, "headerRightElement"], [169, 35, 162, 33], [169, 39, 162, 37], [169, 43, 162, 41], [169, 46, 162, 44], [169, 59, 162, 57], [169, 63, 162, 57, "_jsx"], [169, 78, 162, 61], [169, 80, 162, 62, "ScreenStackHeaderRightView"], [169, 126, 162, 88], [169, 128, 162, 90], [170, 8, 163, 6, "children"], [170, 16, 163, 14], [170, 18, 163, 16, "headerRightElement"], [171, 6, 164, 4], [171, 7, 164, 5], [171, 8, 164, 6], [171, 11, 164, 9], [171, 15, 164, 13], [171, 17, 164, 15, "hasHeaderSearchBar"], [171, 35, 164, 33], [171, 38, 164, 36], [171, 51, 164, 49], [171, 55, 164, 49, "_jsx"], [171, 70, 164, 53], [171, 72, 164, 54, "ScreenStackHeaderSearchBarView"], [171, 122, 164, 84], [171, 124, 164, 86], [172, 8, 165, 6, "children"], [172, 16, 165, 14], [172, 18, 165, 16], [172, 31, 165, 29], [172, 35, 165, 29, "_jsx"], [172, 50, 165, 33], [172, 52, 165, 34, "SearchBar"], [172, 81, 165, 43], [172, 83, 165, 45], [173, 10, 166, 8], [173, 13, 166, 11, "headerSearchBarOptions"], [174, 8, 167, 6], [174, 9, 167, 7], [175, 6, 168, 4], [175, 7, 168, 5], [175, 8, 168, 6], [175, 11, 168, 9], [175, 15, 168, 13], [176, 4, 169, 2], [176, 5, 169, 3], [176, 6, 169, 4], [177, 4, 170, 2], [177, 11, 170, 9], [178, 6, 171, 4, "backButtonInCustomView"], [178, 28, 171, 26], [179, 6, 172, 4, "backgroundColor"], [179, 21, 172, 19], [179, 23, 172, 21, "headerBackgroundColor"], [179, 44, 172, 42], [180, 6, 173, 4, "backTitle"], [180, 15, 173, 13], [180, 17, 173, 15, "headerBackTitle"], [180, 32, 173, 30], [181, 6, 174, 4, "backTitleVisible"], [181, 22, 174, 20], [181, 24, 174, 22, "isBackButtonDisplayModeAvailable"], [181, 56, 174, 54], [181, 59, 174, 57, "undefined"], [181, 68, 174, 66], [181, 71, 174, 69, "headerBackButtonDisplayMode"], [181, 98, 174, 96], [181, 103, 174, 101], [181, 112, 174, 110], [182, 6, 175, 4, "backButtonDisplayMode"], [182, 27, 175, 25], [182, 29, 175, 27, "isBackButtonDisplayModeAvailable"], [182, 61, 175, 59], [182, 64, 175, 62, "headerBackButtonDisplayMode"], [182, 91, 175, 89], [182, 94, 175, 92, "undefined"], [182, 103, 175, 101], [183, 6, 176, 4, "backTitleFontFamily"], [183, 25, 176, 23], [184, 6, 177, 4, "backTitleFontSize"], [184, 23, 177, 21], [185, 6, 178, 4, "blurEffect"], [185, 16, 178, 14], [185, 18, 178, 16, "headerBlurEffect"], [185, 34, 178, 32], [186, 6, 179, 4, "color"], [186, 11, 179, 9], [186, 13, 179, 11, "tintColor"], [186, 22, 179, 20], [187, 6, 180, 4, "direction"], [187, 15, 180, 13], [188, 6, 181, 4, "disableBackButtonMenu"], [188, 27, 181, 25], [188, 29, 181, 27, "headerBackButtonMenuEnabled"], [188, 56, 181, 54], [188, 61, 181, 59], [188, 66, 181, 64], [189, 6, 182, 4, "hidden"], [189, 12, 182, 10], [189, 14, 182, 12, "headerShown"], [189, 25, 182, 23], [189, 30, 182, 28], [189, 35, 182, 33], [190, 6, 183, 4, "hideBackButton"], [190, 20, 183, 18], [190, 22, 183, 20, "headerBackVisible"], [190, 39, 183, 37], [190, 44, 183, 42], [190, 49, 183, 47], [191, 6, 184, 4, "hideShadow"], [191, 16, 184, 14], [191, 18, 184, 16, "headerShadowVisible"], [191, 37, 184, 35], [191, 42, 184, 40], [191, 47, 184, 45], [191, 51, 184, 49, "headerBackground"], [191, 67, 184, 65], [191, 71, 184, 69], [191, 75, 184, 73], [191, 79, 184, 77, "headerTransparent"], [191, 96, 184, 94], [191, 100, 184, 98, "headerShadowVisible"], [191, 119, 184, 117], [191, 124, 184, 122], [191, 128, 184, 126], [192, 6, 185, 4, "largeTitle"], [192, 16, 185, 14], [192, 18, 185, 16, "headerLargeTitle"], [192, 34, 185, 32], [193, 6, 186, 4, "largeTitleBackgroundColor"], [193, 31, 186, 29], [194, 6, 187, 4, "largeTitleColor"], [194, 21, 187, 19], [195, 6, 188, 4, "largeTitleFontFamily"], [195, 26, 188, 24], [196, 6, 189, 4, "largeTitleFontSize"], [196, 24, 189, 22], [197, 6, 190, 4, "largeTitleFontWeight"], [197, 26, 190, 24], [198, 6, 191, 4, "largeTitleHideShadow"], [198, 26, 191, 24], [198, 28, 191, 26, "headerLargeTitleShadowVisible"], [198, 57, 191, 55], [198, 62, 191, 60], [198, 67, 191, 65], [199, 6, 192, 4, "title"], [199, 11, 192, 9], [199, 13, 192, 11, "titleText"], [199, 22, 192, 20], [200, 6, 193, 4, "titleColor"], [200, 16, 193, 14], [201, 6, 194, 4, "titleFontFamily"], [201, 21, 194, 19], [202, 6, 195, 4, "titleFontSize"], [202, 19, 195, 17], [203, 6, 196, 4, "titleFontWeight"], [203, 21, 196, 19], [203, 23, 196, 21, "String"], [203, 29, 196, 27], [203, 30, 196, 28, "titleFontWeight"], [203, 45, 196, 43], [203, 46, 196, 44], [204, 6, 197, 4, "topInsetEnabled"], [204, 21, 197, 19], [204, 23, 197, 21, "headerTopInsetEnabled"], [204, 44, 197, 42], [205, 6, 198, 4, "translucent"], [205, 17, 198, 15], [205, 19, 198, 17, "translucent"], [205, 30, 198, 28], [205, 35, 198, 33], [205, 39, 198, 37], [206, 6, 199, 4, "children"], [207, 4, 200, 2], [207, 5, 200, 3], [208, 2, 201, 0], [209, 0, 201, 1], [209, 3]], "functionMap": {"names": ["<global>", "useHeaderConfigProps"], "mappings": "AAA;OCQ;CDgM"}}, "type": "js/module"}]}