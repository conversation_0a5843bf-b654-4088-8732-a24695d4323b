{"dependencies": [{"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 49, "index": 49}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ManualGesture = void 0;\n  var _gesture = require(_dependencyMap[0], \"./gesture\");\n  const _worklet_8341633256781_init_data = {\n    code: \"function changeEventCalculator_reactNativeGestureHandler_manualGestureJs1(current,_previous){return current;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-gesture-handler/lib/module/handlers/gestures/manualGesture.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"changeEventCalculator_reactNativeGestureHandler_manualGestureJs1\\\",\\\"current\\\",\\\"_previous\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-gesture-handler/lib/module/handlers/gestures/manualGesture.js\\\"],\\\"mappings\\\":\\\"AAEA,SAAAA,gEAAmDA,CAAAC,OAAA,CAAAC,SAAA,EAGjD,MAAO,CAAAD,OAAO,CAChB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const changeEventCalculator = function () {\n    const _e = [new global.Error(), 1, -27];\n    const changeEventCalculator = function (current, _previous) {\n      return current;\n    };\n    changeEventCalculator.__closure = {};\n    changeEventCalculator.__workletHash = 8341633256781;\n    changeEventCalculator.__initData = _worklet_8341633256781_init_data;\n    changeEventCalculator.__stackDetails = _e;\n    return changeEventCalculator;\n  }();\n  class ManualGesture extends _gesture.ContinousBaseGesture {\n    constructor() {\n      super();\n      this.handlerName = 'ManualGestureHandler';\n    }\n    onChange(callback) {\n      // @ts-ignore TS being overprotective, Record<string, never> is Record\n      this.handlers.changeEventCalculator = changeEventCalculator;\n      return super.onChange(callback);\n    }\n  }\n  exports.ManualGesture = ManualGesture;\n});", "lineCount": 36, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_gesture"], [6, 14, 1, 0], [6, 17, 1, 0, "require"], [6, 24, 1, 0], [6, 25, 1, 0, "_dependencyMap"], [6, 39, 1, 0], [7, 2, 1, 49], [7, 8, 1, 49, "_worklet_8341633256781_init_data"], [7, 40, 1, 49], [8, 4, 1, 49, "code"], [8, 8, 1, 49], [9, 4, 1, 49, "location"], [9, 12, 1, 49], [10, 4, 1, 49, "sourceMap"], [10, 13, 1, 49], [11, 4, 1, 49, "version"], [11, 11, 1, 49], [12, 2, 1, 49], [13, 2, 1, 49], [13, 8, 1, 49, "changeEventCalculator"], [13, 29, 1, 49], [13, 32, 3, 0], [14, 4, 3, 0], [14, 10, 3, 0, "_e"], [14, 12, 3, 0], [14, 20, 3, 0, "global"], [14, 26, 3, 0], [14, 27, 3, 0, "Error"], [14, 32, 3, 0], [15, 4, 3, 0], [15, 10, 3, 0, "changeEventCalculator"], [15, 31, 3, 0], [15, 43, 3, 0, "changeEventCalculator"], [15, 44, 3, 31, "current"], [15, 51, 3, 38], [15, 53, 3, 40, "_previous"], [15, 62, 3, 49], [15, 64, 3, 51], [16, 6, 6, 2], [16, 13, 6, 9, "current"], [16, 20, 6, 16], [17, 4, 7, 0], [17, 5, 7, 1], [18, 4, 7, 1, "changeEventCalculator"], [18, 25, 7, 1], [18, 26, 7, 1, "__closure"], [18, 35, 7, 1], [19, 4, 7, 1, "changeEventCalculator"], [19, 25, 7, 1], [19, 26, 7, 1, "__workletHash"], [19, 39, 7, 1], [20, 4, 7, 1, "changeEventCalculator"], [20, 25, 7, 1], [20, 26, 7, 1, "__initData"], [20, 36, 7, 1], [20, 39, 7, 1, "_worklet_8341633256781_init_data"], [20, 71, 7, 1], [21, 4, 7, 1, "changeEventCalculator"], [21, 25, 7, 1], [21, 26, 7, 1, "__stackDetails"], [21, 40, 7, 1], [21, 43, 7, 1, "_e"], [21, 45, 7, 1], [22, 4, 7, 1], [22, 11, 7, 1, "changeEventCalculator"], [22, 32, 7, 1], [23, 2, 7, 1], [23, 3, 3, 0], [24, 2, 9, 7], [24, 8, 9, 13, "ManualGesture"], [24, 21, 9, 26], [24, 30, 9, 35, "ContinousBaseGesture"], [24, 59, 9, 55], [24, 60, 9, 56], [25, 4, 10, 2, "constructor"], [25, 15, 10, 13, "constructor"], [25, 16, 10, 13], [25, 18, 10, 16], [26, 6, 11, 4], [26, 11, 11, 9], [26, 12, 11, 10], [26, 13, 11, 11], [27, 6, 12, 4], [27, 10, 12, 8], [27, 11, 12, 9, "handler<PERSON>ame"], [27, 22, 12, 20], [27, 25, 12, 23], [27, 47, 12, 45], [28, 4, 13, 2], [29, 4, 15, 2, "onChange"], [29, 12, 15, 10, "onChange"], [29, 13, 15, 11, "callback"], [29, 21, 15, 19], [29, 23, 15, 21], [30, 6, 16, 4], [31, 6, 17, 4], [31, 10, 17, 8], [31, 11, 17, 9, "handlers"], [31, 19, 17, 17], [31, 20, 17, 18, "changeEventCalculator"], [31, 41, 17, 39], [31, 44, 17, 42, "changeEventCalculator"], [31, 65, 17, 63], [32, 6, 18, 4], [32, 13, 18, 11], [32, 18, 18, 16], [32, 19, 18, 17, "onChange"], [32, 27, 18, 25], [32, 28, 18, 26, "callback"], [32, 36, 18, 34], [32, 37, 18, 35], [33, 4, 19, 2], [34, 2, 21, 0], [35, 2, 21, 1, "exports"], [35, 9, 21, 1], [35, 10, 21, 1, "ManualGesture"], [35, 23, 21, 1], [35, 26, 21, 1, "ManualGesture"], [35, 39, 21, 1], [36, 0, 21, 1], [36, 3]], "functionMap": {"names": ["<global>", "changeEventCalculator", "ManualGesture", "ManualGesture#constructor", "ManualGesture#onChange"], "mappings": "AAA;ACE;CDI;OEE;ECC;GDG;EEE;GFI;CFE"}}, "type": "js/module"}]}