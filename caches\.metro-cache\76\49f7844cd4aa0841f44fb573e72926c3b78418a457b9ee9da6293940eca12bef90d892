{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  /**\n   * Copyright (c) <PERSON>.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var uppercasePattern = /[A-Z]/g;\n  var msPattern = /^ms-/;\n  var cache = {};\n  function toHyphenLower(match) {\n    return '-' + match.toLowerCase();\n  }\n  function hyphenateStyleName(name) {\n    if (name in cache) {\n      return cache[name];\n    }\n    var hName = name.replace(uppercasePattern, toHyphenLower);\n    return cache[name] = msPattern.test(hName) ? '-' + hName : hName;\n  }\n  var _default = exports.default = hyphenateStyleName;\n});", "lineCount": 29, "map": [[6, 2, 1, 0], [7, 0, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [15, 2, 10, 0], [15, 6, 10, 4, "uppercasePattern"], [15, 22, 10, 20], [15, 25, 10, 23], [15, 33, 10, 31], [16, 2, 11, 0], [16, 6, 11, 4, "msPattern"], [16, 15, 11, 13], [16, 18, 11, 16], [16, 24, 11, 22], [17, 2, 12, 0], [17, 6, 12, 4, "cache"], [17, 11, 12, 9], [17, 14, 12, 12], [17, 15, 12, 13], [17, 16, 12, 14], [18, 2, 13, 0], [18, 11, 13, 9, "toHyphenLower"], [18, 24, 13, 22, "toHyphenLower"], [18, 25, 13, 23, "match"], [18, 30, 13, 28], [18, 32, 13, 30], [19, 4, 14, 2], [19, 11, 14, 9], [19, 14, 14, 12], [19, 17, 14, 15, "match"], [19, 22, 14, 20], [19, 23, 14, 21, "toLowerCase"], [19, 34, 14, 32], [19, 35, 14, 33], [19, 36, 14, 34], [20, 2, 15, 0], [21, 2, 16, 0], [21, 11, 16, 9, "hyphenateStyleName"], [21, 29, 16, 27, "hyphenateStyleName"], [21, 30, 16, 28, "name"], [21, 34, 16, 32], [21, 36, 16, 34], [22, 4, 17, 2], [22, 8, 17, 6, "name"], [22, 12, 17, 10], [22, 16, 17, 14, "cache"], [22, 21, 17, 19], [22, 23, 17, 21], [23, 6, 18, 4], [23, 13, 18, 11, "cache"], [23, 18, 18, 16], [23, 19, 18, 17, "name"], [23, 23, 18, 21], [23, 24, 18, 22], [24, 4, 19, 2], [25, 4, 20, 2], [25, 8, 20, 6, "hName"], [25, 13, 20, 11], [25, 16, 20, 14, "name"], [25, 20, 20, 18], [25, 21, 20, 19, "replace"], [25, 28, 20, 26], [25, 29, 20, 27, "uppercasePattern"], [25, 45, 20, 43], [25, 47, 20, 45, "toHyphenLower"], [25, 60, 20, 58], [25, 61, 20, 59], [26, 4, 21, 2], [26, 11, 21, 9, "cache"], [26, 16, 21, 14], [26, 17, 21, 15, "name"], [26, 21, 21, 19], [26, 22, 21, 20], [26, 25, 21, 23, "msPattern"], [26, 34, 21, 32], [26, 35, 21, 33, "test"], [26, 39, 21, 37], [26, 40, 21, 38, "hName"], [26, 45, 21, 43], [26, 46, 21, 44], [26, 49, 21, 47], [26, 52, 21, 50], [26, 55, 21, 53, "hName"], [26, 60, 21, 58], [26, 63, 21, 61, "hName"], [26, 68, 21, 66], [27, 2, 22, 0], [28, 2, 22, 1], [28, 6, 22, 1, "_default"], [28, 14, 22, 1], [28, 17, 22, 1, "exports"], [28, 24, 22, 1], [28, 25, 22, 1, "default"], [28, 32, 22, 1], [28, 35, 23, 15, "hyphenateStyleName"], [28, 53, 23, 33], [29, 0, 23, 33], [29, 3]], "functionMap": {"names": ["<global>", "toHyphenLower", "hyphenateStyleName"], "mappings": "AAA;ACY;CDE;AEC;CFM"}}, "type": "js/module"}]}