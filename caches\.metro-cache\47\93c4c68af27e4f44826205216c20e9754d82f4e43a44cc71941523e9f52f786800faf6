{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * @format\n   */\n\n  var TouchHistoryMath = {\n    /**\n     * This code is optimized and not intended to look beautiful. This allows\n     * computing of touch centroids that have moved after `touchesChangedAfter`\n     * timeStamp. You can compute the current centroid involving all touches\n     * moves after `touchesChangedAfter`, or you can compute the previous\n     * centroid of all touches that were moved after `touchesChangedAfter`.\n     *\n     * @param {TouchHistoryMath} touchHistory Standard Responder touch track\n     * data.\n     * @param {number} touchesChangedAfter timeStamp after which moved touches\n     * are considered \"actively moving\" - not just \"active\".\n     * @param {boolean} isXAxis Consider `x` dimension vs. `y` dimension.\n     * @param {boolean} ofCurrent Compute current centroid for actively moving\n     * touches vs. previous centroid of now actively moving touches.\n     * @return {number} value of centroid in specified dimension.\n     */\n    centroidDimension: function centroidDimension(touchHistory, touchesChangedAfter, isXAxis, ofCurrent) {\n      var touchBank = touchHistory.touchBank;\n      var total = 0;\n      var count = 0;\n      var oneTouchData = touchHistory.numberActiveTouches === 1 ? touchHistory.touchBank[touchHistory.indexOfSingleActiveTouch] : null;\n      if (oneTouchData !== null) {\n        if (oneTouchData.touchActive && oneTouchData.currentTimeStamp > touchesChangedAfter) {\n          total += ofCurrent && isXAxis ? oneTouchData.currentPageX : ofCurrent && !isXAxis ? oneTouchData.currentPageY : !ofCurrent && isXAxis ? oneTouchData.previousPageX : oneTouchData.previousPageY;\n          count = 1;\n        }\n      } else {\n        for (var i = 0; i < touchBank.length; i++) {\n          var touchTrack = touchBank[i];\n          if (touchTrack !== null && touchTrack !== undefined && touchTrack.touchActive && touchTrack.currentTimeStamp >= touchesChangedAfter) {\n            var toAdd = void 0; // Yuck, program temporarily in invalid state.\n            if (ofCurrent && isXAxis) {\n              toAdd = touchTrack.currentPageX;\n            } else if (ofCurrent && !isXAxis) {\n              toAdd = touchTrack.currentPageY;\n            } else if (!ofCurrent && isXAxis) {\n              toAdd = touchTrack.previousPageX;\n            } else {\n              toAdd = touchTrack.previousPageY;\n            }\n            total += toAdd;\n            count++;\n          }\n        }\n      }\n      return count > 0 ? total / count : TouchHistoryMath.noCentroid;\n    },\n    currentCentroidXOfTouchesChangedAfter: function currentCentroidXOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {\n      return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, true,\n      // isXAxis\n      true // ofCurrent\n      );\n    },\n    currentCentroidYOfTouchesChangedAfter: function currentCentroidYOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {\n      return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, false,\n      // isXAxis\n      true // ofCurrent\n      );\n    },\n    previousCentroidXOfTouchesChangedAfter: function previousCentroidXOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {\n      return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, true,\n      // isXAxis\n      false // ofCurrent\n      );\n    },\n    previousCentroidYOfTouchesChangedAfter: function previousCentroidYOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {\n      return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, false,\n      // isXAxis\n      false // ofCurrent\n      );\n    },\n    currentCentroidX: function currentCentroidX(touchHistory) {\n      return TouchHistoryMath.centroidDimension(touchHistory, 0,\n      // touchesChangedAfter\n      true,\n      // isXAxis\n      true // ofCurrent\n      );\n    },\n    currentCentroidY: function currentCentroidY(touchHistory) {\n      return TouchHistoryMath.centroidDimension(touchHistory, 0,\n      // touchesChangedAfter\n      false,\n      // isXAxis\n      true // ofCurrent\n      );\n    },\n    noCentroid: -1\n  };\n  var _default = exports.default = TouchHistoryMath;\n});", "lineCount": 106, "map": [[6, 2, 1, 0], [7, 0, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [15, 2, 10, 0], [15, 6, 10, 4, "TouchHistoryMath"], [15, 22, 10, 20], [15, 25, 10, 23], [16, 4, 11, 2], [17, 0, 12, 0], [18, 0, 13, 0], [19, 0, 14, 0], [20, 0, 15, 0], [21, 0, 16, 0], [22, 0, 17, 0], [23, 0, 18, 0], [24, 0, 19, 0], [25, 0, 20, 0], [26, 0, 21, 0], [27, 0, 22, 0], [28, 0, 23, 0], [29, 0, 24, 0], [30, 0, 25, 0], [31, 0, 26, 0], [32, 4, 27, 2, "centroidDimension"], [32, 21, 27, 19], [32, 23, 27, 21], [32, 32, 27, 30, "centroidDimension"], [32, 49, 27, 47, "centroidDimension"], [32, 50, 27, 48, "touchHistory"], [32, 62, 27, 60], [32, 64, 27, 62, "touchesChangedAfter"], [32, 83, 27, 81], [32, 85, 27, 83, "isXAxis"], [32, 92, 27, 90], [32, 94, 27, 92, "ofCurrent"], [32, 103, 27, 101], [32, 105, 27, 103], [33, 6, 28, 4], [33, 10, 28, 8, "touchBank"], [33, 19, 28, 17], [33, 22, 28, 20, "touchHistory"], [33, 34, 28, 32], [33, 35, 28, 33, "touchBank"], [33, 44, 28, 42], [34, 6, 29, 4], [34, 10, 29, 8, "total"], [34, 15, 29, 13], [34, 18, 29, 16], [34, 19, 29, 17], [35, 6, 30, 4], [35, 10, 30, 8, "count"], [35, 15, 30, 13], [35, 18, 30, 16], [35, 19, 30, 17], [36, 6, 31, 4], [36, 10, 31, 8, "oneTouchData"], [36, 22, 31, 20], [36, 25, 31, 23, "touchHistory"], [36, 37, 31, 35], [36, 38, 31, 36, "numberActiveTouches"], [36, 57, 31, 55], [36, 62, 31, 60], [36, 63, 31, 61], [36, 66, 31, 64, "touchHistory"], [36, 78, 31, 76], [36, 79, 31, 77, "touchBank"], [36, 88, 31, 86], [36, 89, 31, 87, "touchHistory"], [36, 101, 31, 99], [36, 102, 31, 100, "indexOfSingleActiveTouch"], [36, 126, 31, 124], [36, 127, 31, 125], [36, 130, 31, 128], [36, 134, 31, 132], [37, 6, 32, 4], [37, 10, 32, 8, "oneTouchData"], [37, 22, 32, 20], [37, 27, 32, 25], [37, 31, 32, 29], [37, 33, 32, 31], [38, 8, 33, 6], [38, 12, 33, 10, "oneTouchData"], [38, 24, 33, 22], [38, 25, 33, 23, "touchActive"], [38, 36, 33, 34], [38, 40, 33, 38, "oneTouchData"], [38, 52, 33, 50], [38, 53, 33, 51, "currentTimeStamp"], [38, 69, 33, 67], [38, 72, 33, 70, "touchesChangedAfter"], [38, 91, 33, 89], [38, 93, 33, 91], [39, 10, 34, 8, "total"], [39, 15, 34, 13], [39, 19, 34, 17, "ofCurrent"], [39, 28, 34, 26], [39, 32, 34, 30, "isXAxis"], [39, 39, 34, 37], [39, 42, 34, 40, "oneTouchData"], [39, 54, 34, 52], [39, 55, 34, 53, "currentPageX"], [39, 67, 34, 65], [39, 70, 34, 68, "ofCurrent"], [39, 79, 34, 77], [39, 83, 34, 81], [39, 84, 34, 82, "isXAxis"], [39, 91, 34, 89], [39, 94, 34, 92, "oneTouchData"], [39, 106, 34, 104], [39, 107, 34, 105, "currentPageY"], [39, 119, 34, 117], [39, 122, 34, 120], [39, 123, 34, 121, "ofCurrent"], [39, 132, 34, 130], [39, 136, 34, 134, "isXAxis"], [39, 143, 34, 141], [39, 146, 34, 144, "oneTouchData"], [39, 158, 34, 156], [39, 159, 34, 157, "previousPageX"], [39, 172, 34, 170], [39, 175, 34, 173, "oneTouchData"], [39, 187, 34, 185], [39, 188, 34, 186, "previousPageY"], [39, 201, 34, 199], [40, 10, 35, 8, "count"], [40, 15, 35, 13], [40, 18, 35, 16], [40, 19, 35, 17], [41, 8, 36, 6], [42, 6, 37, 4], [42, 7, 37, 5], [42, 13, 37, 11], [43, 8, 38, 6], [43, 13, 38, 11], [43, 17, 38, 15, "i"], [43, 18, 38, 16], [43, 21, 38, 19], [43, 22, 38, 20], [43, 24, 38, 22, "i"], [43, 25, 38, 23], [43, 28, 38, 26, "touchBank"], [43, 37, 38, 35], [43, 38, 38, 36, "length"], [43, 44, 38, 42], [43, 46, 38, 44, "i"], [43, 47, 38, 45], [43, 49, 38, 47], [43, 51, 38, 49], [44, 10, 39, 8], [44, 14, 39, 12, "touchTrack"], [44, 24, 39, 22], [44, 27, 39, 25, "touchBank"], [44, 36, 39, 34], [44, 37, 39, 35, "i"], [44, 38, 39, 36], [44, 39, 39, 37], [45, 10, 40, 8], [45, 14, 40, 12, "touchTrack"], [45, 24, 40, 22], [45, 29, 40, 27], [45, 33, 40, 31], [45, 37, 40, 35, "touchTrack"], [45, 47, 40, 45], [45, 52, 40, 50, "undefined"], [45, 61, 40, 59], [45, 65, 40, 63, "touchTrack"], [45, 75, 40, 73], [45, 76, 40, 74, "touchActive"], [45, 87, 40, 85], [45, 91, 40, 89, "touchTrack"], [45, 101, 40, 99], [45, 102, 40, 100, "currentTimeStamp"], [45, 118, 40, 116], [45, 122, 40, 120, "touchesChangedAfter"], [45, 141, 40, 139], [45, 143, 40, 141], [46, 12, 41, 10], [46, 16, 41, 14, "toAdd"], [46, 21, 41, 19], [46, 24, 41, 22], [46, 29, 41, 27], [46, 30, 41, 28], [46, 31, 41, 29], [46, 32, 41, 30], [47, 12, 42, 10], [47, 16, 42, 14, "ofCurrent"], [47, 25, 42, 23], [47, 29, 42, 27, "isXAxis"], [47, 36, 42, 34], [47, 38, 42, 36], [48, 14, 43, 12, "toAdd"], [48, 19, 43, 17], [48, 22, 43, 20, "touchTrack"], [48, 32, 43, 30], [48, 33, 43, 31, "currentPageX"], [48, 45, 43, 43], [49, 12, 44, 10], [49, 13, 44, 11], [49, 19, 44, 17], [49, 23, 44, 21, "ofCurrent"], [49, 32, 44, 30], [49, 36, 44, 34], [49, 37, 44, 35, "isXAxis"], [49, 44, 44, 42], [49, 46, 44, 44], [50, 14, 45, 12, "toAdd"], [50, 19, 45, 17], [50, 22, 45, 20, "touchTrack"], [50, 32, 45, 30], [50, 33, 45, 31, "currentPageY"], [50, 45, 45, 43], [51, 12, 46, 10], [51, 13, 46, 11], [51, 19, 46, 17], [51, 23, 46, 21], [51, 24, 46, 22, "ofCurrent"], [51, 33, 46, 31], [51, 37, 46, 35, "isXAxis"], [51, 44, 46, 42], [51, 46, 46, 44], [52, 14, 47, 12, "toAdd"], [52, 19, 47, 17], [52, 22, 47, 20, "touchTrack"], [52, 32, 47, 30], [52, 33, 47, 31, "previousPageX"], [52, 46, 47, 44], [53, 12, 48, 10], [53, 13, 48, 11], [53, 19, 48, 17], [54, 14, 49, 12, "toAdd"], [54, 19, 49, 17], [54, 22, 49, 20, "touchTrack"], [54, 32, 49, 30], [54, 33, 49, 31, "previousPageY"], [54, 46, 49, 44], [55, 12, 50, 10], [56, 12, 51, 10, "total"], [56, 17, 51, 15], [56, 21, 51, 19, "toAdd"], [56, 26, 51, 24], [57, 12, 52, 10, "count"], [57, 17, 52, 15], [57, 19, 52, 17], [58, 10, 53, 8], [59, 8, 54, 6], [60, 6, 55, 4], [61, 6, 56, 4], [61, 13, 56, 11, "count"], [61, 18, 56, 16], [61, 21, 56, 19], [61, 22, 56, 20], [61, 25, 56, 23, "total"], [61, 30, 56, 28], [61, 33, 56, 31, "count"], [61, 38, 56, 36], [61, 41, 56, 39, "TouchHistoryMath"], [61, 57, 56, 55], [61, 58, 56, 56, "noCentroid"], [61, 68, 56, 66], [62, 4, 57, 2], [62, 5, 57, 3], [63, 4, 58, 2, "currentCentroidXOfTouchesChangedAfter"], [63, 41, 58, 39], [63, 43, 58, 41], [63, 52, 58, 50, "currentCentroidXOfTouchesChangedAfter"], [63, 89, 58, 87, "currentCentroidXOfTouchesChangedAfter"], [63, 90, 58, 88, "touchHistory"], [63, 102, 58, 100], [63, 104, 58, 102, "touchesChangedAfter"], [63, 123, 58, 121], [63, 125, 58, 123], [64, 6, 59, 4], [64, 13, 59, 11, "TouchHistoryMath"], [64, 29, 59, 27], [64, 30, 59, 28, "centroidDimension"], [64, 47, 59, 45], [64, 48, 59, 46, "touchHistory"], [64, 60, 59, 58], [64, 62, 59, 60, "touchesChangedAfter"], [64, 81, 59, 79], [64, 83, 59, 81], [64, 87, 59, 85], [65, 6, 60, 4], [66, 6, 61, 4], [66, 10, 61, 8], [66, 11, 61, 9], [67, 6, 62, 4], [67, 7, 62, 5], [68, 4, 63, 2], [68, 5, 63, 3], [69, 4, 64, 2, "currentCentroidYOfTouchesChangedAfter"], [69, 41, 64, 39], [69, 43, 64, 41], [69, 52, 64, 50, "currentCentroidYOfTouchesChangedAfter"], [69, 89, 64, 87, "currentCentroidYOfTouchesChangedAfter"], [69, 90, 64, 88, "touchHistory"], [69, 102, 64, 100], [69, 104, 64, 102, "touchesChangedAfter"], [69, 123, 64, 121], [69, 125, 64, 123], [70, 6, 65, 4], [70, 13, 65, 11, "TouchHistoryMath"], [70, 29, 65, 27], [70, 30, 65, 28, "centroidDimension"], [70, 47, 65, 45], [70, 48, 65, 46, "touchHistory"], [70, 60, 65, 58], [70, 62, 65, 60, "touchesChangedAfter"], [70, 81, 65, 79], [70, 83, 65, 81], [70, 88, 65, 86], [71, 6, 66, 4], [72, 6, 67, 4], [72, 10, 67, 8], [72, 11, 67, 9], [73, 6, 68, 4], [73, 7, 68, 5], [74, 4, 69, 2], [74, 5, 69, 3], [75, 4, 70, 2, "previousCentroidXOfTouchesChangedAfter"], [75, 42, 70, 40], [75, 44, 70, 42], [75, 53, 70, 51, "previousCentroidXOfTouchesChangedAfter"], [75, 91, 70, 89, "previousCentroidXOfTouchesChangedAfter"], [75, 92, 70, 90, "touchHistory"], [75, 104, 70, 102], [75, 106, 70, 104, "touchesChangedAfter"], [75, 125, 70, 123], [75, 127, 70, 125], [76, 6, 71, 4], [76, 13, 71, 11, "TouchHistoryMath"], [76, 29, 71, 27], [76, 30, 71, 28, "centroidDimension"], [76, 47, 71, 45], [76, 48, 71, 46, "touchHistory"], [76, 60, 71, 58], [76, 62, 71, 60, "touchesChangedAfter"], [76, 81, 71, 79], [76, 83, 71, 81], [76, 87, 71, 85], [77, 6, 72, 4], [78, 6, 73, 4], [78, 11, 73, 9], [78, 12, 73, 10], [79, 6, 74, 4], [79, 7, 74, 5], [80, 4, 75, 2], [80, 5, 75, 3], [81, 4, 76, 2, "previousCentroidYOfTouchesChangedAfter"], [81, 42, 76, 40], [81, 44, 76, 42], [81, 53, 76, 51, "previousCentroidYOfTouchesChangedAfter"], [81, 91, 76, 89, "previousCentroidYOfTouchesChangedAfter"], [81, 92, 76, 90, "touchHistory"], [81, 104, 76, 102], [81, 106, 76, 104, "touchesChangedAfter"], [81, 125, 76, 123], [81, 127, 76, 125], [82, 6, 77, 4], [82, 13, 77, 11, "TouchHistoryMath"], [82, 29, 77, 27], [82, 30, 77, 28, "centroidDimension"], [82, 47, 77, 45], [82, 48, 77, 46, "touchHistory"], [82, 60, 77, 58], [82, 62, 77, 60, "touchesChangedAfter"], [82, 81, 77, 79], [82, 83, 77, 81], [82, 88, 77, 86], [83, 6, 78, 4], [84, 6, 79, 4], [84, 11, 79, 9], [84, 12, 79, 10], [85, 6, 80, 4], [85, 7, 80, 5], [86, 4, 81, 2], [86, 5, 81, 3], [87, 4, 82, 2, "currentCentroidX"], [87, 20, 82, 18], [87, 22, 82, 20], [87, 31, 82, 29, "currentCentroidX"], [87, 47, 82, 45, "currentCentroidX"], [87, 48, 82, 46, "touchHistory"], [87, 60, 82, 58], [87, 62, 82, 60], [88, 6, 83, 4], [88, 13, 83, 11, "TouchHistoryMath"], [88, 29, 83, 27], [88, 30, 83, 28, "centroidDimension"], [88, 47, 83, 45], [88, 48, 83, 46, "touchHistory"], [88, 60, 83, 58], [88, 62, 83, 60], [88, 63, 83, 61], [89, 6, 84, 4], [90, 6, 85, 4], [90, 10, 85, 8], [91, 6, 86, 4], [92, 6, 87, 4], [92, 10, 87, 8], [92, 11, 87, 9], [93, 6, 88, 4], [93, 7, 88, 5], [94, 4, 89, 2], [94, 5, 89, 3], [95, 4, 90, 2, "currentCentroidY"], [95, 20, 90, 18], [95, 22, 90, 20], [95, 31, 90, 29, "currentCentroidY"], [95, 47, 90, 45, "currentCentroidY"], [95, 48, 90, 46, "touchHistory"], [95, 60, 90, 58], [95, 62, 90, 60], [96, 6, 91, 4], [96, 13, 91, 11, "TouchHistoryMath"], [96, 29, 91, 27], [96, 30, 91, 28, "centroidDimension"], [96, 47, 91, 45], [96, 48, 91, 46, "touchHistory"], [96, 60, 91, 58], [96, 62, 91, 60], [96, 63, 91, 61], [97, 6, 92, 4], [98, 6, 93, 4], [98, 11, 93, 9], [99, 6, 94, 4], [100, 6, 95, 4], [100, 10, 95, 8], [100, 11, 95, 9], [101, 6, 96, 4], [101, 7, 96, 5], [102, 4, 97, 2], [102, 5, 97, 3], [103, 4, 98, 2, "noCentroid"], [103, 14, 98, 12], [103, 16, 98, 14], [103, 17, 98, 15], [104, 2, 99, 0], [104, 3, 99, 1], [105, 2, 99, 2], [105, 6, 99, 2, "_default"], [105, 14, 99, 2], [105, 17, 99, 2, "exports"], [105, 24, 99, 2], [105, 25, 99, 2, "default"], [105, 32, 99, 2], [105, 35, 100, 15, "TouchHistoryMath"], [105, 51, 100, 31], [106, 0, 100, 31], [106, 3]], "functionMap": {"names": ["<global>", "centroidDimension", "currentCentroidXOfTouchesChangedAfter", "currentCentroidYOfTouchesChangedAfter", "previousCentroidXOfTouchesChangedAfter", "previousCentroidYOfTouchesChangedAfter", "currentCentroidX", "currentCentroidY"], "mappings": "AAA;qBC0B;GD8B;yCEC;GFK;yCGC;GHK;0CIC;GJK;0CKC;GLK;oBMC;GNO;oBOC;GPO"}}, "type": "js/module"}]}