{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 36, "index": 247}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}, {"name": "../tools/GestureHandlerOrchestrator", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 248}, "end": {"line": 4, "column": 77, "index": 325}}], "key": "nkMyuZ+jFvH1SEyjdUxCz0RRbms=", "exportNames": ["*"]}}, {"name": "./GestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 326}, "end": {"line": 5, "column": 46, "index": 372}}], "key": "0oKNSZn0AMFFw0m17+cJMO/YqLM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _State = require(_dependencyMap[1], \"../../State\");\n  var _GestureHandlerOrchestrator = _interopRequireDefault(require(_dependencyMap[2], \"../tools/GestureHandlerOrchestrator\"));\n  var _GestureHandler = _interopRequireDefault(require(_dependencyMap[3], \"./GestureHandler\"));\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class HoverGestureHandler extends _GestureHandler.default {\n    constructor(...args) {\n      super(...args);\n      _defineProperty(this, \"stylusData\", void 0);\n    }\n    transformNativeEvent() {\n      return {\n        ...super.transformNativeEvent(),\n        stylusData: this.stylusData\n      };\n    }\n    onPointerMoveOver(event) {\n      _GestureHandlerOrchestrator.default.instance.recordHandlerIfNotPresent(this);\n      this.tracker.addToTracker(event);\n      this.stylusData = event.stylusData;\n      super.onPointerMoveOver(event);\n      if (this.state === _State.State.UNDETERMINED) {\n        this.begin();\n        this.activate();\n      }\n    }\n    onPointerMoveOut(event) {\n      this.tracker.removeFromTracker(event.pointerId);\n      this.stylusData = event.stylusData;\n      super.onPointerMoveOut(event);\n      this.end();\n    }\n    onPointerMove(event) {\n      this.tracker.track(event);\n      this.stylusData = event.stylusData;\n      super.onPointerMove(event);\n    }\n    onPointerCancel(event) {\n      super.onPointerCancel(event);\n      this.reset();\n    }\n  }\n  exports.default = HoverGestureHandler;\n});", "lineCount": 61, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_State"], [7, 12, 3, 0], [7, 15, 3, 0, "require"], [7, 22, 3, 0], [7, 23, 3, 0, "_dependencyMap"], [7, 37, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_GestureHandlerOrchestrator"], [8, 33, 4, 0], [8, 36, 4, 0, "_interopRequireDefault"], [8, 58, 4, 0], [8, 59, 4, 0, "require"], [8, 66, 4, 0], [8, 67, 4, 0, "_dependencyMap"], [8, 81, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_Gesture<PERSON><PERSON>ler"], [9, 21, 5, 0], [9, 24, 5, 0, "_interopRequireDefault"], [9, 46, 5, 0], [9, 47, 5, 0, "require"], [9, 54, 5, 0], [9, 55, 5, 0, "_dependencyMap"], [9, 69, 5, 0], [10, 2, 1, 0], [10, 11, 1, 9, "_defineProperty"], [10, 26, 1, 24, "_defineProperty"], [10, 27, 1, 25, "obj"], [10, 30, 1, 28], [10, 32, 1, 30, "key"], [10, 35, 1, 33], [10, 37, 1, 35, "value"], [10, 42, 1, 40], [10, 44, 1, 42], [11, 4, 1, 44], [11, 8, 1, 48, "key"], [11, 11, 1, 51], [11, 15, 1, 55, "obj"], [11, 18, 1, 58], [11, 20, 1, 60], [12, 6, 1, 62, "Object"], [12, 12, 1, 68], [12, 13, 1, 69, "defineProperty"], [12, 27, 1, 83], [12, 28, 1, 84, "obj"], [12, 31, 1, 87], [12, 33, 1, 89, "key"], [12, 36, 1, 92], [12, 38, 1, 94], [13, 8, 1, 96, "value"], [13, 13, 1, 101], [13, 15, 1, 103, "value"], [13, 20, 1, 108], [14, 8, 1, 110, "enumerable"], [14, 18, 1, 120], [14, 20, 1, 122], [14, 24, 1, 126], [15, 8, 1, 128, "configurable"], [15, 20, 1, 140], [15, 22, 1, 142], [15, 26, 1, 146], [16, 8, 1, 148, "writable"], [16, 16, 1, 156], [16, 18, 1, 158], [17, 6, 1, 163], [17, 7, 1, 164], [17, 8, 1, 165], [18, 4, 1, 167], [18, 5, 1, 168], [18, 11, 1, 174], [19, 6, 1, 176, "obj"], [19, 9, 1, 179], [19, 10, 1, 180, "key"], [19, 13, 1, 183], [19, 14, 1, 184], [19, 17, 1, 187, "value"], [19, 22, 1, 192], [20, 4, 1, 194], [21, 4, 1, 196], [21, 11, 1, 203, "obj"], [21, 14, 1, 206], [22, 2, 1, 208], [23, 2, 6, 15], [23, 8, 6, 21, "HoverGestureHandler"], [23, 27, 6, 40], [23, 36, 6, 49, "Gesture<PERSON>andler"], [23, 59, 6, 63], [23, 60, 6, 64], [24, 4, 7, 2, "constructor"], [24, 15, 7, 13, "constructor"], [24, 16, 7, 14], [24, 19, 7, 17, "args"], [24, 23, 7, 21], [24, 25, 7, 23], [25, 6, 8, 4], [25, 11, 8, 9], [25, 12, 8, 10], [25, 15, 8, 13, "args"], [25, 19, 8, 17], [25, 20, 8, 18], [26, 6, 10, 4, "_defineProperty"], [26, 21, 10, 19], [26, 22, 10, 20], [26, 26, 10, 24], [26, 28, 10, 26], [26, 40, 10, 38], [26, 42, 10, 40], [26, 47, 10, 45], [26, 48, 10, 46], [26, 49, 10, 47], [27, 4, 11, 2], [28, 4, 13, 2, "transformNativeEvent"], [28, 24, 13, 22, "transformNativeEvent"], [28, 25, 13, 22], [28, 27, 13, 25], [29, 6, 14, 4], [29, 13, 14, 11], [30, 8, 14, 13], [30, 11, 14, 16], [30, 16, 14, 21], [30, 17, 14, 22, "transformNativeEvent"], [30, 37, 14, 42], [30, 38, 14, 43], [30, 39, 14, 44], [31, 8, 15, 6, "stylusData"], [31, 18, 15, 16], [31, 20, 15, 18], [31, 24, 15, 22], [31, 25, 15, 23, "stylusData"], [32, 6, 16, 4], [32, 7, 16, 5], [33, 4, 17, 2], [34, 4, 19, 2, "onPointerMoveOver"], [34, 21, 19, 19, "onPointerMoveOver"], [34, 22, 19, 20, "event"], [34, 27, 19, 25], [34, 29, 19, 27], [35, 6, 20, 4, "GestureHandlerOrchestrator"], [35, 41, 20, 30], [35, 42, 20, 31, "instance"], [35, 50, 20, 39], [35, 51, 20, 40, "recordHandlerIfNotPresent"], [35, 76, 20, 65], [35, 77, 20, 66], [35, 81, 20, 70], [35, 82, 20, 71], [36, 6, 21, 4], [36, 10, 21, 8], [36, 11, 21, 9, "tracker"], [36, 18, 21, 16], [36, 19, 21, 17, "addToTracker"], [36, 31, 21, 29], [36, 32, 21, 30, "event"], [36, 37, 21, 35], [36, 38, 21, 36], [37, 6, 22, 4], [37, 10, 22, 8], [37, 11, 22, 9, "stylusData"], [37, 21, 22, 19], [37, 24, 22, 22, "event"], [37, 29, 22, 27], [37, 30, 22, 28, "stylusData"], [37, 40, 22, 38], [38, 6, 23, 4], [38, 11, 23, 9], [38, 12, 23, 10, "onPointerMoveOver"], [38, 29, 23, 27], [38, 30, 23, 28, "event"], [38, 35, 23, 33], [38, 36, 23, 34], [39, 6, 25, 4], [39, 10, 25, 8], [39, 14, 25, 12], [39, 15, 25, 13, "state"], [39, 20, 25, 18], [39, 25, 25, 23, "State"], [39, 37, 25, 28], [39, 38, 25, 29, "UNDETERMINED"], [39, 50, 25, 41], [39, 52, 25, 43], [40, 8, 26, 6], [40, 12, 26, 10], [40, 13, 26, 11, "begin"], [40, 18, 26, 16], [40, 19, 26, 17], [40, 20, 26, 18], [41, 8, 27, 6], [41, 12, 27, 10], [41, 13, 27, 11, "activate"], [41, 21, 27, 19], [41, 22, 27, 20], [41, 23, 27, 21], [42, 6, 28, 4], [43, 4, 29, 2], [44, 4, 31, 2, "onPointerMoveOut"], [44, 20, 31, 18, "onPointerMoveOut"], [44, 21, 31, 19, "event"], [44, 26, 31, 24], [44, 28, 31, 26], [45, 6, 32, 4], [45, 10, 32, 8], [45, 11, 32, 9, "tracker"], [45, 18, 32, 16], [45, 19, 32, 17, "removeFromTracker"], [45, 36, 32, 34], [45, 37, 32, 35, "event"], [45, 42, 32, 40], [45, 43, 32, 41, "pointerId"], [45, 52, 32, 50], [45, 53, 32, 51], [46, 6, 33, 4], [46, 10, 33, 8], [46, 11, 33, 9, "stylusData"], [46, 21, 33, 19], [46, 24, 33, 22, "event"], [46, 29, 33, 27], [46, 30, 33, 28, "stylusData"], [46, 40, 33, 38], [47, 6, 34, 4], [47, 11, 34, 9], [47, 12, 34, 10, "onPointerMoveOut"], [47, 28, 34, 26], [47, 29, 34, 27, "event"], [47, 34, 34, 32], [47, 35, 34, 33], [48, 6, 35, 4], [48, 10, 35, 8], [48, 11, 35, 9, "end"], [48, 14, 35, 12], [48, 15, 35, 13], [48, 16, 35, 14], [49, 4, 36, 2], [50, 4, 38, 2, "onPointerMove"], [50, 17, 38, 15, "onPointerMove"], [50, 18, 38, 16, "event"], [50, 23, 38, 21], [50, 25, 38, 23], [51, 6, 39, 4], [51, 10, 39, 8], [51, 11, 39, 9, "tracker"], [51, 18, 39, 16], [51, 19, 39, 17, "track"], [51, 24, 39, 22], [51, 25, 39, 23, "event"], [51, 30, 39, 28], [51, 31, 39, 29], [52, 6, 40, 4], [52, 10, 40, 8], [52, 11, 40, 9, "stylusData"], [52, 21, 40, 19], [52, 24, 40, 22, "event"], [52, 29, 40, 27], [52, 30, 40, 28, "stylusData"], [52, 40, 40, 38], [53, 6, 41, 4], [53, 11, 41, 9], [53, 12, 41, 10, "onPointerMove"], [53, 25, 41, 23], [53, 26, 41, 24, "event"], [53, 31, 41, 29], [53, 32, 41, 30], [54, 4, 42, 2], [55, 4, 44, 2, "onPointerCancel"], [55, 19, 44, 17, "onPointerCancel"], [55, 20, 44, 18, "event"], [55, 25, 44, 23], [55, 27, 44, 25], [56, 6, 45, 4], [56, 11, 45, 9], [56, 12, 45, 10, "onPointerCancel"], [56, 27, 45, 25], [56, 28, 45, 26, "event"], [56, 33, 45, 31], [56, 34, 45, 32], [57, 6, 46, 4], [57, 10, 46, 8], [57, 11, 46, 9, "reset"], [57, 16, 46, 14], [57, 17, 46, 15], [57, 18, 46, 16], [58, 4, 47, 2], [59, 2, 49, 0], [60, 2, 49, 1, "exports"], [60, 9, 49, 1], [60, 10, 49, 1, "default"], [60, 17, 49, 1], [60, 20, 49, 1, "HoverGestureHandler"], [60, 39, 49, 1], [61, 0, 49, 1], [61, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "HoverGestureHandler", "constructor", "transformNativeEvent", "onPointerMoveOver", "onPointerMoveOut", "onPointerMove", "onPointerCancel"], "mappings": "AAA,iNC;eCK;ECC;GDI;EEE;GFI;EGE;GHU;EIE;GJK;EKE;GLI;EME;GNG;CDE"}}, "type": "js/module"}]}