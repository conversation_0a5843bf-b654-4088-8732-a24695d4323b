{"dependencies": [{"name": "./winter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 18, "index": 18}}], "key": "mkkKJkNfPl3SGB5Bx4OGQ/3JW8Y=", "exportNames": ["*"]}}, {"name": "expo/virtual/rsc", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 19}, "end": {"line": 2, "column": 26, "index": 45}}], "key": "Njna7k+CMzQedLgoLi0KGtlTdvM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  require(_dependencyMap[0], \"./winter\");\n  require(_dependencyMap[1], \"expo/virtual/rsc\");\n  // When users dangerously import a file inside of react-native, it breaks the web alias.\n  // This is one of the most common, and cryptic web errors that users encounter.\n  // This conditional side-effect provides a more helpful error message for debugging.\n  // Use a wrapper `__DEV__` to remove this entire block in production.\n  if (__DEV__) {\n    if (\n    // Skip mocking if someone is shimming this value out.\n    !('__fbBatchedBridgeConfig' in global)) {\n      Object.defineProperty(global, '__fbBatchedBridgeConfig', {\n        get() {\n          throw new Error(\"Your web project is importing a module from 'react-native' instead of 'react-native-web'. Learn more: https://expo.fyi/fb-batched-bridge-config-web\");\n        }\n      });\n    }\n  }\n});", "lineCount": 19, "map": [[2, 2, 1, 0, "require"], [2, 9, 1, 0], [2, 10, 1, 0, "_dependencyMap"], [2, 24, 1, 0], [3, 2, 2, 0, "require"], [3, 9, 2, 0], [3, 10, 2, 0, "_dependencyMap"], [3, 24, 2, 0], [4, 2, 4, 0], [5, 2, 5, 0], [6, 2, 6, 0], [7, 2, 7, 0], [8, 2, 8, 0], [8, 6, 8, 4, "__DEV__"], [8, 13, 8, 11], [8, 15, 8, 13], [9, 4, 9, 2], [10, 4, 10, 4], [11, 4, 11, 4], [11, 6, 11, 6], [11, 31, 11, 31], [11, 35, 11, 35, "global"], [11, 41, 11, 41], [11, 42, 11, 42], [11, 44, 12, 4], [12, 6, 13, 4, "Object"], [12, 12, 13, 10], [12, 13, 13, 11, "defineProperty"], [12, 27, 13, 25], [12, 28, 13, 26, "global"], [12, 34, 13, 32], [12, 36, 13, 34], [12, 61, 13, 59], [12, 63, 13, 61], [13, 8, 14, 6, "get"], [13, 11, 14, 9, "get"], [13, 12, 14, 9], [13, 14, 14, 12], [14, 10, 15, 8], [14, 16, 15, 14], [14, 20, 15, 18, "Error"], [14, 25, 15, 23], [14, 26, 16, 10], [14, 175, 17, 8], [14, 176, 17, 9], [15, 8, 18, 6], [16, 6, 19, 4], [16, 7, 19, 5], [16, 8, 19, 6], [17, 4, 20, 2], [18, 2, 21, 0], [19, 0, 21, 1], [19, 3]], "functionMap": {"names": ["<global>", "Object.defineProperty$argument_2.get"], "mappings": "AAA;MCa;ODI"}}, "type": "js/module"}]}