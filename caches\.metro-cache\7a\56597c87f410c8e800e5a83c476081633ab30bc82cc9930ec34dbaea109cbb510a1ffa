{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/elements", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 80, "index": 95}}], "key": "LmqW7jh+SpCzQZMkzh+Awcuawt0=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 96}, "end": {"line": 4, "column": 52, "index": 148}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "color", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 149}, "end": {"line": 5, "column": 26, "index": 175}}], "key": "WMoKxUKO/GMHeED0pzSR/dc1v7c=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 176}, "end": {"line": 6, "column": 26, "index": 202}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "./TabBarIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 262}, "end": {"line": 8, "column": 45, "index": 307}}], "key": "FMB8MytshydkrDDOSQ85sBeOr4Y=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 308}, "end": {"line": 9, "column": 63, "index": 371}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.BottomTabItem = BottomTabItem;\n  var _elements = require(_dependencyMap[1], \"@react-navigation/elements\");\n  var _native = require(_dependencyMap[2], \"@react-navigation/native\");\n  var _color = _interopRequireDefault(require(_dependencyMap[3], \"color\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[4], \"react\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/StyleSheet\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/View\"));\n  var _TabBarIcon = require(_dependencyMap[8], \"./TabBarIcon.js\");\n  var _jsxRuntime = require(_dependencyMap[9], \"react/jsx-runtime\");\n  const renderButtonDefault = props => /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.PlatformPressable, {\n    ...props\n  });\n  const SUPPORTS_LARGE_CONTENT_VIEWER = _Platform.default.OS === 'ios' && parseInt(_Platform.default.Version, 10) >= 13;\n  function BottomTabItem({\n    route,\n    href,\n    focused,\n    descriptor,\n    label,\n    icon,\n    badge,\n    badgeStyle,\n    button = renderButtonDefault,\n    accessibilityLabel,\n    testID,\n    onPress,\n    onLongPress,\n    horizontal,\n    compact,\n    sidebar,\n    variant,\n    activeTintColor: customActiveTintColor,\n    inactiveTintColor: customInactiveTintColor,\n    activeBackgroundColor: customActiveBackgroundColor,\n    inactiveBackgroundColor = 'transparent',\n    showLabel = true,\n    // On iOS 13+, we use `largeContentTitle` for accessibility\n    // So we don't need the font to scale up\n    // https://developer.apple.com/documentation/uikit/uiview/3183939-largecontenttitle\n    allowFontScaling = SUPPORTS_LARGE_CONTENT_VIEWER ? false : undefined,\n    labelStyle,\n    iconStyle,\n    style\n  }) {\n    const {\n      colors,\n      fonts\n    } = (0, _native.useTheme)();\n    const activeTintColor = customActiveTintColor ?? (variant === 'uikit' && sidebar && horizontal ? (0, _color.default)(colors.primary).isDark() ? 'white' : (0, _color.default)(colors.primary).darken(0.71).string() : colors.primary);\n    const inactiveTintColor = customInactiveTintColor === undefined ? variant === 'material' ? (0, _color.default)(colors.text).alpha(0.68).rgb().string() : (0, _color.default)(colors.text).mix((0, _color.default)(colors.card), 0.5).hex() : customInactiveTintColor;\n    const activeBackgroundColor = customActiveBackgroundColor ?? (variant === 'material' ? (0, _color.default)(activeTintColor).alpha(0.12).rgb().string() : sidebar && horizontal ? colors.primary : 'transparent');\n    const {\n      options\n    } = descriptor;\n    const labelString = (0, _elements.getLabel)({\n      label: typeof options.tabBarLabel === 'string' ? options.tabBarLabel : undefined,\n      title: options.title\n    }, route.name);\n    let labelInactiveTintColor = inactiveTintColor;\n    let iconInactiveTintColor = inactiveTintColor;\n    if (variant === 'uikit' && sidebar && horizontal && customInactiveTintColor === undefined) {\n      iconInactiveTintColor = colors.primary;\n      labelInactiveTintColor = colors.text;\n    }\n    const renderLabel = ({\n      focused\n    }) => {\n      if (showLabel === false) {\n        return null;\n      }\n      const color = focused ? activeTintColor : labelInactiveTintColor;\n      if (typeof label !== 'string') {\n        return label({\n          focused,\n          color,\n          position: horizontal ? 'beside-icon' : 'below-icon',\n          children: labelString\n        });\n      }\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.Label, {\n        style: [horizontal ? [styles.labelBeside, variant === 'material' ? styles.labelSidebarMaterial : sidebar ? styles.labelSidebarUiKit : compact ? styles.labelBesideUikitCompact : styles.labelBesideUikit, icon == null && {\n          marginStart: 0\n        }] : styles.labelBeneath, compact || variant === 'uikit' && sidebar && horizontal ? fonts.regular : fonts.medium, labelStyle],\n        allowFontScaling: allowFontScaling,\n        tintColor: color,\n        children: label\n      });\n    };\n    const renderIcon = ({\n      focused\n    }) => {\n      if (icon === undefined) {\n        return null;\n      }\n      const activeOpacity = focused ? 1 : 0;\n      const inactiveOpacity = focused ? 0 : 1;\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_TabBarIcon.TabBarIcon, {\n        route: route,\n        variant: variant,\n        size: compact ? 'compact' : 'regular',\n        badge: badge,\n        badgeStyle: badgeStyle,\n        activeOpacity: activeOpacity,\n        allowFontScaling: allowFontScaling,\n        inactiveOpacity: inactiveOpacity,\n        activeTintColor: activeTintColor,\n        inactiveTintColor: iconInactiveTintColor,\n        renderIcon: icon,\n        style: iconStyle\n      });\n    };\n    const scene = {\n      route,\n      focused\n    };\n    const backgroundColor = focused ? activeBackgroundColor : inactiveBackgroundColor;\n    const {\n      flex\n    } = _StyleSheet.default.flatten(style || {});\n    const borderRadius = variant === 'material' ? horizontal ? 56 : 16 : sidebar && horizontal ? 10 : 0;\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_View.default, {\n      style: [\n      // Clip ripple effect on Android\n      {\n        borderRadius,\n        overflow: variant === 'material' ? 'hidden' : 'visible'\n      }, style],\n      children: button({\n        href,\n        onPress,\n        onLongPress,\n        testID,\n        'aria-label': accessibilityLabel,\n        'accessibilityLargeContentTitle': labelString,\n        'accessibilityShowsLargeContentViewer': true,\n        // FIXME: role: 'tab' doesn't seem to work as expected on iOS\n        'role': _Platform.default.select({\n          ios: 'button',\n          default: 'tab'\n        }),\n        'aria-selected': focused,\n        'android_ripple': {\n          borderless: true\n        },\n        'hoverEffect': variant === 'material' || sidebar && horizontal ? {\n          color: colors.text\n        } : undefined,\n        'pressOpacity': 1,\n        'style': [styles.tab, {\n          flex,\n          backgroundColor,\n          borderRadius\n        }, sidebar ? variant === 'material' ? horizontal ? styles.tabBarSidebarMaterial : styles.tabVerticalMaterial : horizontal ? styles.tabBarSidebarUiKit : styles.tabVerticalUiKit : variant === 'material' ? styles.tabVerticalMaterial : horizontal ? styles.tabHorizontalUiKit : styles.tabVerticalUiKit],\n        'children': /*#__PURE__*/(0, _jsxRuntime.jsxs)(_react.default.Fragment, {\n          children: [renderIcon(scene), renderLabel(scene)]\n        })\n      })\n    });\n  }\n  const styles = _StyleSheet.default.create({\n    tab: {\n      alignItems: 'center',\n      // Roundness for iPad hover effect\n      borderRadius: 10\n    },\n    tabVerticalUiKit: {\n      justifyContent: 'flex-start',\n      flexDirection: 'column',\n      padding: 5\n    },\n    tabVerticalMaterial: {\n      padding: 10\n    },\n    tabHorizontalUiKit: {\n      justifyContent: 'center',\n      alignItems: 'center',\n      flexDirection: 'row',\n      padding: 5\n    },\n    tabBarSidebarUiKit: {\n      justifyContent: 'flex-start',\n      alignItems: 'center',\n      flexDirection: 'row',\n      paddingVertical: 7,\n      paddingHorizontal: 5\n    },\n    tabBarSidebarMaterial: {\n      justifyContent: 'flex-start',\n      alignItems: 'center',\n      flexDirection: 'row',\n      paddingVertical: 15,\n      paddingStart: 16,\n      paddingEnd: 24\n    },\n    labelSidebarMaterial: {\n      marginStart: 12\n    },\n    labelSidebarUiKit: {\n      fontSize: 17,\n      marginStart: 10\n    },\n    labelBeneath: {\n      fontSize: 10\n    },\n    labelBeside: {\n      marginEnd: 12,\n      lineHeight: 24\n    },\n    labelBesideUikit: {\n      fontSize: 13,\n      marginStart: 5\n    },\n    labelBesideUikitCompact: {\n      fontSize: 12,\n      marginStart: 5\n    }\n  });\n});", "lineCount": 226, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "BottomTabItem"], [8, 23, 1, 13], [8, 26, 1, 13, "BottomTabItem"], [8, 39, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_elements"], [9, 15, 3, 0], [9, 18, 3, 0, "require"], [9, 25, 3, 0], [9, 26, 3, 0, "_dependencyMap"], [9, 40, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_native"], [10, 13, 4, 0], [10, 16, 4, 0, "require"], [10, 23, 4, 0], [10, 24, 4, 0, "_dependencyMap"], [10, 38, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_color"], [11, 12, 5, 0], [11, 15, 5, 0, "_interopRequireDefault"], [11, 37, 5, 0], [11, 38, 5, 0, "require"], [11, 45, 5, 0], [11, 46, 5, 0, "_dependencyMap"], [11, 60, 5, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_react"], [12, 12, 6, 0], [12, 15, 6, 0, "_interopRequireDefault"], [12, 37, 6, 0], [12, 38, 6, 0, "require"], [12, 45, 6, 0], [12, 46, 6, 0, "_dependencyMap"], [12, 60, 6, 0], [13, 2, 6, 26], [13, 6, 6, 26, "_Platform"], [13, 15, 6, 26], [13, 18, 6, 26, "_interopRequireDefault"], [13, 40, 6, 26], [13, 41, 6, 26, "require"], [13, 48, 6, 26], [13, 49, 6, 26, "_dependencyMap"], [13, 63, 6, 26], [14, 2, 6, 26], [14, 6, 6, 26, "_StyleSheet"], [14, 17, 6, 26], [14, 20, 6, 26, "_interopRequireDefault"], [14, 42, 6, 26], [14, 43, 6, 26, "require"], [14, 50, 6, 26], [14, 51, 6, 26, "_dependencyMap"], [14, 65, 6, 26], [15, 2, 6, 26], [15, 6, 6, 26, "_View"], [15, 11, 6, 26], [15, 14, 6, 26, "_interopRequireDefault"], [15, 36, 6, 26], [15, 37, 6, 26, "require"], [15, 44, 6, 26], [15, 45, 6, 26, "_dependencyMap"], [15, 59, 6, 26], [16, 2, 8, 0], [16, 6, 8, 0, "_TabBarIcon"], [16, 17, 8, 0], [16, 20, 8, 0, "require"], [16, 27, 8, 0], [16, 28, 8, 0, "_dependencyMap"], [16, 42, 8, 0], [17, 2, 9, 0], [17, 6, 9, 0, "_jsxRuntime"], [17, 17, 9, 0], [17, 20, 9, 0, "require"], [17, 27, 9, 0], [17, 28, 9, 0, "_dependencyMap"], [17, 42, 9, 0], [18, 2, 10, 0], [18, 8, 10, 6, "renderButtonDefault"], [18, 27, 10, 25], [18, 30, 10, 28, "props"], [18, 35, 10, 33], [18, 39, 10, 37], [18, 52, 10, 50], [18, 56, 10, 50, "_jsx"], [18, 71, 10, 54], [18, 73, 10, 55, "PlatformPressable"], [18, 100, 10, 72], [18, 102, 10, 74], [19, 4, 11, 2], [19, 7, 11, 5, "props"], [20, 2, 12, 0], [20, 3, 12, 1], [20, 4, 12, 2], [21, 2, 13, 0], [21, 8, 13, 6, "SUPPORTS_LARGE_CONTENT_VIEWER"], [21, 37, 13, 35], [21, 40, 13, 38, "Platform"], [21, 57, 13, 46], [21, 58, 13, 47, "OS"], [21, 60, 13, 49], [21, 65, 13, 54], [21, 70, 13, 59], [21, 74, 13, 63, "parseInt"], [21, 82, 13, 71], [21, 83, 13, 72, "Platform"], [21, 100, 13, 80], [21, 101, 13, 81, "Version"], [21, 108, 13, 88], [21, 110, 13, 90], [21, 112, 13, 92], [21, 113, 13, 93], [21, 117, 13, 97], [21, 119, 13, 99], [22, 2, 14, 7], [22, 11, 14, 16, "BottomTabItem"], [22, 24, 14, 29, "BottomTabItem"], [22, 25, 14, 30], [23, 4, 15, 2, "route"], [23, 9, 15, 7], [24, 4, 16, 2, "href"], [24, 8, 16, 6], [25, 4, 17, 2, "focused"], [25, 11, 17, 9], [26, 4, 18, 2, "descriptor"], [26, 14, 18, 12], [27, 4, 19, 2, "label"], [27, 9, 19, 7], [28, 4, 20, 2, "icon"], [28, 8, 20, 6], [29, 4, 21, 2, "badge"], [29, 9, 21, 7], [30, 4, 22, 2, "badgeStyle"], [30, 14, 22, 12], [31, 4, 23, 2, "button"], [31, 10, 23, 8], [31, 13, 23, 11, "renderButtonDefault"], [31, 32, 23, 30], [32, 4, 24, 2, "accessibilityLabel"], [32, 22, 24, 20], [33, 4, 25, 2, "testID"], [33, 10, 25, 8], [34, 4, 26, 2, "onPress"], [34, 11, 26, 9], [35, 4, 27, 2, "onLongPress"], [35, 15, 27, 13], [36, 4, 28, 2, "horizontal"], [36, 14, 28, 12], [37, 4, 29, 2, "compact"], [37, 11, 29, 9], [38, 4, 30, 2, "sidebar"], [38, 11, 30, 9], [39, 4, 31, 2, "variant"], [39, 11, 31, 9], [40, 4, 32, 2, "activeTintColor"], [40, 19, 32, 17], [40, 21, 32, 19, "customActiveTintColor"], [40, 42, 32, 40], [41, 4, 33, 2, "inactiveTintColor"], [41, 21, 33, 19], [41, 23, 33, 21, "customInactiveTintColor"], [41, 46, 33, 44], [42, 4, 34, 2, "activeBackgroundColor"], [42, 25, 34, 23], [42, 27, 34, 25, "customActiveBackgroundColor"], [42, 54, 34, 52], [43, 4, 35, 2, "inactiveBackgroundColor"], [43, 27, 35, 25], [43, 30, 35, 28], [43, 43, 35, 41], [44, 4, 36, 2, "showLabel"], [44, 13, 36, 11], [44, 16, 36, 14], [44, 20, 36, 18], [45, 4, 37, 2], [46, 4, 38, 2], [47, 4, 39, 2], [48, 4, 40, 2, "allowFontScaling"], [48, 20, 40, 18], [48, 23, 40, 21, "SUPPORTS_LARGE_CONTENT_VIEWER"], [48, 52, 40, 50], [48, 55, 40, 53], [48, 60, 40, 58], [48, 63, 40, 61, "undefined"], [48, 72, 40, 70], [49, 4, 41, 2, "labelStyle"], [49, 14, 41, 12], [50, 4, 42, 2, "iconStyle"], [50, 13, 42, 11], [51, 4, 43, 2, "style"], [52, 2, 44, 0], [52, 3, 44, 1], [52, 5, 44, 3], [53, 4, 45, 2], [53, 10, 45, 8], [54, 6, 46, 4, "colors"], [54, 12, 46, 10], [55, 6, 47, 4, "fonts"], [56, 4, 48, 2], [56, 5, 48, 3], [56, 8, 48, 6], [56, 12, 48, 6, "useTheme"], [56, 28, 48, 14], [56, 30, 48, 15], [56, 31, 48, 16], [57, 4, 49, 2], [57, 10, 49, 8, "activeTintColor"], [57, 25, 49, 23], [57, 28, 49, 26, "customActiveTintColor"], [57, 49, 49, 47], [57, 54, 49, 52, "variant"], [57, 61, 49, 59], [57, 66, 49, 64], [57, 73, 49, 71], [57, 77, 49, 75, "sidebar"], [57, 84, 49, 82], [57, 88, 49, 86, "horizontal"], [57, 98, 49, 96], [57, 101, 49, 99], [57, 105, 49, 99, "Color"], [57, 119, 49, 104], [57, 121, 49, 105, "colors"], [57, 127, 49, 111], [57, 128, 49, 112, "primary"], [57, 135, 49, 119], [57, 136, 49, 120], [57, 137, 49, 121, "isDark"], [57, 143, 49, 127], [57, 144, 49, 128], [57, 145, 49, 129], [57, 148, 49, 132], [57, 155, 49, 139], [57, 158, 49, 142], [57, 162, 49, 142, "Color"], [57, 176, 49, 147], [57, 178, 49, 148, "colors"], [57, 184, 49, 154], [57, 185, 49, 155, "primary"], [57, 192, 49, 162], [57, 193, 49, 163], [57, 194, 49, 164, "darken"], [57, 200, 49, 170], [57, 201, 49, 171], [57, 205, 49, 175], [57, 206, 49, 176], [57, 207, 49, 177, "string"], [57, 213, 49, 183], [57, 214, 49, 184], [57, 215, 49, 185], [57, 218, 49, 188, "colors"], [57, 224, 49, 194], [57, 225, 49, 195, "primary"], [57, 232, 49, 202], [57, 233, 49, 203], [58, 4, 50, 2], [58, 10, 50, 8, "inactiveTintColor"], [58, 27, 50, 25], [58, 30, 50, 28, "customInactiveTintColor"], [58, 53, 50, 51], [58, 58, 50, 56, "undefined"], [58, 67, 50, 65], [58, 70, 50, 68, "variant"], [58, 77, 50, 75], [58, 82, 50, 80], [58, 92, 50, 90], [58, 95, 50, 93], [58, 99, 50, 93, "Color"], [58, 113, 50, 98], [58, 115, 50, 99, "colors"], [58, 121, 50, 105], [58, 122, 50, 106, "text"], [58, 126, 50, 110], [58, 127, 50, 111], [58, 128, 50, 112, "alpha"], [58, 133, 50, 117], [58, 134, 50, 118], [58, 138, 50, 122], [58, 139, 50, 123], [58, 140, 50, 124, "rgb"], [58, 143, 50, 127], [58, 144, 50, 128], [58, 145, 50, 129], [58, 146, 50, 130, "string"], [58, 152, 50, 136], [58, 153, 50, 137], [58, 154, 50, 138], [58, 157, 50, 141], [58, 161, 50, 141, "Color"], [58, 175, 50, 146], [58, 177, 50, 147, "colors"], [58, 183, 50, 153], [58, 184, 50, 154, "text"], [58, 188, 50, 158], [58, 189, 50, 159], [58, 190, 50, 160, "mix"], [58, 193, 50, 163], [58, 194, 50, 164], [58, 198, 50, 164, "Color"], [58, 212, 50, 169], [58, 214, 50, 170, "colors"], [58, 220, 50, 176], [58, 221, 50, 177, "card"], [58, 225, 50, 181], [58, 226, 50, 182], [58, 228, 50, 184], [58, 231, 50, 187], [58, 232, 50, 188], [58, 233, 50, 189, "hex"], [58, 236, 50, 192], [58, 237, 50, 193], [58, 238, 50, 194], [58, 241, 50, 197, "customInactiveTintColor"], [58, 264, 50, 220], [59, 4, 51, 2], [59, 10, 51, 8, "activeBackgroundColor"], [59, 31, 51, 29], [59, 34, 51, 32, "customActiveBackgroundColor"], [59, 61, 51, 59], [59, 66, 51, 64, "variant"], [59, 73, 51, 71], [59, 78, 51, 76], [59, 88, 51, 86], [59, 91, 51, 89], [59, 95, 51, 89, "Color"], [59, 109, 51, 94], [59, 111, 51, 95, "activeTintColor"], [59, 126, 51, 110], [59, 127, 51, 111], [59, 128, 51, 112, "alpha"], [59, 133, 51, 117], [59, 134, 51, 118], [59, 138, 51, 122], [59, 139, 51, 123], [59, 140, 51, 124, "rgb"], [59, 143, 51, 127], [59, 144, 51, 128], [59, 145, 51, 129], [59, 146, 51, 130, "string"], [59, 152, 51, 136], [59, 153, 51, 137], [59, 154, 51, 138], [59, 157, 51, 141, "sidebar"], [59, 164, 51, 148], [59, 168, 51, 152, "horizontal"], [59, 178, 51, 162], [59, 181, 51, 165, "colors"], [59, 187, 51, 171], [59, 188, 51, 172, "primary"], [59, 195, 51, 179], [59, 198, 51, 182], [59, 211, 51, 195], [59, 212, 51, 196], [60, 4, 52, 2], [60, 10, 52, 8], [61, 6, 53, 4, "options"], [62, 4, 54, 2], [62, 5, 54, 3], [62, 8, 54, 6, "descriptor"], [62, 18, 54, 16], [63, 4, 55, 2], [63, 10, 55, 8, "labelString"], [63, 21, 55, 19], [63, 24, 55, 22], [63, 28, 55, 22, "get<PERSON><PERSON><PERSON>"], [63, 46, 55, 30], [63, 48, 55, 31], [64, 6, 56, 4, "label"], [64, 11, 56, 9], [64, 13, 56, 11], [64, 20, 56, 18, "options"], [64, 27, 56, 25], [64, 28, 56, 26, "tabBarLabel"], [64, 39, 56, 37], [64, 44, 56, 42], [64, 52, 56, 50], [64, 55, 56, 53, "options"], [64, 62, 56, 60], [64, 63, 56, 61, "tabBarLabel"], [64, 74, 56, 72], [64, 77, 56, 75, "undefined"], [64, 86, 56, 84], [65, 6, 57, 4, "title"], [65, 11, 57, 9], [65, 13, 57, 11, "options"], [65, 20, 57, 18], [65, 21, 57, 19, "title"], [66, 4, 58, 2], [66, 5, 58, 3], [66, 7, 58, 5, "route"], [66, 12, 58, 10], [66, 13, 58, 11, "name"], [66, 17, 58, 15], [66, 18, 58, 16], [67, 4, 59, 2], [67, 8, 59, 6, "labelInactiveTintColor"], [67, 30, 59, 28], [67, 33, 59, 31, "inactiveTintColor"], [67, 50, 59, 48], [68, 4, 60, 2], [68, 8, 60, 6, "iconInactiveTintColor"], [68, 29, 60, 27], [68, 32, 60, 30, "inactiveTintColor"], [68, 49, 60, 47], [69, 4, 61, 2], [69, 8, 61, 6, "variant"], [69, 15, 61, 13], [69, 20, 61, 18], [69, 27, 61, 25], [69, 31, 61, 29, "sidebar"], [69, 38, 61, 36], [69, 42, 61, 40, "horizontal"], [69, 52, 61, 50], [69, 56, 61, 54, "customInactiveTintColor"], [69, 79, 61, 77], [69, 84, 61, 82, "undefined"], [69, 93, 61, 91], [69, 95, 61, 93], [70, 6, 62, 4, "iconInactiveTintColor"], [70, 27, 62, 25], [70, 30, 62, 28, "colors"], [70, 36, 62, 34], [70, 37, 62, 35, "primary"], [70, 44, 62, 42], [71, 6, 63, 4, "labelInactiveTintColor"], [71, 28, 63, 26], [71, 31, 63, 29, "colors"], [71, 37, 63, 35], [71, 38, 63, 36, "text"], [71, 42, 63, 40], [72, 4, 64, 2], [73, 4, 65, 2], [73, 10, 65, 8, "renderLabel"], [73, 21, 65, 19], [73, 24, 65, 22, "renderLabel"], [73, 25, 65, 23], [74, 6, 66, 4, "focused"], [75, 4, 67, 2], [75, 5, 67, 3], [75, 10, 67, 8], [76, 6, 68, 4], [76, 10, 68, 8, "showLabel"], [76, 19, 68, 17], [76, 24, 68, 22], [76, 29, 68, 27], [76, 31, 68, 29], [77, 8, 69, 6], [77, 15, 69, 13], [77, 19, 69, 17], [78, 6, 70, 4], [79, 6, 71, 4], [79, 12, 71, 10, "color"], [79, 17, 71, 15], [79, 20, 71, 18, "focused"], [79, 27, 71, 25], [79, 30, 71, 28, "activeTintColor"], [79, 45, 71, 43], [79, 48, 71, 46, "labelInactiveTintColor"], [79, 70, 71, 68], [80, 6, 72, 4], [80, 10, 72, 8], [80, 17, 72, 15, "label"], [80, 22, 72, 20], [80, 27, 72, 25], [80, 35, 72, 33], [80, 37, 72, 35], [81, 8, 73, 6], [81, 15, 73, 13, "label"], [81, 20, 73, 18], [81, 21, 73, 19], [82, 10, 74, 8, "focused"], [82, 17, 74, 15], [83, 10, 75, 8, "color"], [83, 15, 75, 13], [84, 10, 76, 8, "position"], [84, 18, 76, 16], [84, 20, 76, 18, "horizontal"], [84, 30, 76, 28], [84, 33, 76, 31], [84, 46, 76, 44], [84, 49, 76, 47], [84, 61, 76, 59], [85, 10, 77, 8, "children"], [85, 18, 77, 16], [85, 20, 77, 18, "labelString"], [86, 8, 78, 6], [86, 9, 78, 7], [86, 10, 78, 8], [87, 6, 79, 4], [88, 6, 80, 4], [88, 13, 80, 11], [88, 26, 80, 24], [88, 30, 80, 24, "_jsx"], [88, 45, 80, 28], [88, 47, 80, 29, "Label"], [88, 62, 80, 34], [88, 64, 80, 36], [89, 8, 81, 6, "style"], [89, 13, 81, 11], [89, 15, 81, 13], [89, 16, 81, 14, "horizontal"], [89, 26, 81, 24], [89, 29, 81, 27], [89, 30, 81, 28, "styles"], [89, 36, 81, 34], [89, 37, 81, 35, "labelBeside"], [89, 48, 81, 46], [89, 50, 81, 48, "variant"], [89, 57, 81, 55], [89, 62, 81, 60], [89, 72, 81, 70], [89, 75, 81, 73, "styles"], [89, 81, 81, 79], [89, 82, 81, 80, "labelSidebarMaterial"], [89, 102, 81, 100], [89, 105, 81, 103, "sidebar"], [89, 112, 81, 110], [89, 115, 81, 113, "styles"], [89, 121, 81, 119], [89, 122, 81, 120, "labelSidebarUiKit"], [89, 139, 81, 137], [89, 142, 81, 140, "compact"], [89, 149, 81, 147], [89, 152, 81, 150, "styles"], [89, 158, 81, 156], [89, 159, 81, 157, "labelBesideUikitCompact"], [89, 182, 81, 180], [89, 185, 81, 183, "styles"], [89, 191, 81, 189], [89, 192, 81, 190, "labelBesideUikit"], [89, 208, 81, 206], [89, 210, 81, 208, "icon"], [89, 214, 81, 212], [89, 218, 81, 216], [89, 222, 81, 220], [89, 226, 81, 224], [90, 10, 82, 8, "marginStart"], [90, 21, 82, 19], [90, 23, 82, 21], [91, 8, 83, 6], [91, 9, 83, 7], [91, 10, 83, 8], [91, 13, 83, 11, "styles"], [91, 19, 83, 17], [91, 20, 83, 18, "labelBeneath"], [91, 32, 83, 30], [91, 34, 83, 32, "compact"], [91, 41, 83, 39], [91, 45, 83, 43, "variant"], [91, 52, 83, 50], [91, 57, 83, 55], [91, 64, 83, 62], [91, 68, 83, 66, "sidebar"], [91, 75, 83, 73], [91, 79, 83, 77, "horizontal"], [91, 89, 83, 87], [91, 92, 83, 90, "fonts"], [91, 97, 83, 95], [91, 98, 83, 96, "regular"], [91, 105, 83, 103], [91, 108, 83, 106, "fonts"], [91, 113, 83, 111], [91, 114, 83, 112, "medium"], [91, 120, 83, 118], [91, 122, 83, 120, "labelStyle"], [91, 132, 83, 130], [91, 133, 83, 131], [92, 8, 84, 6, "allowFontScaling"], [92, 24, 84, 22], [92, 26, 84, 24, "allowFontScaling"], [92, 42, 84, 40], [93, 8, 85, 6, "tintColor"], [93, 17, 85, 15], [93, 19, 85, 17, "color"], [93, 24, 85, 22], [94, 8, 86, 6, "children"], [94, 16, 86, 14], [94, 18, 86, 16, "label"], [95, 6, 87, 4], [95, 7, 87, 5], [95, 8, 87, 6], [96, 4, 88, 2], [96, 5, 88, 3], [97, 4, 89, 2], [97, 10, 89, 8, "renderIcon"], [97, 20, 89, 18], [97, 23, 89, 21, "renderIcon"], [97, 24, 89, 22], [98, 6, 90, 4, "focused"], [99, 4, 91, 2], [99, 5, 91, 3], [99, 10, 91, 8], [100, 6, 92, 4], [100, 10, 92, 8, "icon"], [100, 14, 92, 12], [100, 19, 92, 17, "undefined"], [100, 28, 92, 26], [100, 30, 92, 28], [101, 8, 93, 6], [101, 15, 93, 13], [101, 19, 93, 17], [102, 6, 94, 4], [103, 6, 95, 4], [103, 12, 95, 10, "activeOpacity"], [103, 25, 95, 23], [103, 28, 95, 26, "focused"], [103, 35, 95, 33], [103, 38, 95, 36], [103, 39, 95, 37], [103, 42, 95, 40], [103, 43, 95, 41], [104, 6, 96, 4], [104, 12, 96, 10, "inactiveOpacity"], [104, 27, 96, 25], [104, 30, 96, 28, "focused"], [104, 37, 96, 35], [104, 40, 96, 38], [104, 41, 96, 39], [104, 44, 96, 42], [104, 45, 96, 43], [105, 6, 97, 4], [105, 13, 97, 11], [105, 26, 97, 24], [105, 30, 97, 24, "_jsx"], [105, 45, 97, 28], [105, 47, 97, 29, "TabBarIcon"], [105, 69, 97, 39], [105, 71, 97, 41], [106, 8, 98, 6, "route"], [106, 13, 98, 11], [106, 15, 98, 13, "route"], [106, 20, 98, 18], [107, 8, 99, 6, "variant"], [107, 15, 99, 13], [107, 17, 99, 15, "variant"], [107, 24, 99, 22], [108, 8, 100, 6, "size"], [108, 12, 100, 10], [108, 14, 100, 12, "compact"], [108, 21, 100, 19], [108, 24, 100, 22], [108, 33, 100, 31], [108, 36, 100, 34], [108, 45, 100, 43], [109, 8, 101, 6, "badge"], [109, 13, 101, 11], [109, 15, 101, 13, "badge"], [109, 20, 101, 18], [110, 8, 102, 6, "badgeStyle"], [110, 18, 102, 16], [110, 20, 102, 18, "badgeStyle"], [110, 30, 102, 28], [111, 8, 103, 6, "activeOpacity"], [111, 21, 103, 19], [111, 23, 103, 21, "activeOpacity"], [111, 36, 103, 34], [112, 8, 104, 6, "allowFontScaling"], [112, 24, 104, 22], [112, 26, 104, 24, "allowFontScaling"], [112, 42, 104, 40], [113, 8, 105, 6, "inactiveOpacity"], [113, 23, 105, 21], [113, 25, 105, 23, "inactiveOpacity"], [113, 40, 105, 38], [114, 8, 106, 6, "activeTintColor"], [114, 23, 106, 21], [114, 25, 106, 23, "activeTintColor"], [114, 40, 106, 38], [115, 8, 107, 6, "inactiveTintColor"], [115, 25, 107, 23], [115, 27, 107, 25, "iconInactiveTintColor"], [115, 48, 107, 46], [116, 8, 108, 6, "renderIcon"], [116, 18, 108, 16], [116, 20, 108, 18, "icon"], [116, 24, 108, 22], [117, 8, 109, 6, "style"], [117, 13, 109, 11], [117, 15, 109, 13, "iconStyle"], [118, 6, 110, 4], [118, 7, 110, 5], [118, 8, 110, 6], [119, 4, 111, 2], [119, 5, 111, 3], [120, 4, 112, 2], [120, 10, 112, 8, "scene"], [120, 15, 112, 13], [120, 18, 112, 16], [121, 6, 113, 4, "route"], [121, 11, 113, 9], [122, 6, 114, 4, "focused"], [123, 4, 115, 2], [123, 5, 115, 3], [124, 4, 116, 2], [124, 10, 116, 8, "backgroundColor"], [124, 25, 116, 23], [124, 28, 116, 26, "focused"], [124, 35, 116, 33], [124, 38, 116, 36, "activeBackgroundColor"], [124, 59, 116, 57], [124, 62, 116, 60, "inactiveBackgroundColor"], [124, 85, 116, 83], [125, 4, 117, 2], [125, 10, 117, 8], [126, 6, 118, 4, "flex"], [127, 4, 119, 2], [127, 5, 119, 3], [127, 8, 119, 6, "StyleSheet"], [127, 27, 119, 16], [127, 28, 119, 17, "flatten"], [127, 35, 119, 24], [127, 36, 119, 25, "style"], [127, 41, 119, 30], [127, 45, 119, 34], [127, 46, 119, 35], [127, 47, 119, 36], [127, 48, 119, 37], [128, 4, 120, 2], [128, 10, 120, 8, "borderRadius"], [128, 22, 120, 20], [128, 25, 120, 23, "variant"], [128, 32, 120, 30], [128, 37, 120, 35], [128, 47, 120, 45], [128, 50, 120, 48, "horizontal"], [128, 60, 120, 58], [128, 63, 120, 61], [128, 65, 120, 63], [128, 68, 120, 66], [128, 70, 120, 68], [128, 73, 120, 71, "sidebar"], [128, 80, 120, 78], [128, 84, 120, 82, "horizontal"], [128, 94, 120, 92], [128, 97, 120, 95], [128, 99, 120, 97], [128, 102, 120, 100], [128, 103, 120, 101], [129, 4, 121, 2], [129, 11, 121, 9], [129, 24, 121, 22], [129, 28, 121, 22, "_jsx"], [129, 43, 121, 26], [129, 45, 121, 27, "View"], [129, 58, 121, 31], [129, 60, 121, 33], [130, 6, 122, 4, "style"], [130, 11, 122, 9], [130, 13, 122, 11], [131, 6, 123, 4], [132, 6, 124, 4], [133, 8, 125, 6, "borderRadius"], [133, 20, 125, 18], [134, 8, 126, 6, "overflow"], [134, 16, 126, 14], [134, 18, 126, 16, "variant"], [134, 25, 126, 23], [134, 30, 126, 28], [134, 40, 126, 38], [134, 43, 126, 41], [134, 51, 126, 49], [134, 54, 126, 52], [135, 6, 127, 4], [135, 7, 127, 5], [135, 9, 127, 7, "style"], [135, 14, 127, 12], [135, 15, 127, 13], [136, 6, 128, 4, "children"], [136, 14, 128, 12], [136, 16, 128, 14, "button"], [136, 22, 128, 20], [136, 23, 128, 21], [137, 8, 129, 6, "href"], [137, 12, 129, 10], [138, 8, 130, 6, "onPress"], [138, 15, 130, 13], [139, 8, 131, 6, "onLongPress"], [139, 19, 131, 17], [140, 8, 132, 6, "testID"], [140, 14, 132, 12], [141, 8, 133, 6], [141, 20, 133, 18], [141, 22, 133, 20, "accessibilityLabel"], [141, 40, 133, 38], [142, 8, 134, 6], [142, 40, 134, 38], [142, 42, 134, 40, "labelString"], [142, 53, 134, 51], [143, 8, 135, 6], [143, 46, 135, 44], [143, 48, 135, 46], [143, 52, 135, 50], [144, 8, 136, 6], [145, 8, 137, 6], [145, 14, 137, 12], [145, 16, 137, 14, "Platform"], [145, 33, 137, 22], [145, 34, 137, 23, "select"], [145, 40, 137, 29], [145, 41, 137, 30], [146, 10, 138, 8, "ios"], [146, 13, 138, 11], [146, 15, 138, 13], [146, 23, 138, 21], [147, 10, 139, 8, "default"], [147, 17, 139, 15], [147, 19, 139, 17], [148, 8, 140, 6], [148, 9, 140, 7], [148, 10, 140, 8], [149, 8, 141, 6], [149, 23, 141, 21], [149, 25, 141, 23, "focused"], [149, 32, 141, 30], [150, 8, 142, 6], [150, 24, 142, 22], [150, 26, 142, 24], [151, 10, 143, 8, "borderless"], [151, 20, 143, 18], [151, 22, 143, 20], [152, 8, 144, 6], [152, 9, 144, 7], [153, 8, 145, 6], [153, 21, 145, 19], [153, 23, 145, 21, "variant"], [153, 30, 145, 28], [153, 35, 145, 33], [153, 45, 145, 43], [153, 49, 145, 47, "sidebar"], [153, 56, 145, 54], [153, 60, 145, 58, "horizontal"], [153, 70, 145, 68], [153, 73, 145, 71], [154, 10, 146, 8, "color"], [154, 15, 146, 13], [154, 17, 146, 15, "colors"], [154, 23, 146, 21], [154, 24, 146, 22, "text"], [155, 8, 147, 6], [155, 9, 147, 7], [155, 12, 147, 10, "undefined"], [155, 21, 147, 19], [156, 8, 148, 6], [156, 22, 148, 20], [156, 24, 148, 22], [156, 25, 148, 23], [157, 8, 149, 6], [157, 15, 149, 13], [157, 17, 149, 15], [157, 18, 149, 16, "styles"], [157, 24, 149, 22], [157, 25, 149, 23, "tab"], [157, 28, 149, 26], [157, 30, 149, 28], [158, 10, 150, 8, "flex"], [158, 14, 150, 12], [159, 10, 151, 8, "backgroundColor"], [159, 25, 151, 23], [160, 10, 152, 8, "borderRadius"], [161, 8, 153, 6], [161, 9, 153, 7], [161, 11, 153, 9, "sidebar"], [161, 18, 153, 16], [161, 21, 153, 19, "variant"], [161, 28, 153, 26], [161, 33, 153, 31], [161, 43, 153, 41], [161, 46, 153, 44, "horizontal"], [161, 56, 153, 54], [161, 59, 153, 57, "styles"], [161, 65, 153, 63], [161, 66, 153, 64, "tabBarSidebarMaterial"], [161, 87, 153, 85], [161, 90, 153, 88, "styles"], [161, 96, 153, 94], [161, 97, 153, 95, "tabVerticalMaterial"], [161, 116, 153, 114], [161, 119, 153, 117, "horizontal"], [161, 129, 153, 127], [161, 132, 153, 130, "styles"], [161, 138, 153, 136], [161, 139, 153, 137, "tabBarSidebarUiKit"], [161, 157, 153, 155], [161, 160, 153, 158, "styles"], [161, 166, 153, 164], [161, 167, 153, 165, "tabVerticalUiKit"], [161, 183, 153, 181], [161, 186, 153, 184, "variant"], [161, 193, 153, 191], [161, 198, 153, 196], [161, 208, 153, 206], [161, 211, 153, 209, "styles"], [161, 217, 153, 215], [161, 218, 153, 216, "tabVerticalMaterial"], [161, 237, 153, 235], [161, 240, 153, 238, "horizontal"], [161, 250, 153, 248], [161, 253, 153, 251, "styles"], [161, 259, 153, 257], [161, 260, 153, 258, "tabHorizontalUiKit"], [161, 278, 153, 276], [161, 281, 153, 279, "styles"], [161, 287, 153, 285], [161, 288, 153, 286, "tabVerticalUiKit"], [161, 304, 153, 302], [161, 305, 153, 303], [162, 8, 154, 6], [162, 18, 154, 16], [162, 20, 154, 18], [162, 33, 154, 31], [162, 37, 154, 31, "_jsxs"], [162, 53, 154, 36], [162, 55, 154, 37, "React"], [162, 69, 154, 42], [162, 70, 154, 43, "Fragment"], [162, 78, 154, 51], [162, 80, 154, 53], [163, 10, 155, 8, "children"], [163, 18, 155, 16], [163, 20, 155, 18], [163, 21, 155, 19, "renderIcon"], [163, 31, 155, 29], [163, 32, 155, 30, "scene"], [163, 37, 155, 35], [163, 38, 155, 36], [163, 40, 155, 38, "renderLabel"], [163, 51, 155, 49], [163, 52, 155, 50, "scene"], [163, 57, 155, 55], [163, 58, 155, 56], [164, 8, 156, 6], [164, 9, 156, 7], [165, 6, 157, 4], [165, 7, 157, 5], [166, 4, 158, 2], [166, 5, 158, 3], [166, 6, 158, 4], [167, 2, 159, 0], [168, 2, 160, 0], [168, 8, 160, 6, "styles"], [168, 14, 160, 12], [168, 17, 160, 15, "StyleSheet"], [168, 36, 160, 25], [168, 37, 160, 26, "create"], [168, 43, 160, 32], [168, 44, 160, 33], [169, 4, 161, 2, "tab"], [169, 7, 161, 5], [169, 9, 161, 7], [170, 6, 162, 4, "alignItems"], [170, 16, 162, 14], [170, 18, 162, 16], [170, 26, 162, 24], [171, 6, 163, 4], [172, 6, 164, 4, "borderRadius"], [172, 18, 164, 16], [172, 20, 164, 18], [173, 4, 165, 2], [173, 5, 165, 3], [174, 4, 166, 2, "tabVerticalUiKit"], [174, 20, 166, 18], [174, 22, 166, 20], [175, 6, 167, 4, "justifyContent"], [175, 20, 167, 18], [175, 22, 167, 20], [175, 34, 167, 32], [176, 6, 168, 4, "flexDirection"], [176, 19, 168, 17], [176, 21, 168, 19], [176, 29, 168, 27], [177, 6, 169, 4, "padding"], [177, 13, 169, 11], [177, 15, 169, 13], [178, 4, 170, 2], [178, 5, 170, 3], [179, 4, 171, 2, "tabVerticalMaterial"], [179, 23, 171, 21], [179, 25, 171, 23], [180, 6, 172, 4, "padding"], [180, 13, 172, 11], [180, 15, 172, 13], [181, 4, 173, 2], [181, 5, 173, 3], [182, 4, 174, 2, "tabHorizontalUiKit"], [182, 22, 174, 20], [182, 24, 174, 22], [183, 6, 175, 4, "justifyContent"], [183, 20, 175, 18], [183, 22, 175, 20], [183, 30, 175, 28], [184, 6, 176, 4, "alignItems"], [184, 16, 176, 14], [184, 18, 176, 16], [184, 26, 176, 24], [185, 6, 177, 4, "flexDirection"], [185, 19, 177, 17], [185, 21, 177, 19], [185, 26, 177, 24], [186, 6, 178, 4, "padding"], [186, 13, 178, 11], [186, 15, 178, 13], [187, 4, 179, 2], [187, 5, 179, 3], [188, 4, 180, 2, "tabBarSidebarUiKit"], [188, 22, 180, 20], [188, 24, 180, 22], [189, 6, 181, 4, "justifyContent"], [189, 20, 181, 18], [189, 22, 181, 20], [189, 34, 181, 32], [190, 6, 182, 4, "alignItems"], [190, 16, 182, 14], [190, 18, 182, 16], [190, 26, 182, 24], [191, 6, 183, 4, "flexDirection"], [191, 19, 183, 17], [191, 21, 183, 19], [191, 26, 183, 24], [192, 6, 184, 4, "paddingVertical"], [192, 21, 184, 19], [192, 23, 184, 21], [192, 24, 184, 22], [193, 6, 185, 4, "paddingHorizontal"], [193, 23, 185, 21], [193, 25, 185, 23], [194, 4, 186, 2], [194, 5, 186, 3], [195, 4, 187, 2, "tabBarSidebarMaterial"], [195, 25, 187, 23], [195, 27, 187, 25], [196, 6, 188, 4, "justifyContent"], [196, 20, 188, 18], [196, 22, 188, 20], [196, 34, 188, 32], [197, 6, 189, 4, "alignItems"], [197, 16, 189, 14], [197, 18, 189, 16], [197, 26, 189, 24], [198, 6, 190, 4, "flexDirection"], [198, 19, 190, 17], [198, 21, 190, 19], [198, 26, 190, 24], [199, 6, 191, 4, "paddingVertical"], [199, 21, 191, 19], [199, 23, 191, 21], [199, 25, 191, 23], [200, 6, 192, 4, "paddingStart"], [200, 18, 192, 16], [200, 20, 192, 18], [200, 22, 192, 20], [201, 6, 193, 4, "paddingEnd"], [201, 16, 193, 14], [201, 18, 193, 16], [202, 4, 194, 2], [202, 5, 194, 3], [203, 4, 195, 2, "labelSidebarMaterial"], [203, 24, 195, 22], [203, 26, 195, 24], [204, 6, 196, 4, "marginStart"], [204, 17, 196, 15], [204, 19, 196, 17], [205, 4, 197, 2], [205, 5, 197, 3], [206, 4, 198, 2, "labelSidebarUiKit"], [206, 21, 198, 19], [206, 23, 198, 21], [207, 6, 199, 4, "fontSize"], [207, 14, 199, 12], [207, 16, 199, 14], [207, 18, 199, 16], [208, 6, 200, 4, "marginStart"], [208, 17, 200, 15], [208, 19, 200, 17], [209, 4, 201, 2], [209, 5, 201, 3], [210, 4, 202, 2, "labelBeneath"], [210, 16, 202, 14], [210, 18, 202, 16], [211, 6, 203, 4, "fontSize"], [211, 14, 203, 12], [211, 16, 203, 14], [212, 4, 204, 2], [212, 5, 204, 3], [213, 4, 205, 2, "labelBeside"], [213, 15, 205, 13], [213, 17, 205, 15], [214, 6, 206, 4, "marginEnd"], [214, 15, 206, 13], [214, 17, 206, 15], [214, 19, 206, 17], [215, 6, 207, 4, "lineHeight"], [215, 16, 207, 14], [215, 18, 207, 16], [216, 4, 208, 2], [216, 5, 208, 3], [217, 4, 209, 2, "labelBesideUikit"], [217, 20, 209, 18], [217, 22, 209, 20], [218, 6, 210, 4, "fontSize"], [218, 14, 210, 12], [218, 16, 210, 14], [218, 18, 210, 16], [219, 6, 211, 4, "marginStart"], [219, 17, 211, 15], [219, 19, 211, 17], [220, 4, 212, 2], [220, 5, 212, 3], [221, 4, 213, 2, "labelBesideUikitCompact"], [221, 27, 213, 25], [221, 29, 213, 27], [222, 6, 214, 4, "fontSize"], [222, 14, 214, 12], [222, 16, 214, 14], [222, 18, 214, 16], [223, 6, 215, 4, "marginStart"], [223, 17, 215, 15], [223, 19, 215, 17], [224, 4, 216, 2], [225, 2, 217, 0], [225, 3, 217, 1], [225, 4, 217, 2], [226, 0, 217, 3], [226, 3]], "functionMap": {"names": ["<global>", "renderButtonDefault", "BottomTabItem", "renderLabel", "renderIcon"], "mappings": "AAA;4BCS;EDE;OEE;sBCmD;GDuB;qBEC;GFsB;CFgD"}}, "type": "js/module"}]}