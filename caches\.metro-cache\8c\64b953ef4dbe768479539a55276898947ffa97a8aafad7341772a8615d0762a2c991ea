{"dependencies": [{"name": "./RendererImplementation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 41}}], "key": "12hF92i5XVDjRpIIb7ZEuNg9bK0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _RendererImplementation = require(_dependencyMap[0], \"./RendererImplementation\");\n  Object.keys(_RendererImplementation).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _RendererImplementation[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _RendererImplementation[key];\n      }\n    });\n  });\n});", "lineCount": 16, "map": [[5, 2, 26, 0], [5, 6, 26, 0, "_RendererImplementation"], [5, 29, 26, 0], [5, 32, 26, 0, "require"], [5, 39, 26, 0], [5, 40, 26, 0, "_dependencyMap"], [5, 54, 26, 0], [6, 2, 26, 0, "Object"], [6, 8, 26, 0], [6, 9, 26, 0, "keys"], [6, 13, 26, 0], [6, 14, 26, 0, "_RendererImplementation"], [6, 37, 26, 0], [6, 39, 26, 0, "for<PERSON>ach"], [6, 46, 26, 0], [6, 57, 26, 0, "key"], [6, 60, 26, 0], [7, 4, 26, 0], [7, 8, 26, 0, "key"], [7, 11, 26, 0], [7, 29, 26, 0, "key"], [7, 32, 26, 0], [8, 4, 26, 0], [8, 8, 26, 0, "key"], [8, 11, 26, 0], [8, 15, 26, 0, "exports"], [8, 22, 26, 0], [8, 26, 26, 0, "exports"], [8, 33, 26, 0], [8, 34, 26, 0, "key"], [8, 37, 26, 0], [8, 43, 26, 0, "_RendererImplementation"], [8, 66, 26, 0], [8, 67, 26, 0, "key"], [8, 70, 26, 0], [9, 4, 26, 0, "Object"], [9, 10, 26, 0], [9, 11, 26, 0, "defineProperty"], [9, 25, 26, 0], [9, 26, 26, 0, "exports"], [9, 33, 26, 0], [9, 35, 26, 0, "key"], [9, 38, 26, 0], [10, 6, 26, 0, "enumerable"], [10, 16, 26, 0], [11, 6, 26, 0, "get"], [11, 9, 26, 0], [11, 20, 26, 0, "get"], [11, 21, 26, 0], [12, 8, 26, 0], [12, 15, 26, 0, "_RendererImplementation"], [12, 38, 26, 0], [12, 39, 26, 0, "key"], [12, 42, 26, 0], [13, 6, 26, 0], [14, 4, 26, 0], [15, 2, 26, 0], [16, 0, 26, 41], [16, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}