{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../Core/Devtools/openFileInEditor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 68}}], "key": "3vH1y5vrRpr8TQTBUBKQt4U0X5I=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Text/Text", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 35}}], "key": "2Uowcf8dI9Q+9EqAhRxQzVpiZEk=", "exportNames": ["*"]}}, {"name": "../../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 48}}], "key": "/m0HqCpVZ4yItbJJaw+YeR/qFWU=", "exportNames": ["*"]}}, {"name": "./LogBoxButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 42}}], "key": "M6ofQu070ZUTf+Oq+Zz+7FQEgjs=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorSection", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 62}}], "key": "psfwCNco8+nKb+3u3V4A+OhHW2E=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"../../Components/View/View\"));\n  var _openFileInEditor = _interopRequireDefault(require(_dependencyMap[3], \"../../Core/Devtools/openFileInEditor\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"../../StyleSheet/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[5], \"../../Text/Text\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[6], \"../../Utilities/Platform\"));\n  var _LogBoxButton = _interopRequireDefault(require(_dependencyMap[7], \"./LogBoxButton\"));\n  var _LogBoxInspectorSection = _interopRequireDefault(require(_dependencyMap[8], \"./LogBoxInspectorSection\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[9], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[10], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[11], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorReactFrames.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var BEFORE_SLASH_RE = /^(.*)[\\\\/]/;\n  function getPrettyFileName(path) {\n    var fileName = path.replace(BEFORE_SLASH_RE, '');\n    if (/^index\\./.test(fileName)) {\n      var match = path.match(BEFORE_SLASH_RE);\n      if (match) {\n        var pathBeforeSlash = match[1];\n        if (pathBeforeSlash) {\n          var folderName = pathBeforeSlash.replace(BEFORE_SLASH_RE, '');\n          fileName = folderName + '/​' + fileName;\n        }\n      }\n    }\n    return fileName;\n  }\n  function LogBoxInspectorReactFrames(props) {\n    var _React$useState = React.useState(true),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      collapsed = _React$useState2[0],\n      setCollapsed = _React$useState2[1];\n    if (props.log.getAvailableComponentStack() == null || props.log.getAvailableComponentStack().length < 1) {\n      return null;\n    }\n    function getStackList() {\n      if (collapsed) {\n        return props.log.getAvailableComponentStack().slice(0, 3);\n      } else {\n        return props.log.getAvailableComponentStack();\n      }\n    }\n    function getCollapseMessage() {\n      if (props.log.getAvailableComponentStack().length <= 3) {\n        return;\n      }\n      var count = props.log.getAvailableComponentStack().length - 3;\n      if (collapsed) {\n        return `See ${count} more components`;\n      } else {\n        return `Collapse ${count} components`;\n      }\n    }\n    return (0, _jsxRuntime.jsxs)(_LogBoxInspectorSection.default, {\n      heading: \"Component Stack\",\n      children: [getStackList().map((frame, index) => (0, _jsxRuntime.jsx)(_View.default, {\n        style: componentStyles.frameContainer,\n        children: (0, _jsxRuntime.jsxs)(_LogBoxButton.default, {\n          backgroundColor: {\n            default: 'transparent',\n            pressed: LogBoxStyle.getBackgroundColor(1)\n          },\n          onPress: frame.fileName.startsWith('/') ? () => (0, _openFileInEditor.default)(frame.fileName, frame.location?.row ?? 1) : null,\n          style: componentStyles.frame,\n          children: [(0, _jsxRuntime.jsx)(_View.default, {\n            style: componentStyles.component,\n            children: (0, _jsxRuntime.jsxs)(_Text.default, {\n              id: \"logbox_component_stack_frame_text\",\n              style: componentStyles.frameName,\n              children: [(0, _jsxRuntime.jsx)(_Text.default, {\n                style: componentStyles.bracket,\n                children: '<'\n              }), frame.content, (0, _jsxRuntime.jsx)(_Text.default, {\n                style: componentStyles.bracket,\n                children: ' />'\n              })]\n            })\n          }), (0, _jsxRuntime.jsxs)(_Text.default, {\n            style: componentStyles.frameLocation,\n            children: [getPrettyFileName(frame.fileName), frame.location ? `:${frame.location.row}` : '']\n          })]\n        })\n      }, index)), (0, _jsxRuntime.jsx)(_View.default, {\n        style: componentStyles.collapseContainer,\n        children: (0, _jsxRuntime.jsx)(_LogBoxButton.default, {\n          backgroundColor: {\n            default: 'transparent',\n            pressed: LogBoxStyle.getBackgroundColor(1)\n          },\n          onPress: () => setCollapsed(!collapsed),\n          style: componentStyles.collapseButton,\n          children: (0, _jsxRuntime.jsx)(_Text.default, {\n            style: componentStyles.collapse,\n            children: getCollapseMessage()\n          })\n        })\n      })]\n    });\n  }\n  var componentStyles = _StyleSheet.default.create({\n    collapseContainer: {\n      marginLeft: 15,\n      flexDirection: 'row'\n    },\n    collapseButton: {\n      borderRadius: 5\n    },\n    collapse: {\n      color: LogBoxStyle.getTextColor(0.7),\n      fontSize: 12,\n      fontWeight: '300',\n      lineHeight: 20,\n      marginTop: 0,\n      paddingVertical: 5,\n      paddingHorizontal: 10\n    },\n    frameContainer: {\n      flexDirection: 'row',\n      paddingHorizontal: 15\n    },\n    frame: {\n      flex: 1,\n      paddingVertical: 4,\n      paddingHorizontal: 10,\n      borderRadius: 5\n    },\n    component: {\n      flexDirection: 'row',\n      paddingRight: 10\n    },\n    frameName: {\n      fontFamily: _Platform.default.select({\n        android: 'monospace',\n        ios: 'Menlo'\n      }),\n      color: LogBoxStyle.getTextColor(1),\n      fontSize: 14,\n      includeFontPadding: false,\n      lineHeight: 18\n    },\n    bracket: {\n      fontFamily: _Platform.default.select({\n        android: 'monospace',\n        ios: 'Menlo'\n      }),\n      color: LogBoxStyle.getTextColor(0.4),\n      fontSize: 14,\n      fontWeight: '500',\n      includeFontPadding: false,\n      lineHeight: 18\n    },\n    frameLocation: {\n      color: LogBoxStyle.getTextColor(0.7),\n      fontSize: 12,\n      fontWeight: '300',\n      includeFontPadding: false,\n      lineHeight: 16,\n      paddingLeft: 10\n    }\n  });\n  var _default = exports.default = LogBoxInspectorReactFrames;\n});", "lineCount": 169, "map": [[8, 2, 13, 0], [8, 6, 13, 0, "_View"], [8, 11, 13, 0], [8, 14, 13, 0, "_interopRequireDefault"], [8, 36, 13, 0], [8, 37, 13, 0, "require"], [8, 44, 13, 0], [8, 45, 13, 0, "_dependencyMap"], [8, 59, 13, 0], [9, 2, 14, 0], [9, 6, 14, 0, "_openFileInEditor"], [9, 23, 14, 0], [9, 26, 14, 0, "_interopRequireDefault"], [9, 48, 14, 0], [9, 49, 14, 0, "require"], [9, 56, 14, 0], [9, 57, 14, 0, "_dependencyMap"], [9, 71, 14, 0], [10, 2, 15, 0], [10, 6, 15, 0, "_StyleSheet"], [10, 17, 15, 0], [10, 20, 15, 0, "_interopRequireDefault"], [10, 42, 15, 0], [10, 43, 15, 0, "require"], [10, 50, 15, 0], [10, 51, 15, 0, "_dependencyMap"], [10, 65, 15, 0], [11, 2, 16, 0], [11, 6, 16, 0, "_Text"], [11, 11, 16, 0], [11, 14, 16, 0, "_interopRequireDefault"], [11, 36, 16, 0], [11, 37, 16, 0, "require"], [11, 44, 16, 0], [11, 45, 16, 0, "_dependencyMap"], [11, 59, 16, 0], [12, 2, 17, 0], [12, 6, 17, 0, "_Platform"], [12, 15, 17, 0], [12, 18, 17, 0, "_interopRequireDefault"], [12, 40, 17, 0], [12, 41, 17, 0, "require"], [12, 48, 17, 0], [12, 49, 17, 0, "_dependencyMap"], [12, 63, 17, 0], [13, 2, 18, 0], [13, 6, 18, 0, "_LogBoxButton"], [13, 19, 18, 0], [13, 22, 18, 0, "_interopRequireDefault"], [13, 44, 18, 0], [13, 45, 18, 0, "require"], [13, 52, 18, 0], [13, 53, 18, 0, "_dependencyMap"], [13, 67, 18, 0], [14, 2, 19, 0], [14, 6, 19, 0, "_LogBoxInspectorSection"], [14, 29, 19, 0], [14, 32, 19, 0, "_interopRequireDefault"], [14, 54, 19, 0], [14, 55, 19, 0, "require"], [14, 62, 19, 0], [14, 63, 19, 0, "_dependencyMap"], [14, 77, 19, 0], [15, 2, 20, 0], [15, 6, 20, 0, "LogBoxStyle"], [15, 17, 20, 0], [15, 20, 20, 0, "_interopRequireWildcard"], [15, 43, 20, 0], [15, 44, 20, 0, "require"], [15, 51, 20, 0], [15, 52, 20, 0, "_dependencyMap"], [15, 66, 20, 0], [16, 2, 21, 0], [16, 6, 21, 0, "React"], [16, 11, 21, 0], [16, 14, 21, 0, "_interopRequireWildcard"], [16, 37, 21, 0], [16, 38, 21, 0, "require"], [16, 45, 21, 0], [16, 46, 21, 0, "_dependencyMap"], [16, 60, 21, 0], [17, 2, 21, 31], [17, 6, 21, 31, "_jsxRuntime"], [17, 17, 21, 31], [17, 20, 21, 31, "require"], [17, 27, 21, 31], [17, 28, 21, 31, "_dependencyMap"], [17, 42, 21, 31], [18, 2, 21, 31], [18, 6, 21, 31, "_jsxFileName"], [18, 18, 21, 31], [19, 2, 21, 31], [19, 11, 21, 31, "_interopRequireWildcard"], [19, 35, 21, 31, "e"], [19, 36, 21, 31], [19, 38, 21, 31, "t"], [19, 39, 21, 31], [19, 68, 21, 31, "WeakMap"], [19, 75, 21, 31], [19, 81, 21, 31, "r"], [19, 82, 21, 31], [19, 89, 21, 31, "WeakMap"], [19, 96, 21, 31], [19, 100, 21, 31, "n"], [19, 101, 21, 31], [19, 108, 21, 31, "WeakMap"], [19, 115, 21, 31], [19, 127, 21, 31, "_interopRequireWildcard"], [19, 150, 21, 31], [19, 162, 21, 31, "_interopRequireWildcard"], [19, 163, 21, 31, "e"], [19, 164, 21, 31], [19, 166, 21, 31, "t"], [19, 167, 21, 31], [19, 176, 21, 31, "t"], [19, 177, 21, 31], [19, 181, 21, 31, "e"], [19, 182, 21, 31], [19, 186, 21, 31, "e"], [19, 187, 21, 31], [19, 188, 21, 31, "__esModule"], [19, 198, 21, 31], [19, 207, 21, 31, "e"], [19, 208, 21, 31], [19, 214, 21, 31, "o"], [19, 215, 21, 31], [19, 217, 21, 31, "i"], [19, 218, 21, 31], [19, 220, 21, 31, "f"], [19, 221, 21, 31], [19, 226, 21, 31, "__proto__"], [19, 235, 21, 31], [19, 243, 21, 31, "default"], [19, 250, 21, 31], [19, 252, 21, 31, "e"], [19, 253, 21, 31], [19, 270, 21, 31, "e"], [19, 271, 21, 31], [19, 294, 21, 31, "e"], [19, 295, 21, 31], [19, 320, 21, 31, "e"], [19, 321, 21, 31], [19, 330, 21, 31, "f"], [19, 331, 21, 31], [19, 337, 21, 31, "o"], [19, 338, 21, 31], [19, 341, 21, 31, "t"], [19, 342, 21, 31], [19, 345, 21, 31, "n"], [19, 346, 21, 31], [19, 349, 21, 31, "r"], [19, 350, 21, 31], [19, 358, 21, 31, "o"], [19, 359, 21, 31], [19, 360, 21, 31, "has"], [19, 363, 21, 31], [19, 364, 21, 31, "e"], [19, 365, 21, 31], [19, 375, 21, 31, "o"], [19, 376, 21, 31], [19, 377, 21, 31, "get"], [19, 380, 21, 31], [19, 381, 21, 31, "e"], [19, 382, 21, 31], [19, 385, 21, 31, "o"], [19, 386, 21, 31], [19, 387, 21, 31, "set"], [19, 390, 21, 31], [19, 391, 21, 31, "e"], [19, 392, 21, 31], [19, 394, 21, 31, "f"], [19, 395, 21, 31], [19, 409, 21, 31, "_t"], [19, 411, 21, 31], [19, 415, 21, 31, "e"], [19, 416, 21, 31], [19, 432, 21, 31, "_t"], [19, 434, 21, 31], [19, 441, 21, 31, "hasOwnProperty"], [19, 455, 21, 31], [19, 456, 21, 31, "call"], [19, 460, 21, 31], [19, 461, 21, 31, "e"], [19, 462, 21, 31], [19, 464, 21, 31, "_t"], [19, 466, 21, 31], [19, 473, 21, 31, "i"], [19, 474, 21, 31], [19, 478, 21, 31, "o"], [19, 479, 21, 31], [19, 482, 21, 31, "Object"], [19, 488, 21, 31], [19, 489, 21, 31, "defineProperty"], [19, 503, 21, 31], [19, 508, 21, 31, "Object"], [19, 514, 21, 31], [19, 515, 21, 31, "getOwnPropertyDescriptor"], [19, 539, 21, 31], [19, 540, 21, 31, "e"], [19, 541, 21, 31], [19, 543, 21, 31, "_t"], [19, 545, 21, 31], [19, 552, 21, 31, "i"], [19, 553, 21, 31], [19, 554, 21, 31, "get"], [19, 557, 21, 31], [19, 561, 21, 31, "i"], [19, 562, 21, 31], [19, 563, 21, 31, "set"], [19, 566, 21, 31], [19, 570, 21, 31, "o"], [19, 571, 21, 31], [19, 572, 21, 31, "f"], [19, 573, 21, 31], [19, 575, 21, 31, "_t"], [19, 577, 21, 31], [19, 579, 21, 31, "i"], [19, 580, 21, 31], [19, 584, 21, 31, "f"], [19, 585, 21, 31], [19, 586, 21, 31, "_t"], [19, 588, 21, 31], [19, 592, 21, 31, "e"], [19, 593, 21, 31], [19, 594, 21, 31, "_t"], [19, 596, 21, 31], [19, 607, 21, 31, "f"], [19, 608, 21, 31], [19, 613, 21, 31, "e"], [19, 614, 21, 31], [19, 616, 21, 31, "t"], [19, 617, 21, 31], [20, 2, 27, 0], [20, 6, 27, 6, "BEFORE_SLASH_RE"], [20, 21, 27, 21], [20, 24, 27, 24], [20, 36, 27, 36], [21, 2, 30, 0], [21, 11, 30, 9, "getPrettyFileName"], [21, 28, 30, 26, "getPrettyFileName"], [21, 29, 30, 27, "path"], [21, 33, 30, 39], [21, 35, 30, 41], [22, 4, 31, 2], [22, 8, 31, 6, "fileName"], [22, 16, 31, 14], [22, 19, 31, 17, "path"], [22, 23, 31, 21], [22, 24, 31, 22, "replace"], [22, 31, 31, 29], [22, 32, 31, 30, "BEFORE_SLASH_RE"], [22, 47, 31, 45], [22, 49, 31, 47], [22, 51, 31, 49], [22, 52, 31, 50], [23, 4, 35, 2], [23, 8, 35, 6], [23, 18, 35, 16], [23, 19, 35, 17, "test"], [23, 23, 35, 21], [23, 24, 35, 22, "fileName"], [23, 32, 35, 30], [23, 33, 35, 31], [23, 35, 35, 33], [24, 6, 36, 4], [24, 10, 36, 10, "match"], [24, 15, 36, 15], [24, 18, 36, 18, "path"], [24, 22, 36, 22], [24, 23, 36, 23, "match"], [24, 28, 36, 28], [24, 29, 36, 29, "BEFORE_SLASH_RE"], [24, 44, 36, 44], [24, 45, 36, 45], [25, 6, 37, 4], [25, 10, 37, 8, "match"], [25, 15, 37, 13], [25, 17, 37, 15], [26, 8, 38, 6], [26, 12, 38, 12, "pathBeforeSlash"], [26, 27, 38, 27], [26, 30, 38, 30, "match"], [26, 35, 38, 35], [26, 36, 38, 36], [26, 37, 38, 37], [26, 38, 38, 38], [27, 8, 39, 6], [27, 12, 39, 10, "pathBeforeSlash"], [27, 27, 39, 25], [27, 29, 39, 27], [28, 10, 40, 8], [28, 14, 40, 14, "folderName"], [28, 24, 40, 24], [28, 27, 40, 27, "pathBeforeSlash"], [28, 42, 40, 42], [28, 43, 40, 43, "replace"], [28, 50, 40, 50], [28, 51, 40, 51, "BEFORE_SLASH_RE"], [28, 66, 40, 66], [28, 68, 40, 68], [28, 70, 40, 70], [28, 71, 40, 71], [29, 10, 44, 8, "fileName"], [29, 18, 44, 16], [29, 21, 44, 19, "folderName"], [29, 31, 44, 29], [29, 34, 44, 32], [29, 38, 44, 36], [29, 41, 44, 39, "fileName"], [29, 49, 44, 47], [30, 8, 45, 6], [31, 6, 46, 4], [32, 4, 47, 2], [33, 4, 49, 2], [33, 11, 49, 9, "fileName"], [33, 19, 49, 17], [34, 2, 50, 0], [35, 2, 51, 0], [35, 11, 51, 9, "LogBoxInspectorReactFrames"], [35, 37, 51, 35, "LogBoxInspectorReactFrames"], [35, 38, 51, 36, "props"], [35, 43, 51, 48], [35, 45, 51, 62], [36, 4, 52, 2], [36, 8, 52, 2, "_React$useState"], [36, 23, 52, 2], [36, 26, 52, 36, "React"], [36, 31, 52, 41], [36, 32, 52, 42, "useState"], [36, 40, 52, 50], [36, 41, 52, 51], [36, 45, 52, 55], [36, 46, 52, 56], [37, 6, 52, 56, "_React$useState2"], [37, 22, 52, 56], [37, 29, 52, 56, "_slicedToArray2"], [37, 44, 52, 56], [37, 45, 52, 56, "default"], [37, 52, 52, 56], [37, 54, 52, 56, "_React$useState"], [37, 69, 52, 56], [38, 6, 52, 9, "collapsed"], [38, 15, 52, 18], [38, 18, 52, 18, "_React$useState2"], [38, 34, 52, 18], [39, 6, 52, 20, "setCollapsed"], [39, 18, 52, 32], [39, 21, 52, 32, "_React$useState2"], [39, 37, 52, 32], [40, 4, 53, 2], [40, 8, 54, 4, "props"], [40, 13, 54, 9], [40, 14, 54, 10, "log"], [40, 17, 54, 13], [40, 18, 54, 14, "getAvailableComponentStack"], [40, 44, 54, 40], [40, 45, 54, 41], [40, 46, 54, 42], [40, 50, 54, 46], [40, 54, 54, 50], [40, 58, 55, 4, "props"], [40, 63, 55, 9], [40, 64, 55, 10, "log"], [40, 67, 55, 13], [40, 68, 55, 14, "getAvailableComponentStack"], [40, 94, 55, 40], [40, 95, 55, 41], [40, 96, 55, 42], [40, 97, 55, 43, "length"], [40, 103, 55, 49], [40, 106, 55, 52], [40, 107, 55, 53], [40, 109, 56, 4], [41, 6, 57, 4], [41, 13, 57, 11], [41, 17, 57, 15], [42, 4, 58, 2], [43, 4, 60, 2], [43, 13, 60, 11, "getStackList"], [43, 25, 60, 23, "getStackList"], [43, 26, 60, 23], [43, 28, 60, 26], [44, 6, 61, 4], [44, 10, 61, 8, "collapsed"], [44, 19, 61, 17], [44, 21, 61, 19], [45, 8, 62, 6], [45, 15, 62, 13, "props"], [45, 20, 62, 18], [45, 21, 62, 19, "log"], [45, 24, 62, 22], [45, 25, 62, 23, "getAvailableComponentStack"], [45, 51, 62, 49], [45, 52, 62, 50], [45, 53, 62, 51], [45, 54, 62, 52, "slice"], [45, 59, 62, 57], [45, 60, 62, 58], [45, 61, 62, 59], [45, 63, 62, 61], [45, 64, 62, 62], [45, 65, 62, 63], [46, 6, 63, 4], [46, 7, 63, 5], [46, 13, 63, 11], [47, 8, 64, 6], [47, 15, 64, 13, "props"], [47, 20, 64, 18], [47, 21, 64, 19, "log"], [47, 24, 64, 22], [47, 25, 64, 23, "getAvailableComponentStack"], [47, 51, 64, 49], [47, 52, 64, 50], [47, 53, 64, 51], [48, 6, 65, 4], [49, 4, 66, 2], [50, 4, 68, 2], [50, 13, 68, 11, "getCollapseMessage"], [50, 31, 68, 29, "getCollapseMessage"], [50, 32, 68, 29], [50, 34, 68, 32], [51, 6, 69, 4], [51, 10, 69, 8, "props"], [51, 15, 69, 13], [51, 16, 69, 14, "log"], [51, 19, 69, 17], [51, 20, 69, 18, "getAvailableComponentStack"], [51, 46, 69, 44], [51, 47, 69, 45], [51, 48, 69, 46], [51, 49, 69, 47, "length"], [51, 55, 69, 53], [51, 59, 69, 57], [51, 60, 69, 58], [51, 62, 69, 60], [52, 8, 70, 6], [53, 6, 71, 4], [54, 6, 73, 4], [54, 10, 73, 10, "count"], [54, 15, 73, 15], [54, 18, 73, 18, "props"], [54, 23, 73, 23], [54, 24, 73, 24, "log"], [54, 27, 73, 27], [54, 28, 73, 28, "getAvailableComponentStack"], [54, 54, 73, 54], [54, 55, 73, 55], [54, 56, 73, 56], [54, 57, 73, 57, "length"], [54, 63, 73, 63], [54, 66, 73, 66], [54, 67, 73, 67], [55, 6, 74, 4], [55, 10, 74, 8, "collapsed"], [55, 19, 74, 17], [55, 21, 74, 19], [56, 8, 75, 6], [56, 15, 75, 13], [56, 22, 75, 20, "count"], [56, 27, 75, 25], [56, 45, 75, 43], [57, 6, 76, 4], [57, 7, 76, 5], [57, 13, 76, 11], [58, 8, 77, 6], [58, 15, 77, 13], [58, 27, 77, 25, "count"], [58, 32, 77, 30], [58, 45, 77, 43], [59, 6, 78, 4], [60, 4, 79, 2], [61, 4, 81, 2], [61, 11, 82, 4], [61, 15, 82, 4, "_jsxRuntime"], [61, 26, 82, 4], [61, 27, 82, 4, "jsxs"], [61, 31, 82, 4], [61, 33, 82, 5, "_LogBoxInspectorSection"], [61, 56, 82, 5], [61, 57, 82, 5, "default"], [61, 64, 82, 27], [62, 6, 82, 28, "heading"], [62, 13, 82, 35], [62, 15, 82, 36], [62, 32, 82, 53], [63, 6, 82, 53, "children"], [63, 14, 82, 53], [63, 17, 83, 7, "getStackList"], [63, 29, 83, 19], [63, 30, 83, 20], [63, 31, 83, 21], [63, 32, 83, 22, "map"], [63, 35, 83, 25], [63, 36, 83, 26], [63, 37, 83, 27, "frame"], [63, 42, 83, 32], [63, 44, 83, 34, "index"], [63, 49, 83, 39], [63, 54, 84, 8], [63, 58, 84, 8, "_jsxRuntime"], [63, 69, 84, 8], [63, 70, 84, 8, "jsx"], [63, 73, 84, 8], [63, 75, 84, 9, "_View"], [63, 80, 84, 9], [63, 81, 84, 9, "default"], [63, 88, 84, 13], [64, 8, 87, 10, "style"], [64, 13, 87, 15], [64, 15, 87, 17, "componentStyles"], [64, 30, 87, 32], [64, 31, 87, 33, "frameContainer"], [64, 45, 87, 48], [65, 8, 87, 48, "children"], [65, 16, 87, 48], [65, 18, 88, 10], [65, 22, 88, 10, "_jsxRuntime"], [65, 33, 88, 10], [65, 34, 88, 10, "jsxs"], [65, 38, 88, 10], [65, 40, 88, 11, "_LogBoxButton"], [65, 53, 88, 11], [65, 54, 88, 11, "default"], [65, 61, 88, 23], [66, 10, 89, 12, "backgroundColor"], [66, 25, 89, 27], [66, 27, 89, 29], [67, 12, 90, 14, "default"], [67, 19, 90, 21], [67, 21, 90, 23], [67, 34, 90, 36], [68, 12, 91, 14, "pressed"], [68, 19, 91, 21], [68, 21, 91, 23, "LogBoxStyle"], [68, 32, 91, 34], [68, 33, 91, 35, "getBackgroundColor"], [68, 51, 91, 53], [68, 52, 91, 54], [68, 53, 91, 55], [69, 10, 92, 12], [69, 11, 92, 14], [70, 10, 93, 12, "onPress"], [70, 17, 93, 19], [70, 19, 97, 14, "frame"], [70, 24, 97, 19], [70, 25, 97, 20, "fileName"], [70, 33, 97, 28], [70, 34, 97, 29, "startsWith"], [70, 44, 97, 39], [70, 45, 97, 40], [70, 48, 97, 43], [70, 49, 97, 44], [70, 52, 98, 18], [70, 58, 99, 20], [70, 62, 99, 20, "openFileInEditor"], [70, 87, 99, 36], [70, 89, 99, 37, "frame"], [70, 94, 99, 42], [70, 95, 99, 43, "fileName"], [70, 103, 99, 51], [70, 105, 99, 53, "frame"], [70, 110, 99, 58], [70, 111, 99, 59, "location"], [70, 119, 99, 67], [70, 121, 99, 69, "row"], [70, 124, 99, 72], [70, 128, 99, 76], [70, 129, 99, 77], [70, 130, 99, 78], [70, 133, 100, 18], [70, 137, 101, 13], [71, 10, 102, 12, "style"], [71, 15, 102, 17], [71, 17, 102, 19, "componentStyles"], [71, 32, 102, 34], [71, 33, 102, 35, "frame"], [71, 38, 102, 41], [72, 10, 102, 41, "children"], [72, 18, 102, 41], [72, 21, 103, 12], [72, 25, 103, 12, "_jsxRuntime"], [72, 36, 103, 12], [72, 37, 103, 12, "jsx"], [72, 40, 103, 12], [72, 42, 103, 13, "_View"], [72, 47, 103, 13], [72, 48, 103, 13, "default"], [72, 55, 103, 17], [73, 12, 103, 18, "style"], [73, 17, 103, 23], [73, 19, 103, 25, "componentStyles"], [73, 34, 103, 40], [73, 35, 103, 41, "component"], [73, 44, 103, 51], [74, 12, 103, 51, "children"], [74, 20, 103, 51], [74, 22, 104, 14], [74, 26, 104, 14, "_jsxRuntime"], [74, 37, 104, 14], [74, 38, 104, 14, "jsxs"], [74, 42, 104, 14], [74, 44, 104, 15, "_Text"], [74, 49, 104, 15], [74, 50, 104, 15, "default"], [74, 57, 104, 19], [75, 14, 105, 16, "id"], [75, 16, 105, 18], [75, 18, 105, 19], [75, 53, 105, 54], [76, 14, 106, 16, "style"], [76, 19, 106, 21], [76, 21, 106, 23, "componentStyles"], [76, 36, 106, 38], [76, 37, 106, 39, "frameName"], [76, 46, 106, 49], [77, 14, 106, 49, "children"], [77, 22, 106, 49], [77, 25, 107, 16], [77, 29, 107, 16, "_jsxRuntime"], [77, 40, 107, 16], [77, 41, 107, 16, "jsx"], [77, 44, 107, 16], [77, 46, 107, 17, "_Text"], [77, 51, 107, 17], [77, 52, 107, 17, "default"], [77, 59, 107, 21], [78, 16, 107, 22, "style"], [78, 21, 107, 27], [78, 23, 107, 29, "componentStyles"], [78, 38, 107, 44], [78, 39, 107, 45, "bracket"], [78, 46, 107, 53], [79, 16, 107, 53, "children"], [79, 24, 107, 53], [79, 26, 107, 55], [80, 14, 107, 58], [80, 15, 107, 65], [80, 16, 107, 66], [80, 18, 108, 17, "frame"], [80, 23, 108, 22], [80, 24, 108, 23, "content"], [80, 31, 108, 30], [80, 33, 109, 16], [80, 37, 109, 16, "_jsxRuntime"], [80, 48, 109, 16], [80, 49, 109, 16, "jsx"], [80, 52, 109, 16], [80, 54, 109, 17, "_Text"], [80, 59, 109, 17], [80, 60, 109, 17, "default"], [80, 67, 109, 21], [81, 16, 109, 22, "style"], [81, 21, 109, 27], [81, 23, 109, 29, "componentStyles"], [81, 38, 109, 44], [81, 39, 109, 45, "bracket"], [81, 46, 109, 53], [82, 16, 109, 53, "children"], [82, 24, 109, 53], [82, 26, 109, 55], [83, 14, 109, 60], [83, 15, 109, 67], [83, 16, 109, 68], [84, 12, 109, 68], [84, 13, 110, 20], [85, 10, 110, 21], [85, 11, 111, 18], [85, 12, 111, 19], [85, 14, 112, 12], [85, 18, 112, 12, "_jsxRuntime"], [85, 29, 112, 12], [85, 30, 112, 12, "jsxs"], [85, 34, 112, 12], [85, 36, 112, 13, "_Text"], [85, 41, 112, 13], [85, 42, 112, 13, "default"], [85, 49, 112, 17], [86, 12, 112, 18, "style"], [86, 17, 112, 23], [86, 19, 112, 25, "componentStyles"], [86, 34, 112, 40], [86, 35, 112, 41, "frameLocation"], [86, 48, 112, 55], [87, 12, 112, 55, "children"], [87, 20, 112, 55], [87, 23, 113, 15, "getPrettyFileName"], [87, 40, 113, 32], [87, 41, 113, 33, "frame"], [87, 46, 113, 38], [87, 47, 113, 39, "fileName"], [87, 55, 113, 47], [87, 56, 113, 48], [87, 58, 114, 15, "frame"], [87, 63, 114, 20], [87, 64, 114, 21, "location"], [87, 72, 114, 29], [87, 75, 114, 32], [87, 79, 114, 36, "frame"], [87, 84, 114, 41], [87, 85, 114, 42, "location"], [87, 93, 114, 50], [87, 94, 114, 51, "row"], [87, 97, 114, 54], [87, 99, 114, 56], [87, 102, 114, 59], [87, 104, 114, 61], [88, 10, 114, 61], [88, 11, 115, 18], [88, 12, 115, 19], [89, 8, 115, 19], [89, 9, 116, 24], [90, 6, 116, 25], [90, 9, 86, 15, "index"], [90, 14, 117, 14], [90, 15, 118, 7], [90, 16, 118, 8], [90, 18, 119, 6], [90, 22, 119, 6, "_jsxRuntime"], [90, 33, 119, 6], [90, 34, 119, 6, "jsx"], [90, 37, 119, 6], [90, 39, 119, 7, "_View"], [90, 44, 119, 7], [90, 45, 119, 7, "default"], [90, 52, 119, 11], [91, 8, 119, 12, "style"], [91, 13, 119, 17], [91, 15, 119, 19, "componentStyles"], [91, 30, 119, 34], [91, 31, 119, 35, "collapseContainer"], [91, 48, 119, 53], [92, 8, 119, 53, "children"], [92, 16, 119, 53], [92, 18, 120, 8], [92, 22, 120, 8, "_jsxRuntime"], [92, 33, 120, 8], [92, 34, 120, 8, "jsx"], [92, 37, 120, 8], [92, 39, 120, 9, "_LogBoxButton"], [92, 52, 120, 9], [92, 53, 120, 9, "default"], [92, 60, 120, 21], [93, 10, 121, 10, "backgroundColor"], [93, 25, 121, 25], [93, 27, 121, 27], [94, 12, 122, 12, "default"], [94, 19, 122, 19], [94, 21, 122, 21], [94, 34, 122, 34], [95, 12, 123, 12, "pressed"], [95, 19, 123, 19], [95, 21, 123, 21, "LogBoxStyle"], [95, 32, 123, 32], [95, 33, 123, 33, "getBackgroundColor"], [95, 51, 123, 51], [95, 52, 123, 52], [95, 53, 123, 53], [96, 10, 124, 10], [96, 11, 124, 12], [97, 10, 125, 10, "onPress"], [97, 17, 125, 17], [97, 19, 125, 19, "onPress"], [97, 20, 125, 19], [97, 25, 125, 25, "setCollapsed"], [97, 37, 125, 37], [97, 38, 125, 38], [97, 39, 125, 39, "collapsed"], [97, 48, 125, 48], [97, 49, 125, 50], [98, 10, 126, 10, "style"], [98, 15, 126, 15], [98, 17, 126, 17, "componentStyles"], [98, 32, 126, 32], [98, 33, 126, 33, "collapseButton"], [98, 47, 126, 48], [99, 10, 126, 48, "children"], [99, 18, 126, 48], [99, 20, 127, 10], [99, 24, 127, 10, "_jsxRuntime"], [99, 35, 127, 10], [99, 36, 127, 10, "jsx"], [99, 39, 127, 10], [99, 41, 127, 11, "_Text"], [99, 46, 127, 11], [99, 47, 127, 11, "default"], [99, 54, 127, 15], [100, 12, 127, 16, "style"], [100, 17, 127, 21], [100, 19, 127, 23, "componentStyles"], [100, 34, 127, 38], [100, 35, 127, 39, "collapse"], [100, 43, 127, 48], [101, 12, 127, 48, "children"], [101, 20, 127, 48], [101, 22, 127, 50, "getCollapseMessage"], [101, 40, 127, 68], [101, 41, 127, 69], [102, 10, 127, 70], [102, 11, 127, 77], [103, 8, 127, 78], [103, 9, 128, 22], [104, 6, 128, 23], [104, 7, 129, 12], [104, 8, 129, 13], [105, 4, 129, 13], [105, 5, 130, 28], [105, 6, 130, 29], [106, 2, 132, 0], [107, 2, 134, 0], [107, 6, 134, 6, "componentStyles"], [107, 21, 134, 21], [107, 24, 134, 24, "StyleSheet"], [107, 43, 134, 34], [107, 44, 134, 35, "create"], [107, 50, 134, 41], [107, 51, 134, 42], [108, 4, 135, 2, "collapseContainer"], [108, 21, 135, 19], [108, 23, 135, 21], [109, 6, 136, 4, "marginLeft"], [109, 16, 136, 14], [109, 18, 136, 16], [109, 20, 136, 18], [110, 6, 137, 4, "flexDirection"], [110, 19, 137, 17], [110, 21, 137, 19], [111, 4, 138, 2], [111, 5, 138, 3], [112, 4, 139, 2, "collapseButton"], [112, 18, 139, 16], [112, 20, 139, 18], [113, 6, 140, 4, "borderRadius"], [113, 18, 140, 16], [113, 20, 140, 18], [114, 4, 141, 2], [114, 5, 141, 3], [115, 4, 142, 2, "collapse"], [115, 12, 142, 10], [115, 14, 142, 12], [116, 6, 143, 4, "color"], [116, 11, 143, 9], [116, 13, 143, 11, "LogBoxStyle"], [116, 24, 143, 22], [116, 25, 143, 23, "getTextColor"], [116, 37, 143, 35], [116, 38, 143, 36], [116, 41, 143, 39], [116, 42, 143, 40], [117, 6, 144, 4, "fontSize"], [117, 14, 144, 12], [117, 16, 144, 14], [117, 18, 144, 16], [118, 6, 145, 4, "fontWeight"], [118, 16, 145, 14], [118, 18, 145, 16], [118, 23, 145, 21], [119, 6, 146, 4, "lineHeight"], [119, 16, 146, 14], [119, 18, 146, 16], [119, 20, 146, 18], [120, 6, 147, 4, "marginTop"], [120, 15, 147, 13], [120, 17, 147, 15], [120, 18, 147, 16], [121, 6, 148, 4, "paddingVertical"], [121, 21, 148, 19], [121, 23, 148, 21], [121, 24, 148, 22], [122, 6, 149, 4, "paddingHorizontal"], [122, 23, 149, 21], [122, 25, 149, 23], [123, 4, 150, 2], [123, 5, 150, 3], [124, 4, 151, 2, "frameContainer"], [124, 18, 151, 16], [124, 20, 151, 18], [125, 6, 152, 4, "flexDirection"], [125, 19, 152, 17], [125, 21, 152, 19], [125, 26, 152, 24], [126, 6, 153, 4, "paddingHorizontal"], [126, 23, 153, 21], [126, 25, 153, 23], [127, 4, 154, 2], [127, 5, 154, 3], [128, 4, 155, 2, "frame"], [128, 9, 155, 7], [128, 11, 155, 9], [129, 6, 156, 4, "flex"], [129, 10, 156, 8], [129, 12, 156, 10], [129, 13, 156, 11], [130, 6, 157, 4, "paddingVertical"], [130, 21, 157, 19], [130, 23, 157, 21], [130, 24, 157, 22], [131, 6, 158, 4, "paddingHorizontal"], [131, 23, 158, 21], [131, 25, 158, 23], [131, 27, 158, 25], [132, 6, 159, 4, "borderRadius"], [132, 18, 159, 16], [132, 20, 159, 18], [133, 4, 160, 2], [133, 5, 160, 3], [134, 4, 161, 2, "component"], [134, 13, 161, 11], [134, 15, 161, 13], [135, 6, 162, 4, "flexDirection"], [135, 19, 162, 17], [135, 21, 162, 19], [135, 26, 162, 24], [136, 6, 163, 4, "paddingRight"], [136, 18, 163, 16], [136, 20, 163, 18], [137, 4, 164, 2], [137, 5, 164, 3], [138, 4, 165, 2, "frameName"], [138, 13, 165, 11], [138, 15, 165, 13], [139, 6, 166, 4, "fontFamily"], [139, 16, 166, 14], [139, 18, 166, 16, "Platform"], [139, 35, 166, 24], [139, 36, 166, 25, "select"], [139, 42, 166, 31], [139, 43, 166, 32], [140, 8, 166, 33, "android"], [140, 15, 166, 40], [140, 17, 166, 42], [140, 28, 166, 53], [141, 8, 166, 55, "ios"], [141, 11, 166, 58], [141, 13, 166, 60], [142, 6, 166, 67], [142, 7, 166, 68], [142, 8, 166, 69], [143, 6, 167, 4, "color"], [143, 11, 167, 9], [143, 13, 167, 11, "LogBoxStyle"], [143, 24, 167, 22], [143, 25, 167, 23, "getTextColor"], [143, 37, 167, 35], [143, 38, 167, 36], [143, 39, 167, 37], [143, 40, 167, 38], [144, 6, 168, 4, "fontSize"], [144, 14, 168, 12], [144, 16, 168, 14], [144, 18, 168, 16], [145, 6, 169, 4, "includeFontPadding"], [145, 24, 169, 22], [145, 26, 169, 24], [145, 31, 169, 29], [146, 6, 170, 4, "lineHeight"], [146, 16, 170, 14], [146, 18, 170, 16], [147, 4, 171, 2], [147, 5, 171, 3], [148, 4, 172, 2, "bracket"], [148, 11, 172, 9], [148, 13, 172, 11], [149, 6, 173, 4, "fontFamily"], [149, 16, 173, 14], [149, 18, 173, 16, "Platform"], [149, 35, 173, 24], [149, 36, 173, 25, "select"], [149, 42, 173, 31], [149, 43, 173, 32], [150, 8, 173, 33, "android"], [150, 15, 173, 40], [150, 17, 173, 42], [150, 28, 173, 53], [151, 8, 173, 55, "ios"], [151, 11, 173, 58], [151, 13, 173, 60], [152, 6, 173, 67], [152, 7, 173, 68], [152, 8, 173, 69], [153, 6, 174, 4, "color"], [153, 11, 174, 9], [153, 13, 174, 11, "LogBoxStyle"], [153, 24, 174, 22], [153, 25, 174, 23, "getTextColor"], [153, 37, 174, 35], [153, 38, 174, 36], [153, 41, 174, 39], [153, 42, 174, 40], [154, 6, 175, 4, "fontSize"], [154, 14, 175, 12], [154, 16, 175, 14], [154, 18, 175, 16], [155, 6, 176, 4, "fontWeight"], [155, 16, 176, 14], [155, 18, 176, 16], [155, 23, 176, 21], [156, 6, 177, 4, "includeFontPadding"], [156, 24, 177, 22], [156, 26, 177, 24], [156, 31, 177, 29], [157, 6, 178, 4, "lineHeight"], [157, 16, 178, 14], [157, 18, 178, 16], [158, 4, 179, 2], [158, 5, 179, 3], [159, 4, 180, 2, "frameLocation"], [159, 17, 180, 15], [159, 19, 180, 17], [160, 6, 181, 4, "color"], [160, 11, 181, 9], [160, 13, 181, 11, "LogBoxStyle"], [160, 24, 181, 22], [160, 25, 181, 23, "getTextColor"], [160, 37, 181, 35], [160, 38, 181, 36], [160, 41, 181, 39], [160, 42, 181, 40], [161, 6, 182, 4, "fontSize"], [161, 14, 182, 12], [161, 16, 182, 14], [161, 18, 182, 16], [162, 6, 183, 4, "fontWeight"], [162, 16, 183, 14], [162, 18, 183, 16], [162, 23, 183, 21], [163, 6, 184, 4, "includeFontPadding"], [163, 24, 184, 22], [163, 26, 184, 24], [163, 31, 184, 29], [164, 6, 185, 4, "lineHeight"], [164, 16, 185, 14], [164, 18, 185, 16], [164, 20, 185, 18], [165, 6, 186, 4, "paddingLeft"], [165, 17, 186, 15], [165, 19, 186, 17], [166, 4, 187, 2], [167, 2, 188, 0], [167, 3, 188, 1], [167, 4, 188, 2], [168, 2, 188, 3], [168, 6, 188, 3, "_default"], [168, 14, 188, 3], [168, 17, 188, 3, "exports"], [168, 24, 188, 3], [168, 25, 188, 3, "default"], [168, 32, 188, 3], [168, 35, 190, 15, "LogBoxInspectorReactFrames"], [168, 61, 190, 41], [169, 0, 190, 41], [169, 3]], "functionMap": {"names": ["<global>", "getPrettyFileName", "LogBoxInspectorReactFrames", "getStackList", "getCollapseMessage", "getStackList.map$argument_0", "<anonymous>", "LogBoxButton.props.onPress"], "mappings": "AAA;AC6B;CDoB;AEC;ECS;GDM;EEE;GFW;0BGI;kBCe;8EDC;OHmB;mBKO,8BL;CFO"}}, "type": "js/module"}]}