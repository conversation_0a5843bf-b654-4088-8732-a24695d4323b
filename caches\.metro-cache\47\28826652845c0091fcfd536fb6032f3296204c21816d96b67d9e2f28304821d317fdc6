{"dependencies": [{"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 49, "index": 260}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.hoverGestureHandlerProps = exports.HoverGesture = exports.HoverEffect = void 0;\n  var _gesture = require(_dependencyMap[0], \"./gesture\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  let HoverEffect = exports.HoverEffect = void 0;\n  (function (HoverEffect) {\n    HoverEffect[HoverEffect[\"NONE\"] = 0] = \"NONE\";\n    HoverEffect[HoverEffect[\"LIFT\"] = 1] = \"LIFT\";\n    HoverEffect[HoverEffect[\"HIGHLIGHT\"] = 2] = \"HIGHLIGHT\";\n  })(HoverEffect || (exports.HoverEffect = HoverEffect = {}));\n  const hoverGestureHandlerProps = exports.hoverGestureHandlerProps = ['hoverEffect'];\n  const _worklet_15895574323364_init_data = {\n    code: \"function changeEventCalculator_reactNativeGestureHandler_hoverGestureJs1(current,previous){let changePayload;if(previous===undefined){changePayload={changeX:current.x,changeY:current.y};}else{changePayload={changeX:current.x-previous.x,changeY:current.y-previous.y};}return{...current,...changePayload};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-gesture-handler/lib/module/handlers/gestures/hoverGesture.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"changeEventCalculator_reactNativeGestureHandler_hoverGestureJs1\\\",\\\"current\\\",\\\"previous\\\",\\\"changePayload\\\",\\\"undefined\\\",\\\"changeX\\\",\\\"x\\\",\\\"changeY\\\",\\\"y\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-gesture-handler/lib/module/handlers/gestures/hoverGesture.js\\\"],\\\"mappings\\\":\\\"AAaA,SAAAA,+DAAkDA,CAAAC,OAAA,CAAAC,QAAA,EAGhD,GAAI,CAAAC,aAAa,CAEjB,GAAID,QAAQ,GAAKE,SAAS,CAAE,CAC1BD,aAAa,CAAG,CACdE,OAAO,CAAEJ,OAAO,CAACK,CAAC,CAClBC,OAAO,CAAEN,OAAO,CAACO,CACnB,CAAC,CACH,CAAC,IAAM,CACLL,aAAa,CAAG,CACdE,OAAO,CAAEJ,OAAO,CAACK,CAAC,CAAGJ,QAAQ,CAACI,CAAC,CAC/BC,OAAO,CAAEN,OAAO,CAACO,CAAC,CAAGN,QAAQ,CAACM,CAChC,CAAC,CACH,CAEA,MAAO,CAAE,GAAGP,OAAO,CACjB,GAAGE,aACL,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const changeEventCalculator = function () {\n    const _e = [new global.Error(), 1, -27];\n    const changeEventCalculator = function (current, previous) {\n      let changePayload;\n      if (previous === undefined) {\n        changePayload = {\n          changeX: current.x,\n          changeY: current.y\n        };\n      } else {\n        changePayload = {\n          changeX: current.x - previous.x,\n          changeY: current.y - previous.y\n        };\n      }\n      return {\n        ...current,\n        ...changePayload\n      };\n    };\n    changeEventCalculator.__closure = {};\n    changeEventCalculator.__workletHash = 15895574323364;\n    changeEventCalculator.__initData = _worklet_15895574323364_init_data;\n    changeEventCalculator.__stackDetails = _e;\n    return changeEventCalculator;\n  }();\n  class HoverGesture extends _gesture.ContinousBaseGesture {\n    constructor() {\n      super();\n      _defineProperty(this, \"config\", {});\n      this.handlerName = 'HoverGestureHandler';\n    }\n    /**\n     * #### iOS only\n     * Sets the visual hover effect.\n     */\n\n    effect(effect) {\n      this.config.hoverEffect = effect;\n      return this;\n    }\n    onChange(callback) {\n      // @ts-ignore TS being overprotective, HoverGestureHandlerEventPayload is Record\n      this.handlers.changeEventCalculator = changeEventCalculator;\n      return super.onChange(callback);\n    }\n  }\n  exports.HoverGesture = HoverGesture;\n});", "lineCount": 81, "map": [[6, 2, 3, 0], [6, 6, 3, 0, "_gesture"], [6, 14, 3, 0], [6, 17, 3, 0, "require"], [6, 24, 3, 0], [6, 25, 3, 0, "_dependencyMap"], [6, 39, 3, 0], [7, 2, 1, 0], [7, 11, 1, 9, "_defineProperty"], [7, 26, 1, 24, "_defineProperty"], [7, 27, 1, 25, "obj"], [7, 30, 1, 28], [7, 32, 1, 30, "key"], [7, 35, 1, 33], [7, 37, 1, 35, "value"], [7, 42, 1, 40], [7, 44, 1, 42], [8, 4, 1, 44], [8, 8, 1, 48, "key"], [8, 11, 1, 51], [8, 15, 1, 55, "obj"], [8, 18, 1, 58], [8, 20, 1, 60], [9, 6, 1, 62, "Object"], [9, 12, 1, 68], [9, 13, 1, 69, "defineProperty"], [9, 27, 1, 83], [9, 28, 1, 84, "obj"], [9, 31, 1, 87], [9, 33, 1, 89, "key"], [9, 36, 1, 92], [9, 38, 1, 94], [10, 8, 1, 96, "value"], [10, 13, 1, 101], [10, 15, 1, 103, "value"], [10, 20, 1, 108], [11, 8, 1, 110, "enumerable"], [11, 18, 1, 120], [11, 20, 1, 122], [11, 24, 1, 126], [12, 8, 1, 128, "configurable"], [12, 20, 1, 140], [12, 22, 1, 142], [12, 26, 1, 146], [13, 8, 1, 148, "writable"], [13, 16, 1, 156], [13, 18, 1, 158], [14, 6, 1, 163], [14, 7, 1, 164], [14, 8, 1, 165], [15, 4, 1, 167], [15, 5, 1, 168], [15, 11, 1, 174], [16, 6, 1, 176, "obj"], [16, 9, 1, 179], [16, 10, 1, 180, "key"], [16, 13, 1, 183], [16, 14, 1, 184], [16, 17, 1, 187, "value"], [16, 22, 1, 192], [17, 4, 1, 194], [18, 4, 1, 196], [18, 11, 1, 203, "obj"], [18, 14, 1, 206], [19, 2, 1, 208], [20, 2, 4, 7], [20, 6, 4, 11, "HoverEffect"], [20, 17, 4, 22], [20, 20, 4, 22, "exports"], [20, 27, 4, 22], [20, 28, 4, 22, "HoverEffect"], [20, 39, 4, 22], [21, 2, 6, 0], [21, 3, 6, 1], [21, 13, 6, 11, "HoverEffect"], [21, 24, 6, 22], [21, 26, 6, 24], [22, 4, 7, 2, "HoverEffect"], [22, 15, 7, 13], [22, 16, 7, 14, "HoverEffect"], [22, 27, 7, 25], [22, 28, 7, 26], [22, 34, 7, 32], [22, 35, 7, 33], [22, 38, 7, 36], [22, 39, 7, 37], [22, 40, 7, 38], [22, 43, 7, 41], [22, 49, 7, 47], [23, 4, 8, 2, "HoverEffect"], [23, 15, 8, 13], [23, 16, 8, 14, "HoverEffect"], [23, 27, 8, 25], [23, 28, 8, 26], [23, 34, 8, 32], [23, 35, 8, 33], [23, 38, 8, 36], [23, 39, 8, 37], [23, 40, 8, 38], [23, 43, 8, 41], [23, 49, 8, 47], [24, 4, 9, 2, "HoverEffect"], [24, 15, 9, 13], [24, 16, 9, 14, "HoverEffect"], [24, 27, 9, 25], [24, 28, 9, 26], [24, 39, 9, 37], [24, 40, 9, 38], [24, 43, 9, 41], [24, 44, 9, 42], [24, 45, 9, 43], [24, 48, 9, 46], [24, 59, 9, 57], [25, 2, 10, 0], [25, 3, 10, 1], [25, 5, 10, 3, "HoverEffect"], [25, 16, 10, 14], [25, 21, 10, 14, "exports"], [25, 28, 10, 14], [25, 29, 10, 14, "HoverEffect"], [25, 40, 10, 14], [25, 43, 10, 19, "HoverEffect"], [25, 54, 10, 30], [25, 57, 10, 33], [25, 58, 10, 34], [25, 59, 10, 35], [25, 60, 10, 36], [25, 61, 10, 37], [26, 2, 12, 7], [26, 8, 12, 13, "hoverGestureHandlerProps"], [26, 32, 12, 37], [26, 35, 12, 37, "exports"], [26, 42, 12, 37], [26, 43, 12, 37, "hoverGestureHandlerProps"], [26, 67, 12, 37], [26, 70, 12, 40], [26, 71, 12, 41], [26, 84, 12, 54], [26, 85, 12, 55], [27, 2, 12, 56], [27, 8, 12, 56, "_worklet_15895574323364_init_data"], [27, 41, 12, 56], [28, 4, 12, 56, "code"], [28, 8, 12, 56], [29, 4, 12, 56, "location"], [29, 12, 12, 56], [30, 4, 12, 56, "sourceMap"], [30, 13, 12, 56], [31, 4, 12, 56, "version"], [31, 11, 12, 56], [32, 2, 12, 56], [33, 2, 12, 56], [33, 8, 12, 56, "changeEventCalculator"], [33, 29, 12, 56], [33, 32, 14, 0], [34, 4, 14, 0], [34, 10, 14, 0, "_e"], [34, 12, 14, 0], [34, 20, 14, 0, "global"], [34, 26, 14, 0], [34, 27, 14, 0, "Error"], [34, 32, 14, 0], [35, 4, 14, 0], [35, 10, 14, 0, "changeEventCalculator"], [35, 31, 14, 0], [35, 43, 14, 0, "changeEventCalculator"], [35, 44, 14, 31, "current"], [35, 51, 14, 38], [35, 53, 14, 40, "previous"], [35, 61, 14, 48], [35, 63, 14, 50], [36, 6, 17, 2], [36, 10, 17, 6, "changePayload"], [36, 23, 17, 19], [37, 6, 19, 2], [37, 10, 19, 6, "previous"], [37, 18, 19, 14], [37, 23, 19, 19, "undefined"], [37, 32, 19, 28], [37, 34, 19, 30], [38, 8, 20, 4, "changePayload"], [38, 21, 20, 17], [38, 24, 20, 20], [39, 10, 21, 6, "changeX"], [39, 17, 21, 13], [39, 19, 21, 15, "current"], [39, 26, 21, 22], [39, 27, 21, 23, "x"], [39, 28, 21, 24], [40, 10, 22, 6, "changeY"], [40, 17, 22, 13], [40, 19, 22, 15, "current"], [40, 26, 22, 22], [40, 27, 22, 23, "y"], [41, 8, 23, 4], [41, 9, 23, 5], [42, 6, 24, 2], [42, 7, 24, 3], [42, 13, 24, 9], [43, 8, 25, 4, "changePayload"], [43, 21, 25, 17], [43, 24, 25, 20], [44, 10, 26, 6, "changeX"], [44, 17, 26, 13], [44, 19, 26, 15, "current"], [44, 26, 26, 22], [44, 27, 26, 23, "x"], [44, 28, 26, 24], [44, 31, 26, 27, "previous"], [44, 39, 26, 35], [44, 40, 26, 36, "x"], [44, 41, 26, 37], [45, 10, 27, 6, "changeY"], [45, 17, 27, 13], [45, 19, 27, 15, "current"], [45, 26, 27, 22], [45, 27, 27, 23, "y"], [45, 28, 27, 24], [45, 31, 27, 27, "previous"], [45, 39, 27, 35], [45, 40, 27, 36, "y"], [46, 8, 28, 4], [46, 9, 28, 5], [47, 6, 29, 2], [48, 6, 31, 2], [48, 13, 31, 9], [49, 8, 31, 11], [49, 11, 31, 14, "current"], [49, 18, 31, 21], [50, 8, 32, 4], [50, 11, 32, 7, "changePayload"], [51, 6, 33, 2], [51, 7, 33, 3], [52, 4, 34, 0], [52, 5, 34, 1], [53, 4, 34, 1, "changeEventCalculator"], [53, 25, 34, 1], [53, 26, 34, 1, "__closure"], [53, 35, 34, 1], [54, 4, 34, 1, "changeEventCalculator"], [54, 25, 34, 1], [54, 26, 34, 1, "__workletHash"], [54, 39, 34, 1], [55, 4, 34, 1, "changeEventCalculator"], [55, 25, 34, 1], [55, 26, 34, 1, "__initData"], [55, 36, 34, 1], [55, 39, 34, 1, "_worklet_15895574323364_init_data"], [55, 72, 34, 1], [56, 4, 34, 1, "changeEventCalculator"], [56, 25, 34, 1], [56, 26, 34, 1, "__stackDetails"], [56, 40, 34, 1], [56, 43, 34, 1, "_e"], [56, 45, 34, 1], [57, 4, 34, 1], [57, 11, 34, 1, "changeEventCalculator"], [57, 32, 34, 1], [58, 2, 34, 1], [58, 3, 14, 0], [59, 2, 36, 7], [59, 8, 36, 13, "HoverGesture"], [59, 20, 36, 25], [59, 29, 36, 34, "ContinousBaseGesture"], [59, 58, 36, 54], [59, 59, 36, 55], [60, 4, 37, 2, "constructor"], [60, 15, 37, 13, "constructor"], [60, 16, 37, 13], [60, 18, 37, 16], [61, 6, 38, 4], [61, 11, 38, 9], [61, 12, 38, 10], [61, 13, 38, 11], [62, 6, 40, 4, "_defineProperty"], [62, 21, 40, 19], [62, 22, 40, 20], [62, 26, 40, 24], [62, 28, 40, 26], [62, 36, 40, 34], [62, 38, 40, 36], [62, 39, 40, 37], [62, 40, 40, 38], [62, 41, 40, 39], [63, 6, 42, 4], [63, 10, 42, 8], [63, 11, 42, 9, "handler<PERSON>ame"], [63, 22, 42, 20], [63, 25, 42, 23], [63, 46, 42, 44], [64, 4, 43, 2], [65, 4, 44, 2], [66, 0, 45, 0], [67, 0, 46, 0], [68, 0, 47, 0], [70, 4, 50, 2, "effect"], [70, 10, 50, 8, "effect"], [70, 11, 50, 9, "effect"], [70, 17, 50, 15], [70, 19, 50, 17], [71, 6, 51, 4], [71, 10, 51, 8], [71, 11, 51, 9, "config"], [71, 17, 51, 15], [71, 18, 51, 16, "hoverEffect"], [71, 29, 51, 27], [71, 32, 51, 30, "effect"], [71, 38, 51, 36], [72, 6, 52, 4], [72, 13, 52, 11], [72, 17, 52, 15], [73, 4, 53, 2], [74, 4, 55, 2, "onChange"], [74, 12, 55, 10, "onChange"], [74, 13, 55, 11, "callback"], [74, 21, 55, 19], [74, 23, 55, 21], [75, 6, 56, 4], [76, 6, 57, 4], [76, 10, 57, 8], [76, 11, 57, 9, "handlers"], [76, 19, 57, 17], [76, 20, 57, 18, "changeEventCalculator"], [76, 41, 57, 39], [76, 44, 57, 42, "changeEventCalculator"], [76, 65, 57, 63], [77, 6, 58, 4], [77, 13, 58, 11], [77, 18, 58, 16], [77, 19, 58, 17, "onChange"], [77, 27, 58, 25], [77, 28, 58, 26, "callback"], [77, 36, 58, 34], [77, 37, 58, 35], [78, 4, 59, 2], [79, 2, 61, 0], [80, 2, 61, 1, "exports"], [80, 9, 61, 1], [80, 10, 61, 1, "HoverGesture"], [80, 22, 61, 1], [80, 25, 61, 1, "HoverGesture"], [80, 37, 61, 1], [81, 0, 61, 1], [81, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "<anonymous>", "changeEventCalculator", "HoverGesture", "HoverGesture#constructor", "HoverGesture#effect", "HoverGesture#onChange"], "mappings": "AAA,iNC;CCK;CDI;AEI;CFoB;OGE;ECC;GDM;EEO;GFG;EGE;GHI;CHE"}}, "type": "js/module"}]}