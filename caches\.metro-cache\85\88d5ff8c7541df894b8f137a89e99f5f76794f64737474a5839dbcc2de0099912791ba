{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = function walk(nodes, cb, bubble) {\n    var i, max, node, result;\n    for (i = 0, max = nodes.length; i < max; i += 1) {\n      node = nodes[i];\n      if (!bubble) {\n        result = cb(node, i, nodes);\n      }\n      if (result !== false && node.type === \"function\" && Array.isArray(node.nodes)) {\n        walk(node.nodes, cb, bubble);\n      }\n      if (bubble) {\n        cb(node, i, nodes);\n      }\n    }\n  };\n});", "lineCount": 17, "map": [[2, 2, 1, 0, "module"], [2, 8, 1, 6], [2, 9, 1, 7, "exports"], [2, 16, 1, 14], [2, 19, 1, 17], [2, 28, 1, 26, "walk"], [2, 32, 1, 30, "walk"], [2, 33, 1, 31, "nodes"], [2, 38, 1, 36], [2, 40, 1, 38, "cb"], [2, 42, 1, 40], [2, 44, 1, 42, "bubble"], [2, 50, 1, 48], [2, 52, 1, 50], [3, 4, 2, 2], [3, 8, 2, 6, "i"], [3, 9, 2, 7], [3, 11, 2, 9, "max"], [3, 14, 2, 12], [3, 16, 2, 14, "node"], [3, 20, 2, 18], [3, 22, 2, 20, "result"], [3, 28, 2, 26], [4, 4, 4, 2], [4, 9, 4, 7, "i"], [4, 10, 4, 8], [4, 13, 4, 11], [4, 14, 4, 12], [4, 16, 4, 14, "max"], [4, 19, 4, 17], [4, 22, 4, 20, "nodes"], [4, 27, 4, 25], [4, 28, 4, 26, "length"], [4, 34, 4, 32], [4, 36, 4, 34, "i"], [4, 37, 4, 35], [4, 40, 4, 38, "max"], [4, 43, 4, 41], [4, 45, 4, 43, "i"], [4, 46, 4, 44], [4, 50, 4, 48], [4, 51, 4, 49], [4, 53, 4, 51], [5, 6, 5, 4, "node"], [5, 10, 5, 8], [5, 13, 5, 11, "nodes"], [5, 18, 5, 16], [5, 19, 5, 17, "i"], [5, 20, 5, 18], [5, 21, 5, 19], [6, 6, 6, 4], [6, 10, 6, 8], [6, 11, 6, 9, "bubble"], [6, 17, 6, 15], [6, 19, 6, 17], [7, 8, 7, 6, "result"], [7, 14, 7, 12], [7, 17, 7, 15, "cb"], [7, 19, 7, 17], [7, 20, 7, 18, "node"], [7, 24, 7, 22], [7, 26, 7, 24, "i"], [7, 27, 7, 25], [7, 29, 7, 27, "nodes"], [7, 34, 7, 32], [7, 35, 7, 33], [8, 6, 8, 4], [9, 6, 10, 4], [9, 10, 11, 6, "result"], [9, 16, 11, 12], [9, 21, 11, 17], [9, 26, 11, 22], [9, 30, 12, 6, "node"], [9, 34, 12, 10], [9, 35, 12, 11, "type"], [9, 39, 12, 15], [9, 44, 12, 20], [9, 54, 12, 30], [9, 58, 13, 6, "Array"], [9, 63, 13, 11], [9, 64, 13, 12, "isArray"], [9, 71, 13, 19], [9, 72, 13, 20, "node"], [9, 76, 13, 24], [9, 77, 13, 25, "nodes"], [9, 82, 13, 30], [9, 83, 13, 31], [9, 85, 14, 6], [10, 8, 15, 6, "walk"], [10, 12, 15, 10], [10, 13, 15, 11, "node"], [10, 17, 15, 15], [10, 18, 15, 16, "nodes"], [10, 23, 15, 21], [10, 25, 15, 23, "cb"], [10, 27, 15, 25], [10, 29, 15, 27, "bubble"], [10, 35, 15, 33], [10, 36, 15, 34], [11, 6, 16, 4], [12, 6, 18, 4], [12, 10, 18, 8, "bubble"], [12, 16, 18, 14], [12, 18, 18, 16], [13, 8, 19, 6, "cb"], [13, 10, 19, 8], [13, 11, 19, 9, "node"], [13, 15, 19, 13], [13, 17, 19, 15, "i"], [13, 18, 19, 16], [13, 20, 19, 18, "nodes"], [13, 25, 19, 23], [13, 26, 19, 24], [14, 6, 20, 4], [15, 4, 21, 2], [16, 2, 22, 0], [16, 3, 22, 1], [17, 0, 22, 2], [17, 3]], "functionMap": {"names": ["<global>", "walk"], "mappings": "AAA,iBC;CDqB"}}, "type": "js/module"}]}