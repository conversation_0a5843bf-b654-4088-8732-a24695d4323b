{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../modules/getBoundingClientRect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 181}, "end": {"line": 10, "column": 72, "index": 253}}], "key": "i4wgS+O9VZXcSNXv3Sd7drhcmAo=", "exportNames": ["*"]}}, {"name": "../../modules/setValueForStyles", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 254}, "end": {"line": 11, "column": 64, "index": 318}}], "key": "8T60R5WvgUDPslvv+qnW3zrEfIM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _getBoundingClientRect = _interopRequireDefault(require(_dependencyMap[1], \"../../modules/getBoundingClientRect\"));\n  var _setValueForStyles = _interopRequireDefault(require(_dependencyMap[2], \"../../modules/setValueForStyles\"));\n  /**\n   * Copyright (c) Nicolas <PERSON>.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var getRect = node => {\n    var height = node.offsetHeight;\n    var width = node.offsetWidth;\n    var left = node.offsetLeft;\n    var top = node.offsetTop;\n    node = node.offsetParent;\n    while (node && node.nodeType === 1 /* Node.ELEMENT_NODE */) {\n      left += node.offsetLeft + node.clientLeft - node.scrollLeft;\n      top += node.offsetTop + node.clientTop - node.scrollTop;\n      node = node.offsetParent;\n    }\n    top -= window.scrollY;\n    left -= window.scrollX;\n    return {\n      width,\n      height,\n      top,\n      left\n    };\n  };\n  var measureLayout = (node, relativeToNativeNode, callback) => {\n    var relativeNode = relativeToNativeNode || node && node.parentNode;\n    if (node && relativeNode) {\n      setTimeout(() => {\n        if (node.isConnected && relativeNode.isConnected) {\n          var relativeRect = getRect(relativeNode);\n          var _getRect = getRect(node),\n            height = _getRect.height,\n            left = _getRect.left,\n            top = _getRect.top,\n            width = _getRect.width;\n          var x = left - relativeRect.left;\n          var y = top - relativeRect.top;\n          callback(x, y, width, height, left, top);\n        }\n      }, 0);\n    }\n  };\n  var elementsToIgnore = {\n    A: true,\n    BODY: true,\n    INPUT: true,\n    SELECT: true,\n    TEXTAREA: true\n  };\n  var UIManager = {\n    blur(node) {\n      try {\n        node.blur();\n      } catch (err) {}\n    },\n    focus(node) {\n      try {\n        var name = node.nodeName;\n        // A tabIndex of -1 allows element to be programmatically focused but\n        // prevents keyboard focus. We don't want to set the tabindex value on\n        // elements that should not prevent keyboard focus.\n        if (node.getAttribute('tabIndex') == null && node.isContentEditable !== true && elementsToIgnore[name] == null) {\n          node.setAttribute('tabIndex', '-1');\n        }\n        node.focus();\n      } catch (err) {}\n    },\n    measure(node, callback) {\n      measureLayout(node, null, callback);\n    },\n    measureInWindow(node, callback) {\n      if (node) {\n        setTimeout(() => {\n          var _getBoundingClientRec = (0, _getBoundingClientRect.default)(node),\n            height = _getBoundingClientRec.height,\n            left = _getBoundingClientRec.left,\n            top = _getBoundingClientRec.top,\n            width = _getBoundingClientRec.width;\n          callback(left, top, width, height);\n        }, 0);\n      }\n    },\n    measureLayout(node, relativeToNativeNode, onFail, onSuccess) {\n      measureLayout(node, relativeToNativeNode, onSuccess);\n    },\n    updateView(node, props) {\n      for (var prop in props) {\n        if (!Object.prototype.hasOwnProperty.call(props, prop)) {\n          continue;\n        }\n        var value = props[prop];\n        switch (prop) {\n          case 'style':\n            {\n              (0, _setValueForStyles.default)(node, value);\n              break;\n            }\n          case 'class':\n          case 'className':\n            {\n              node.setAttribute('class', value);\n              break;\n            }\n          case 'text':\n          case 'value':\n            // native platforms use `text` prop to replace text input value\n            node.value = value;\n            break;\n          default:\n            node.setAttribute(prop, value);\n        }\n      }\n    },\n    configureNextLayoutAnimation(config, onAnimationDidEnd) {\n      onAnimationDidEnd();\n    },\n    // mocks\n    setLayoutAnimationEnabledExperimental() {}\n  };\n  var _default = exports.default = UIManager;\n});", "lineCount": 134, "map": [[7, 2, 10, 0], [7, 6, 10, 0, "_getBoundingClientRect"], [7, 28, 10, 0], [7, 31, 10, 0, "_interopRequireDefault"], [7, 53, 10, 0], [7, 54, 10, 0, "require"], [7, 61, 10, 0], [7, 62, 10, 0, "_dependencyMap"], [7, 76, 10, 0], [8, 2, 11, 0], [8, 6, 11, 0, "_setValueForStyles"], [8, 24, 11, 0], [8, 27, 11, 0, "_interopRequireDefault"], [8, 49, 11, 0], [8, 50, 11, 0, "require"], [8, 57, 11, 0], [8, 58, 11, 0, "_dependencyMap"], [8, 72, 11, 0], [9, 2, 1, 0], [10, 0, 2, 0], [11, 0, 3, 0], [12, 0, 4, 0], [13, 0, 5, 0], [14, 0, 6, 0], [15, 0, 7, 0], [16, 0, 8, 0], [18, 2, 12, 0], [18, 6, 12, 4, "getRect"], [18, 13, 12, 11], [18, 16, 12, 14, "node"], [18, 20, 12, 18], [18, 24, 12, 22], [19, 4, 13, 2], [19, 8, 13, 6, "height"], [19, 14, 13, 12], [19, 17, 13, 15, "node"], [19, 21, 13, 19], [19, 22, 13, 20, "offsetHeight"], [19, 34, 13, 32], [20, 4, 14, 2], [20, 8, 14, 6, "width"], [20, 13, 14, 11], [20, 16, 14, 14, "node"], [20, 20, 14, 18], [20, 21, 14, 19, "offsetWidth"], [20, 32, 14, 30], [21, 4, 15, 2], [21, 8, 15, 6, "left"], [21, 12, 15, 10], [21, 15, 15, 13, "node"], [21, 19, 15, 17], [21, 20, 15, 18, "offsetLeft"], [21, 30, 15, 28], [22, 4, 16, 2], [22, 8, 16, 6, "top"], [22, 11, 16, 9], [22, 14, 16, 12, "node"], [22, 18, 16, 16], [22, 19, 16, 17, "offsetTop"], [22, 28, 16, 26], [23, 4, 17, 2, "node"], [23, 8, 17, 6], [23, 11, 17, 9, "node"], [23, 15, 17, 13], [23, 16, 17, 14, "offsetParent"], [23, 28, 17, 26], [24, 4, 18, 2], [24, 11, 18, 9, "node"], [24, 15, 18, 13], [24, 19, 18, 17, "node"], [24, 23, 18, 21], [24, 24, 18, 22, "nodeType"], [24, 32, 18, 30], [24, 37, 18, 35], [24, 38, 18, 36], [24, 39, 18, 37], [24, 64, 18, 62], [25, 6, 19, 4, "left"], [25, 10, 19, 8], [25, 14, 19, 12, "node"], [25, 18, 19, 16], [25, 19, 19, 17, "offsetLeft"], [25, 29, 19, 27], [25, 32, 19, 30, "node"], [25, 36, 19, 34], [25, 37, 19, 35, "clientLeft"], [25, 47, 19, 45], [25, 50, 19, 48, "node"], [25, 54, 19, 52], [25, 55, 19, 53, "scrollLeft"], [25, 65, 19, 63], [26, 6, 20, 4, "top"], [26, 9, 20, 7], [26, 13, 20, 11, "node"], [26, 17, 20, 15], [26, 18, 20, 16, "offsetTop"], [26, 27, 20, 25], [26, 30, 20, 28, "node"], [26, 34, 20, 32], [26, 35, 20, 33, "clientTop"], [26, 44, 20, 42], [26, 47, 20, 45, "node"], [26, 51, 20, 49], [26, 52, 20, 50, "scrollTop"], [26, 61, 20, 59], [27, 6, 21, 4, "node"], [27, 10, 21, 8], [27, 13, 21, 11, "node"], [27, 17, 21, 15], [27, 18, 21, 16, "offsetParent"], [27, 30, 21, 28], [28, 4, 22, 2], [29, 4, 23, 2, "top"], [29, 7, 23, 5], [29, 11, 23, 9, "window"], [29, 17, 23, 15], [29, 18, 23, 16, "scrollY"], [29, 25, 23, 23], [30, 4, 24, 2, "left"], [30, 8, 24, 6], [30, 12, 24, 10, "window"], [30, 18, 24, 16], [30, 19, 24, 17, "scrollX"], [30, 26, 24, 24], [31, 4, 25, 2], [31, 11, 25, 9], [32, 6, 26, 4, "width"], [32, 11, 26, 9], [33, 6, 27, 4, "height"], [33, 12, 27, 10], [34, 6, 28, 4, "top"], [34, 9, 28, 7], [35, 6, 29, 4, "left"], [36, 4, 30, 2], [36, 5, 30, 3], [37, 2, 31, 0], [37, 3, 31, 1], [38, 2, 32, 0], [38, 6, 32, 4, "measureLayout"], [38, 19, 32, 17], [38, 22, 32, 20, "measureLayout"], [38, 23, 32, 21, "node"], [38, 27, 32, 25], [38, 29, 32, 27, "relativeToNativeNode"], [38, 49, 32, 47], [38, 51, 32, 49, "callback"], [38, 59, 32, 57], [38, 64, 32, 62], [39, 4, 33, 2], [39, 8, 33, 6, "relativeNode"], [39, 20, 33, 18], [39, 23, 33, 21, "relativeToNativeNode"], [39, 43, 33, 41], [39, 47, 33, 45, "node"], [39, 51, 33, 49], [39, 55, 33, 53, "node"], [39, 59, 33, 57], [39, 60, 33, 58, "parentNode"], [39, 70, 33, 68], [40, 4, 34, 2], [40, 8, 34, 6, "node"], [40, 12, 34, 10], [40, 16, 34, 14, "relativeNode"], [40, 28, 34, 26], [40, 30, 34, 28], [41, 6, 35, 4, "setTimeout"], [41, 16, 35, 14], [41, 17, 35, 15], [41, 23, 35, 21], [42, 8, 36, 6], [42, 12, 36, 10, "node"], [42, 16, 36, 14], [42, 17, 36, 15, "isConnected"], [42, 28, 36, 26], [42, 32, 36, 30, "relativeNode"], [42, 44, 36, 42], [42, 45, 36, 43, "isConnected"], [42, 56, 36, 54], [42, 58, 36, 56], [43, 10, 37, 8], [43, 14, 37, 12, "relativeRect"], [43, 26, 37, 24], [43, 29, 37, 27, "getRect"], [43, 36, 37, 34], [43, 37, 37, 35, "relativeNode"], [43, 49, 37, 47], [43, 50, 37, 48], [44, 10, 38, 8], [44, 14, 38, 12, "_getRect"], [44, 22, 38, 20], [44, 25, 38, 23, "getRect"], [44, 32, 38, 30], [44, 33, 38, 31, "node"], [44, 37, 38, 35], [44, 38, 38, 36], [45, 12, 39, 10, "height"], [45, 18, 39, 16], [45, 21, 39, 19, "_getRect"], [45, 29, 39, 27], [45, 30, 39, 28, "height"], [45, 36, 39, 34], [46, 12, 40, 10, "left"], [46, 16, 40, 14], [46, 19, 40, 17, "_getRect"], [46, 27, 40, 25], [46, 28, 40, 26, "left"], [46, 32, 40, 30], [47, 12, 41, 10, "top"], [47, 15, 41, 13], [47, 18, 41, 16, "_getRect"], [47, 26, 41, 24], [47, 27, 41, 25, "top"], [47, 30, 41, 28], [48, 12, 42, 10, "width"], [48, 17, 42, 15], [48, 20, 42, 18, "_getRect"], [48, 28, 42, 26], [48, 29, 42, 27, "width"], [48, 34, 42, 32], [49, 10, 43, 8], [49, 14, 43, 12, "x"], [49, 15, 43, 13], [49, 18, 43, 16, "left"], [49, 22, 43, 20], [49, 25, 43, 23, "relativeRect"], [49, 37, 43, 35], [49, 38, 43, 36, "left"], [49, 42, 43, 40], [50, 10, 44, 8], [50, 14, 44, 12, "y"], [50, 15, 44, 13], [50, 18, 44, 16, "top"], [50, 21, 44, 19], [50, 24, 44, 22, "relativeRect"], [50, 36, 44, 34], [50, 37, 44, 35, "top"], [50, 40, 44, 38], [51, 10, 45, 8, "callback"], [51, 18, 45, 16], [51, 19, 45, 17, "x"], [51, 20, 45, 18], [51, 22, 45, 20, "y"], [51, 23, 45, 21], [51, 25, 45, 23, "width"], [51, 30, 45, 28], [51, 32, 45, 30, "height"], [51, 38, 45, 36], [51, 40, 45, 38, "left"], [51, 44, 45, 42], [51, 46, 45, 44, "top"], [51, 49, 45, 47], [51, 50, 45, 48], [52, 8, 46, 6], [53, 6, 47, 4], [53, 7, 47, 5], [53, 9, 47, 7], [53, 10, 47, 8], [53, 11, 47, 9], [54, 4, 48, 2], [55, 2, 49, 0], [55, 3, 49, 1], [56, 2, 50, 0], [56, 6, 50, 4, "elementsToIgnore"], [56, 22, 50, 20], [56, 25, 50, 23], [57, 4, 51, 2, "A"], [57, 5, 51, 3], [57, 7, 51, 5], [57, 11, 51, 9], [58, 4, 52, 2, "BODY"], [58, 8, 52, 6], [58, 10, 52, 8], [58, 14, 52, 12], [59, 4, 53, 2, "INPUT"], [59, 9, 53, 7], [59, 11, 53, 9], [59, 15, 53, 13], [60, 4, 54, 2, "SELECT"], [60, 10, 54, 8], [60, 12, 54, 10], [60, 16, 54, 14], [61, 4, 55, 2, "TEXTAREA"], [61, 12, 55, 10], [61, 14, 55, 12], [62, 2, 56, 0], [62, 3, 56, 1], [63, 2, 57, 0], [63, 6, 57, 4, "UIManager"], [63, 15, 57, 13], [63, 18, 57, 16], [64, 4, 58, 2, "blur"], [64, 8, 58, 6, "blur"], [64, 9, 58, 7, "node"], [64, 13, 58, 11], [64, 15, 58, 13], [65, 6, 59, 4], [65, 10, 59, 8], [66, 8, 60, 6, "node"], [66, 12, 60, 10], [66, 13, 60, 11, "blur"], [66, 17, 60, 15], [66, 18, 60, 16], [66, 19, 60, 17], [67, 6, 61, 4], [67, 7, 61, 5], [67, 8, 61, 6], [67, 15, 61, 13, "err"], [67, 18, 61, 16], [67, 20, 61, 18], [67, 21, 61, 19], [68, 4, 62, 2], [68, 5, 62, 3], [69, 4, 63, 2, "focus"], [69, 9, 63, 7, "focus"], [69, 10, 63, 8, "node"], [69, 14, 63, 12], [69, 16, 63, 14], [70, 6, 64, 4], [70, 10, 64, 8], [71, 8, 65, 6], [71, 12, 65, 10, "name"], [71, 16, 65, 14], [71, 19, 65, 17, "node"], [71, 23, 65, 21], [71, 24, 65, 22, "nodeName"], [71, 32, 65, 30], [72, 8, 66, 6], [73, 8, 67, 6], [74, 8, 68, 6], [75, 8, 69, 6], [75, 12, 69, 10, "node"], [75, 16, 69, 14], [75, 17, 69, 15, "getAttribute"], [75, 29, 69, 27], [75, 30, 69, 28], [75, 40, 69, 38], [75, 41, 69, 39], [75, 45, 69, 43], [75, 49, 69, 47], [75, 53, 69, 51, "node"], [75, 57, 69, 55], [75, 58, 69, 56, "isContentEditable"], [75, 75, 69, 73], [75, 80, 69, 78], [75, 84, 69, 82], [75, 88, 69, 86, "elementsToIgnore"], [75, 104, 69, 102], [75, 105, 69, 103, "name"], [75, 109, 69, 107], [75, 110, 69, 108], [75, 114, 69, 112], [75, 118, 69, 116], [75, 120, 69, 118], [76, 10, 70, 8, "node"], [76, 14, 70, 12], [76, 15, 70, 13, "setAttribute"], [76, 27, 70, 25], [76, 28, 70, 26], [76, 38, 70, 36], [76, 40, 70, 38], [76, 44, 70, 42], [76, 45, 70, 43], [77, 8, 71, 6], [78, 8, 72, 6, "node"], [78, 12, 72, 10], [78, 13, 72, 11, "focus"], [78, 18, 72, 16], [78, 19, 72, 17], [78, 20, 72, 18], [79, 6, 73, 4], [79, 7, 73, 5], [79, 8, 73, 6], [79, 15, 73, 13, "err"], [79, 18, 73, 16], [79, 20, 73, 18], [79, 21, 73, 19], [80, 4, 74, 2], [80, 5, 74, 3], [81, 4, 75, 2, "measure"], [81, 11, 75, 9, "measure"], [81, 12, 75, 10, "node"], [81, 16, 75, 14], [81, 18, 75, 16, "callback"], [81, 26, 75, 24], [81, 28, 75, 26], [82, 6, 76, 4, "measureLayout"], [82, 19, 76, 17], [82, 20, 76, 18, "node"], [82, 24, 76, 22], [82, 26, 76, 24], [82, 30, 76, 28], [82, 32, 76, 30, "callback"], [82, 40, 76, 38], [82, 41, 76, 39], [83, 4, 77, 2], [83, 5, 77, 3], [84, 4, 78, 2, "measureInWindow"], [84, 19, 78, 17, "measureInWindow"], [84, 20, 78, 18, "node"], [84, 24, 78, 22], [84, 26, 78, 24, "callback"], [84, 34, 78, 32], [84, 36, 78, 34], [85, 6, 79, 4], [85, 10, 79, 8, "node"], [85, 14, 79, 12], [85, 16, 79, 14], [86, 8, 80, 6, "setTimeout"], [86, 18, 80, 16], [86, 19, 80, 17], [86, 25, 80, 23], [87, 10, 81, 8], [87, 14, 81, 12, "_getBoundingClientRec"], [87, 35, 81, 33], [87, 38, 81, 36], [87, 42, 81, 36, "getBoundingClientRect"], [87, 72, 81, 57], [87, 74, 81, 58, "node"], [87, 78, 81, 62], [87, 79, 81, 63], [88, 12, 82, 10, "height"], [88, 18, 82, 16], [88, 21, 82, 19, "_getBoundingClientRec"], [88, 42, 82, 40], [88, 43, 82, 41, "height"], [88, 49, 82, 47], [89, 12, 83, 10, "left"], [89, 16, 83, 14], [89, 19, 83, 17, "_getBoundingClientRec"], [89, 40, 83, 38], [89, 41, 83, 39, "left"], [89, 45, 83, 43], [90, 12, 84, 10, "top"], [90, 15, 84, 13], [90, 18, 84, 16, "_getBoundingClientRec"], [90, 39, 84, 37], [90, 40, 84, 38, "top"], [90, 43, 84, 41], [91, 12, 85, 10, "width"], [91, 17, 85, 15], [91, 20, 85, 18, "_getBoundingClientRec"], [91, 41, 85, 39], [91, 42, 85, 40, "width"], [91, 47, 85, 45], [92, 10, 86, 8, "callback"], [92, 18, 86, 16], [92, 19, 86, 17, "left"], [92, 23, 86, 21], [92, 25, 86, 23, "top"], [92, 28, 86, 26], [92, 30, 86, 28, "width"], [92, 35, 86, 33], [92, 37, 86, 35, "height"], [92, 43, 86, 41], [92, 44, 86, 42], [93, 8, 87, 6], [93, 9, 87, 7], [93, 11, 87, 9], [93, 12, 87, 10], [93, 13, 87, 11], [94, 6, 88, 4], [95, 4, 89, 2], [95, 5, 89, 3], [96, 4, 90, 2, "measureLayout"], [96, 17, 90, 15, "measureLayout"], [96, 18, 90, 16, "node"], [96, 22, 90, 20], [96, 24, 90, 22, "relativeToNativeNode"], [96, 44, 90, 42], [96, 46, 90, 44, "onFail"], [96, 52, 90, 50], [96, 54, 90, 52, "onSuccess"], [96, 63, 90, 61], [96, 65, 90, 63], [97, 6, 91, 4, "measureLayout"], [97, 19, 91, 17], [97, 20, 91, 18, "node"], [97, 24, 91, 22], [97, 26, 91, 24, "relativeToNativeNode"], [97, 46, 91, 44], [97, 48, 91, 46, "onSuccess"], [97, 57, 91, 55], [97, 58, 91, 56], [98, 4, 92, 2], [98, 5, 92, 3], [99, 4, 93, 2, "updateView"], [99, 14, 93, 12, "updateView"], [99, 15, 93, 13, "node"], [99, 19, 93, 17], [99, 21, 93, 19, "props"], [99, 26, 93, 24], [99, 28, 93, 26], [100, 6, 94, 4], [100, 11, 94, 9], [100, 15, 94, 13, "prop"], [100, 19, 94, 17], [100, 23, 94, 21, "props"], [100, 28, 94, 26], [100, 30, 94, 28], [101, 8, 95, 6], [101, 12, 95, 10], [101, 13, 95, 11, "Object"], [101, 19, 95, 17], [101, 20, 95, 18, "prototype"], [101, 29, 95, 27], [101, 30, 95, 28, "hasOwnProperty"], [101, 44, 95, 42], [101, 45, 95, 43, "call"], [101, 49, 95, 47], [101, 50, 95, 48, "props"], [101, 55, 95, 53], [101, 57, 95, 55, "prop"], [101, 61, 95, 59], [101, 62, 95, 60], [101, 64, 95, 62], [102, 10, 96, 8], [103, 8, 97, 6], [104, 8, 98, 6], [104, 12, 98, 10, "value"], [104, 17, 98, 15], [104, 20, 98, 18, "props"], [104, 25, 98, 23], [104, 26, 98, 24, "prop"], [104, 30, 98, 28], [104, 31, 98, 29], [105, 8, 99, 6], [105, 16, 99, 14, "prop"], [105, 20, 99, 18], [106, 10, 100, 8], [106, 15, 100, 13], [106, 22, 100, 20], [107, 12, 101, 10], [108, 14, 102, 12], [108, 18, 102, 12, "setValueForStyles"], [108, 44, 102, 29], [108, 46, 102, 30, "node"], [108, 50, 102, 34], [108, 52, 102, 36, "value"], [108, 57, 102, 41], [108, 58, 102, 42], [109, 14, 103, 12], [110, 12, 104, 10], [111, 10, 105, 8], [111, 15, 105, 13], [111, 22, 105, 20], [112, 10, 106, 8], [112, 15, 106, 13], [112, 26, 106, 24], [113, 12, 107, 10], [114, 14, 108, 12, "node"], [114, 18, 108, 16], [114, 19, 108, 17, "setAttribute"], [114, 31, 108, 29], [114, 32, 108, 30], [114, 39, 108, 37], [114, 41, 108, 39, "value"], [114, 46, 108, 44], [114, 47, 108, 45], [115, 14, 109, 12], [116, 12, 110, 10], [117, 10, 111, 8], [117, 15, 111, 13], [117, 21, 111, 19], [118, 10, 112, 8], [118, 15, 112, 13], [118, 22, 112, 20], [119, 12, 113, 10], [120, 12, 114, 10, "node"], [120, 16, 114, 14], [120, 17, 114, 15, "value"], [120, 22, 114, 20], [120, 25, 114, 23, "value"], [120, 30, 114, 28], [121, 12, 115, 10], [122, 10, 116, 8], [123, 12, 117, 10, "node"], [123, 16, 117, 14], [123, 17, 117, 15, "setAttribute"], [123, 29, 117, 27], [123, 30, 117, 28, "prop"], [123, 34, 117, 32], [123, 36, 117, 34, "value"], [123, 41, 117, 39], [123, 42, 117, 40], [124, 8, 118, 6], [125, 6, 119, 4], [126, 4, 120, 2], [126, 5, 120, 3], [127, 4, 121, 2, "configureNextLayoutAnimation"], [127, 32, 121, 30, "configureNextLayoutAnimation"], [127, 33, 121, 31, "config"], [127, 39, 121, 37], [127, 41, 121, 39, "onAnimationDidEnd"], [127, 58, 121, 56], [127, 60, 121, 58], [128, 6, 122, 4, "onAnimationDidEnd"], [128, 23, 122, 21], [128, 24, 122, 22], [128, 25, 122, 23], [129, 4, 123, 2], [129, 5, 123, 3], [130, 4, 124, 2], [131, 4, 125, 2, "setLayoutAnimationEnabledExperimental"], [131, 41, 125, 39, "setLayoutAnimationEnabledExperimental"], [131, 42, 125, 39], [131, 44, 125, 42], [131, 45, 125, 43], [132, 2, 126, 0], [132, 3, 126, 1], [133, 2, 126, 2], [133, 6, 126, 2, "_default"], [133, 14, 126, 2], [133, 17, 126, 2, "exports"], [133, 24, 126, 2], [133, 25, 126, 2, "default"], [133, 32, 126, 2], [133, 35, 127, 15, "UIManager"], [133, 44, 127, 24], [134, 0, 127, 24], [134, 3]], "functionMap": {"names": ["<global>", "getRect", "measureLayout", "setTimeout$argument_0", "UIManager.blur", "UIManager.focus", "UIManager.measure", "UIManager.measureInWindow", "UIManager.measureLayout", "UIManager.updateView", "UIManager.configureNextLayoutAnimation", "UIManager.setLayoutAnimationEnabledExperimental"], "mappings": "AAA;cCW;CDmB;oBEC;eCG;KDY;CFE;EIS;GJI;EKC;GLW;EMC;GNE;EOC;iBJE;OIO;GPE;EQC;GRE;ESC;GT2B;EUC;GVE;EWE,0CX"}}, "type": "js/module"}]}