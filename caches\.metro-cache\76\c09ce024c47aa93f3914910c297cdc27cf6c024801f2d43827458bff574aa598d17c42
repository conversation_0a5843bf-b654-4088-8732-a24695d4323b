{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 42, "index": 57}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 58}, "end": {"line": 4, "column": 49, "index": 107}}], "key": "6AA7RQghlqlrd3hVWNoLh/rI420=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.componentWithRef = componentWithRef;\n  exports.isFirstReactRender = isFirstReactRender;\n  exports.isReactRendering = isReactRendering;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _PlatformChecker = require(_dependencyMap[1], \"./PlatformChecker.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const IS_REACT_19 = (0, _PlatformChecker.isReact19)();\n  function getCurrentReactOwner() {\n    return (\n      // @ts-expect-error React secret internals aren't typed\n      _react.default.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE?.A?.getOwner() ||\n      // @ts-expect-error React secret internals aren't typed\n      _react.default.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED?.ReactCurrentOwner?.current ||\n      // @ts-expect-error React secret internals aren't typed\n      _react.default.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE?.ReactCurrentOwner?.current\n    );\n  }\n  function isReactRendering() {\n    return !!getCurrentReactOwner();\n  }\n  function isFirstReactRender() {\n    const currentOwner = getCurrentReactOwner();\n    // alternate is not null only after the first render and stores all the\n    // data from the previous component render\n    return currentOwner && !currentOwner?.alternate;\n  }\n\n  // This is an adjusted version of https://github.com/adobe/react-spectrum/issues/7494#issuecomment-2546940052\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  function componentWithRef(render) {\n    if (IS_REACT_19) {\n      return ({\n        ref,\n        ...props\n      }) => render(props, ref);\n    }\n    return /*#__PURE__*/(0, _react.forwardRef)(render);\n  }\n});", "lineCount": 45, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "componentWithRef"], [7, 26, 1, 13], [7, 29, 1, 13, "componentWithRef"], [7, 45, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "isFirstReactRender"], [8, 28, 1, 13], [8, 31, 1, 13, "isFirstReactRender"], [8, 49, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "isReactRendering"], [9, 26, 1, 13], [9, 29, 1, 13, "isReactRendering"], [9, 45, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_react"], [10, 12, 3, 0], [10, 15, 3, 0, "_interopRequireWildcard"], [10, 38, 3, 0], [10, 39, 3, 0, "require"], [10, 46, 3, 0], [10, 47, 3, 0, "_dependencyMap"], [10, 61, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_PlatformChecker"], [11, 22, 4, 0], [11, 25, 4, 0, "require"], [11, 32, 4, 0], [11, 33, 4, 0, "_dependencyMap"], [11, 47, 4, 0], [12, 2, 4, 49], [12, 11, 4, 49, "_interopRequireWildcard"], [12, 35, 4, 49, "e"], [12, 36, 4, 49], [12, 38, 4, 49, "t"], [12, 39, 4, 49], [12, 68, 4, 49, "WeakMap"], [12, 75, 4, 49], [12, 81, 4, 49, "r"], [12, 82, 4, 49], [12, 89, 4, 49, "WeakMap"], [12, 96, 4, 49], [12, 100, 4, 49, "n"], [12, 101, 4, 49], [12, 108, 4, 49, "WeakMap"], [12, 115, 4, 49], [12, 127, 4, 49, "_interopRequireWildcard"], [12, 150, 4, 49], [12, 162, 4, 49, "_interopRequireWildcard"], [12, 163, 4, 49, "e"], [12, 164, 4, 49], [12, 166, 4, 49, "t"], [12, 167, 4, 49], [12, 176, 4, 49, "t"], [12, 177, 4, 49], [12, 181, 4, 49, "e"], [12, 182, 4, 49], [12, 186, 4, 49, "e"], [12, 187, 4, 49], [12, 188, 4, 49, "__esModule"], [12, 198, 4, 49], [12, 207, 4, 49, "e"], [12, 208, 4, 49], [12, 214, 4, 49, "o"], [12, 215, 4, 49], [12, 217, 4, 49, "i"], [12, 218, 4, 49], [12, 220, 4, 49, "f"], [12, 221, 4, 49], [12, 226, 4, 49, "__proto__"], [12, 235, 4, 49], [12, 243, 4, 49, "default"], [12, 250, 4, 49], [12, 252, 4, 49, "e"], [12, 253, 4, 49], [12, 270, 4, 49, "e"], [12, 271, 4, 49], [12, 294, 4, 49, "e"], [12, 295, 4, 49], [12, 320, 4, 49, "e"], [12, 321, 4, 49], [12, 330, 4, 49, "f"], [12, 331, 4, 49], [12, 337, 4, 49, "o"], [12, 338, 4, 49], [12, 341, 4, 49, "t"], [12, 342, 4, 49], [12, 345, 4, 49, "n"], [12, 346, 4, 49], [12, 349, 4, 49, "r"], [12, 350, 4, 49], [12, 358, 4, 49, "o"], [12, 359, 4, 49], [12, 360, 4, 49, "has"], [12, 363, 4, 49], [12, 364, 4, 49, "e"], [12, 365, 4, 49], [12, 375, 4, 49, "o"], [12, 376, 4, 49], [12, 377, 4, 49, "get"], [12, 380, 4, 49], [12, 381, 4, 49, "e"], [12, 382, 4, 49], [12, 385, 4, 49, "o"], [12, 386, 4, 49], [12, 387, 4, 49, "set"], [12, 390, 4, 49], [12, 391, 4, 49, "e"], [12, 392, 4, 49], [12, 394, 4, 49, "f"], [12, 395, 4, 49], [12, 411, 4, 49, "t"], [12, 412, 4, 49], [12, 416, 4, 49, "e"], [12, 417, 4, 49], [12, 433, 4, 49, "t"], [12, 434, 4, 49], [12, 441, 4, 49, "hasOwnProperty"], [12, 455, 4, 49], [12, 456, 4, 49, "call"], [12, 460, 4, 49], [12, 461, 4, 49, "e"], [12, 462, 4, 49], [12, 464, 4, 49, "t"], [12, 465, 4, 49], [12, 472, 4, 49, "i"], [12, 473, 4, 49], [12, 477, 4, 49, "o"], [12, 478, 4, 49], [12, 481, 4, 49, "Object"], [12, 487, 4, 49], [12, 488, 4, 49, "defineProperty"], [12, 502, 4, 49], [12, 507, 4, 49, "Object"], [12, 513, 4, 49], [12, 514, 4, 49, "getOwnPropertyDescriptor"], [12, 538, 4, 49], [12, 539, 4, 49, "e"], [12, 540, 4, 49], [12, 542, 4, 49, "t"], [12, 543, 4, 49], [12, 550, 4, 49, "i"], [12, 551, 4, 49], [12, 552, 4, 49, "get"], [12, 555, 4, 49], [12, 559, 4, 49, "i"], [12, 560, 4, 49], [12, 561, 4, 49, "set"], [12, 564, 4, 49], [12, 568, 4, 49, "o"], [12, 569, 4, 49], [12, 570, 4, 49, "f"], [12, 571, 4, 49], [12, 573, 4, 49, "t"], [12, 574, 4, 49], [12, 576, 4, 49, "i"], [12, 577, 4, 49], [12, 581, 4, 49, "f"], [12, 582, 4, 49], [12, 583, 4, 49, "t"], [12, 584, 4, 49], [12, 588, 4, 49, "e"], [12, 589, 4, 49], [12, 590, 4, 49, "t"], [12, 591, 4, 49], [12, 602, 4, 49, "f"], [12, 603, 4, 49], [12, 608, 4, 49, "e"], [12, 609, 4, 49], [12, 611, 4, 49, "t"], [12, 612, 4, 49], [13, 2, 5, 0], [13, 8, 5, 6, "IS_REACT_19"], [13, 19, 5, 17], [13, 22, 5, 20], [13, 26, 5, 20, "isReact19"], [13, 52, 5, 29], [13, 54, 5, 30], [13, 55, 5, 31], [14, 2, 6, 0], [14, 11, 6, 9, "getCurrentReactOwner"], [14, 31, 6, 29, "getCurrentReactOwner"], [14, 32, 6, 29], [14, 34, 6, 32], [15, 4, 7, 2], [16, 6, 8, 4], [17, 6, 9, 4, "React"], [17, 20, 9, 9], [17, 21, 9, 10, "__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE"], [17, 84, 9, 73], [17, 86, 9, 75, "A"], [17, 87, 9, 76], [17, 89, 9, 78, "get<PERSON>wner"], [17, 97, 9, 86], [17, 98, 9, 87], [17, 99, 9, 88], [18, 6, 10, 4], [19, 6, 11, 4, "React"], [19, 20, 11, 9], [19, 21, 11, 10, "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED"], [19, 71, 11, 60], [19, 73, 11, 62, "ReactCurrentOwner"], [19, 90, 11, 79], [19, 92, 11, 81, "current"], [19, 99, 11, 88], [20, 6, 12, 4], [21, 6, 13, 4, "React"], [21, 20, 13, 9], [21, 21, 13, 10, "__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE"], [21, 84, 13, 73], [21, 86, 13, 75, "ReactCurrentOwner"], [21, 103, 13, 92], [21, 105, 13, 94, "current"], [22, 4, 13, 101], [23, 2, 15, 0], [24, 2, 16, 7], [24, 11, 16, 16, "isReactRendering"], [24, 27, 16, 32, "isReactRendering"], [24, 28, 16, 32], [24, 30, 16, 35], [25, 4, 17, 2], [25, 11, 17, 9], [25, 12, 17, 10], [25, 13, 17, 11, "getCurrentReactOwner"], [25, 33, 17, 31], [25, 34, 17, 32], [25, 35, 17, 33], [26, 2, 18, 0], [27, 2, 19, 7], [27, 11, 19, 16, "isFirstReactRender"], [27, 29, 19, 34, "isFirstReactRender"], [27, 30, 19, 34], [27, 32, 19, 37], [28, 4, 20, 2], [28, 10, 20, 8, "current<PERSON>wner"], [28, 22, 20, 20], [28, 25, 20, 23, "getCurrentReactOwner"], [28, 45, 20, 43], [28, 46, 20, 44], [28, 47, 20, 45], [29, 4, 21, 2], [30, 4, 22, 2], [31, 4, 23, 2], [31, 11, 23, 9, "current<PERSON>wner"], [31, 23, 23, 21], [31, 27, 23, 25], [31, 28, 23, 26, "current<PERSON>wner"], [31, 40, 23, 38], [31, 42, 23, 40, "alternate"], [31, 51, 23, 49], [32, 2, 24, 0], [34, 2, 26, 0], [35, 2, 27, 0], [36, 2, 28, 7], [36, 11, 28, 16, "componentWithRef"], [36, 27, 28, 32, "componentWithRef"], [36, 28, 28, 33, "render"], [36, 34, 28, 39], [36, 36, 28, 41], [37, 4, 29, 2], [37, 8, 29, 6, "IS_REACT_19"], [37, 19, 29, 17], [37, 21, 29, 19], [38, 6, 30, 4], [38, 13, 30, 11], [38, 14, 30, 12], [39, 8, 31, 6, "ref"], [39, 11, 31, 9], [40, 8, 32, 6], [40, 11, 32, 9, "props"], [41, 6, 33, 4], [41, 7, 33, 5], [41, 12, 33, 10, "render"], [41, 18, 33, 16], [41, 19, 33, 17, "props"], [41, 24, 33, 22], [41, 26, 33, 24, "ref"], [41, 29, 33, 27], [41, 30, 33, 28], [42, 4, 34, 2], [43, 4, 35, 2], [43, 24, 35, 9], [43, 28, 35, 9, "forwardRef"], [43, 45, 35, 19], [43, 47, 35, 20, "render"], [43, 53, 35, 26], [43, 54, 35, 27], [44, 2, 36, 0], [45, 0, 36, 1], [45, 3]], "functionMap": {"names": ["<global>", "getCurrentReactOwner", "isReactRendering", "isFirstReactRender", "componentWithRef", "<anonymous>"], "mappings": "AAA;ACK;CDS;OEC;CFE;OGC;CHK;OII;WCE;4BDG;CJG"}}, "type": "js/module"}]}